<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout_b4}">

<head>
    <title>View Boost Tenant</title>
    <link th:href="@{/dist/css/boostTenants.css}" rel="stylesheet">
</head>

<body>

<div layout:fragment="content" id="root">
    <div class="row wrapper border-bottom white-bg page-heading">
        <div class="col-lg-10">
            <h1>View Boost Tenant</h1>
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="/boostTenants">Boost Tenants</a>
                </li>
                <li class="breadcrumb-item active">
                    <strong>View</strong>
                </li>
            </ol>
        </div>
        <div class="header-button-wrapper">
            <a class="btn btn-primary btn-md" th:href="@{'/boostTenants?form'}">
                <em class="fa fa-plus fa-fw"></em> Add Boost Tenant
            </a>
            <a class="btn btn-secondary btn-md" th:href="@{'/boostTenants'}">
                <em class="fa fa-arrow-left fa-fw"></em> Back to Search
            </a>
        </div>
    </div>

    <div class="container-fluid">
        <view-tenant></view-tenant>
        <div class="row">
            <div class="col-lg-12">
                <!-- Tenant details will be rendered by the Vue component -->
            </div>
        </div>
    </div>
</div>

<th:block layout:fragment="page-scripts">
    <script type="text/javascript" th:src="@{/dist/js/boostTenants.js}"></script>
</th:block>

</body>
</html>
