package com.carsaver.portal.client.product;

import com.carsaver.magellan.auth.CarSaverAuthService;
import com.carsaver.magellan.auth.TokenResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xebialabs.restito.semantics.Action;
import com.xebialabs.restito.server.StubServer;
import lombok.SneakyThrows;
import org.glassfish.grizzly.http.util.HttpStatus;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration;
import org.springframework.boot.autoconfigure.web.reactive.HttpHandlerAutoConfiguration;
import org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.client.CommonsClientAutoConfiguration;
import org.springframework.cloud.commons.httpclient.HttpClientConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.Pageable;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.PagedModel;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

import static com.xebialabs.restito.builder.stub.StubHttp.whenHttp;
import static com.xebialabs.restito.semantics.Action.*;
import static io.netty.util.internal.SystemPropertyUtil.get;
import static org.mockito.Mockito.mock;
import static org.unitils.reflectionassert.ReflectionAssert.assertReflectionEquals;

@SpringBootTest
@ActiveProfiles("client-test")
@TestPropertySource(properties =
    "harbor-service.api-uri=http://localhost:52422")
class ProductClientTest {

    private final StubServer server = new StubServer(52422).run();

    private final Pageable pageable = mock(Pageable.class);

    @MockBean
    private CarSaverAuthService carSaverAuthService;

    @Autowired
    private ProductClient productClient;

    @Configuration
    @EnableFeignClients(clients = ProductClient.class)
    @Import({FeignAutoConfiguration.class,
        ClientHttpConnectorAutoConfiguration.class,
        HttpClientConfiguration.class,
        HttpMessageConvertersAutoConfiguration.class,
        HttpHandlerAutoConfiguration.class,
        CommonsClientAutoConfiguration.class})

    @Profile("client-test")
    public static class ProductTestConfiguration {
        @Bean
        public ObjectMapper mapper() {
            return new ObjectMapper();
        }
    }

    @Test
    public void test() {
        TokenResponse tokenResponse = new TokenResponse();
        tokenResponse.setAccessToken("access_token");
        Mockito.when(carSaverAuthService.getToken()).thenReturn(tokenResponse);

        ProductView productView = new ProductView();
        productView.setId(123);
        productView.setName("Test");
        List<ProductView> expected = List.of(
            ProductView.builder()
                .id(123)
                .name("Test")

                .build()
        );
        whenHttp(server)
            .match(get("/products"))
            .then(status(HttpStatus.OK_200), jsonContent(CollectionModel.of(expected)), contentType("application/json"));


        CollectionModel<ProductView> actual = productClient.findAll();
        assertReflectionEquals(expected, actual.getContent());

        PagedModel<ProductView> actual1 = productClient.findAll(pageable);
        assertReflectionEquals(expected, actual1.getContent());
    }

    private final ObjectMapper objectMapper = new ObjectMapper();

    @SneakyThrows
    private Action jsonContent(Object object) {
        return stringContent(objectMapper.writeValueAsString(object));
    }
}
