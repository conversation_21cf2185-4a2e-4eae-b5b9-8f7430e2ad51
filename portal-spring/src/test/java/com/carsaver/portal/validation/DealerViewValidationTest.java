package com.carsaver.portal.validation;

import com.carsaver.core.SalesTxSource;
import com.carsaver.magellan.model.DealerView;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Created by mi<PERSON><PERSON><PERSON><PERSON> on 2/7/17.
 */
@Slf4j
public class DealerViewValidationTest {

    private DealerViewValidator validator;
    private Errors errors;
    private List<DealerView.HoursOfOperation> hoursOfOperations;

    @BeforeEach
    public void setUp() {
        validator = new DealerViewValidator();
        hoursOfOperations = new ArrayList<>();
        DealerView dealerView = new DealerView();
        dealerView.setHoursOfOperations(hoursOfOperations);
        errors = new BeanPropertyBindingResult(dealerView, "dealerView");
    }

    @Test
    public void testValidateHoursOfOperation1() {

        validator.validateHoursOfOperation(hoursOfOperations, errors);

        assertTrue(errors.hasErrors(), "should have errors");

        log.info("errors are = {}", errors.getAllErrors());
    }

    @Test
    public void testValidateHoursOfOperation2() {

        validator.validateHoursOfOperation(null, errors);

        assertTrue(errors.hasErrors(), "should have errors");

        log.info("errors are = {}", errors.getAllErrors());
    }

    @Test
    public void testValidateHoursOfOperation3() {

        validator.validateHoursOfOperation(hoursOfOperations, errors);

        assertTrue(errors.hasErrors(), "should not be empty");

        log.info("errors are = {}", errors.getAllErrors());
    }

    @Test
    public void testValidateHoursOfOperation4() {

        validator.validateHoursOfOperation(hoursOfOperations, errors);

        assertTrue(errors.hasErrors(), "hours of operation should be in correct format: \"hh:mm PM|pm|AM|am\" or \"CLOSED\"");

        log.info("errors are = {}", errors.getAllErrors());
    }

    @Test
    public void testValidateHoursOfOperation5() {
        hoursOfOperations.add(new DealerView.HoursOfOperation("bad data", "bad data"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("bad data", "bad data"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("bad data", "bad data"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("bad data", "bad data"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("bad data", "bad data"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("bad data", "bad data"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("bad data", "bad data"));
        validator.validateHoursOfOperation(hoursOfOperations, errors);

        assertTrue(errors.hasErrors(), "The Hydrated Data is Bad Data");

        log.info("errors are = {}", errors.getAllErrors());
    }

    @Test
    public void testValidateHoursOfOperationWithOpenHoursClosed() {
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 AM", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 PM"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("CLOSED", "9:00 pm"));
        validator.validateHoursOfOperation(hoursOfOperations, errors);

        assertTrue(errors.hasErrors(), "Both open and close hours need to be set to 'CLOSED'");

        log.info("errors are = {}", errors.getAllErrors());
    }

    @Test
    public void testValidateHoursOfOperationWithCloseHoursClosed() {
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 AM", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 PM"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "CLOSED"));
        validator.validateHoursOfOperation(hoursOfOperations, errors);

        assertTrue(errors.hasErrors(), "Both open and close hours need to be set to 'CLOSED'");

        log.info("errors are = {}", errors.getAllErrors());
    }

    @Test
    public void testValidateHoursOfOperationAllGoodExceptOne() {
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 AM", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 PM"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("bad data", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        validator.validateHoursOfOperation(hoursOfOperations, errors);

        assertTrue(errors.hasErrors(), "The Hydrated Data is all good data except for one day");

        log.info("errors are = {}", errors.getAllErrors());
    }

    @Test
    public void testValidateHoursOfOperationWithNullCloseHours() {
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", null));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 AM", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 PM"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("bad data", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        validator.validateHoursOfOperation(hoursOfOperations, errors);

        assertTrue(errors.hasErrors());
        assertEquals(
            "close hours cannot be null",
            errors.getFieldError("hoursOfOperations").getDefaultMessage()
        );

        log.info("errors are = {}", errors.getAllErrors());
    }

    @Test
    public void testValidateHoursOfOperationWithNullOpenHours() {
        hoursOfOperations.add(new DealerView.HoursOfOperation(null, "9:00 am"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 AM", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 PM"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("bad data", "9:00 pm"));
        hoursOfOperations.add(new DealerView.HoursOfOperation("9:00 am", "9:00 pm"));
        validator.validateHoursOfOperation(hoursOfOperations, errors);

        assertTrue(errors.hasErrors());
        assertEquals(
            "open hours cannot be null",
            errors.getFieldError("hoursOfOperations").getDefaultMessage()
        );

        log.info("errors are = {}", errors.getAllErrors());
    }

    @Test
    public void testUpdatingSalesTxSourceToCdkPasses() {
        validator.validateSalesTxSourceUpdate(SalesTxSource.CDK, "vendorDmsId", errors);

        assertFalse(errors.hasErrors());
    }

    @Test
    public void testUpdatingSalesTxSourceToCdkWithNullVendorDmsIdFails() {
        validator.validateSalesTxSourceUpdate(SalesTxSource.CDK, null, errors);

        assertTrue(errors.hasErrors());
        assertEquals(
            "CDK dealers must have a Vendor DMS ID.",
            errors.getFieldError("salesTxSource").getDefaultMessage()
        );
    }

    @Test
    public void testUpdatingSalesTxSourceToCdkWithEmptyVendorDmsIdFails() {
        validator.validateSalesTxSourceUpdate(SalesTxSource.CDK, "", errors);

        assertTrue(errors.hasErrors());
        assertEquals(
            "CDK dealers must have a Vendor DMS ID.",
            errors.getFieldError("salesTxSource").getDefaultMessage()
        );
    }
}
