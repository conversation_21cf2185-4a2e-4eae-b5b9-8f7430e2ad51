package com.carsaver.portal.controller.api;

import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.portal.client.product.ProductView;
import com.carsaver.portal.client.program.ProgramClient;
import com.carsaver.portal.client.program.ProgramView;
import com.carsaver.portal.model.program.EditProgramForm;
import com.carsaver.portal.model.program.ProgramForm;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.hateoas.PagedModel;
import org.springframework.http.HttpStatus;

import java.lang.reflect.UndeclaredThrowableException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProgramsApiControllerTest {

    private final String ID = "123456789";

    private final Pageable pageable = PageRequest.of(1, 20);

    private final ProgramClient programClient = mock(ProgramClient.class);

    private final PagedModel.PageMetadata pageMetadata = mock(PagedModel.PageMetadata.class);

    private final PagedModel<ProgramView> pagedModel = mock(PagedModel.class);

    private final ProgramView programView = new ProgramView();

    private final ProductView productView = new ProductView();

    private final List<ProgramView> programList = new ArrayList<>();

    private final ProgramForm newProgram = new ProgramForm();

    private final ProgramForm existingProgram = new ProgramForm();

    private final EditProgramForm editProgram = new EditProgramForm();

    @InjectMocks
    private final ProgramsApiController programsApiController = new ProgramsApiController();

    @Test
    void fetchAllPrograms() {
        productView.setId(1);
        productView.setName("Test Product");

        programView.setId(ID);
        programView.setName("Test Program");
        programView.setProduct(productView);
        programView.setSupportEmail("<EMAIL>");
        programView.setSupportPhone("123456789");
        programView.setSupportGroupId(null);


        when(pagedModel.getContent()).thenReturn(Collections.emptyList());
        when(pagedModel.getMetadata()).thenReturn(pageMetadata);
        when(pageMetadata.getTotalElements()).thenReturn(1L);
        when(programClient.findAll(pageable)).thenReturn(pagedModel);
        when(pagedModel.getContent()).thenReturn(List.of(programView));
        assertThrows(NullPointerException.class, () -> programsApiController.fetchAllPrograms(pageable));

        PagedModel<ProgramView> programs = programClient.findAll(pageable);
        assertEquals(1, programs.getContent().size());
    }

    @Test
    void testAddProgram() {
        newProgram.setName("Test Program");
        existingProgram.setName("Test");

        BeanUtils.copyProperties(newProgram, programView);
        BeanUtils.copyProperties(existingProgram, programView);

        var u = new UndeclaredThrowableException(new Throwable());

        when(programClient.save(programView)).thenThrow(u);

        var newProgramResponse = programsApiController.addProgram(newProgram);
        assertEquals(HttpStatus.OK, newProgramResponse.getStatusCode());

        var existingProgramResponse = programsApiController.addProgram(existingProgram);
        assertEquals(HttpStatus.CONFLICT, existingProgramResponse.getStatusCode());
    }

    @Test
    void testFetchProgram() {
        productView.setId(1);
        productView.setName("Test Product");

        programView.setId(ID);
        programView.setName("Test Program");
        programView.setProduct(productView);
        programView.setSupportEmail("<EMAIL>");
        programView.setSupportPhone("123456789");
        programView.setSupportGroupId(null);

        ProgramView expected = programClient.findById(ID);
        ProgramView result = programsApiController.fetchProgram(ID);

        assertEquals(expected, result);
    }

    @Test
    void testUpdateProgram() {
        // When Name is unique
        editProgram.setName("Test Program");
        when(programClient.findById(ID)).thenReturn(programView);
        var newProgramResponse = programsApiController.updateProgram(ID, editProgram);
        assertEquals(HttpStatus.OK, newProgramResponse.getStatusCode());

        // When Name is not unique
        editProgram.setName("Test");
        when(programClient.findById(ID)).thenReturn(programView);
        var u = new UndeclaredThrowableException(new Throwable());
        when(programClient.save(programView)).thenThrow(u);
        var existingProgramResponse = programsApiController.updateProgram(ID, editProgram);
        assertEquals(HttpStatus.CONFLICT, existingProgramResponse.getStatusCode());
    }

    @Test
    void testFetchProgramsByName() {
        programView.setId(ID);
        programView.setName("Test Program");

        programList.add(programView);

        when(pagedModel.getContent()).thenReturn(Collections.emptyList());
        when(pagedModel.getMetadata()).thenReturn(pageMetadata);
        when(pageMetadata.getTotalElements()).thenReturn(1L);
        when(programClient.findByProgramName("T", PageRequestUtils.asOneBasedPage(pageable))).thenReturn(pagedModel);
        when(pagedModel.getContent()).thenReturn(List.of(programView));

        ProgramsApiController.PageWrapper expected = new ProgramsApiController.PageWrapper(pageMetadata, programList);

        ProgramsApiController.PageWrapper actual = programsApiController.fetchProgramsByName("T", PageRequestUtils.asOneBasedPage(pageable));

        assertEquals(expected, actual);
    }

    @Test
    void testFetchProgramsByName_WhenNameIsHavingSpecialChar() {
        programView.setName("CarSaver/atlas-app#124");

        programList.add(programView);

        when(pagedModel.getContent()).thenReturn(Collections.emptyList());
        when(pagedModel.getMetadata()).thenReturn(pageMetadata);
        when(pageMetadata.getTotalElements()).thenReturn(1L);
        when(programClient.findByProgramName("/atlas-app", PageRequestUtils.asOneBasedPage(pageable))).thenReturn(pagedModel);
        when(pagedModel.getContent()).thenReturn(List.of(programView));

        ProgramsApiController.PageWrapper expected = new ProgramsApiController.PageWrapper(pageMetadata, programList);

        ProgramsApiController.PageWrapper actual = programsApiController.fetchProgramsByName("/atlas-app", PageRequestUtils.asOneBasedPage(pageable));

        assertEquals(expected, actual);
    }
}
