package com.carsaver.portal.controller.api;

import com.carsaver.portal.client.tenant.TenantClient;
import com.carsaver.portal.client.tenant.TenantView;
import com.carsaver.portal.controller.advice.GlobalExceptionHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ActiveProfiles("controller-test")
@SpringBootTest
@AutoConfigureMockMvc
@TestPropertySource(properties =
    "spring.cloud.config.enabled=false"
)
public class TenantsApiControllerSpringTest {

    @MockBean
    private TenantClient tenantClient;

    @Autowired
    private TenantsApiController apiController;

    @Autowired
    private GlobalExceptionHandler globalExceptionHandler;


    @Configuration
    @Profile("controller-test")
    public static class ControllerTestConfiguration {

        @Bean
        public TenantsApiController controller() {
            return new TenantsApiController();
        }

        @Bean
        public GlobalExceptionHandler globalExceptionHandler() {
            return new GlobalExceptionHandler();
        }
    }

    @Test
    @WithMockUser(username = "username", roles = {"ADMIN"})
    void testValidation() throws Exception {
        TenantView tenantView = new TenantView();
        tenantView.setName("Test");

        MockMvc mockMvc = MockMvcBuilders.standaloneSetup(apiController)
            .setControllerAdvice(globalExceptionHandler)
            .build();

        mockMvc.perform(MockMvcRequestBuilders.get("/api/tenants/search")
                .content(asJsonString(tenantView))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isBadRequest());
    }

    public static String asJsonString(final Object obj) {
        try {
            return new ObjectMapper().writeValueAsString(obj);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
