package com.carsaver.portal.controller.api;

import com.carsaver.portal.model.store.dealer.dashboard.PricedWidget;
import com.carsaver.portal.service.dashboard.DashboardMetricsService;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class DashboardApiControllerTest {

    private final DashboardMetricsService dashboardMetricsService = mock(DashboardMetricsService.class);

    private final DashboardApiController dashboardApiController = new DashboardApiController(dashboardMetricsService);

    @Test
    void testGetNewInTransitPriced() {
        String dealerIds = "dealerIds";
        PricedWidget pricedWidget = new PricedWidget();
        pricedWidget.setPricedCount(100);
        pricedWidget.setTotal(100);
        DashboardApiController.DealerKpiCriteria dealerKpiCriteria = new DashboardApiController.DealerKpiCriteria();
        dealerKpiCriteria.setDealerIds(new ArrayList<>(List.of(dealerIds)));
        PricedWidget expected = pricedWidget;
        when(dashboardMetricsService.getNewInTransitPriced(dealerKpiCriteria.getDealerIds(), null)).thenReturn(pricedWidget);
        PricedWidget result = dashboardApiController.getNewInTransitPriced(dealerKpiCriteria);
        assertEquals(expected, result);
    }

    @Test
    void testGetUsedInTransitPriced() {
        String dealerIds = "dealerIds";
        PricedWidget pricedWidget = new PricedWidget();
        pricedWidget.setPricedCount(50);
        pricedWidget.setTotal(50);
        DashboardApiController.DealerKpiCriteria dealerKpiCriteria = new DashboardApiController.DealerKpiCriteria();
        dealerKpiCriteria.setDealerIds(new ArrayList<>(List.of(dealerIds)));
        PricedWidget expected = pricedWidget;
        when(dashboardMetricsService.getUsedInTransitPriced(dealerKpiCriteria.getDealerIds(), null)).thenReturn(pricedWidget);
        PricedWidget result = dashboardApiController.getUsedInTransitPriced(dealerKpiCriteria);
        assertEquals(expected, result);
    }
}
