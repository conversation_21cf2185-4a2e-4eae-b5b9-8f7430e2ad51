package com.carsaver.portal.controller.export;

import com.carsaver.elasticsearch.model.WalmartStoreDoc;
import com.carsaver.portal.elasticsearch.WalmartDocService;
import com.carsaver.portal.elasticsearch.criteria.WalmartSearchCriteria;
import com.carsaver.search.model.SearchMethod;
import org.junit.jupiter.api.Test;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class WalmartStoreExportControllerTest {

    public static final String ID = "123456";
    private final WalmartDocService walmartDocService = mock(WalmartDocService.class);
    private final WalmartStoreExportController walmartStoreExportController = new WalmartStoreExportController(walmartDocService);
    private final WalmartSearchCriteria walmartSearchCriteria = new WalmartSearchCriteria();
    private final List<String> columns = new ArrayList<>(List.of("id", "hasActiveDisplayVehicle"));
    private final List<String> negativeSearchColumns = new ArrayList<>(List.of("qwe", "rty"));
    private final HttpServletResponse httpServletResponse = mock(HttpServletResponse.class);
    private final StringWriter stringWriter = new StringWriter();

    @Test
    public void testExport() throws Exception {
        when(httpServletResponse.getWriter()).thenReturn(new PrintWriter(stringWriter));
        WalmartStoreDoc walmartStoreDoc = new WalmartStoreDoc();
        walmartStoreDoc.setHasActiveDisplayVehicle(true);
        walmartStoreDoc.setId(ID);
        when(walmartDocService.scrollStream(walmartSearchCriteria)).thenReturn(Stream.of(walmartStoreDoc));
        walmartStoreExportController.exportWalmartStoreListToCsv(walmartSearchCriteria, columns, negativeSearchColumns, httpServletResponse);
        assertEquals(Map.of(
            "rty", SearchMethod.NEGATIVE,
            "qwe", SearchMethod.NEGATIVE
        ), walmartSearchCriteria.getSearchMethods());
        assertEquals("Id,\"Has Active Display Vehicle\"\n123456,true\n", stringWriter.toString());
    }

}
