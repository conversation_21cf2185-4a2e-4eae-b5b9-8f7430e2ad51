package com.carsaver.portal.controller.export;

import com.carsaver.portal.elasticsearch.criteria.LocalVehicleSearchCriteria;
import com.carsaver.elasticsearch.model.vehicle.VehicleDoc;
import com.carsaver.elasticsearch.service.VehicleDocService;
import com.carsaver.search.model.SearchMethod;
import com.carsaver.search.model.IntegerRange;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class InventoryExportControllerTest {

    public static final String ID = "123456";
    private final VehicleDocService vehicleDocService = mock(VehicleDocService.class);
    private final InventoryExportController inventoryExportController = new InventoryExportController(vehicleDocService);
    private final LocalVehicleSearchCriteria vehicleSearchCriteria = new LocalVehicleSearchCriteria();
    private final List<String> columns = new ArrayList<>(List.of("id", "active"));
    private final List<String> negativeSearchColumns =  new ArrayList<>(List.of("qwe", "rty"));
    private final HttpServletResponse httpServletResponse = mock(HttpServletResponse.class);
    private final StringWriter stringWriter = new StringWriter();

    @Test
    public void testExport() throws Exception {
        when(httpServletResponse.getWriter()).thenReturn(new PrintWriter(stringWriter));
        PodamFactory podamFactory = new PodamFactoryImpl();
        VehicleDoc vehicleDoc = podamFactory.manufacturePojo(VehicleDoc.class);
        vehicleDoc.setActive(true);
        vehicleDoc.setId(ID);
        when(vehicleDocService.scrollStream(vehicleSearchCriteria)).thenReturn(Stream.of(vehicleDoc));
        inventoryExportController.exportInventoryListToCsv(vehicleSearchCriteria, columns, negativeSearchColumns, httpServletResponse);
        assertEquals(Map.of(
            "rty", SearchMethod.NEGATIVE ,
            "qwe", SearchMethod.NEGATIVE
        ), vehicleSearchCriteria.getSearchMethods());
        assertEquals("Id,Active\n123456,true\n", stringWriter.toString());
    }

    @Test
    public void testExportWithAllCriteriaTypes() throws Exception {
        when(httpServletResponse.getWriter()).thenReturn(new PrintWriter(stringWriter));
        PodamFactory podamFactory = new PodamFactoryImpl();
        VehicleDoc vehicleDoc = podamFactory.manufacturePojo(VehicleDoc.class);
        vehicleDoc.setActive(true);
        vehicleDoc.setId(ID);

        // Set up different criteria types
        vehicleSearchCriteria.setActive(true);
        vehicleSearchCriteria.setProgramIds(List.of("program1", "program2"));
        vehicleSearchCriteria.setDealerIds(List.of("dealer1", "dealer2"));
        vehicleSearchCriteria.setCertifiedDealer(true);
        vehicleSearchCriteria.setDealerName("Test Dealer");
        vehicleSearchCriteria.setDeliveryOnly(true);
        vehicleSearchCriteria.setStockTypes(List.of("NEW", "USED"));
        vehicleSearchCriteria.setYears(List.of("2022", "2023"));
        vehicleSearchCriteria.setPrice(new IntegerRange(10000, 50000));
        vehicleSearchCriteria.setBodyStyles(List.of("SUV", "SEDAN"));
        vehicleSearchCriteria.setMakes(List.of("Toyota", "Honda"));
        vehicleSearchCriteria.setModels(List.of("Camry", "Civic"));
        vehicleSearchCriteria.setVin("1HGCM82633A123456");

        when(vehicleDocService.scrollStream(vehicleSearchCriteria)).thenReturn(Stream.of(vehicleDoc));

        inventoryExportController.exportInventoryListToCsv(
                vehicleSearchCriteria,
                columns,
                negativeSearchColumns,
                httpServletResponse);

        // Verify search methods are set correctly
        assertEquals(Map.of(
                "rty", SearchMethod.NEGATIVE,
                "qwe", SearchMethod.NEGATIVE), vehicleSearchCriteria.getSearchMethods());

        // Verify the criteria types are preserved
        assertNotNull(vehicleSearchCriteria.getActive());
        assertTrue(vehicleSearchCriteria.getActive());
        assertEquals(2, vehicleSearchCriteria.getProgramIds().size());
        assertEquals(2, vehicleSearchCriteria.getDealerIds().size());
        assertTrue(vehicleSearchCriteria.getCertifiedDealer());
        assertEquals("Test Dealer", vehicleSearchCriteria.getDealerName());
        assertTrue(vehicleSearchCriteria.getDeliveryOnly());
        assertEquals(2, vehicleSearchCriteria.getStockTypes().size());
        assertEquals(2, vehicleSearchCriteria.getYears().size());
        assertEquals(Integer.valueOf(10000), vehicleSearchCriteria.getPrice().getLowerBound());
        assertEquals(Integer.valueOf(50000), vehicleSearchCriteria.getPrice().getUpperBound());
        assertEquals(2, vehicleSearchCriteria.getBodyStyles().size());
        assertEquals(2, vehicleSearchCriteria.getMakes().size());
        assertEquals(2, vehicleSearchCriteria.getModels().size());
        assertEquals("1HGCM82633A123456", vehicleSearchCriteria.getVin());

        // Verify CSV output
        assertEquals("Id,Active\n123456,true\n", stringWriter.toString());
    }
}
