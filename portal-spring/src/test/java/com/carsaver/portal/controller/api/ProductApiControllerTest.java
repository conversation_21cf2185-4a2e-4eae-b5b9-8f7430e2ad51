package com.carsaver.portal.controller.api;

import com.carsaver.portal.client.product.ProductClient;
import com.carsaver.portal.client.product.ProductView;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;
import org.springframework.hateoas.PagedModel;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProductApiControllerTest {

    private final Pageable pageable = mock(Pageable.class);
    private final ProductClient productClient = mock(ProductClient.class);
    private final PagedModel.PageMetadata pageMetadata = mock(PagedModel.PageMetadata.class);
    private final PagedModel<ProductView> pagedModel = mock(PagedModel.class);
    private final ProductView productView = spy(new ProductView());

    @InjectMocks
    private final ProductApiController productApiController = new ProductApiController();

    @Test
    void testFetchAllProducts() {
        productView.setId(101);
        productView.setName("Test");

        when(pagedModel.getContent()).thenReturn(Collections.emptyList());
        when(pagedModel.getMetadata()).thenReturn(pageMetadata);
        when(pageMetadata.getTotalElements()).thenReturn(1L);
        when(productClient.findAll(pageable)).thenReturn(pagedModel);
        when(pagedModel.getContent()).thenReturn(List.of(productView));
        assertThrows(IllegalArgumentException.class, () -> productApiController.fetchAllProducts(pageable));

        PagedModel<ProductView> tenants = productClient.findAll(pageable);
        assertEquals(1, tenants.getContent().size());
    }
}
