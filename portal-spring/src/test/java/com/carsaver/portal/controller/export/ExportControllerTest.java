package com.carsaver.portal.controller.export;

import com.carsaver.elasticsearch.model.LeadDoc;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

class ExportControllerTest {

    private final ObjectMapper objectMapper = new ObjectMapper()
            .findAndRegisterModules();

    @Test
    void test() throws IOException {
        var stringWriter = new StringWriter();
        var list = new ArrayList<>(List.of("dealer.subscriptions"));
        var builder = ExportController.createCsvWriter(stringWriter, list);
        var writer = builder.build();


        var doc = objectMapper.readValue(new File("src/test/resources/leadDoc.json"), LeadDoc.class);
        writer.writeHeader();
        writer.writeRow(doc);
        assertEquals("\"Dealer Subscriptions\"\n" +
                "\"<PERSON><PERSON>, <PERSON>\"\n", stringWriter.toString());

    }

}
