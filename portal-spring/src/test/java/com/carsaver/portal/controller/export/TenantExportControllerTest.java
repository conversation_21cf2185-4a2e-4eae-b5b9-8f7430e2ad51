package com.carsaver.portal.controller.export;

import com.carsaver.portal.client.tenant.TenantClient;
import com.carsaver.portal.client.tenant.TenantView;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.hateoas.PagedModel;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TenantExportControllerTest {

    private List<String> columns = List.of("id", "name");
    private final Pageable pageable = PageRequest.of(0, Integer.MAX_VALUE, Sort.by("name").ascending());
    private final TenantClient tenantClient = mock(TenantClient.class);
    private final PagedModel.PageMetadata pageMetadata = mock(PagedModel.PageMetadata.class);
    private final PagedModel<TenantView> pagedModel = mock(PagedModel.class);
    private final TenantView tenantView = spy(new TenantView());
    private final List<TenantView> tenantList = new ArrayList<>();
    private final HttpServletResponse response = mock(HttpServletResponse.class);
    private final StringWriter stringWriter = new StringWriter();

    @InjectMocks
    private final TenantExportController controller = new TenantExportController();


    @Test
    void testExportToCsv_WhenNoSearchData() throws IOException {
        when(response.getWriter()).thenReturn(new PrintWriter(stringWriter));

        tenantView.setId("123");
        tenantView.setName("Test");
        tenantList.add(tenantView);

        when(pagedModel.getContent()).thenReturn(tenantList);
        when(pagedModel.getMetadata()).thenReturn(pageMetadata);
        when(pageMetadata.getTotalElements()).thenReturn(1L);
        when(tenantClient.findAll(pageable)).thenReturn(pagedModel);

        controller.exportToCsv(null, columns, null, response);

        assertEquals("ID,Tenant Name" + "123,Test", stringWriter.toString().trim().replaceAll("\n", "").replaceAll("\r", ""));
    }

    @Test
    void testExportToCsv_WhenSearchData() throws Exception {
        String name = "Test";

        when(response.getWriter()).thenReturn(new PrintWriter(stringWriter));

        tenantView.setId("123");
        tenantView.setName("Test");
        tenantList.add(tenantView);

        when(pagedModel.getContent()).thenReturn(tenantList);
        when(pagedModel.getMetadata()).thenReturn(pageMetadata);
        when(pageMetadata.getTotalElements()).thenReturn(1L);
        when(tenantClient.findByTenantName("T", pageable)).thenReturn(pagedModel);

        controller.exportToCsv(name, columns, null, response);

        assertEquals("ID,Tenant Name" + "123,Test", stringWriter.toString().trim().replaceAll("\n", "").replaceAll("\r", ""));
    }
}
