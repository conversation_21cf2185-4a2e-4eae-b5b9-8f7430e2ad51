package com.carsaver.portal.controller.api;

import com.carsaver.magellan.api.GeocodingService;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.AddressView;
import com.carsaver.magellan.model.google.GeocodingResponse;
import com.carsaver.magellan.model.google.StatusCode;
import com.carsaver.portal.client.program.ProgramClient;
import com.carsaver.portal.client.UserProgramsClient;
import com.carsaver.portal.client.tenant.TenantClient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.PagedModel;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserApiControllerTest {

    @InjectMocks
    private UserApiController controller;

    @Mock
    private GeocodingService geocodingService;
    @Mock
    private TenantClient tenantClient;
    @Mock
    private ProgramClient programClient;
    @Mock
    private UserClient userClient;
    @Mock
    private UserProgramsClient userProgramsClient;

    @Test
    public void findGeocodeAddress_shouldReturnGeocodingResponse_whenGeoCodeResponseIsOK() throws Exception {
        GeocodingResponse geocodingResponse = new GeocodingResponse();
        geocodingResponse.setStatus(StatusCode.OK);

        when(geocodingService.findGeocoding(anyString())).thenReturn(geocodingResponse);

        ResponseEntity<GeocodingResponse> result = controller.findGeocodeAddress(new AddressView());

        assertEquals(StatusCode.OK, Objects.requireNonNull(result.getBody()).getStatus());
    }

    @ParameterizedTest
    @EnumSource(value = StatusCode.class, names = {"INVALID_REQUEST", "UNKNOWN_ERROR", "ZERO_RESULTS", "OVER_QUERY_LIMIT", "REQUEST_DENIED",})
    public void findGeocodeAddress_shouldThrowException_whenNotAbleToFindAddress(StatusCode statusCode) {

        GeocodingResponse geocodingResponse = new GeocodingResponse();
        geocodingResponse.setStatus(statusCode);

        when(geocodingService.findGeocoding(anyString())).thenReturn(geocodingResponse);

        Throwable exception = assertThrows(Exception.class, () -> controller.findGeocodeAddress(new AddressView()));

        assertEquals("user address not found", exception.getMessage());
    }

    @Test
    public void userFormData() {
        when(userClient.getDealerJobTitles()).thenReturn(CollectionModel.empty());
        when(userClient.getAdminJobTitles()).thenReturn(CollectionModel.empty());

        PagedModel tenants = mock(PagedModel.class);
        when(tenantClient.findAll(any())).thenReturn(tenants);
        when(programClient.findAllPrograms(any())).thenReturn(CollectionModel.empty());
        when(userProgramsClient.findProgramsByUserId(anyString())).thenReturn(Collections.emptyList());

        controller.getUserFormData(null);
        verify(userProgramsClient, Mockito.never()).findProgramsByUserId(anyString());

        controller.getUserFormData("test userId");
        verify(userProgramsClient, Mockito.times(1)).findProgramsByUserId(anyString());
    }

}
