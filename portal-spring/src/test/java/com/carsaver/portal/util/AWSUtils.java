package com.carsaver.portal.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.experimental.UtilityClass;

import java.io.File;
import java.io.IOException;
import java.util.Map;

@UtilityClass
public class AWSUtils {

    public void setCredentials() throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, String> map = mapper.readValue(new File("credentials.json"), Map.class);
        map.forEach((key, value) -> {
            System.out.println("Setting " + key + " to value " + value);
            System.setProperty(key, value);
        });

    }
}
