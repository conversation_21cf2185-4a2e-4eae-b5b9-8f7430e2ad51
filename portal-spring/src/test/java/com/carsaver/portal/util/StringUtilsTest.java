package com.carsaver.portal.util;

import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.magellan.util.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class StringUtilsTest {

    @Test
    public void testStartCase() {
        assertEquals("User City", StringUtils.startCase("userCity"));
        assertEquals("User Source Hostname", StringUtils.startCase("user.source.hostname"));
    }

    @Test
    public void testIt() {
        ObjectMapper mapper = new ObjectMapper();

        mapper.registerModule(new JavaTimeModule());
        DealerDoc dealerDoc = new DealerDoc();
        try {
            dealerDoc.setActiveDate(ZonedDateTime.now());
            System.out.println(mapper.writeValueAsString(dealerDoc));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
