package com.carsaver.portal.security;

import com.carsaver.core.DealerStatus;
import com.carsaver.magellan.auth.CarSaverAuthService;
import com.carsaver.magellan.auth.CarSaverJWTToken;
import com.carsaver.magellan.auth.CarSaverUserDetails;
import com.carsaver.magellan.auth.PermissionDetail;
import com.carsaver.magellan.auth.TokenResponse;
import com.carsaver.magellan.client.BasicUserAssociationClient;
import com.carsaver.magellan.model.security.DealerPermissionView;
import com.carsaver.magellan.model.user.BasicDealerUserRoleAssociation;
import com.google.common.collect.Iterables;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.hateoas.CollectionModel;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Component
public class CarSaverAuthenticationProvider implements AuthenticationProvider {

    @Value("${tenant.portal-id}")
    private String portalTenantId;

    @Value("${spring.security.oauth2.client.provider.carsaver.jwk-set-uri}")
    private String jwkSetUri;

    @Autowired
    private CarSaverAuthService carSaverAuthService;

    @Autowired
    private BasicUserAssociationClient basicUserAssociationClient;

    private NimbusJwtDecoder decoder;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String email = authentication.getName();
        String password = (String) authentication.getCredentials();

        List<SimpleGrantedAuthority> grantedAuthorities;
        TokenResponse tokenResponse;
        try {
            tokenResponse = carSaverAuthService.login(portalTenantId, email, password);

            Jwt jwt = getDecoder().decode(tokenResponse.getAccessToken());

            ArrayList<String> authorities = (ArrayList<String>) jwt.getClaims().get("authorities");
            grantedAuthorities = authorities.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
        } catch (Exception e) {
            throw new BadCredentialsException("1000");
        }

        CollectionModel<BasicDealerUserRoleAssociation> userPrivileges = basicUserAssociationClient.findByUserId(tokenResponse.getId());

        Collection<BasicDealerUserRoleAssociation> privileges = Optional.ofNullable(userPrivileges).map(CollectionModel::getContent).orElse(Collections.emptyList());

        privileges.forEach(privilege -> {
            var dealer = privilege.getDealer();
            var permissionId = privilege.getPermissionList().stream()
                .filter(permission -> permission.getName().equalsIgnoreCase("warranty:create"))
                .mapToInt(DealerPermissionView::getId)
                .sum();
            if(dealer.isProbation()) {
                privilege.removePermission(permissionId);
                //basicUserAssociationClient.replaceUserPermissions(privilege.getId(), privilege);
            }
        });

        CarSaverJWTToken token = new CarSaverJWTToken(tokenResponse, grantedAuthorities);
        CarSaverUserDetails userDetails = new CarSaverUserDetails(tokenResponse.getId(), privileges, grantedAuthorities);
        token.setPrincipal(userDetails);

        token.setAuthenticated(true);

        return token;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.isAssignableFrom(UsernamePasswordAuthenticationToken.class);
    }

    private NimbusJwtDecoder getDecoder() {
        if (this.decoder == null) {
            this.decoder = NimbusJwtDecoder.withJwkSetUri(jwkSetUri).build();
        }

        return this.decoder;
    }
}
