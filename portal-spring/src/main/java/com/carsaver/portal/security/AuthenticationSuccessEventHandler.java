package com.carsaver.portal.security;

import com.carsaver.magellan.auth.CarSaverUserDetails;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.client.request.user.LastLoginUpdateRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.security.authentication.event.AbstractAuthenticationEvent;
import org.springframework.security.authentication.event.AuthenticationSuccessEvent;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.time.ZonedDateTime;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Component
@Slf4j
@AllArgsConstructor
public class AuthenticationSuccessEventHandler implements AutoCloseable {

    private final ExecutorService executorService = Executors.newFixedThreadPool(5);
    private final UserClient userClient;

    @EventListener(AuthenticationSuccessEvent.class)
    public void onAuthenticationSuccess(AbstractAuthenticationEvent event) {
        Object userDetails = Optional.ofNullable(event).map(AbstractAuthenticationEvent::getAuthentication).map(Authentication::getPrincipal).orElse(null);
        if (userDetails instanceof CarSaverUserDetails) {
            executorService.execute(() -> {
                try {
                    userClient.updateLastLoginAt(((CarSaverUserDetails) userDetails).getUserId(), LastLoginUpdateRequest.builder().lastLoginAt(ZonedDateTime.now()).build());
                } catch (Exception e) {
                    log.error("Unable to updateLastLoginAt", e.getMessage());
                }
            });
        }

    }

    @PreDestroy
    @Override
    public void close() throws Exception {
        executorService.shutdownNow();
    }
}
