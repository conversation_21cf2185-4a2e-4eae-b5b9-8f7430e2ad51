package com.carsaver.portal.client.campaign.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MarketingChannels {
    @NotNull
    private MarketingChannelType type;

    @NotNull
    private MarketingProvider provider;

    private Boolean enabled = true;
    private MarketingMode mode = MarketingMode.LIVE;
    private Boolean stopCustomer = false;
}
