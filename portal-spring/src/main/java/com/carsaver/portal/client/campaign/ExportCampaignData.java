package com.carsaver.portal.client.campaign;

import com.carsaver.magellan.format.annotation.PhoneFormat;
import com.carsaver.portal.client.campaign.config.PurchaseIncentive;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.time.ZonedDateTime;
import java.util.List;

@JsonSerialize
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class ExportCampaignData {
    private String id;

    @NotEmpty
    private String name;

    private String tenantId;
    private String programId;

    @NotEmpty
    private String domain;

    private String channel;

    @NotEmpty
    @PhoneFormat
    private String phoneNumber;

    private Boolean pinEnabled;
    private String upgradeStrategy;
    private String inventoryConfig;
    private String viewConfig;
    private String volieConfig;
    private String securityConfig;
    private String warrantyConfig;
    private String financeConfig;
    private List<Integer> makeFilters;
    private String leadConfig;
    private List<Integer> userTags;
    private Boolean warrantyEnabled;
    private List<PurchaseIncentive> incentives;
    private String loadingConfig;
    private String createdBy;
    private String createdName;
    private ZonedDateTime createdDate;
    private String lastModifiedBy;
    private String lastModifiedName;
    private ZonedDateTime lastModifiedDate;
    private String tenantName;
    private String programName;
    private String lenderName;
}
