package com.carsaver.portal.client.campaign.config;

import com.carsaver.core.PaymentType;
import com.carsaver.core.StockTypeCondition;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class InventoryConfig {
    private Boolean nonCertifiedEnabled;
    private Integer defaultDistance;
    private List<Integer> supplierPricing;
    private PaymentType paymentType;
    private String externalOffersProvider;
    private List<StockTypeCondition> stockTypeConditions;
    private Boolean isReservationsEnabled;
}
