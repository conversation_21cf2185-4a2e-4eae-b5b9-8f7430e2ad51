package com.carsaver.portal.client.tenant;

import com.carsaver.magellan.client.config.CarSaverFeignConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Pageable;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.PagedModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "tenantClient", url = "${harbor-service.api-uri}", decode404 = true, configuration = CarSaverFeignConfig.class)
public interface TenantClient {

    @GetMapping("/tenants")
    CollectionModel<TenantView> findAll();

    @Cacheable(value = "tenants", unless = "#result == null")
    @GetMapping("/tenants/{id}")
    TenantView findById(@PathVariable("id") String id);

    @GetMapping("/tenants")
    PagedModel<TenantView> findAll(Pageable pageable);

    @PostMapping("/tenants")
    TenantView save(TenantView tenant);

    @GetMapping("/tenants/search/byNameContains")
    PagedModel<TenantView> findByTenantName(@RequestParam("name") String name, Pageable pageable);
}
