package com.carsaver.portal.client;

import com.carsaver.magellan.api.util.TokenUtils;
import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.auth.CarSaverAuthService;
import com.carsaver.magellan.auth.TokenResponse;
import com.carsaver.portal.model.programconfig.DealerChatConfig;
import com.carsaver.portal.service.exception.DealerChatConfigNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.Optional;

@Slf4j
public class DealerChatConfigClient {

    static final String GET_DEALER_CHAT_CONFIG_BY_ID_URI_FORMAT = "%s/dealer_chat_config/%s";
    static final String GET_DEALER_CHAT_CONFIG_BY_DEALER_AND_PROGRAM_URI_FORMAT = "%s/dealer_chat_config/dealer/%s/program/%s";
    static final String UPDATE_DEALER_CHAT_CONFIG_URI_FORMAT = "%s/dealer_chat_config/%s";
    static final String UPSERT_DEALER_CHAT_CONFIG_URI_FORMAT = "%s/dealer_chat_config/upsert";

    private CarSaverAuthService carSaverAuthService;
    RestTemplate restTemplate;
    String rootUri;

    String applicationName;

    public DealerChatConfigClient(String rootUri, RestTemplate restTemplate, CarSaverAuthService carSaverAuthService, String applicationName) {
        this.rootUri = rootUri;
        this.restTemplate = restTemplate;
        this.carSaverAuthService = carSaverAuthService;
        this.applicationName = applicationName;
    }

    public DealerChatConfig getDealerChatConfigById(String dealerChatConfigId) throws DealerChatConfigNotFoundException {
        URI uri = URI.create(String.format(GET_DEALER_CHAT_CONFIG_BY_ID_URI_FORMAT, rootUri, dealerChatConfigId));
        ResponseEntity<DealerChatConfig> response;
        try {
            response = restTemplate.exchange(new RequestEntity<>(getHeaders(), HttpMethod.GET, uri), DealerChatConfig.class);
            log.debug(response.toString());
            if (response.getStatusCode() == HttpStatus.NOT_FOUND) {
                throw new DealerChatConfigNotFoundException(dealerChatConfigId);
            }
        } catch (HttpClientErrorException ex) {
            // Throw more helpful exception for 404, otherwise rethrow
            if (ex.getStatusCode() == HttpStatus.NOT_FOUND) {
                throw new DealerChatConfigNotFoundException(dealerChatConfigId);
            } else {
                throw ex;
            }
        }
        return response.getBody();
    }

    public DealerChatConfig getDealerChatConfigByDealerAndProgram(String dealerId, String programId) throws DealerChatConfigNotFoundException {
        URI uri = URI.create(String.format(GET_DEALER_CHAT_CONFIG_BY_DEALER_AND_PROGRAM_URI_FORMAT, rootUri, dealerId, programId));
        ResponseEntity<DealerChatConfig> response;
        try {
            response = restTemplate.exchange(new RequestEntity<>(getHeaders(), HttpMethod.GET, uri), DealerChatConfig.class);
            log.debug(response.toString());
            if (response.getStatusCode() == HttpStatus.NOT_FOUND) {
                throw new DealerChatConfigNotFoundException(dealerId, programId);
            }
        } catch (HttpClientErrorException ex) {
            // Throw more helpful exception for 404, otherwise rethrow
            if (ex.getStatusCode() == HttpStatus.NOT_FOUND) {
                throw new DealerChatConfigNotFoundException(dealerId, programId);
            } else {
                throw ex;
            }
        }
        return response.getBody();
    }

    public DealerChatConfig updateDealerChatConfig(DealerChatConfig dealerChatConfig) throws DealerChatConfigNotFoundException {
        URI uri = URI.create(String.format(UPDATE_DEALER_CHAT_CONFIG_URI_FORMAT, rootUri, dealerChatConfig.getId()));
        ResponseEntity<DealerChatConfig> response;
        try {
            HttpHeaders headers = getHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            response = restTemplate.exchange(uri, HttpMethod.PUT, new HttpEntity<>(dealerChatConfig, headers), DealerChatConfig.class);
            log.debug(response.toString());
        } catch (HttpClientErrorException ex) {
            // Throw more helpful exception for 404, otherwise rethrow
            if (ex.getStatusCode() == HttpStatus.NOT_FOUND) {
                throw new DealerChatConfigNotFoundException(dealerChatConfig.getId());
            } else {
                throw ex;
            }
        }
        return response.getBody();
    }

    public DealerChatConfig upsertDealerChatConfig(DealerChatConfig dealerChatConfig) throws DealerChatConfigNotFoundException {
        URI uri = URI.create(String.format(UPSERT_DEALER_CHAT_CONFIG_URI_FORMAT, rootUri));
        ResponseEntity<DealerChatConfig> response;
        HttpHeaders headers = getHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        response = restTemplate.exchange(uri, HttpMethod.POST, new HttpEntity<>(dealerChatConfig, headers), DealerChatConfig.class);
        log.debug(response.toString());
        return response.getBody();
    }

    protected HttpHeaders getHeaders() {
        String jwtToken;
        Optional<String> authJwt = AuthUtils.getJwtTokenFromSecurityContext();
        if (authJwt.isPresent()) {
            log.debug("Retrieved Jwt from SecurityContext");
            jwtToken = "Bearer " + authJwt.get();
        } else {
            log.debug("No token in SecurityContext, retrieving from {}", CarSaverAuthService.class);
            TokenResponse token = carSaverAuthService.getToken();
            jwtToken = "Bearer " + token.getAccessToken();
        }

        HttpHeaders headers = TokenUtils.getTokenHeaders(jwtToken);
        headers.add("X-Service-Caller", applicationName);

        return headers;
    }

}
