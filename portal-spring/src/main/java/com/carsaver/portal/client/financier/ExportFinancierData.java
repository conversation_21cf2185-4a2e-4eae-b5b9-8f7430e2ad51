package com.carsaver.portal.client.financier;

import com.carsaver.portal.client.financier.config.FinancierConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExportFinancierData {
    private Long id;
    private String name;
    private String routeOneId;
    private Integer horizonId;
    private String payoffPhoneNumber;
    private Boolean enabled;
    private String config;
}
