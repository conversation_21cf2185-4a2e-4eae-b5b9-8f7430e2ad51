package com.carsaver.portal.client.campaign.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VolieConfig {
    private boolean enabled = true;
    private String defaultQueue;
    private Map<String, VolieCallCampaign> callQueueConfig = new HashMap<>();
}
