package com.carsaver.portal.client.campaign;

import com.carsaver.magellan.format.annotation.PhoneFormat;
import com.carsaver.portal.client.campaign.config.FinanceConfig;
import com.carsaver.portal.client.campaign.config.InventoryConfig;
import com.carsaver.portal.client.campaign.config.LeadConfig;
import com.carsaver.portal.client.campaign.config.LoadingConfig;
import com.carsaver.portal.client.campaign.config.PurchaseIncentive;
import com.carsaver.portal.client.campaign.config.SecurityConfig;
import com.carsaver.portal.client.campaign.config.UpgradeStrategy;
import com.carsaver.portal.client.campaign.config.ViewConfig;
import com.carsaver.portal.client.campaign.config.VolieConfig;
import com.carsaver.portal.client.campaign.config.WarrantyConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.time.ZonedDateTime;
import java.util.List;

@JsonSerialize
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class CampaignView {
    private String id;

    @NotEmpty
    private String name;

    private String tenantId;
    private String programId;

    @NotEmpty
    private String domain;

    private String channel;

    @NotEmpty
    @PhoneFormat
    private String phoneNumber;

    private Boolean pinEnabled;
    private UpgradeStrategy upgradeStrategy;
    private InventoryConfig inventoryConfig;
    private ViewConfig viewConfig;
    private VolieConfig volieConfig;
    private SecurityConfig securityConfig;
    private WarrantyConfig warrantyConfig;
    private FinanceConfig financeConfig;
    private List<Integer> makeFilters;
    private LeadConfig leadConfig;
    private List<Integer> userTags;
    private Boolean warrantyEnabled;
    private List<PurchaseIncentive> incentives;
    private LoadingConfig loadingConfig;
    private String createdBy;
    private String createdName;
    private ZonedDateTime createdDate;
    private String lastModifiedBy;
    private String lastModifiedName;
    private ZonedDateTime lastModifiedDate;
    private String tenantName;
    private String programName;
    private String lenderName;
}
