package com.carsaver.portal.model.dealer;

import com.carsaver.core.StockType;
import com.carsaver.magellan.model.dealer.DealType;
import com.carsaver.magellan.model.dealer.DealerFeeView;
import com.carsaver.magellan.model.dealer.FeeType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DealerFeeModel {
    private String id;

    private String dealerId;

    @Size(max=50, message = "Fee Name must be less than 50 characters")
    @NotBlank(message = "Fee name is required")
    private String name;

    @Size(max=500, message = "Fee Description must be less than 500 characters")
    private String description;

    @Max(value = 9999, message = "Max amount is $9,999")
    @NotNull(message = "Fee amount is required")
    private Double amount;

    private StockType stockType;

    @NotNull(message = "Fee type is required")
    private FeeType feeType;

    private Boolean isCapFee;
    private Boolean isTaxable;
    private Boolean isInception;

    @NotNull(message = "Deal type is required")
    private DealType dealType;

    public DealerFeeView toDealerFeeView() {
        var dealerFeeView = new DealerFeeView();

        BeanUtils.copyProperties(this, dealerFeeView);
        dealerFeeView.setId(this.getId());

        return dealerFeeView;
    }
}
