package com.carsaver.portal.model.program;

import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

@Data
public class EditProgramForm {

    @NotEmpty
    private String name;

    @NotEmpty
    @Email(regexp = "[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,3}")
    private String supportEmail;

    @NotEmpty
    @Pattern(regexp = "(^[0-9]{10})")
    private String supportPhone;

    private String supportGroupId;
}
