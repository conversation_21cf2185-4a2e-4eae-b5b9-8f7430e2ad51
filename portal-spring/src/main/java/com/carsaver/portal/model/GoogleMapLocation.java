package com.carsaver.portal.model;

import com.carsaver.core.DealerStatus;
import com.carsaver.elasticsearch.model.AddressDoc;
import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.elasticsearch.model.GeoDoc;
import com.carsaver.elasticsearch.model.Location;
import com.carsaver.magellan.model.AddressView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.WalmartStoreView;
import lombok.Data;

import java.util.Optional;

@Data
public class GoogleMapLocation {

    private String id;

    private String name;

    private String address1;

    private String city;

    private String state;

    private String zipCode;

    private Boolean active;

    // Geolocation
    private Double lat;
    private Double lng;

    public GoogleMapLocation(DealerDoc dealer) {
        this.id = dealer.getId();
        this.name = dealer.getName();
        this.address1 = dealer.getAddress().getStreet();
        this.city = dealer.getAddress().getCity();
        this.state = dealer.getAddress().getStateCode();
        this.zipCode = dealer.getAddress().getZipCode();
        this.active = dealer.getStatus() == DealerStatus.LIVE;
        this.lat = Optional.ofNullable(dealer.getAddress()).map(AddressDoc::getGeo).map(GeoDoc::getLocation).map(Location::getLat).orElse(null);
        this.lng = Optional.ofNullable(dealer.getAddress()).map(AddressDoc::getGeo).map(GeoDoc::getLocation).map(Location::getLon).orElse(null);
    }

    public GoogleMapLocation(DealerView dealer) {
        this.id = dealer.getId();
        this.name = dealer.getName();
        this.address1 = dealer.getAddress1();
        this.city = dealer.getCity();
        this.state = dealer.getState();
        this.zipCode = dealer.getZipCode();
        this.active = dealer.getActive();
        this.lat = dealer.getLat();
        this.lng = dealer.getLng();
    }

    public GoogleMapLocation(WalmartStoreView walmartStore) {
        this.id = walmartStore.getId().toString();
        this.name = walmartStore.getName();
        this.address1 = walmartStore.getStreet();
        this.city = walmartStore.getCity();
        this.state = walmartStore.getState();
        this.zipCode = walmartStore.getZipCode();
        this.lat = walmartStore.getLat();
        this.lng = walmartStore.getLng();
    }

    public boolean isValid() {
        return lat != null && lng != null;
    }

    public AddressView getAddress() {
        return AddressView.builder()
            .street1(address1)
            .city(city).state(state).zipCode(zipCode)
            .build();
    }

    @Deprecated
    public AddressView getAddressView() {
        return AddressView.builder()
            .street1(address1)
            .city(city).state(state).zipCode(zipCode)
            .build();
    }

}
