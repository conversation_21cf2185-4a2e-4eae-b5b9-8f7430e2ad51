package com.carsaver.portal.model.user;

import com.carsaver.core.StockType;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealerLinkView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.certificate.Options;
import com.carsaver.magellan.model.chrome.MPGTechSpecView;
import com.carsaver.magellan.model.chrome.MakeView;
import com.carsaver.magellan.model.chrome.ModelView;
import com.carsaver.magellan.model.chrome.StyleView;
import com.carsaver.magellan.model.color.ExteriorColor;
import com.carsaver.magellan.model.color.InteriorColor;
import com.carsaver.magellan.model.preferences.DeliveryAvailability;
import com.carsaver.magellan.model.pricing.PricesView;
import lombok.Builder;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@Builder
public class UserVehicle {
    private Integer certificateId;
    private Integer year;
    private String make;
    private String model;
    private String trim;
    private String vin;
    private String stockNumber;
    private Dealer dealer;
    private Integer miles;
    private StockType stockType;
    private Type type;
    private Pricing pricing;
    private String carfaxUrl;
    private String exteriorColor;
    private String interiorColor;
    private String doors;
    private Integer cityMpg;
    private Integer highwayMpg;
    private String fuelType;
    private String engine;
    private String transmission;
    private List<String> options;
    private Boolean active;
    private ZonedDateTime createdAt;
    private ZonedDateTime modifiedDate;

    public static UserVehicle from(CertificateView certificateView) {
        StyleView style = certificateView.getStyle();
        Optional<StyleView> optStyle = Optional.ofNullable(style);
        VehicleView vehicle = certificateView.getVehicle();
        Optional<VehicleView> optVehicle = Optional.ofNullable(vehicle);

        return UserVehicle.builder()
            .certificateId(certificateView.getId())
            .year(optVehicle.map(VehicleView::getYear).orElseGet(() -> optStyle.map(StyleView::getYear).orElse(null)))
            .make(optVehicle.map(VehicleView::getMake).orElseGet(() -> optStyle.map(StyleView::getMake).map(MakeView::getName).orElse(null)))
            .model(optVehicle.map(VehicleView::getModel).orElseGet(() -> optStyle.map(StyleView::getModel).map(ModelView::getName).orElse(null)))
            .trim(optVehicle.map(VehicleView::getTrim).orElseGet(() -> optStyle.map(StyleView::getTrim).orElse(null)))
            .vin(optVehicle.map(VehicleView::getVin).orElse(null))
            .stockNumber(optVehicle.map(VehicleView::getStockNumber).orElse(null))
            .dealer(Dealer.from(certificateView))
            .miles(optVehicle.map(VehicleView::getMiles).orElse(null))
            .stockType(certificateView.getStockType())
            .type(certificateView.getSaved() != null && certificateView.getSaved() ? Type.SAVED : Type.RECENTLY_VIEWED)
            .active(optVehicle.map(VehicleView::getActive).orElseGet(certificateView::isConfigured))
            .pricing(Pricing.from(certificateView))
            .carfaxUrl(optVehicle.map(VehicleView::getCarfaxUrl).orElse(null))
            .exteriorColor(optVehicle.map(VehicleView::getExteriorColor).orElseGet(() -> Optional.ofNullable(certificateView).map(CertificateView::getExteriorColor).map(ExteriorColor::getName).orElse(null)))
            .interiorColor(optVehicle.map(VehicleView::getInteriorColor).orElseGet(() -> Optional.ofNullable(certificateView).map(CertificateView::getInteriorColor).map(InteriorColor::getName).orElse(null)))
            .doors(optVehicle.map(VehicleView::getDoors).map(String::valueOf).orElseGet(() -> Optional.ofNullable(style).map(StyleView::getPassengerDoors).orElse(null)))
            .cityMpg(optVehicle.map(VehicleView::getMpgCity).orElseGet(() -> Optional.ofNullable(style).map(StyleView::getMpgTechSpec).map(MPGTechSpecView::getCity).orElse(null)))
            .highwayMpg(optVehicle.map(VehicleView::getMpgHighway).orElseGet(() -> Optional.ofNullable(style).map(StyleView::getMpgTechSpec).map(MPGTechSpecView::getHighway).orElse(null)))
            .fuelType(optVehicle.map(VehicleView::getFuelType).orElse(null))
            .engine(optVehicle.map(VehicleView::getEngine).orElse(null))
            .transmission(optVehicle.map(VehicleView::getTransmission).orElse(null))
            .options(getOptions(certificateView))
            .createdAt(certificateView.getCreatedAt())
            .modifiedDate(certificateView.getUpdatedAt())
            .build();
    }

    private static List<String> getOptions(CertificateView certificateView) {
        return Optional.ofNullable(certificateView.getVehicle()).map(VehicleView::getOptions).orElseGet(() ->
            Optional.of(certificateView)
                .map(CertificateView::getOptions)
                .map(Options::getItems)
                .map(optionItems -> optionItems.stream().map(Options.OptionItem::getDescriptions)
                    .map(descriptions -> descriptions.size() > 0 ? descriptions.get(0) : null)
                    .filter(Objects::nonNull)
                    .map(Options.OptionItem.Description::getDescription)
                    .collect(Collectors.toList()))
                .orElse(null));
    }

    @Data
    @Builder
    private static class Pricing {
        private Integer msrp;
        private Integer averageMarketPrice;
        private Integer carsaverPrice;
        private Integer invoice;
        private Double totalDealerFees;
        private Integer destinationFee;
        private Integer rebates;
        private Double salesTax;
        private Double titleLicenseFee;
        private Double outTheDoorPrice;
        private Integer savings;

        public static Pricing from(CertificateView certificateView) {
            PricesView prices = certificateView.getVehiclePrices();
            if (prices == null) {
                return null;
            }

            return Pricing.builder()
                .msrp(prices.getMsrp())
                .averageMarketPrice(prices.getAverageMarketPrice())
                .carsaverPrice(prices.getCarsaverPrice())
                .invoice(prices.getInvoicePrice())
                .totalDealerFees(prices.getTotalDealerFees())
                .destinationFee(prices.getDestinationFees())
                .rebates(prices.getRebates())
                .salesTax(prices.getSalesTax())
                .titleLicenseFee(prices.getTitleLicenseFee())
                .outTheDoorPrice(prices.getOutTheDoorPrice())
                .savings(prices.getSavings())
                .build();
        }
    }

    @Data
    @Builder
    private static class Dealer {
        private String id;
        private String name;
        private boolean live;
        private DeliveryAvailability deliveryAvailability;
        private boolean certified;
        private Double distanceFromUser;

        public static Dealer from(CertificateView certificateView) {
            DealerView dealer = certificateView.getDealer();
            if (dealer == null) {
                return null;
            }

            return Dealer.builder()
                .id(dealer.getId())
                .name(dealer.getName())
                .live(dealer.isLive())
                .deliveryAvailability(dealer.getEffectivePreferences().getDeliveryAvailability())
                .certified(dealer.isCertified())
                .distanceFromUser(Optional.ofNullable(certificateView.getDealerLink()).map(DealerLinkView::getDistanceFromUser).orElse(null))
                .build();
        }
    }

    private enum Type {
        SAVED,
        RECENTLY_VIEWED
    }
}
