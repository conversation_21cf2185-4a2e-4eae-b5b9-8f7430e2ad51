package com.carsaver.portal.model.metrics;

import com.carsaver.core.DealerStatus;
import com.carsaver.core.SalesTxSource;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.time.ZonedDateTime;


@Data
@Builder
public class DealerImportMetric {
    private String id;
    private String dealerId;
    private String name;
    private DealerStatus status;
    private SalesTxSource salesSource;
    private ZonedDateTime createdDate;
    private ZonedDateTime modifiedDate;
    private LocalDate mostRecentSaleDate;
    private ZonedDateTime lastContainedInReportOn;
    private Integer lastReportSaleCount;
}
