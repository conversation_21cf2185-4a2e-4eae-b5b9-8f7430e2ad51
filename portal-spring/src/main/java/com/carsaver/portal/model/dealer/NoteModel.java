package com.carsaver.portal.model.dealer;

import com.carsaver.magellan.model.JobTitleView;
import com.carsaver.magellan.model.NoteView;
import com.carsaver.magellan.model.UserView;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.Optional;

@Data
@Builder
public class NoteModel {

    private String id;
    private String content;
    private boolean dealerVisible;
    private boolean modified;
    private CreatedByUser createdByUser;
    protected ZonedDateTime createdDate;
    protected ZonedDateTime lastModifiedDate;

    public static NoteModel from(NoteView dealerNoteView) {

        return NoteModel.builder()
            .id(dealerNoteView.getId())
            .content(dealerNoteView.getContent())
            .dealerVisible(dealerNoteView.isDealerVisible())
            .createdDate(dealerNoteView.getCreatedDate())
            .createdByUser(CreatedByUser.from((UserView) dealerNoteView.getCreatedByUser()))
            .lastModifiedDate(dealerNoteView.getLastModifiedDate())
            .modified(dealerNoteView.isModified())
            .build();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CreatedByUser {
        private String id;
        private String name;
        private String gravitarEmailHash;
        private String jobTitle;

        public static CreatedByUser from(UserView user) {
            if (user == null) {
                return null;
            }

            return CreatedByUser.builder()
                .id(user.getId())
                .name(user.getFullName())
                .gravitarEmailHash(user.getGravitarEmailHash())
                .jobTitle(Optional.ofNullable(user.getJobTitle()).map(JobTitleView::getTitle).orElse(null))
                .build();
        }
    }
}
