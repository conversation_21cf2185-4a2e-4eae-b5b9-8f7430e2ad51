package com.carsaver.portal.model;

import com.carsaver.magellan.api.LeadService;
import com.carsaver.magellan.model.ActivityEventView;
import com.carsaver.magellan.model.DealerLeadTransactionView;
import com.carsaver.magellan.model.UserView;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.Optional;

@Data
@Builder
public class EventModel {

    @JsonIgnore
    @Autowired(required = false)
    private transient LeadService leadService;

    private String id;
    private String description;
    private String eventType;
    protected ZonedDateTime eventTime;
    private CreatedByUser createdByUser;
    private Map<String,Object> metadata;
    private String adfPayload;
    private Duration durationSinceCreatedDate;

    public static EventModel from(ActivityEventView activityEventView) {

        return EventModel.builder()
            .id(activityEventView.getId())
            .description(activityEventView.getDescription())
            .eventType(activityEventView.getEventType())
            .eventTime(activityEventView.getEventTime())
            .createdByUser(CreatedByUser.from(activityEventView.getTriggeredByUser()))
            .metadata(activityEventView.getMetadata())
            .adfPayload(Optional.ofNullable(activityEventView.getDealerLeadTransaction()).map(DealerLeadTransactionView::getPayload).orElse(null))
            .durationSinceCreatedDate(activityEventView.getDurationSinceCreatedDate())
            .build();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CreatedByUser {
        private String id;
        private String name;
        private String gravitarEmailHash;

        public static CreatedByUser from(UserView user) {
            if (user == null) {
                return null;
            }

            return CreatedByUser.builder()
                .id(user.getId())
                .name(user.getFullName())
                .gravitarEmailHash(user.getGravitarEmailHash())
                .build();
        }
    }

}
