package com.carsaver.portal.model.store.dealer.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTypeCountWidget {
    private int newCount;
    private int usedCount;
    private int unknownCount;
    private int totalCount;

    public int getTotalCount() {
        if (this.totalCount > 0) {
            return this.totalCount;
        }

        return this.newCount + this.usedCount + this.unknownCount;
    }
}
