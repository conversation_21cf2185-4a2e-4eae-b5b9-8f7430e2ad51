package com.carsaver.portal.model.user;

import com.carsaver.magellan.model.JobTitleView;
import com.carsaver.magellan.model.UserNoteView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.stereotype.UserIdentity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.Optional;

@Data
@Builder
public class UserNote {

    private String id;
    private String content;
    private boolean dealerVisible;
    private boolean modified;
    private CreatedByUser createdByUser;
    protected ZonedDateTime createdDate;
    protected ZonedDateTime lastModifiedDate;

    public static UserNote from(UserNoteView userNoteView) {

        return UserNote.builder()
            .id(userNoteView.getId())
            .content(userNoteView.getContent())
            .dealerVisible(userNoteView.isDealerVisible())
            .createdDate(userNoteView.getCreatedDate())
            .createdByUser(CreatedByUser.from((UserView) userNoteView.getCreatedByUser()))
            .lastModifiedDate(userNoteView.getLastModifiedDate())
            .modified(userNoteView.isModified())
            .build();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CreatedByUser {
        private String id;
        private String name;
        private String gravitarEmailHash;
        private String jobTitle;

        public static CreatedByUser from(UserView user) {
            if (user == null) {
                return null;
            }

            return CreatedByUser.builder()
                .id(user.getId())
                .name(user.getFullName())
                .gravitarEmailHash(user.getGravitarEmailHash())
                .jobTitle(Optional.ofNullable(user.getJobTitle()).map(JobTitleView::getTitle).orElse(null))
                .build();
        }
    }
}
