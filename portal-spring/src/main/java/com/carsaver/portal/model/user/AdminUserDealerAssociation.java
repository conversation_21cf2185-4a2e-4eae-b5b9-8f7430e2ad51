package com.carsaver.portal.model.user;

import groovy.transform.builder.Builder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdminUserDealerAssociation {
    private String dealerName;
    private String dealerId;
    private boolean isSuccessManager;
    private boolean isAccountManager;
}
