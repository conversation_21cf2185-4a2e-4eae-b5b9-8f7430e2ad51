package com.carsaver.portal.model;

import com.carsaver.core.audit.ChangeSet;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

@EqualsAndHashCode(callSuper = true)
@Data
public class AuditLog extends ChangeSet {

    private String lastModifiedByUserName;

    public static AuditLog from(ChangeSet changeSet) {
        AuditLog auditLog = new AuditLog();
        BeanUtils.copyProperties(changeSet, auditLog);
        return auditLog;
    }

}
