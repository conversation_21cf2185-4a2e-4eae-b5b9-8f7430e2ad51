package com.carsaver.portal.model.user;

import com.carsaver.magellan.model.ConnectionView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.Optional;

@Data
@Builder
public class UserConnection {

    private String id;
    private String type;
    private Dealer dealer;
    private ProgramManager programManager;
    private ZonedDateTime createdDate;

    public static UserConnection from(ConnectionView connectionView) {

        return UserConnection.builder()
            .id(connectionView.getId())
            .type(connectionView.getType())
            .dealer(Dealer.from(connectionView.getDealer()))
            .programManager(Optional.ofNullable(ProgramManager.from(connectionView.getProgramManager())).orElse(null))
            .createdDate(connectionView.getCreatedDate())
            .build();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Dealer {
        private String id;
        private String name;
        private String timezone;

        public static Dealer from(DealerView dealerView) {
            if (dealerView == null) {
                return null;
            }

            return Dealer.builder()
                .id(dealerView.getId())
                .name(dealerView.getName())
                .timezone(dealerView.getTimeZone())
                .build();
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProgramManager {
        private String id;
        private String name;

        public static ProgramManager from(UserView userView) {
            if (userView == null) {
                return null;
            }

            return ProgramManager.builder()
                .id(userView.getId())
                .name(userView.getFullName())
                .build();
        }
    }

}

