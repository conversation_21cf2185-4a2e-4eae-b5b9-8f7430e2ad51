package com.carsaver.portal.service;

import com.carsaver.elasticsearch.CriteriaSearchHandler;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.model.UserDoc;
import com.carsaver.portal.elasticsearch.criteria.LeadKpiSearchCriteria;
import com.carsaver.portal.elasticsearch.facets.LeadKpiFacets;
import com.carsaver.portal.elasticsearch.metrics.LeadMetrics;
import com.carsaver.portal.service.dashboard.SaveADealUtil;
import com.carsaver.search.BoolQueryCollector;
import com.carsaver.search.support.FacetParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import static org.elasticsearch.index.query.QueryBuilders.termQuery;

@Slf4j
@Service
public class LeadMetricsService extends CriteriaSearchHandler<UserDoc> {

    private static final String LEADS_INDEX = "leads";

    @Autowired
    public LeadMetricsService(@Qualifier("gibson") ElasticClient elasticClient, FacetParser facetParser) {
        super(elasticClient, facetParser);
    }

    public LeadMetrics getStats(LeadKpiSearchCriteria criteria) {
        LeadKpiFacets facets = (LeadKpiFacets) super.search(criteria, LeadKpiFacets.class);

        return new LeadMetrics(facets.getTotalLeads(), facets.getUniqueLeads());
    }

    @Override
    protected String[] getSearchIndex() {
        return new String[]{ LEADS_INDEX };
    }

    @Override
    protected void appendRootQuery(Object criteria, BoolQueryCollector boolQueryCollector) {
        boolQueryCollector.mustNot(termQuery("source.hostname", SaveADealUtil.SAVE_A_DEAL_DOMAIN));
    }

}
