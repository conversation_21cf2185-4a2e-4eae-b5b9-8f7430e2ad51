package com.carsaver.portal.service;

import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticResponse;
import lombok.Data;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.Avg;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static org.elasticsearch.index.query.QueryBuilders.boolQuery;
import static org.elasticsearch.index.query.QueryBuilders.termsQuery;

@Service
public class InventoryStatsService {

    public static final String VEHICLES_INDEX = "vehicles";

    @Autowired
    private ElasticClient elasticClient;

    public InventoryStats getInventoryStats() {
        QueryBuilder rootQuery = buildRootQuery();

        SearchSourceBuilder searchBldr = new SearchSourceBuilder();
        searchBldr.size(0); // we don't need any docs, topHits will return the necessary docs itself
        searchBldr.query(rootQuery);

        AggregationBuilder aggs = AggregationBuilders
            .terms("byDealerCertified")
            .field("dealer.certified")
            .subAggregation(AggregationBuilders
                .terms("globalStats")
                .field("stockType")
                .subAggregation(
                    AggregationBuilders
                        .avg("avg_savings")
                        .field("savings")
                )
            );

        searchBldr.aggregation(aggs);

        SearchRequest request = new SearchRequest(VEHICLES_INDEX);
        request.source(searchBldr);

        ElasticResponse response = this.elasticClient.search(request);

        InventoryStats inventoryStats = new InventoryStats();

        Terms byDealerCertified = response.getSearchResponse().getAggregations().get("byDealerCertified");
        byDealerCertified.getBuckets().forEach(byDealerCertifiedBuckets -> {

            InventoryStockTypeStats stockTypeStats = new InventoryStockTypeStats();
            Terms globalStats = byDealerCertifiedBuckets.getAggregations().get("globalStats");
            globalStats.getBuckets().forEach(globalStatsBucket -> {
                StockTypeStats stats = new StockTypeStats();
                stats.setCount(globalStatsBucket.getDocCount());

                Avg avgSavings = globalStatsBucket.getAggregations().get("avg_savings");
                stats.setAverageSavings(avgSavings.getValue());

                switch (globalStatsBucket.getKeyAsString()) {
                    case "NEW":
                        stockTypeStats.setNewVehicleStats(stats);
                        break;
                    case "USED":
                        stockTypeStats.setUsedVehicleStats(stats);
                        break;
                }
            });


            switch (byDealerCertifiedBuckets.getKeyAsString()) {
                case "true":
                    inventoryStats.setCertified(stockTypeStats);
                    break;
                case "false":
                    inventoryStats.setNonCertified(stockTypeStats);
                    break;
            }

        });

        return inventoryStats;
    }

    private QueryBuilder buildRootQuery() {
        BoolQueryBuilder query = boolQuery();
        query.filter(isActiveQuery());

        return query;
    }

    private QueryBuilder isActiveQuery() {
        return termsQuery("active", true);
    }

    @Data
    public static class InventoryStats {
        private InventoryStockTypeStats certified;
        private InventoryStockTypeStats nonCertified;
    }

    @Data
    public static class InventoryStockTypeStats {
        private StockTypeStats newVehicleStats;
        private StockTypeStats usedVehicleStats;
    }

    @Data
    public static class StockTypeStats {
        private long count;
        private Double averageSavings;
    }
}
