package com.carsaver.portal.service.dealerperformance;

import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticResponse;
import com.carsaver.elasticsearch.model.vehicle.VehicleDoc;
import com.carsaver.portal.model.dealerperformance.DealerPerformanceRow;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

import static org.elasticsearch.index.query.QueryBuilders.*;

@Service
public class DealerPerformanceVehicleDocService {
    private static final String INDEX = "vehicles";

    @Autowired
    @Qualifier("vega")
    private ElasticClient elasticClient;

    public List<DealerPerformanceRow> getPriced(List<DealerPerformanceRow> performanceRows) {
        SearchSourceBuilder searchBldr = new SearchSourceBuilder();

        /******************************************** QUERIES *******************************************/
        BoolQueryBuilder query = boolQuery();

        query.must(termQuery("pricingValid", true));
        query.must(termQuery("active", true));

        searchBldr.query(query);

        searchBldr.aggregation(AggregationBuilders.terms("dealers").field("dealer.id").size(10000)
            .subAggregation(AggregationBuilders.filter("withPhotos", existsQuery("imageUrl")))
            .subAggregation(AggregationBuilders.terms("stockTypes").field("stockType")
                .subAggregation(AggregationBuilders.terms("priced").field("active"))));
        searchBldr.size(0);

        SearchRequest request = new SearchRequest(INDEX);
        request.source(searchBldr);

        ElasticResponse<VehicleDoc> response = this.elasticClient.search(request);

        ParsedStringTerms dealersAgg = response.getSearchResponse().getAggregations().get("dealers");
        for (Terms.Bucket dealerBucket : dealersAgg.getBuckets()) {
            performanceRows.forEach(row -> {
                if (row.getDealerId().equals(dealerBucket.getKeyAsString())) {
                    int inventoryCount = Math.toIntExact(dealerBucket.getDocCount());
                    row.setInventoryCount(inventoryCount);

                    ParsedFilter withPhotosAgg = dealerBucket.getAggregations().get("withPhotos");
                    double percentWithPhotos = BigDecimal.valueOf(Optional.ofNullable(withPhotosAgg.getDocCount()).orElse(0L)).setScale(2, RoundingMode.UP).divide(BigDecimal.valueOf(inventoryCount), RoundingMode.UP).doubleValue();
                    row.setPercentWithPhotos(percentWithPhotos);

                    ParsedStringTerms stockTypesAgg = dealerBucket.getAggregations().get("stockTypes");
                    for (Terms.Bucket stockTypeBucket : stockTypesAgg.getBuckets()) {
                        if (stockTypeBucket.getKeyAsString().equalsIgnoreCase("NEW")) {
                            long totalNew = stockTypeBucket.getDocCount();
                            ParsedTerms pricedAgg = stockTypeBucket.getAggregations().get("priced");

                            double percentNewPriced = BigDecimal.valueOf(Optional.ofNullable(pricedAgg.getBucketByKey("true")).map(MultiBucketsAggregation.Bucket::getDocCount).orElse(0L)).setScale(2, RoundingMode.UP).divide(BigDecimal.valueOf(totalNew), RoundingMode.UP).doubleValue();
                            row.setPercentNewPriced(percentNewPriced);
                        } else if (stockTypeBucket.getKeyAsString().equalsIgnoreCase("USED")) {
                            long totalUsed = stockTypeBucket.getDocCount();
                            ParsedTerms pricedAgg = stockTypeBucket.getAggregations().get("priced");

                            double percentUsedPriced = BigDecimal.valueOf(Optional.ofNullable(pricedAgg.getBucketByKey("true")).map(MultiBucketsAggregation.Bucket::getDocCount).orElse(0L)).setScale(2, RoundingMode.UP).divide(BigDecimal.valueOf(totalUsed), RoundingMode.UP).doubleValue();
                            row.setPercentUsedPriced(percentUsedPriced);
                        }
                    }
                }
            });
        }

        return performanceRows;
    }
}
