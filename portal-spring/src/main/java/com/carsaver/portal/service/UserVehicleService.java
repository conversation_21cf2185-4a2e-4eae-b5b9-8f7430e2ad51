package com.carsaver.portal.service;

import com.carsaver.magellan.client.CertificateClient;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.portal.model.user.UserVehicle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.hateoas.PagedModel;
import org.springframework.stereotype.Service;

@Service
public class UserVehicleService {

    @Autowired
    private CertificateClient certificateClient;

    public Page<UserVehicle> getSavedUserVehicles(String userId, Pageable pageable) {
        PagedModel<CertificateView> certificates = certificateClient.findSavedByUser(userId, pageable);

        Page<CertificateView> page = PageRequestUtils.toPage(certificates, pageable);

        return page.map(UserVehicle::from);
    }

    public Page<UserVehicle> getRecentlyViewedUserVehicles(String userId, Pageable pageable) {
        PagedModel<CertificateView> certificates = certificateClient.findRecentlyViewedByUser(userId, pageable);

        Page<CertificateView> page = PageRequestUtils.toPage(certificates, pageable);

        return page.map(UserVehicle::from);
    }

    public Page<UserVehicle> getSavedUserVehicles(String userId, String dealerId, Pageable pageable) {
        PagedModel<CertificateView> certificates = certificateClient.findSavedByUserAndDealer(userId, dealerId, pageable);

        Page<CertificateView> page = PageRequestUtils.toPage(certificates, pageable);

        return page.map(UserVehicle::from);
    }

    public Page<UserVehicle> getRecentlyViewedUserVehicles(String userId, String dealerId, Pageable pageable) {
        PagedModel<CertificateView> certificates = certificateClient.findRecentlyViewedByUserAndDealer(userId, dealerId, pageable);

        Page<CertificateView> page = PageRequestUtils.toPage(certificates, pageable);

        return page.map(UserVehicle::from);
    }

}
