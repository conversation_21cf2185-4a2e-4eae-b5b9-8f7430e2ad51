package com.carsaver.portal.service.dashboard;

import com.carsaver.core.StockType;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticResponse;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

import static org.elasticsearch.index.query.QueryBuilders.*;

@Service
public class SearchAnalyticsDocService {
    private static final String MODEL_SEARCH_INDEX_PREFIX = "model-search";
    private static final String INVENTORY_SEARCH_INDEX_PREFIX = "inventory-search";

    @Autowired
    @Qualifier("nova")
    private ElasticClient elasticClient;

    public int getSearchCountsForDealer(List<String> zipCodes, StockType stockType, LocalDate startDate, LocalDate endDate, String programId) {
        if (startDate.isAfter(endDate)) {
            throw new RuntimeException("Start date must not be after end date");
        }

        SearchSourceBuilder searchBldr = new SearchSourceBuilder();

        /******************************************** QUERIES *******************************************/
        BoolQueryBuilder query = boolQuery();

        if (stockType != null) {
            query.must(termQuery("filterCriteria.stockTypes", stockType.toString()));
        }
        query.must(termsQuery("filterCriteria.zipCode", zipCodes));
        ZoneId zoneId = ZoneId.of("US/Eastern");
        query.must(
            rangeQuery("timestamp")
                .gte(startDate.atStartOfDay(zoneId).toEpochSecond() * 1000)
                .lte(endDate.atStartOfDay(zoneId).toEpochSecond() * 1000)
                .format("epoch_millis")
        );

        boolean isProgramId = StringUtils.isNotEmpty(programId) && StringUtils.isNotBlank(programId);

        if(isProgramId) {
            query.must(termQuery("filterCriteria.programId", programId));
        }

        searchBldr.query(query);
        searchBldr.size(0);

        LocalDate currentDate = startDate;
        List<String> indexes = new ArrayList<>();
        while (currentDate.isBefore(endDate)) {
            String dateString = String.format("%d-%02d*", currentDate.getYear(), currentDate.getMonthValue());
            indexes.add(String.format("%s-%s", MODEL_SEARCH_INDEX_PREFIX, dateString));
            indexes.add(String.format("%s-%s", INVENTORY_SEARCH_INDEX_PREFIX, dateString));
            currentDate = currentDate.plusMonths(1);
        }

        SearchRequest request = new SearchRequest(String.join(",", indexes));
        request.source(searchBldr);

        ElasticResponse response = this.elasticClient.search(request);

        return (int)response.getSearchResponse().getHits().getTotalHits().value;
    }
}
