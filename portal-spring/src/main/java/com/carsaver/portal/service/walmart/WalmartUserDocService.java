package com.carsaver.portal.service.walmart;

import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import static org.elasticsearch.index.query.QueryBuilders.boolQuery;
import static org.elasticsearch.index.query.QueryBuilders.rangeQuery;
import static org.elasticsearch.index.query.QueryBuilders.termQuery;

@Service
public class WalmartUserDocService {

    private static final String INDEX = "users";

    @Autowired
    @Qualifier("gibson")
    private ElasticClient elasticClient;

    public long getUserCountByHostName(String hostname) {
        if (hostname == null) {
            return 0;
        }

        SearchSourceBuilder searchBldr = new SearchSourceBuilder();
        BoolQueryBuilder query = boolQuery();

        query.must(termQuery("source.hostname", hostname));
        query.must(termQuery("type", "user"));
        query.must(rangeQuery("createdDate").gte("now-30d/d").lte("now/d"));

        searchBldr.query(query);
        searchBldr.size(0);

        SearchRequest request = new SearchRequest(INDEX);
        request.source(searchBldr);

        ElasticResponse response = this.elasticClient.search(request);

        return response.getSearchResponse().getHits().getTotalHits().value;
    }

    public long getUserCountByWalmartId(Integer walmartStoreId) {
        if (walmartStoreId == null) {
            return 0;
        }

        SearchSourceBuilder searchBldr = new SearchSourceBuilder();
        BoolQueryBuilder query = boolQuery();

        query.must(termQuery("walmartStoreId", walmartStoreId));
        query.must(termQuery("type", "user"));
        query.must(rangeQuery("createdDate").gte("now-30d/d").lte("now/d"));

        searchBldr.query(query);
        searchBldr.size(0);

        SearchRequest request = new SearchRequest(INDEX);
        request.source(searchBldr);

        ElasticResponse response = this.elasticClient.search(request);

        return response.getSearchResponse().getHits().getTotalHits().value;
    }
}
