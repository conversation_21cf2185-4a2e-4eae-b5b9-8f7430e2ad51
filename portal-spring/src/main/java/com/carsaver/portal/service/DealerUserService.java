package com.carsaver.portal.service;

import com.carsaver.core.StockType;
import com.carsaver.magellan.client.BasicUserAssociationClient;
import com.carsaver.magellan.client.FollowUpManagerClient;
import com.carsaver.magellan.client.ProgramManagerClient;
import com.carsaver.magellan.client.SalesManagerClient;
import com.carsaver.magellan.model.JobTitleView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.security.DealerPermissionView;
import com.carsaver.magellan.model.user.BasicDealerUserRoleAssociation;
import com.carsaver.magellan.model.user.FollowUpManagerRoleAssociation;
import com.carsaver.magellan.model.user.ProgramManagerRoleAssociation;
import com.carsaver.magellan.model.user.SalesManagerRoleAssociation;
import com.carsaver.portal.model.DealerUser;
import com.carsaver.portal.model.user.DealerUserAtlasData;
import com.carsaver.portal.service.dashboard.DashboardDealerUserAtlasDocService;
import com.carsaver.stereotype.ContactType;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DealerUserService {

    private final BasicUserAssociationClient basicUserAssociationClient;
    private final DashboardDealerUserAtlasDocService dashboardDealerUserAtlasDocService;
    private final SalesManagerClient salesManagerClient;
    private final FollowUpManagerClient followUpManagerClient;
    private final ProgramManagerClient programManagerClient;

    public DealerUserService(BasicUserAssociationClient basicUserAssociationClient, DashboardDealerUserAtlasDocService dashboardDealerUserAtlasDocService, SalesManagerClient salesManagerClient, FollowUpManagerClient followUpManagerClient, ProgramManagerClient programManagerClient) {
        this.basicUserAssociationClient = basicUserAssociationClient;
        this.dashboardDealerUserAtlasDocService = dashboardDealerUserAtlasDocService;
        this.salesManagerClient = salesManagerClient;
        this.followUpManagerClient = followUpManagerClient;
        this.programManagerClient = programManagerClient;
    }

    public List<DealerUser> getDealerUsersByDealerId(String dealerId) {
        List<DealerUser> dealerUsers = getDealerUsersFromBasicUserAssociationClients(dealerId);

        List<DealerUser> result = buildDealerUsersWithAtlasData(dealerId, dealerUsers);

        return result;
    }

    private List<DealerUser> getDealerUsersFromBasicUserAssociationClients(String dealerId) {
        Collection<BasicDealerUserRoleAssociation> basicDealerUserRoleAssociations =
            basicUserAssociationClient.findDealerGrantedPrivileges(dealerId).getContent();

        List<DealerUser> result = basicDealerUserRoleAssociations.stream()
            .map(basicDealerUserRoleAssociation ->
                buildDealerUserFromBasicUserRoleAssociation(basicDealerUserRoleAssociation, dealerId))
            .collect(Collectors.toList());
        return result;
    }

    private DealerUser buildDealerUserFromBasicUserRoleAssociation(BasicDealerUserRoleAssociation basicDealerUserRoleAssociation, String dealerId) {
        UserView user = basicDealerUserRoleAssociation.getUser();

        DealerUser result = DealerUser.builder()
            .id(user.getId())
            .firstName(user.getFirstName())
            .lastName(user.getLastName())
            .type(user.getType())
            .phoneNumber(user.getPhoneNumber())
            .permissions(dealerPermissionsNotLegacy(basicDealerUserRoleAssociation))
            .email(user.getEmail())
            .jobTitle(Optional.ofNullable(user.getJobTitle()).map(JobTitleView::getTitle).orElse(null))
            .isAssociated(true)
            .salesManager(salesManagerClient.getProgramManager(dealerId, user.getId()).isPresent())
            .followupManager(followUpManagerClient.getFollowUpManager(dealerId, user.getId()).isPresent())
            .programManager(programManagerClient.getProgramManager(dealerId, user.getId()).isPresent())
            .smStockType(getSmStockType(dealerId, user))
            .fmStockType(getFmStockType(dealerId, user))
            .pmStockType(getPmStockType(dealerId, user))
            .contactType(Optional.ofNullable(basicDealerUserRoleAssociation.getContactType()).map(ContactType::toString).orElse(null))
            .build();

        return result;
    }

    private List<DealerPermissionView> dealerPermissionsNotLegacy(BasicDealerUserRoleAssociation basicDealerUserRoleAssociation) {
        List<DealerPermissionView> result = basicDealerUserRoleAssociation.getPermissionList().stream()
            .filter(p -> !p.isLegacy())
            .collect(Collectors.toList());
        return result;
    }

    private String getSmStockType(String dealerId, UserView user) {
        return salesManagerClient.getProgramManager(dealerId, user.getId())
            .map(SalesManagerRoleAssociation::getStockType)
            .map(StockType::toProperCaseStr).orElse(null);
    }

    private String getFmStockType(String dealerId, UserView user) {
        return followUpManagerClient.getFollowUpManager(dealerId, user.getId())
            .map(FollowUpManagerRoleAssociation::getStockType)
            .map(StockType::toProperCaseStr).orElse(null);
    }

    private String getPmStockType(String dealerId, UserView user) {
        return programManagerClient.getProgramManager(dealerId, user.getId())
            .map(ProgramManagerRoleAssociation::getStockType)
            .map(StockType::toProperCaseStr).orElse(null);
    }

    private List<DealerUser> buildDealerUsersWithAtlasData(String dealerId, List<DealerUser> dealerUsers) {
        List<DealerUserAtlasData> dealerUserAtlasDataList = dashboardDealerUserAtlasDocService.getDealerUserAtlasData(
            dealerId, getDealerUserIds(dealerUsers)
        );

        List<DealerUser> result = dealerUsers.stream()
            .map(dealerUser -> addAtlasDataToDealerUser(dealerUser, dealerUserAtlasDataList))
            .collect(Collectors.toList());
        return result;
    }

    private List<String> getDealerUserIds(List<DealerUser> dealerUsers) {
        List<String> result = dealerUsers.stream()
            .map(DealerUser::getId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        return result;
    }

    private DealerUser addAtlasDataToDealerUser(DealerUser dealerUser, List<DealerUserAtlasData> dealerUserAtlasDataList) {
        DealerUser result = dealerUser;

        if (CollectionUtils.isNotEmpty(dealerUserAtlasDataList)) {
            dealerUserAtlasDataList.stream()
                .filter(dealerUserAtlas -> dealerUser.getId().equals(dealerUserAtlas.getUserId()))
                .forEach(result::setDealerUserAtlasData);
        }

        return result;
    }
}
