package com.carsaver.portal.service.dashboard;

import lombok.Getter;

public enum Program {

    CARSAVER(100),
    EXPRESS(101),
    NISSAN_BUY_AT_HOME(102),

    UPGRADE(103);

    @Getter
    private final int associatedProductId;

    Program(int associatedProductId) {
        this.associatedProductId = associatedProductId;
    }

    public static Integer getAssociatedFilter(Program program) {
        if (program != null) {
            return program.getAssociatedProductId();
        }
        return null;
    }
}
