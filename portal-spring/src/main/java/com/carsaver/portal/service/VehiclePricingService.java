package com.carsaver.portal.service;

import com.carsaver.core.InventorySource;
import com.carsaver.core.StockType;
import com.carsaver.magellan.api.InventoryService;
import com.carsaver.magellan.api.PriceAdjustmentService;
import com.carsaver.magellan.client.InventoryClient;
import com.carsaver.magellan.client.PricingClient;
import com.carsaver.magellan.model.CarPriceAdjustmentView;
import com.carsaver.magellan.model.CarWithPriceAdjustment;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.ModelPriceAdjustmentView;
import com.carsaver.magellan.model.ModelWithPriceAdjustment;
import com.carsaver.magellan.model.PriceAdjustmentView;
import com.carsaver.magellan.model.StockTypePriceAdjustmentView;
import com.carsaver.magellan.model.StylePriceAdjustmentView;
import com.carsaver.magellan.model.StyleWithPriceAdjustment;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.YearMakeModelsView;
import com.carsaver.magellan.model.metrics.InventoryMetrics;
import com.carsaver.magellan.search.PriceAdjustmentSearchCriteria;
import com.carsaver.portal.model.vehiclePricing.NewVehiclePricingForm;
import com.carsaver.portal.model.vehiclePricing.PricedCarRow;
import com.carsaver.portal.model.vehiclePricing.PricedModelRow;
import com.carsaver.portal.model.vehiclePricing.PricedStyleRow;
import com.carsaver.portal.model.vehiclePricing.UsedVehiclePricingForm;
import lombok.Getter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.carsaver.magellan.model.PriceAdjustmentView.AdjustedValue;
import static java.util.Comparator.comparing;
import static org.springframework.util.StringUtils.hasLength;
import static org.springframework.util.StringUtils.isEmpty;

@Slf4j
@Service
@Getter
public class VehiclePricingService {
    private static final List<InventorySource> SUPPORTED_INVENTORY_SOURCES = Arrays.asList(InventorySource.HN);

    @Autowired
    private PriceAdjustmentService priceAdjustmentService;

    @Autowired
    private InventoryClient inventoryClient;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private PricingClient pricingClient;

    public static boolean isPricingPortalSupportedBy(@NonNull DealerView dealer) {
        return SUPPORTED_INVENTORY_SOURCES.contains(dealer.getInventorySource());
    }

    public NewVehiclePricingForm prepareNewVehiclePricingForm(DealerView dealer, NewVehiclePricingForm newVehiclePricingForm) {
        PriceAdjustmentSearchCriteria searchForm = newVehiclePricingForm.getSearchForm();

        StockTypePriceAdjustmentView stockTypePriceAdjustment = priceAdjustmentService.findStockTypePriceAdjustmentBy(dealer.getId(), StockType.NEW);
        newVehiclePricingForm.addStockTypePriceAdjustmentWithDefaults(stockTypePriceAdjustment, dealer.getId());

        Integer pricingFormStyleFilter = null;
        String pricingFormInventoryIdFilter = null;
        boolean vehicleSearchAttempted = false;
        boolean vehicleSearchFoundVehicle = false;
        String vehicleSearchFailureMessage = "";

        // if vehicle VIN was provided, provide vehicle specific filters to the table view
        if(StringUtils.hasText(searchForm.getVin())) {
            vehicleSearchAttempted = true;
            Optional<VehicleView> foundVehicle = inventoryClient.findNewVehicleByDealerIdVinAndInInventoryTrue(dealer.getId(), searchForm.getVin());
            pricingFormStyleFilter = foundVehicle.map(VehicleView::getStyleId).orElse(null);
            pricingFormInventoryIdFilter = foundVehicle.map(VehicleView::getId).orElse(null);
            vehicleSearchFoundVehicle = foundVehicle.isPresent();
            if(!vehicleSearchFoundVehicle) {
                vehicleSearchFailureMessage = "No vehicle found for vin " + searchForm.getVin() + " so showing all models.";
            }
        }

        // if vehicle stock number was provided, provide vehicle specific filters to the table view
        if(StringUtils.hasText(searchForm.getStockNumber())){
            vehicleSearchAttempted = true;
            Optional<VehicleView> foundVehicle = inventoryClient.findNewVehicleByDealerIdStockNumberAndInInventoryTrue(dealer.getId(), searchForm.getStockNumber());
            pricingFormStyleFilter = foundVehicle.map(VehicleView::getStyleId).orElse(null);
            pricingFormInventoryIdFilter = foundVehicle.map(VehicleView::getId).orElse(null);
            vehicleSearchFoundVehicle = foundVehicle.isPresent();
            if(!vehicleSearchFoundVehicle) {
                vehicleSearchFailureMessage = "No vehicle found for stock number " + searchForm.getStockNumber() + " so showing all models.";
            }
        }

        newVehiclePricingForm.setSearchForm(searchForm);
        newVehiclePricingForm.setEnforcePricingRules(dealer.getEffectivePreferences().getPricingRules().enforceFor(StockType.NEW));
        newVehiclePricingForm.setVehicleSearchAttempted(vehicleSearchAttempted);
        newVehiclePricingForm.setVehicleSearchFoundVehicle(vehicleSearchFoundVehicle);
        newVehiclePricingForm.setPricingFormStyleFilter(pricingFormStyleFilter);
        newVehiclePricingForm.setPricingFormInventoryIdFilter(pricingFormInventoryIdFilter);
        newVehiclePricingForm.setVehicleSearchFailureMessage(vehicleSearchFailureMessage);
        newVehiclePricingForm.setEnableLeasePricing(dealer.getEffectivePreferences().getEnableLeasePricing());
        newVehiclePricingForm.setPricedModelRows( preparePricedModelRows(dealer, searchForm) );

        return newVehiclePricingForm;
    }

    private List<PricedModelRow> preparePricedModelRows(DealerView dealer, PriceAdjustmentSearchCriteria searchForm) {
        List<ModelWithPriceAdjustment> modelWithPriceAdjustemnts = searchForm.isCriteriaPresent()
            ? priceAdjustmentService.findNewModelsWithPriceAdjustmentsFor(dealer.getId(), searchForm)
            : priceAdjustmentService.findNewModelsWithPriceAdjustmentsFor(dealer.getId());

        Comparator<PricedModelRow> compareByYearDescThenName = comparing(
            (PricedModelRow mwpa) -> mwpa.getModel().getYear()).reversed()
            .thenComparing(mwpa -> mwpa.getModel().getName());

        return modelWithPriceAdjustemnts
            .stream()
            .filter(mwpa -> searchForm.getActive() == null ||  mwpa.getInventoryCount() > 0)
            .map(PricedModelRow::new)
            .sorted(compareByYearDescThenName)
            .collect(Collectors.toList());
    }

    public NewVehiclePricingForm updateNewPriceAdjustments(DealerView dealer, NewVehiclePricingForm newVehiclePricingForm) {
        this.saveCarPriceAdjustments(newVehiclePricingForm.getPricedCarMapAsList(), dealer.getId(), StockType.NEW);
        this.saveStyleWithPriceAdjustments(newVehiclePricingForm.getPricedStyleMapAsList(), dealer.getId());
        this.saveModelWithPriceAdjustments(newVehiclePricingForm.getPricedModelMapAsList(), dealer.getId());
        this.saveStockTypePriceAdjustment(newVehiclePricingForm.getStockTypePriceAdjustment());
        this.recalculateAndSaveAllNewVehiclePricesFor(dealer.getId());

        return prepareNewVehiclePricingForm(dealer, newVehiclePricingForm);
    }

    public UsedVehiclePricingForm updatedUsedPriceAdjustments(DealerView dealer, UsedVehiclePricingForm usedVehiclePricingForm) {
        if(usedVehiclePricingForm.getStockTypePriceAdjustment() != null && usedVehiclePricingForm.getStockTypePriceAdjustment().getAdjustment() != null) {
            usedVehiclePricingForm.getStockTypePriceAdjustment().getAdjustment().setAdjustedValue(AdjustedValue.INTERNET_PRICE);
        }

        List<CarWithPriceAdjustment> carWithPriceAdjustments = Collections.emptyList();

        if(usedVehiclePricingForm.getPricedCarRows() != null) {
            carWithPriceAdjustments = usedVehiclePricingForm.getPricedCarRows()
                .stream()
                .map(PricedCarRow::toCarWithPriceAdjustment)
                .collect(Collectors.toList());
        }

        carWithPriceAdjustments.forEach(carWithPriceAdjustment -> {
            if(carWithPriceAdjustment.getAdjustment() != null) {
                carWithPriceAdjustment.getAdjustment().setAdjustedValue(AdjustedValue.INTERNET_PRICE);
            }
        });

        this.saveStockTypePriceAdjustment(usedVehiclePricingForm.getStockTypePriceAdjustment());
        this.saveCarPriceAdjustments(carWithPriceAdjustments, dealer.getId(), StockType.USED);
        this.recalculateAndSaveAllUsedVehiclePricesFor(dealer.getId());

        return prepareUsedVehiclePricingForm(dealer, usedVehiclePricingForm);
    }

    public YearMakeModelsView getYmmForDealerAndStockType(DealerView dealer, StockType stockType) {
        YearMakeModelsView ymm = priceAdjustmentService.getDistinctYearMakeModelsBy(dealer.getId(), stockType);

        //front end code is guarding against null values in various places, so removing them here
        ymm.setYears(ymm.getYears().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        ymm.setMakes(ymm.getMakes().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        ymm.setModels(ymm.getModels().stream().filter(Objects::nonNull).collect(Collectors.toList()));

        return ymm;
    }

    public List<PricedStyleRow> getStyleWithPriceAdjustmentsFor(DealerView dealer, Integer modelId, Integer pricingFormStyleFilter, Boolean active) {
        List<StyleWithPriceAdjustment> styleWithPriceAdjustments = priceAdjustmentService
            .findStylesWithPriceAdjustmentFor(dealer.getId(), modelId, active);

        List<PricedStyleRow> pricedStyleRows = styleWithPriceAdjustments.stream()
            .filter(swpa -> pricingFormStyleFilter == null || swpa.getStyle().getId().equals(pricingFormStyleFilter))
            .filter(swpa -> active == null || swpa.getInventoryCount() > 0)
            .map(PricedStyleRow::new)
            .collect(Collectors.toList());

        return pricedStyleRows;
    }

    public List<PricedCarRow> getNewCarWithPriceAdjustmentsFor(DealerView dealer, Integer styleId, String pricingFormInventoryIdFilter, Boolean active) {
        List<PricedCarRow> pricedCarRows = priceAdjustmentService
            .findNewCarsWithPriceAdjustmentFor(dealer.getId(), styleId, active)
            .stream()
            .filter(cwpa -> isEmpty(pricingFormInventoryIdFilter) || cwpa.getVehicle().getId().equals(pricingFormInventoryIdFilter))
            .map(cwpa -> new PricedCarRow(cwpa, StockType.NEW))
            .collect(Collectors.toList());

        return pricedCarRows;
    }

    public UsedVehiclePricingForm prepareUsedVehiclePricingForm(DealerView dealer, UsedVehiclePricingForm usedVehiclePricingForm) {
        StockTypePriceAdjustmentView stockTypePriceAdjustment = priceAdjustmentService.findStockTypePriceAdjustmentBy(dealer.getId(), StockType.USED);
        usedVehiclePricingForm.addStockTypePriceAdjustmentWithDefaults(stockTypePriceAdjustment, dealer.getId());
        usedVehiclePricingForm.setEnforcePricingRules(dealer.getEffectivePreferences().getPricingRules().enforceFor(StockType.USED));
        usedVehiclePricingForm.setPricedCarRows(prepareUsedPricedCarRows(dealer, usedVehiclePricingForm));

        if(usedVehiclePricingForm.getSearchForm().isCriteriaPresent()) {
            usedVehiclePricingForm.setVehicleSearchAttempted(true);
        } else {
            usedVehiclePricingForm.setVehicleSearchAttempted(false);
        }

        //handle no vehicle found
        if(usedVehiclePricingForm.getPricedCarRows().isEmpty()) {
            usedVehiclePricingForm.setVehicleSearchFoundVehicle(false);
            String vehicleSearchFailureMessage;

            if(usedVehiclePricingForm.getSearchForm().isVinSearch()) {
                vehicleSearchFailureMessage = "No vehicle found for vin " + usedVehiclePricingForm.getSearchForm().getVin();
                usedVehiclePricingForm.setVehicleSearchFailureMessage(vehicleSearchFailureMessage);
            }
            if(usedVehiclePricingForm.getSearchForm().isStockNumberSearch()) {
                vehicleSearchFailureMessage = "No vehicle found for stock number " + usedVehiclePricingForm.getSearchForm().getStockNumber();
                usedVehiclePricingForm.setVehicleSearchFailureMessage(vehicleSearchFailureMessage);
            }
        } else if(!usedVehiclePricingForm.getPricedCarRows().isEmpty() && usedVehiclePricingForm.isVehicleSearchAttempted()) {
            usedVehiclePricingForm.setVehicleSearchFoundVehicle(true);
        }

        return usedVehiclePricingForm;
    }

    public InventoryMetrics getInventoryMetrics(DealerView dealer) {
        return inventoryService.getInventoryMetrics((dealer.getId()));
    }

    public void deletePricingRules(DealerView dealer, StockType stockType) {
        pricingClient.deletePricingRules(dealer.getId(), stockType);
    }

    private StockTypePriceAdjustmentView saveStockTypePriceAdjustment(StockTypePriceAdjustmentView stockTypePriceAdjustment) {
        PriceAdjustmentView stockTypeAdjustment = stockTypePriceAdjustment.getAdjustment();
        boolean shouldBeSaved = hasLength(stockTypeAdjustment.getId()) || stockTypeAdjustment.isEnabled();

        return shouldBeSaved ? priceAdjustmentService.saveStockTypePriceAdjustment(stockTypePriceAdjustment) : null;
    }

    private List<CarPriceAdjustmentView> saveCarPriceAdjustments(List<CarWithPriceAdjustment> carWithPriceAdjustments, String dealerId, StockType stockType) {
        List<CarPriceAdjustmentView> carPriceAdjustments = carWithPriceAdjustments.stream()
            .filter(cwpa -> hasLength(cwpa.getAdjustment().getId()) || cwpa.getAdjustment().isEnabled()) //only save if previously saved OR enabled
            .map(cwpa -> new CarPriceAdjustmentView(dealerId, stockType, cwpa))
            .collect(Collectors.toList());
        log.info("number of vehicles submitted = {}", carPriceAdjustments.size());

        return priceAdjustmentService.saveCarPriceAdjustmentsForDealer(carPriceAdjustments, dealerId);
    }

    private List<ModelPriceAdjustmentView> saveModelWithPriceAdjustments(List<ModelWithPriceAdjustment> modelWithPriceAdjustments, String dealerId) {
        List<ModelPriceAdjustmentView> modelPriceAdjustments = modelWithPriceAdjustments.stream()
            .filter(mwpa -> hasLength(mwpa.getPriceAdjustment().getId()) || mwpa.getPriceAdjustment().isEnabled()) //only save if previously saved OR enabled
            .map(mwpa -> new ModelPriceAdjustmentView(dealerId, mwpa))
            .collect(Collectors.toList());
        log.info("number of model priceAdjustments submitted = {}", modelPriceAdjustments.size());

        return getPriceAdjustmentService().saveModelPriceAdjustmentsForDealer(modelPriceAdjustments, dealerId);
    }

    private List<StylePriceAdjustmentView> saveStyleWithPriceAdjustments(List<StyleWithPriceAdjustment> styleWithPriceAdjustments, String dealerId) {
        List<StylePriceAdjustmentView> stylePriceAdjustments = styleWithPriceAdjustments.stream()
            .filter(swpa -> hasLength(swpa.getPriceAdjustment().getId()) || swpa.getPriceAdjustment().isEnabled()) //only save if previously saved OR enabled
            .map(swpa -> new StylePriceAdjustmentView(dealerId, swpa))
            .collect(Collectors.toList());
        log.info("number of style priceAdjustments submitted = {}", stylePriceAdjustments.size());

        return priceAdjustmentService.saveStylePriceAdjustmentsForDealer(stylePriceAdjustments, dealerId);
    }

    private void recalculateAndSaveAllNewVehiclePricesFor(String dealerId){
        priceAdjustmentService.recalculateAndSaveAllNewVehiclePricesFor(dealerId);
    }

    private void recalculateAndSaveAllUsedVehiclePricesFor(String dealerId){
        priceAdjustmentService.recalculateAndSaveAllUsedVehiclePricesFor(dealerId);
    }

    private List<PricedCarRow> prepareUsedPricedCarRows(DealerView dealer, UsedVehiclePricingForm usedVehiclePricingForm) {
        PriceAdjustmentSearchCriteria searchForm = usedVehiclePricingForm.getSearchForm();

        List<CarWithPriceAdjustment> carWithPriceAdjustments;
        if(!searchForm.isCriteriaPresent()) {
            carWithPriceAdjustments = priceAdjustmentService.findCarWithPriceAdjustments(dealer.getId(), StockType.USED, null);
        } else {
            if(searchForm.isStockNumberSearch()) {
                carWithPriceAdjustments = priceAdjustmentService.findCarWithPriceAdjustmentsByStockNumber(dealer.getId(), StockType.USED, searchForm.getStockNumber());
            } else if(searchForm.isVinSearch()) {
                carWithPriceAdjustments = priceAdjustmentService.findCarWithPriceAdjustmentsByVin(dealer.getId(), StockType.USED, searchForm.getVin());
            } else {
                carWithPriceAdjustments = priceAdjustmentService.findUsedCarWithPriceAdjustmentsBy(dealer.getId(), searchForm, null);
            }
        }

        Comparator<PricedCarRow> compareByYearDescThenVin = comparing(
            (PricedCarRow cwpa) -> cwpa.getVehicle().getYear()).reversed()
            .thenComparing(PricedCarRow::getVin);

        return carWithPriceAdjustments.stream()
            .map(cwpa -> new PricedCarRow(cwpa, StockType.USED))
            .sorted(compareByYearDescThenVin)
            .collect(Collectors.toList());

    }
}
