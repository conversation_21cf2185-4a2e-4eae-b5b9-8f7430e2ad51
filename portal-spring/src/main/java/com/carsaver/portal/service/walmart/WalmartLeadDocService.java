package com.carsaver.portal.service.walmart;

import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;

import static org.elasticsearch.index.query.QueryBuilders.boolQuery;
import static org.elasticsearch.index.query.QueryBuilders.rangeQuery;
import static org.elasticsearch.index.query.QueryBuilders.termQuery;


@Service
public class WalmartLeadDocService {

    private static final String INDEX = "leads";

    @Autowired
    @Qualifier("gibson")
    private ElasticClient elasticClient;

    public HashMap<String,Long> getLeadsByHostname(String hostname) {
        HashMap<String,Long> map = new HashMap<>();

        SearchSourceBuilder searchBldr = new SearchSourceBuilder();
        BoolQueryBuilder query = boolQuery();

        query.must(termQuery("source.hostname", hostname));
        query.must(rangeQuery("createdDate").gte("now-30d/d").lte("now/d"));

        searchBldr.query(query);

        CardinalityAggregationBuilder userIdAggregation = AggregationBuilders
            .cardinality("userId").field("user.id");

        searchBldr.aggregation(userIdAggregation);
        searchBldr.size(0);

        SearchRequest request = new SearchRequest(INDEX);
        request.source(searchBldr);

        ElasticResponse response = this.elasticClient.search(request);

        long totalLeads = response.getSearchResponse().getHits().getTotalHits().value;

        ParsedCardinality userIdCardinality = response.getSearchResponse().getAggregations().get("userId");
        long totalUniqueLeads = userIdCardinality.getValue();

        map.put("totalLeads", totalLeads);
        map.put("totalUniqueLeads", totalUniqueLeads);

        return map;
    }
}
