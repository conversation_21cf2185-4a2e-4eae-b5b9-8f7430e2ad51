package com.carsaver.portal.service;

import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticResponse;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.Sum;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class DealerStatsService {

    public static final String DEALERS_INDEX = "dealers";

    @Qualifier("gibson")
    @Autowired
    private ElasticClient elasticClient;

    public DealerType getStats() {
        SearchSourceBuilder searchBldr = new SearchSourceBuilder();
        searchBldr.size(0); // we don't need any docs, topHits will return the necessary docs itself

        AggregationBuilder aggs = AggregationBuilders
            .terms("byDealerCertified")
            .field("certified")
            .subAggregation(AggregationBuilders
                .terms("dealerStatusAgg")
                .field("status")
                .subAggregation(AggregationBuilders
                    .sum("makesCountAgg")
                    .script(new Script("doc['divisionIds'].size() > 0 ? doc['divisionIds'].size() : 1")))
            );

        searchBldr.aggregation(aggs);

        SearchRequest request = new SearchRequest(DEALERS_INDEX);
        request.source(searchBldr);

        ElasticResponse response = this.elasticClient.search(request);

        DealerType type = new DealerType();
        Terms byDealerCertified = response.getSearchResponse().getAggregations().get("byDealerCertified");

        byDealerCertified.getBuckets().forEach(byDealerCertifiedBuckets -> {
            DealerStatus status = new DealerStatus();

            Terms dealerStatusAgg = byDealerCertifiedBuckets.getAggregations().get("dealerStatusAgg");
            dealerStatusAgg.getBuckets().forEach(dealerStatusAggBucket -> {
                Sum makesCountAgg = dealerStatusAggBucket.getAggregations().get("makesCountAgg");
                long makesCount = Math.round(Double.parseDouble(makesCountAgg.getValueAsString()));

                DealerStat dealerStat = DealerStat.builder()
                    .count(dealerStatusAggBucket.getDocCount())
                    .makesCount(makesCount)
                    .build();
                status.addStats(dealerStatusAggBucket.getKeyAsString(), dealerStat);
            });

            switch (byDealerCertifiedBuckets.getKeyAsString()) {
                case "true":
                    type.setCertified(status);
                    break;
                case "false":
                    type.setNonCertified(status);
                    break;
            }
        });

        return type;
    }

    @Data
    public static class DealerType {
        private DealerStatus certified;
        private DealerStatus nonCertified;
    }

    public static class DealerStatus {
        @Getter
        private Map<String, DealerStat> stats = new HashMap<>();

        void addStats(String status, DealerStat stat) {
            stats.put(status, stat);
        }
    }

    @Builder
    public static class DealerStat {
        @Getter
        private Long count;
        @Getter
        private Long makesCount;
    }

}
