package com.carsaver.portal.service.dashboard;

import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticResponse;
import com.carsaver.portal.model.store.dealer.dashboard.StockTypeCountWidget;
import com.carsaver.portal.model.store.dealer.dashboard.WarrantyCountWidget;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import static com.carsaver.portal.service.dashboard.SaveADealUtil.removeSaveADealSales;
import static org.elasticsearch.index.query.QueryBuilders.boolQuery;
import static org.elasticsearch.index.query.QueryBuilders.existsQuery;
import static org.elasticsearch.index.query.QueryBuilders.rangeQuery;
import static org.elasticsearch.index.query.QueryBuilders.termQuery;
import static org.elasticsearch.index.query.QueryBuilders.termsQuery;

@Service
public class DashboardSalesDocService {
    private static final String INDEX = "vehicle-sales";

    private final ElasticClient elasticClient;

    public DashboardSalesDocService(@Qualifier("gibson") ElasticClient elasticClient) {
        this.elasticClient = elasticClient;
    }

    public StockTypeCountWidget getSalesCount(List<String> dealerIds, ZonedDateTime startDate, ZonedDateTime endDate, ProgramFilter filter) {
        validateDate(startDate, endDate);

        SearchSourceBuilder searchBldr = new SearchSourceBuilder();

        /******************************************** QUERIES *******************************************/
        BoolQueryBuilder query = boolQuery();

        if (CollectionUtils.isNotEmpty(dealerIds)) {
            query.must(termsQuery("dealer.id", dealerIds));
        }

        if (filter.isNotExpress()) {
            removeSaveADealSales(query);
        }

        query.must(
            rangeQuery("createdDate")
                .gte(startDate.toEpochSecond() * 1000)
                .lte(endDate.toEpochSecond() * 1000)
                .format("epoch_millis")
        );
        query.must(termQuery("status", "VERIFIED"));

        filter.filterByProgramIfNecessary(query);

        searchBldr.query(query);

        searchBldr.aggregation(AggregationBuilders.terms("stockType").field("vehicle.stockType").missing("UNKNOWN"));
        searchBldr.size(0);

        SearchRequest request = new SearchRequest(INDEX);
        request.source(searchBldr);

        ElasticResponse<?> response = this.elasticClient.search(request);

        ParsedStringTerms stockTypeTerms = response.getSearchResponse().getAggregations().get("stockType");
        Long totalNew = Optional.ofNullable(stockTypeTerms.getBucketByKey("NEW")).map(MultiBucketsAggregation.Bucket::getDocCount).orElse(0L);
        Long totalUsed = Optional.ofNullable(stockTypeTerms.getBucketByKey("USED")).map(MultiBucketsAggregation.Bucket::getDocCount).orElse(0L);
        Long totalUnknown = Optional.ofNullable(stockTypeTerms.getBucketByKey("UNKNOWN")).map(MultiBucketsAggregation.Bucket::getDocCount).orElse(0L);

        StockTypeCountWidget stockTypeCountWidget = new StockTypeCountWidget();
        stockTypeCountWidget.setNewCount(totalNew.intValue());
        stockTypeCountWidget.setUsedCount(totalUsed.intValue());
        stockTypeCountWidget.setUnknownCount(totalUnknown.intValue());
        stockTypeCountWidget.setTotalCount(Math.toIntExact(response.getSearchResponse().getHits().getTotalHits().value));

        return stockTypeCountWidget;
    }

    public WarrantyCountWidget getLifetimeWarrantyCount(List<String> dealerIds, ZonedDateTime startDate, ZonedDateTime endDate, ProgramFilter filter) {
        validateDate(startDate, endDate);

        SearchSourceBuilder searchBldr = new SearchSourceBuilder();

        /******************************************** QUERIES *******************************************/
        BoolQueryBuilder query = boolQuery();

        if (CollectionUtils.isNotEmpty(dealerIds)) {
            query.must(termsQuery("dealer.id", dealerIds));
        }

        if (filter.isNotExpress()) {
            removeSaveADealSales(query);
        }

        query.must(
            rangeQuery("createdDate")
                .gte(startDate.toEpochSecond() * 1000)
                .lte(endDate.toEpochSecond() * 1000)
                .format("epoch_millis")
        );
        query.must(existsQuery("lifetimeWarrantyStatus"));

        filter.filterByProgramIfNecessary(query);

        searchBldr.query(query);

        searchBldr.aggregation(AggregationBuilders.terms("warrantyStatus").field("lifetimeWarrantyStatus"));
        searchBldr.size(0);

        SearchRequest request = new SearchRequest(INDEX);
        request.source(searchBldr);

        ElasticResponse<?> response = this.elasticClient.search(request);

        ParsedStringTerms stockTypeTerms = response.getSearchResponse().getAggregations().get("warrantyStatus");
        Long vscCount = Optional.ofNullable(stockTypeTerms.getBucketByKey("ENROLLED_VSC")).map(MultiBucketsAggregation.Bucket::getDocCount).orElse(0L);
        Long standardCount = Optional.ofNullable(stockTypeTerms.getBucketByKey("ENROLLED_STANDARD")).map(MultiBucketsAggregation.Bucket::getDocCount).orElse(0L);

        WarrantyCountWidget warrantyCountWidget = new WarrantyCountWidget();
        warrantyCountWidget.setVscCount(vscCount.intValue());
        warrantyCountWidget.setWarrantyCount(standardCount.intValue());

        return warrantyCountWidget;
    }

    private static void validateDate(ZonedDateTime startDate, ZonedDateTime endDate) {
        if (startDate.isAfter(endDate)) {
            throw new RuntimeException("Start date must not be after end date");
        }
    }
}
