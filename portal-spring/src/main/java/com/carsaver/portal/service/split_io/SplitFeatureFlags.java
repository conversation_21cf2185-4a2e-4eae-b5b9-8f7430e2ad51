package com.carsaver.portal.service.split_io;

import com.carsaver.split.io.client.SplitClientFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class SplitFeatureFlags {

    private static final String PORTAL = "Portal";
    private static final String ORGANIZATIONS_FEATURE = "organizationsFeature";
    private static final String ATLAS_DEALER_TRACK_PHASE_2_ENABLED =  "AtlasDealerTrackPhase2Enabled";

    private final SplitClientFacade splitClientFacade;

    public SplitFeatureFlags(SplitClientFacade splitClientFacade) {
        this.splitClientFacade = splitClientFacade;
    }

    public Map<String, Boolean> getAllFeatureFlags() {
        Map<String, Boolean> featureFlags = new HashMap<>();

        featureFlags.put("ORGANIZATIONS_FEATURE", isOrganizationsFeatureEnabled());
        featureFlags.put("ATLAS_DEALER_TRACK_PHASE_2_ENABLED", isFeatureEnabled(PORTAL, ATLAS_DEALER_TRACK_PHASE_2_ENABLED));

        return featureFlags;
    }


    public boolean isFeatureEnabled(String key, String featureName) {
        boolean isOn = false;

        try {
            isOn = splitClientFacade.isNewFeatureOn(key, featureName);
        } catch (Exception ex) {
            log.error("Split.io call threw error for split feature: {}, Error: {}", featureName, ex);
        }

        return isOn;
    }


    public boolean isOrganizationsFeatureEnabled() {
        boolean enabled = isFeatureEnabled(PORTAL, ORGANIZATIONS_FEATURE);
        return enabled;
    }


}
