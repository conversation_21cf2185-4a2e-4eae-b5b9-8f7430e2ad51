package com.carsaver.portal.config;

import com.carsaver.magellan.auth.CarSaverAuthService;
import com.carsaver.portal.client.OrganizationClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@ConditionalOnProperty(prefix = "dealer-service", name = "api-uri")
@Slf4j
@Configuration
public class OrganizationConfiguration {
    @Value("${dealer-service.api-uri}")
    private String rootUri;

    @Value("${spring.application.name}")
    private String applicationName;

    @Bean
    CarSaverAuthService carSaverAuthService() {
        return new CarSaverAuthService();
    }

    @Bean
    RestTemplate organizationConfigRestTemplate() {
        return new RestTemplate();
    }

    @Bean
    public OrganizationClient organizationClient() {
        OrganizationClient organizationClient = new OrganizationClient(rootUri, organizationConfigRestTemplate(), carSaverAuthService(), applicationName);
        return organizationClient;
    }
}
