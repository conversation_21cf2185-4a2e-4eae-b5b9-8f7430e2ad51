package com.carsaver.portal.config.support;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.tuckey.web.filters.urlrewrite.Conf;
import org.tuckey.web.filters.urlrewrite.UrlRewriteFilter;

import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import java.io.IOException;

public class CustomerUrlRewriteFilter extends UrlRewriteFilter {

    private static final String CONFIG_LOCATION = "urlrewrite.xml";

    private Resource resource = new ClassPathResource(CONFIG_LOCATION);

    @Override
    protected void loadUrlRewriter(FilterConfig filterConfig) throws ServletException {
        try {
            Conf conf = new Conf(filterConfig.getServletContext(), resource.getInputStream(), resource.getFilename(), "carsaver-portal");
            checkConf(conf);
        } catch (IOException ex) {
            throw new ServletException("Unable to load URL rewrite configuration file from " + CONFIG_LOCATION, ex);
        }
    }
}
