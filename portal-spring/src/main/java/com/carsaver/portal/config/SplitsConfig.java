package com.carsaver.portal.config;

import com.carsaver.split.io.client.SplitClientFacade;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class SplitsConfig {
    @Bean(name = "splitDefaults")
    @ConfigurationProperties(prefix = "splits.defaults")
    public Map<String, String> splitDefaults() {
        final HashMap<String, String> result = new HashMap<>();
        return result;
    }

    @Bean(destroyMethod = "close")
    public SplitClientFacade splitClientFacade(
        @Qualifier("splitDefaults") Map<String, String> map,
        @Value("${splits.key}") String key
    ) {
        final SplitClientFacade result = new SplitClientFacade(map, key);
        return result;
    }

}
