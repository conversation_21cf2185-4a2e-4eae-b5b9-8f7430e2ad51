package com.carsaver.portal.config;

import com.carsaver.magellan.auth.servlet.JwtTokenFilter;
import com.carsaver.magellan.auth.servlet.JwtTokenProvider;
import com.carsaver.magellan.security.CustomWebSecurityExpressionHandler;
import com.carsaver.magellan.security.DealerPermissionEvaluator;
import com.carsaver.portal.security.CarSaverAuthenticationProvider;
import com.carsaver.portal.security.KibanaRequestMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.SecurityProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.access.expression.SecurityExpressionHandler;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@ComponentScan(value = {"com.carsaver.magellan.filter"})
@EnableWebSecurity
@Order(SecurityProperties.BASIC_AUTH_ORDER)
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Autowired
    private CarSaverAuthenticationProvider carSaverAuthenticationProvider;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        JwtTokenFilter jwtTokenFilter = new JwtTokenFilter(jwtTokenProvider);
        http
            .csrf().ignoringRequestMatchers(new KibanaRequestMatcher())
            .and()
            .addFilterBefore(jwtTokenFilter, UsernamePasswordAuthenticationFilter.class)
            .csrf()
            .and()
            .antMatcher("/**").authorizeRequests()
                .antMatchers("/login", "/password-reset", "/update-password").permitAll()
                .antMatchers("/stripe/**").permitAll()
                .antMatchers("/stratus/quickview/**").permitAll()
                .antMatchers("/sms-verification").permitAll()
                .antMatchers("/callback**", "/welcome").permitAll()
                .antMatchers("/checkin", "/checkin/**").hasAnyRole("DEALER")
                .antMatchers("/", "/stratus", "/stratus/**", "/api", "/api/**", "/partial", "/partial/**").hasAnyRole("DEALER", "ADMIN")
                .antMatchers("/**").hasAnyRole("ADMIN")
            .and()
                .logout()
            .and()
                .formLogin()
                    .loginPage("/login")
            .and()
                .headers().frameOptions().sameOrigin();

    }

    @Override
    public void configure(WebSecurity web) {
        //ignore static resource patterns
        web.ignoring()
            .antMatchers("/b4/**", "/dist/**", "/images/**", "/css/**", "/fonts/**", "/js/**", "/favicon.ico", "/robots.txt", "/built/**");
        CustomWebSecurityExpressionHandler handler = new CustomWebSecurityExpressionHandler();
        handler.setPermissionEvaluator(new DealerPermissionEvaluator());
        web.expressionHandler(handler);
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) {
        auth.authenticationProvider(carSaverAuthenticationProvider);
    }

    @Bean(name = "webExpressionHandler")
    public SecurityExpressionHandler<FilterInvocation> webExpressionHandler() {
        CustomWebSecurityExpressionHandler handler = new CustomWebSecurityExpressionHandler();
        handler.setPermissionEvaluator(new DealerPermissionEvaluator());
        return handler;
    }

    @Bean(name = "expressionHandler")
    public SecurityExpressionHandler<FilterInvocation> expressionHandler() {
        CustomWebSecurityExpressionHandler handler = new CustomWebSecurityExpressionHandler();
        handler.setPermissionEvaluator(new DealerPermissionEvaluator());
        return handler;
    }

}
