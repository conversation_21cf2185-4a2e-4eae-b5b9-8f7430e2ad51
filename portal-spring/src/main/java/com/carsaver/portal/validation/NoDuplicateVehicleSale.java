package com.carsaver.portal.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = NoDuplicateVehicleSaleValidator.class)
public @interface NoDuplicateVehicleSale {

    String message() default "{NoDuplicateVehicleSale.message}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
