package com.carsaver.portal.domain;

import com.carsaver.magellan.model.CarWithPriceAdjustment;
import com.carsaver.magellan.model.ModelWithPriceAdjustment;
import com.carsaver.magellan.model.StockTypePriceAdjustmentView;
import com.carsaver.magellan.model.StyleWithPriceAdjustment;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Data
@Deprecated
public class NewVehiclePricingForm {

    private Map<Integer, ModelWithPriceAdjustment> pricedModel = new HashMap<>();
    private Map<Integer, StyleWithPriceAdjustment> pricedStyle = new HashMap<>();
    private Map<String, CarWithPriceAdjustment> pricedCar = new HashMap<>();

    @Valid
    private StockTypePriceAdjustmentView stockTypePriceAdjustment = new StockTypePriceAdjustmentView();


    public List<ModelWithPriceAdjustment> getPricedModelAsList(){
        return getPricedModel()
            .entrySet()
            .stream()
            .map(pricingMap -> pricingMap.getValue())
            .filter(modelWithPriceAdjustment -> modelWithPriceAdjustment.getPriceAdjustment() != null)
            .collect(Collectors.toList());
    }

    public List<StyleWithPriceAdjustment> getPricedStyleAsList(){
        return getPricedStyle()
            .entrySet()
            .stream()
            .map(pricingMap -> pricingMap.getValue())
            .filter(styleWithPriceAdjustment -> styleWithPriceAdjustment.getPriceAdjustment() != null)
            .collect(Collectors.toList());
    }

    public List<CarWithPriceAdjustment> getPricedCarAsList(){
        return getPricedCar()
            .entrySet()
            .stream()
            .map(pricingMap -> pricingMap.getValue())
            .filter(carWithPriceAdjustment -> carWithPriceAdjustment.getAdjustment() != null)
            .collect(Collectors.toList());
    }
}
