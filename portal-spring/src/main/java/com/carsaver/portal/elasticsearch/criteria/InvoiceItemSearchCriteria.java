package com.carsaver.portal.elasticsearch.criteria;

import com.carsaver.search.annotation.MatchQuery;
import com.carsaver.search.annotation.MultiMatchQuery;
import com.carsaver.search.annotation.RangeQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.query.AbstractSearchCriteria;
import com.carsaver.search.query.SearchScope;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class InvoiceItemSearchCriteria extends AbstractSearchCriteria {

    @MultiMatchQuery(fields = {"referenceItem.user.firstName", "referenceItem.user.lastName"}, operator = Operator.AND, type = MultiMatchQueryBuilder.Type.CROSS_FIELDS)
    private String name;

    @TermQuery(field = "dealer.id", scope = SearchScope.POST_FILTER)
    private List<String> dealerIds;

    @MatchQuery(field = "dealer.name", operator = Operator.AND)
    private String dealerName;

    @TermQuery(field = "dealer.address.dmaCode", scope = SearchScope.POST_FILTER)
    private List<Integer> dealerDmaCodes;

    @TermQuery(field = "referenceItem.catalogItem", scope = SearchScope.POST_FILTER)
    private List<String> catalogItems;

    @TermQuery(field = "status", scope = SearchScope.POST_FILTER)
    private List<String> statuses;

    @TermQuery(field = "charge", scope = SearchScope.POST_FILTER)
    private List<String> charges;

    @TermQuery(field = "referenceItem.id", scope = SearchScope.POST_FILTER)
    private String referenceItemId;

    @TermQuery(field = "referenceItem.user.id",  scope = SearchScope.POST_FILTER)
    private String referenceItemUserId;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @RangeQuery
    private ZonedDateRange createdDate;

    @TermQuery(field = "vin", scope = SearchScope.POST_FILTER)
    private String vin;

    @TermQuery(field = "dealer.subscriptions.program.name", scope = SearchScope.POST_FILTER)
    private List<String> programs;
}
