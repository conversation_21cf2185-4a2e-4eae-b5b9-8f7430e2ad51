package com.carsaver.portal.elasticsearch.converter;

import com.carsaver.magellan.model.finance.LoanRequestView;
import com.carsaver.search.converter.TermFacetConverter;
import com.carsaver.search.facet.TermFacet;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.stereotype.Component;

@Component
public class FinanceTermFacetConverter implements TermFacetConverter {

    public TermFacet convert(Terms.Bucket bucket) {
        String bucketKey = bucket.getKeyAsString();
        String description = LoanRequestView.Status.valueOf(bucketKey).getDescription();

        return TermFacet.builder()
            .id(bucketKey)
            .name(description)
            .count(bucket.getDocCount())
            .build();
    }
}
