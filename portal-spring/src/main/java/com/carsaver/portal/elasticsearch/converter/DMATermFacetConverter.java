package com.carsaver.portal.elasticsearch.converter;

import com.carsaver.magellan.client.ZipCodeClient;
import com.carsaver.magellan.model.DesignatedMarketAreaView;
import com.carsaver.search.converter.FacetConverter;
import com.carsaver.search.facet.NumberTermFacet;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class DMATermFacetConverter implements FacetConverter<NumberTermFacet, Terms.Bucket> {

    @Autowired
    private ZipCodeClient zipCodeClient;

    public NumberTermFacet convert(Terms.Bucket bucket) {
        Number bucketKey = bucket.getKeyAsNumber();
        Optional<DesignatedMarketAreaView> dmaOpt = zipCodeClient.findDMAById(bucketKey.intValue());
        if(dmaOpt.isPresent()) {
            DesignatedMarketAreaView dma = dmaOpt.get();
            return NumberTermFacet.builder()
                .id(dma.getCode())
                .name(String.format("%s (%s)", dma.getName(), dma.getRank()))
                .count(bucket.getDocCount())
                .build();
        }

        return NumberTermFacet.builder()
            .id(bucketKey)
            .name(bucketKey.toString())
            .count(bucket.getDocCount())
            .build();
    }
}
