package com.carsaver.portal.elasticsearch.facets;

import com.carsaver.portal.elasticsearch.converter.CarvanaStatusTermFacetConverter;
import com.carsaver.portal.elasticsearch.converter.DMATermFacetConverter;
import com.carsaver.portal.elasticsearch.converter.DealerGroupTermFacetConverter;
import com.carsaver.portal.elasticsearch.converter.DealerTermFacetConverter;
import com.carsaver.portal.elasticsearch.converter.TenantFacetConverter;
import com.carsaver.portal.elasticsearch.converter.UserTermFacetConverter;
import com.carsaver.search.annotation.AggConverter;
import com.carsaver.search.annotation.Aggregate;
import com.carsaver.search.annotation.CardinalityAgg;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.support.DocFacets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeadDocFacets implements DocFacets {

    @Aggregate(field = "dealer.group.id")
    @AggConverter(DealerGroupTermFacetConverter.class)
    private List<TermFacet> dealerGroups;

    @Aggregate(field = "dealer.id")
    @AggConverter(DealerTermFacetConverter.class)
    private List<TermFacet> dealers;

    @Aggregate(field = "dealer.makes")
    private List<TermFacet> dealerMakes;

    @Aggregate(field = "dealer.certified")
    private List<TermFacet> dealerCertifiedStatus;

    @Aggregate(field = "status")
    private List<TermFacet> statuses;

    @Aggregate(field = "dealer.salesTxSource")
    private List<TermFacet> salesTxSources;

    @Aggregate(field = "dealerLink.status")
    private List<TermFacet> dealerLinkStatuses;

    @Aggregate(field = "type")
    private List<TermFacet> types;

    @Aggregate(field = "leadType")
    private List<TermFacet> leadTypes;

    @Aggregate(field = "source.utmSource")
    private List<TermFacet> leadUtmSources;

    @Aggregate(field = "source.utmMedium")
    private List<TermFacet> leadUtmMediums;

    @Aggregate(field = "source.utmCampaign")
    private List<TermFacet> leadUtmCampaigns;

    @Aggregate(field = "source.utmTerm")
    private List<TermFacet> leadUtmTerms;

    @Aggregate(field = "source.utmContent")
    private List<TermFacet> leadUtmContents;

    @Aggregate(field = "deliveryStats.deliveryStatus")
    private List<TermFacet> deliveryStatuses;

    @Aggregate(field = "dealer.address.dmaCode")
    @AggConverter(DMATermFacetConverter.class)
    private List<TermFacet> dmas;

    @Aggregate(field = "dealer.address.stateCode")
    private List<TermFacet> states;

    @Aggregate(field = "dealer.subscriptions.successManager.id")
    @AggConverter(UserTermFacetConverter.class)
    private List<TermFacet> successManagers;

    @Aggregate(field = "vehicle.stockType")
    private List<TermFacet> stockTypes;

    @Aggregate(field = "vehicle.certified")
    private List<TermFacet> vehicleCertified;

    @Aggregate(field = "vehicle.make")
    private List<TermFacet> makes;

    @Aggregate(field = "financing.applied")
    private List<TermFacet> financingApplications;

    @Aggregate(field = "financing.approved")
    private List<TermFacet> financingApprovals;

    @Aggregate(field = "user.source.hostname")
    private List<TermFacet> userSourceHosts;

    @Aggregate(field = "user.source.utmSource")
    private List<TermFacet> userUtmSources;

    @Aggregate(field = "user.source.utmMedium")
    private List<TermFacet> userUtmMediums;

    @Aggregate(field = "user.source.utmCampaign")
    private List<TermFacet> userUtmCampaigns;

    @Aggregate(field = "user.source.utmTerm")
    private List<TermFacet> userUtmTerms;

    @Aggregate(field = "user.source.utmContent")
    private List<TermFacet> userUtmContents;

    @Aggregate(field = "userAgent.id")
    @AggConverter(UserTermFacetConverter.class)
    private List<TermFacet> userAgents;

    @Aggregate(field = "user.tags")
    private List<TermFacet> userTags;

    @Aggregate(field = "user.tenant.id")
    @AggConverter(TenantFacetConverter.class)
    private List<TermFacet> tenants;

    @Aggregate(field = "user.userRefs.tenant.id")
    @AggConverter(TenantFacetConverter.class)
    private List<TermFacet> userRefTenants;

    @Aggregate(field = "user.address.dmaCode")
    @AggConverter(DMATermFacetConverter.class)
    private List<TermFacet> userDmas;

    @Aggregate(field = "user.address.city")
    private List<TermFacet> userCities;

    @Aggregate(field = "user.address.stateCode")
    private List<TermFacet> userStates;

    @Aggregate(field = "source.hostname")
    private List<TermFacet> leadSourceHosts;

    @Aggregate(field = "leadAgent.id")
    @AggConverter(UserTermFacetConverter.class)
    private List<TermFacet> leadAgents;

    @CardinalityAgg(field = "user.id")
    private Long uniqueUserLeadCount;

    @Aggregate(field = "carvanaStatuses")
    @AggConverter(CarvanaStatusTermFacetConverter.class)
    private List<TermFacet> carvanaStatuses;

    @Builder.Default
    private List<TermFacet> topDmas = Arrays.asList(
        TermFacet.builder().id("20").name("Top 20 DMAs").build(),
        TermFacet.builder().id("50").name("Top 50 DMAs").build()
    );

}
