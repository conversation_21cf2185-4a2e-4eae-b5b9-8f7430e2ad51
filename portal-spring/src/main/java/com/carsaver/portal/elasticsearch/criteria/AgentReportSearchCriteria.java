package com.carsaver.portal.elasticsearch.criteria;

import com.carsaver.search.annotation.ExistsQuery;
import com.carsaver.search.annotation.RangeQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.query.AbstractSearchCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class AgentReportSearchCriteria extends AbstractSearchCriteria {

    @TermQuery(field = "type")
    private List<String> types;

    @ExistsQuery(field = "source.agentUserId")
    private boolean agentUserId = true;

    @RangeQuery
    private ZonedDateRange createdDate;

}
