package com.carsaver.portal.elasticsearch.criteria;

import com.carsaver.core.SalesTxSource;
import com.carsaver.core.StockType;
import com.carsaver.magellan.model.finance.LoanRequestView;
import com.carsaver.search.annotation.GeoBoundsQuery;
import com.carsaver.search.annotation.MatchQuery;
import com.carsaver.search.annotation.MultiMatchQuery;
import com.carsaver.search.annotation.RangeQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.model.GeoBounds;
import com.carsaver.search.model.IntegerRange;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.query.AbstractSearchCriteria;
import com.carsaver.search.query.BoolQueryOccurrence;
import com.carsaver.search.query.SearchScope;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.Operator;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class FinanceSearchCriteria extends AbstractSearchCriteria {

    @TermQuery(field = "appId")
    private Integer appId;

    @TermQuery(field = "source.hostname")
    private List<String> sourceHosts;

    @RangeQuery
    private ZonedDateRange createdDate;

    @JsonIgnore
    @TermQuery(boolQuery = BoolQueryOccurrence.MUST_NOT)
    private LoanRequestView.Status status = LoanRequestView.Status.REQUEST_REGISTERED;

    @TermQuery(field = "status", scope = SearchScope.POST_FILTER)
    private List<LoanRequestView.Status> statuses;

    /**
     * Vehicle Filters
     */

    @TermQuery(field = "vehicle.stockType", scope = SearchScope.POST_FILTER)
    private StockType stockType;

    @TermQuery(field = "vehicle.make", scope = SearchScope.POST_FILTER)
    private List<String> vehicleMakes;

    @TermQuery(field = "vehicle.model", scope = SearchScope.POST_FILTER)
    private List<String> vehicleModels;

    @TermQuery(field = "vehicle.year", scope = SearchScope.POST_FILTER)
    private List<Integer> vehicleYears;

    /**
     * User Filters
     */

    @TermQuery(field = "user.id", scope = SearchScope.POST_FILTER)
    private String userId;

    @TermQuery(field = "user.tags", scope = SearchScope.POST_FILTER)
    private List<String> userTags;

    @TermQuery(field = "user.tenant.id", scope = SearchScope.POST_FILTER)
    private List<String> userTenants;

    @TermQuery(field = "user.program.id", scope = SearchScope.POST_FILTER)
    private List<String> userPrograms;

    @TermQuery(field = "user.source.hostname", scope = SearchScope.POST_FILTER)
    private List<String> userSourceHostnames;

    @TermQuery(field = "user.source.utmSource", scope = SearchScope.POST_FILTER)
    private List<String> userUtmSources;

    @TermQuery(field = "user.source.utmMedium", scope = SearchScope.POST_FILTER)
    private List<String> userUtmMediums;

    @TermQuery(field = "user.source.utmCampaign", scope = SearchScope.POST_FILTER)
    private List<String> userUtmCampaigns;

    @TermQuery(field = "user.source.utmTerm", scope = SearchScope.POST_FILTER)
    private List<String> userUtmTerms;

    @TermQuery(field = "user.source.utmContent", scope = SearchScope.POST_FILTER)
    private List<String> userUtmContents;

    @TermQuery(field = "user.address.city", scope = SearchScope.POST_FILTER)
    private List<String> userCities;

    @RangeQuery(field = "user.createdDate")
    private ZonedDateRange userCreatedDate;

    @TermQuery(field = "user.userRefs.tenant.id", scope = SearchScope.POST_FILTER)
    private List<String> userRefTenantIds;

    @MultiMatchQuery(fields = {"user.firstName", "user.lastName"}, operator = Operator.AND, type = MultiMatchQueryBuilder.Type.CROSS_FIELDS)
    private String name;

    @TermQuery(field = "user.address.stateCode", scope = SearchScope.POST_FILTER)
    private List<String> userStateCodes;

    @TermQuery(field = "user.address.dma.code", scope = SearchScope.POST_FILTER)
    private List<Integer> userDmaCodes;

    @RangeQuery(field = "user.address.dma.rank")
    private IntegerRange topDmas;


    /**
     * Dealer Filters
     */

    @MatchQuery(field = "dealer.name", operator = Operator.AND)
    private String dealerName;

    @TermQuery(field = "dealer.id", scope = SearchScope.POST_FILTER)
    private List<String> dealerIds;

    @TermQuery(field = "dealer.address.stateCode", scope = SearchScope.POST_FILTER)
    private List<String> dealerStates;

    @TermQuery(field = "dealer.address.dmaCode", scope = SearchScope.POST_FILTER)
    private List<Integer> dealerDmaCodes;

    @TermQuery(field = "dealer.salesTxSource", scope = SearchScope.POST_FILTER)
    private List<SalesTxSource> salesTxSources;

    @TermQuery(field = "dealer.group.id", scope = SearchScope.POST_FILTER)
    private String dealerGroupId;

    @TermQuery(field = "dealer.successManager.id", scope = SearchScope.POST_FILTER)
    private List<String> successManagerIds;

    @TermQuery(field = "dealer.contractDetails.certified", scope = SearchScope.POST_FILTER)
    private Boolean certifiedDealer;

    @GeoBoundsQuery(field = "dealer.address.geo.location")
    private GeoBounds geoBounds;
}
