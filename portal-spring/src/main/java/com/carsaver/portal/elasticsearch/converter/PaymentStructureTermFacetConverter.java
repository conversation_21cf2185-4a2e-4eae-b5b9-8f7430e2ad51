package com.carsaver.portal.elasticsearch.converter;

import com.carsaver.core.PaymentStructure;
import com.carsaver.search.converter.TermFacetConverter;
import com.carsaver.search.facet.TermFacet;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PaymentStructureTermFacetConverter implements TermFacetConverter {

    public TermFacet convert(Terms.Bucket bucket) {
        String bucketKey = bucket.getKeyAsString();
        try {
            PaymentStructure paymentStructure = PaymentStructure.valueOf(bucketKey);
            return TermFacet.builder()
                .id(bucketKey)
                .name(paymentStructure.getDescription())
                .count(bucket.getDocCount())
                .build();
        } catch(Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return TermFacet.builder()
            .id(bucketKey)
            .name(bucketKey)
            .count(bucket.getDocCount())
            .build();
    }
}
