package com.carsaver.portal.elasticsearch.facets;

import com.carsaver.search.annotation.Aggregate;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.support.DocFacets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CallQueueDocFacets implements DocFacets {

    @Aggregate(field = "user.source.hostname")
    private List<TermFacet> userSourceHosts;

}
