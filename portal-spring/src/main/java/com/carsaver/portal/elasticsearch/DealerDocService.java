package com.carsaver.portal.elasticsearch;

import com.carsaver.elasticsearch.CriteriaSearchHandler;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticGetResponse;
import com.carsaver.elasticsearch.criteria.DealerSearchCriteria;
import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.magellan.client.ZipCodeClient;
import com.carsaver.magellan.model.ZipCodeView;
import com.carsaver.search.BoolQueryCollector;
import com.carsaver.search.support.FacetParser;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.AbstractMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.elasticsearch.index.query.QueryBuilders.termsQuery;

@Slf4j
@Service
public class DealerDocService extends CriteriaSearchHandler<DealerDoc> {

    private static final String[] SEARCH_INDEX = {"dealers"};

    @Autowired
    private ZipCodeClient zipCodeClient;

    @Autowired
    public DealerDocService(@Qualifier("gibson") ElasticClient elasticClient, FacetParser facetParser) {
        super(elasticClient, facetParser);
    }

    public DealerDoc findById(String id) {
        GetRequest getRequest = new GetRequest(getSearchIndex()[0]);
        getRequest.id(id);
        ElasticGetResponse<DealerDoc> response = getElasticClient().getDocument(getRequest, DealerDoc.class);

        return response.getItem();
    }

    @Override
    protected void appendRootQuery(Object c, BoolQueryCollector boolQueryCollector) {
        if(c instanceof DealerSearchCriteria) {
            DealerSearchCriteria criteria = (DealerSearchCriteria) c;
            QueryBuilder zipCodeQuery = null;
            if (criteria.isZipCodeFiltered()) {
                // if we have a distance we will do a geoDistanceQuery otherwise just a termsQuery
                if (criteria.isDistanceFiltered()) {
                    Optional<ZipCodeView> zipCodeOpt = zipCodeClient.findByZipCode(criteria.getZipCode());

                    if (zipCodeOpt.isPresent()) {
                        ZipCodeView zipCode = zipCodeOpt.get();
                        zipCodeQuery = QueryBuilders.geoDistanceQuery("address.geo.location")
                            .distance(criteria.getDistance(), DistanceUnit.MILES)
                            .point(zipCode.getLat(), zipCode.getLng());
                    }
                } else {
                    zipCodeQuery = termsQuery("address.zipCode", criteria.getZipCode());
                }
            }

            Stream.of(zipCodeQuery)
                .filter(Objects::nonNull)
                .forEach(boolQueryCollector::filter);
        }
    }

    @Override
    protected String[] getSearchIndex() {
        return SEARCH_INDEX;
    }

    @Override
    protected Map<String, String> getSortKeyMap() {
        return Stream.of(
            new AbstractMap.SimpleEntry<>("name", "name.raw")
        ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }
}
