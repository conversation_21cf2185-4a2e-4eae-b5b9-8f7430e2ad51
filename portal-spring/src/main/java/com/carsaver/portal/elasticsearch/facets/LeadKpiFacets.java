package com.carsaver.portal.elasticsearch.facets;

import com.carsaver.search.annotation.CardinalityAgg;
import com.carsaver.search.support.DocFacets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeadKpiFacets implements DocFacets {
    @CardinalityAgg(field = "id")
    private Long totalLeads;

    @CardinalityAgg(field = "user.id")
    private Long uniqueLeads;
}
