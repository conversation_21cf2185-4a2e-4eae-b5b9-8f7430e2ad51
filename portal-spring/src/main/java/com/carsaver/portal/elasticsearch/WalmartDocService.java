package com.carsaver.portal.elasticsearch;

import com.carsaver.elasticsearch.CriteriaSearchHandler;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.model.WalmartStoreDoc;
import com.carsaver.search.support.FacetParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WalmartDocService extends CriteriaSearchHandler<WalmartStoreDoc> {

    private static final String[] SEARCH_INDEX = {"walmarts"};

    @Autowired
    public WalmartDocService(@Qualifier("gibson") ElasticClient elasticClient, FacetParser facetParser) {
        super(elasticClient, facetParser);
    }

    @Override
    protected String[] getSearchIndex() {
        return SEARCH_INDEX;
    }

}
