package com.carsaver.portal.elasticsearch.metrics;

import com.carsaver.search.annotation.CardinalityAgg;
import com.carsaver.search.support.DocFacets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeadMetrics implements DocFacets {

    @CardinalityAgg(field = "id")
    private Long count;

    @CardinalityAgg(field = "user.id")
    private Long uniqueCount;

}
