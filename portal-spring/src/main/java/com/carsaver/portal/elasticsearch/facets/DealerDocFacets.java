package com.carsaver.portal.elasticsearch.facets;

import com.carsaver.portal.elasticsearch.converter.DMATermFacetConverter;
import com.carsaver.portal.elasticsearch.converter.DealerGroupTermFacetConverter;
import com.carsaver.portal.elasticsearch.converter.FinancierTermFacetConverter;
import com.carsaver.portal.elasticsearch.converter.InventorySourceTermFacetConverter;
import com.carsaver.portal.elasticsearch.converter.PaymentStructureTermFacetConverter;
import com.carsaver.portal.elasticsearch.converter.ProgramTermFacetConverter;
import com.carsaver.portal.elasticsearch.converter.UserTermFacetConverter;
import com.carsaver.search.annotation.AggConverter;
import com.carsaver.search.annotation.Aggregate;
import com.carsaver.search.annotation.NestedAggregate;
import com.carsaver.search.facet.NumberTermFacet;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.support.DocFacets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DealerDocFacets implements DocFacets {

    @Aggregate(field = "status")
    private List<TermFacet> statuses;

    @Aggregate(field = "group.id")
    @AggConverter(DealerGroupTermFacetConverter.class)
    private List<TermFacet> dealerGroups;

    @Aggregate(field = "financeProviders")
    private List<TermFacet> financeProviders;

    @Aggregate(field = "lenderIds")
    @AggConverter(FinancierTermFacetConverter.class)
    private List<TermFacet> lenders;

    @Aggregate(field = "salesTxSource")
    private List<TermFacet> salesTxSources;

    @NestedAggregate(path = "subscriptions", field = "contract.billingInterval")
    private List<TermFacet> contractBillingIntervals;

    @NestedAggregate(path = "subscriptions", field = "contract.payPerSaleAmount")
    private List<NumberTermFacet> contractPayPerSaleAmounts;

    @NestedAggregate(path = "subscriptions", field = "contract.lifetimeWarrantyAmount")
    private List<NumberTermFacet> contractLifetimeWarrantyAmounts;

    @NestedAggregate(path = "subscriptions", field = "contract.expressWarrantyAmount")
    private List<NumberTermFacet> contractExpressWarrantyAmounts;

    @NestedAggregate(path = "subscriptions", field = "contract.vscOptOutAmount")
    private List<NumberTermFacet> contractVscOptOutAmounts;

    @Aggregate(field = "tags")
    private List<TermFacet> tags;

    @Aggregate(field = "inventorySource")
    @AggConverter(InventorySourceTermFacetConverter.class)
    private List<TermFacet> inventorySources;

//    @Aggregate(field = "programIds")
    @NestedAggregate(path = "subscriptions", field = "program.id")
    @AggConverter(ProgramTermFacetConverter.class)
    private List<TermFacet> programs;

    @NestedAggregate(path = "subscriptions", field = "status")
    private List<TermFacet> programStatuses;

    @Aggregate(field = "makes")
    private List<TermFacet> makes;

    @Aggregate(field = "address.stateCode")
    private List<TermFacet> states;

    @Aggregate(field = "address.dmaCode")
    @AggConverter(DMATermFacetConverter.class)
    private List<TermFacet> dmas;

    @NestedAggregate(path = "subscriptions", field = "contract.paymentStructure")
    @AggConverter(PaymentStructureTermFacetConverter.class)
    private List<TermFacet> paymentStructures;

    @Aggregate(field = "certified")
    private List<TermFacet> certifiedDealers;

    @NestedAggregate(path = "subscriptions", field = "successManager.id")
    @AggConverter(UserTermFacetConverter.class)
    private List<TermFacet> successManagers;

    @NestedAggregate(path = "subscriptions", field = "accountManager.id")
    @AggConverter(UserTermFacetConverter.class)
    private List<TermFacet> accountManagers;

    @Aggregate(field = "preferences.offeringOptionalVsc")
    private List<TermFacet> offeringOptionalVscs;

    @Aggregate(field = "preferences.sendSmsForConnections")
    private List<TermFacet> sendSmsForConnections;

    @Aggregate(field = "preferences.sendSmsForAppointments")
    private List<TermFacet> sendSmsForAppointments;

    @Aggregate(field = "preferences.sendSmsAlertsAfterHours")
    private List<TermFacet> sendSmsAlertsAfterHours;

    @Aggregate(field = "preferences.enableLeasePricing")
    private List<TermFacet> enableLeasePricing;

    @Aggregate(field = "preferences.uniqueProxyNumbers")
    private List<TermFacet> uniqueProxyNumbers;

    @Aggregate(field = "preferences.enableOutOfMarketLeads")
    private List<TermFacet> enableOutOfMarketLeads;

    @Aggregate(field = "preferences.enablePriceRequests")
    private List<TermFacet> enablePriceRequests;

    @Aggregate(field = "preferences.deliveryAvailability")
    private List<TermFacet> deliveryAvailabilities;

    @Aggregate(field = "preferences.leadTransport")
    private List<TermFacet> leadTransports;

    @Aggregate(field = "preferences.pricingRules")
    private List<TermFacet> pricingRules;

    @Aggregate(field = "preferences.internetPriceEnabled")
    private List<TermFacet> internetPriceEnabled;

    @Builder.Default
    private List<TermFacet> topDmas = Arrays.asList(
        TermFacet.builder().id("20").name("Top 20 DMAs").build(),
        TermFacet.builder().id("50").name("Top 50 DMAs").build()
    );

}
