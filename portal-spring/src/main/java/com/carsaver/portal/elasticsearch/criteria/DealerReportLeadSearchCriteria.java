package com.carsaver.portal.elasticsearch.criteria;

import com.carsaver.search.annotation.RangeQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.query.AbstractSearchCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DealerReportLeadSearchCriteria extends AbstractSearchCriteria {

    @TermQuery(field = "dealer.id")
    private String dealerId;

    @RangeQuery
    private ZonedDateRange createdDate;

}
