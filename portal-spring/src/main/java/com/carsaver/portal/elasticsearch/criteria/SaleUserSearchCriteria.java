package com.carsaver.portal.elasticsearch.criteria;

import com.carsaver.elasticsearch.criteria.UserSearchCriteria;
import com.carsaver.search.annotation.MultiMatchQuery;
import com.carsaver.search.query.BoolQueryOccurrence;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.Operator;

@EqualsAndHashCode(callSuper = true)
@Data
public class SaleUserSearchCriteria extends UserSearchCriteria {

    @MultiMatchQuery(fields = {"firstName", "lastName"}, operator = Operator.AND, type = MultiMatchQueryBuilder.Type.CROSS_FIELDS, boolQuery = BoolQueryOccurrence.SHOULD)
    private String name;

    @MultiMatchQuery(fields = {"phoneNumber", "email"}, boolQuery = BoolQueryOccurrence.SHOULD)
    private String emailOrPhone;

    public void setNameEmailOrPhone(String query) {
        this.name = query;
        this.emailOrPhone = query;
    }
}
