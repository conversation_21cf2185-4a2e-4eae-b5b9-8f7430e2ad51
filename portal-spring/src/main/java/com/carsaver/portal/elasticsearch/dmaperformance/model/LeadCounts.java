package com.carsaver.portal.elasticsearch.dmaperformance.model;

import lombok.Data;

import java.util.Map;

@Data
public class LeadCounts {
    private Map<Integer, DMALeadCounts> dmaCounts;

    @Data
    public static class DMALeadCounts {
        private int total;
        private int maxDistance;

        private Map<String, Integer> brandCounts;

        /**
         * 90th percentile distance. e.g. 90% of leads fall under this distance.
         */
        private Map<String, Integer> brandMaxDistance;
    }
}
