package com.carsaver.portal.elasticsearch.dmaperformance;

import com.carsaver.core.StockType;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.search.model.LocalDateRange;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

import static org.elasticsearch.index.query.QueryBuilders.*;

@Service
public class DMAPerformanceSearchAnalyticsDocService {
    private static final String MODEL_SEARCH_INDEX_PREFIX = "model-search";
    private static final String INVENTORY_SEARCH_INDEX_PREFIX = "inventory-search";

    @Autowired
    @Qualifier("nova")
    private ElasticClient elasticClient;

    public int getSearchCountsForZipCodes(List<String> zipCodes, LocalDateRange dateRange) {
        var searchBldr = new SearchSourceBuilder();

        /******************************************** QUERIES *******************************************/
        BoolQueryBuilder query = boolQuery();

        query.must(termQuery("filterCriteria.stockTypes", StockType.NEW));
        query.must(termsQuery("filterCriteria.zipCode", zipCodes));
        query.must(
            rangeQuery("timestamp")
                .gte(dateRange.getStart().atStartOfDay(ZoneId.systemDefault()).toEpochSecond() * 1000)
                .lte(dateRange.getEnd().atStartOfDay(ZoneId.systemDefault()).toEpochSecond() * 1000)
                .format("epoch_millis")
        );

        searchBldr.query(query);
        searchBldr.size(0);

        LocalDate currentDate = dateRange.getStart();
        List<String> indexes = new ArrayList<>();
        while (currentDate.isBefore(dateRange.getEnd())) {
            var dateString = String.format("%d-%02d*", currentDate.getYear(), currentDate.getMonthValue());
            indexes.add(String.format("%s-%s", MODEL_SEARCH_INDEX_PREFIX, dateString));
            indexes.add(String.format("%s-%s", INVENTORY_SEARCH_INDEX_PREFIX, dateString));
            currentDate = currentDate.plusMonths(1);
        }

        var request = new CountRequest(String.join(",", indexes));
        request.query(query);

        var response = this.elasticClient.count(request);

        return (int) response.getCount();
    }
}
