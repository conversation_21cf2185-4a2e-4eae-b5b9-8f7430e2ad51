package com.carsaver.portal.elasticsearch.converter;

import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.JobTitleView;
import com.carsaver.search.converter.TermFacetConverter;
import com.carsaver.search.facet.TermFacet;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class JobTitleTermFacetConverter implements TermFacetConverter {

    @Autowired
    private  UserClient userClient;

    public TermFacet convert(Terms.Bucket bucket) {
        String bucketKey = bucket.getKeyAsString();
        JobTitleView jobTitle = userClient.findJobTitleById(Integer.parseInt(bucketKey));

        if(jobTitle != null) {
            return TermFacet.builder()
                .id(jobTitle.getId().toString())
                .name(jobTitle.getTitle())
                .count(bucket.getDocCount())
                .build();
        }

        return TermFacet.builder()
            .id(bucketKey)
            .name(bucketKey)
            .count(bucket.getDocCount())
            .build();
    }
}
