package com.carsaver.portal.elasticsearch.converter;

import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.DealerGroupClient;
import com.carsaver.magellan.model.AddressView;
import com.carsaver.magellan.model.DealerGroupView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.search.converter.TermFacetConverter;
import com.carsaver.search.facet.TermFacet;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class DealerTermFacetConverter implements TermFacetConverter {

    @Autowired
    private DealerClient dealerClient;

    public TermFacet convert(Terms.Bucket bucket) {
        String bucketKey = bucket.getKeyAsString();
        Optional<DealerView> dealerOpt = Optional.ofNullable(dealerClient.findById(bucketKey));
        if (dealerOpt.isPresent()) {
            DealerView dealer = dealerOpt.get();
            return TermFacet.builder()
                .id(dealer.getId())
                .name(dealer.getName() + " " + "(" + buildDealerNameStateCode(dealerOpt) + ")")
                .count(bucket.getDocCount())
                .build();
        }

        return TermFacet.builder()
            .id(bucketKey)
            .name(bucketKey)
            .count(bucket.getDocCount())
            .build();
    }

    String buildDealerNameStateCode(Optional<DealerView> dealerOpt) {
        String result = dealerOpt.map(DealerView::getAddress).map(AddressView::getStateCode).orElse("");
        return result;
    }
}
