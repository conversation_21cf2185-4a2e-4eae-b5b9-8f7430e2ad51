package com.carsaver.portal.elasticsearch.dmaperformance.model;

import com.carsaver.magellan.model.DesignatedMarketAreaView;
import lombok.Data;

import java.util.List;

@Data
public class DMAPerformanceRow {
    private DesignatedMarketAreaView dma;

    private int totalDealers;
    private int uniqueLeads;
    private int leadsMaxDistance;
    private double level1CertifiedCoverage;
    private double allBrandsCertifiedCoverage;
    private int searchesCount;

    private List<BrandStats> brands;

    public double getUniqueLeadsPerDealer() {
        if (totalDealers == 0) {
            return 0;
        }

        double uniqueLeadsPerDealer = ((uniqueLeads + 0.0) / (totalDealers + 0.0));
        return Math.round(uniqueLeadsPerDealer * 100) / 100.0;
    }
}
