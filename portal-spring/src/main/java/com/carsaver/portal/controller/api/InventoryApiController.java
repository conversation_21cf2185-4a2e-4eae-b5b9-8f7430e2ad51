package com.carsaver.portal.controller.api;

import com.carsaver.portal.model.AuditLog;
import com.carsaver.portal.service.AuditLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;

@RestController
@RequestMapping("/api/inventory")
public class InventoryApiController {

    @Autowired
    private AuditLogService auditLogService;

    @GetMapping("/{vehicleId}/changes")
    public ResponseEntity<Collection<AuditLog>> fetchAuditChanges(@PathVariable String vehicleId) {
        Collection<AuditLog> changes = auditLogService.getInventoryAuditLog(vehicleId);
        if(changes == null) {
            return ResponseEntity.badRequest().build();
        }
        return ResponseEntity.ok(changes);
    }
}
