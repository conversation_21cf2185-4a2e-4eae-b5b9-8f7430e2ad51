package com.carsaver.portal.controller.api;

import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.portal.client.tenant.TenantClient;
import com.carsaver.portal.client.tenant.TenantView;
import com.carsaver.portal.model.tenant.EditTenantForm;
import com.carsaver.portal.model.tenant.TenantForm;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.hateoas.PagedModel;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/tenants")
public class TenantsApiController {

    @Autowired
    private TenantClient tenantClient;

    @GetMapping
    public PageWrapper fetchAllTenants(Pageable pageable) {
        if (pageable.getPageNumber() == 0) {
            pageable = PageRequestUtils.maxSizeRequest();
        }
        PagedModel<TenantView> tenants = tenantClient.findAll(PageRequestUtils.asOneBasedPage(pageable));

        return PageWrapper.builder()
            .tenantView(new ArrayList<>(tenants.getContent()))
            .page(tenants.getMetadata())
            .build();
    }

    @PostMapping
    public ResponseEntity<TenantView> addTenant(@RequestBody TenantForm tenantForm) {
        if (tenantForm == null) {
            return (ResponseEntity<TenantView>) ResponseEntity.badRequest();
        }
        var tenant = new TenantView();
        BeanUtils.copyProperties(tenantForm, tenant);
        tenant = tenantClient.save(tenant);
        return ResponseEntity.ok(tenant);
    }

    @GetMapping("/{id}")
    public TenantView fetchTenant(@PathVariable("id") String tenantId) {
        return tenantClient.findById(tenantId);
    }

    @PutMapping("/{id}")
    public ResponseEntity<String> updateTenant(@PathVariable("id") String tenantId, @Valid @RequestBody EditTenantForm tenantForm) {
        TenantView tenantView = tenantClient.findById(tenantId);

        tenantView.setMarketingConfig(tenantForm.getMarketingConfig());
        if (StringUtils.isBlank(tenantForm.getMarketingConfig().getApiKey())) {
            tenantView.setMarketingConfig(null);
        }

        tenantView.setIdentityProviderConfig(tenantForm.getIdentityProviderConfig());
        if (StringUtils.isBlank(tenantForm.getIdentityProviderConfig().getProvider())) {
            tenantView.setIdentityProviderConfig(null);
        }

        tenantClient.save(tenantView);
        return ResponseEntity.ok().body("Tenant has been updated successfully.");
    }

    @GetMapping("/search")
    public PageWrapper fetchTenantsByName(@RequestParam("name") String name, Pageable pageable) {
        PagedModel<TenantView> tenants = tenantClient.findByTenantName(name, PageRequestUtils.asOneBasedPage(pageable));

        return PageWrapper.builder()
            .tenantView(new ArrayList<>(tenants.getContent()))
            .page(tenants.getMetadata())
            .build();
    }

    @Data
    @Builder
    static class PageWrapper {
        private PagedModel.PageMetadata page;
        private List<TenantView> tenantView;
    }
}
