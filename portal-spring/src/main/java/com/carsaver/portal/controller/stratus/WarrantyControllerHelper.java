package com.carsaver.portal.controller.stratus;

import com.carsaver.core.DealerStatus;
import com.carsaver.core.StockType;
import com.carsaver.magellan.client.InventoryClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.WarrantyContractView;
import com.carsaver.magellan.model.chrome.StyleView;
import com.carsaver.magellan.model.warranty.CreatedSource;
import com.carsaver.portal.domain.PortalAnnouncement;
import com.carsaver.portal.model.warranty.ApplicantRegistrationForm;
import com.carsaver.portal.security.SecurityUtils;
import com.carsaver.portal.service.MailService;
import com.carsaver.portal.service.WarrantyService;
import com.carsaver.stereotype.State;
import com.carsaver.warranty.model.Agreement;
import com.carsaver.warranty.model.ApplicantRegistration;
import com.carsaver.warranty.model.DealerEnrollment;
import com.carsaver.warranty.model.DealerEnrollmentException;
import com.carsaver.warranty.model.FindSurchargesException;
import com.carsaver.warranty.model.FindSurchargesRequest;
import com.carsaver.warranty.model.Form;
import com.carsaver.warranty.model.GetFormsException;
import com.carsaver.warranty.model.GetFormsRequest;
import com.carsaver.warranty.model.GetVehicleResponse;
import com.carsaver.warranty.model.LoanInstrument;
import com.carsaver.warranty.model.Premium;
import com.carsaver.warranty.model.PremiumsRequest;
import com.carsaver.warranty.model.PremiumsRequestException;
import com.carsaver.warranty.model.Registration;
import com.carsaver.warranty.model.Surcharge;
import com.carsaver.warranty.model.Vehicle;
import com.carsaver.warranty.model.VehicleConditionCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.validation.Valid;
import java.security.Principal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WarrantyControllerHelper {

    private static final int MAX_PURCHASE_POST_DATED_DAYS = 5;

    private static final int USER_TAGS_WALMART_ASSOCIATE = 6;

    @Autowired
    private WarrantyService warrantyService;

    @Autowired
    private InventoryClient inventoryClient;

    @Autowired
    private MailService mailService;

    public void setEffectiveDealerEnrollmentDate(DealerView dealer, ApplicantRegistrationForm applicantRegistrationForm) {
        DealerEnrollment existingDealerEnrollment;
        try {
            existingDealerEnrollment = warrantyService.getDealerEnrollmentBy(dealer);

            if(existingDealerEnrollment != null && existingDealerEnrollment.getEffectiveDate() != null ) {
                LocalDate effectiveLocalDate = ZonedDateTime.ofInstant(existingDealerEnrollment.getEffectiveDate().toInstant(), ZoneId.of("UTC")).toLocalDate();
                applicantRegistrationForm.setDealerEffectiveEnrollmentLocalDate(effectiveLocalDate);
            }
        } catch(DealerEnrollmentException e) {
            log.error("problem finding existing dealer enrollment", e);
        }
    }

    /**
     * check validation errors, purchaseDate not past max post-date, purchaseDate not before dealer enrollment effective date, if PROSPECT dealer, carSaverTransaction MUST be TRUE
     * @return viewOrRedirect url
     */
    public Optional<String> validatePremiumsRequest(Model model, DealerView dealer, PremiumsRequest premiumsRequest, BindingResult bindingResult, ApplicantRegistrationForm applicantRegistrationForm, RedirectAttributes redirectAttrs) {

        LocalDate purchaseDate = premiumsRequest.getPurchaseDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate dealerEffectiveEnrollmenLocalDate = applicantRegistrationForm.getDealerEffectiveEnrollmentLocalDate();

        String redirectToPremiumsSearch = null;

        //check validation errors
        if(bindingResult.hasErrors()) {
            populatePremiumsSearchModel(model, dealer, applicantRegistrationForm);
            redirectToPremiumsSearch = "warranty/premiumsSearch";
        }

        //check purchaseDate not past max post-date
        if(purchaseDate.isAfter(LocalDate.now().plusDays(3))) {
            bindingResult.rejectValue("purchaseDate", "purchaseDate.invalid",
                "Purchase Date CANNOT be post-dated more than " + MAX_PURCHASE_POST_DATED_DAYS + " days");

            populatePremiumsSearchModel(model, dealer, applicantRegistrationForm);

            redirectToPremiumsSearch =  "warranty/premiumsSearch";
        }

        //check purchaseDate not before dealer enrollment effective date
        log.info("checking that purchaeDate = {} is NOT before effectiveDate = {}", purchaseDate, dealerEffectiveEnrollmenLocalDate);
        if(dealerEffectiveEnrollmenLocalDate != null && purchaseDate.isBefore(dealerEffectiveEnrollmenLocalDate)) {
            redirectAttrs.addFlashAttribute("globalMessage",
                PortalAnnouncement.error("Purchase date: " + purchaseDate + " can NOT be before Dealer enrollment date of: " + dealerEffectiveEnrollmenLocalDate));

            redirectToPremiumsSearch =  "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/search-premiums";
        }

        //check if PROSPECT dealer, carSaverTransaction MUST be TRUE. i.e. only CarSaver employees(isAdmin=true) can set carSaverTransaction to TRUE
        if(DealerStatus.PROSPECT == dealer.getDealerStatus() && !Boolean.TRUE.equals(premiumsRequest.getCarSaverTransaction())) {
            redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.error("Dealer has status = 'PROSPECT', CarSaver Transaction must be 'Yes', Only a CarSaver employee can complete this registration"));

            redirectToPremiumsSearch =  "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/search-premiums";
        }

        // check if Odometer has discrepancy between what dealer enters for miles and what the database has
        Collection<VehicleView> vehicleInDb = inventoryClient.getVehiclesByVin(premiumsRequest.getVin()).getContent();
        if(!vehicleInDb.isEmpty() && vehicleInDb.iterator().next().getStockType() == StockType.USED) {
            VehicleView vehicle = vehicleInDb.iterator().next();
            Integer milesInDb = vehicle.getMiles();
            Integer milesFromDealer = premiumsRequest.getOdometer();

            if(milesInDb != null) {
                int milesWithCushion = milesInDb - 50;

                if (milesFromDealer < milesWithCushion) {
                    bindingResult.rejectValue("odometer", "odometer.invalid", "Odometer miles are incorrect. The correct mileage should be no less than " + milesWithCushion);

                    String vehicleName = Optional.of(vehicle.getStyle()).map(StyleView::getFullName).orElse(null);
                    mailService.emailWarrantyOdometerNotification(dealer, premiumsRequest, applicantRegistrationForm, vehicleName, milesFromDealer, milesInDb);
                    populatePremiumsSearchModel(model, dealer, applicantRegistrationForm);

                    redirectToPremiumsSearch = "warranty/premiumsSearch";
                }
            }
        }

        return Optional.ofNullable(redirectToPremiumsSearch);
    }

    public PremiumsRequest getPremiumsRequest(DealerView dealer, ApplicantRegistrationForm applicantRegistrationForm, String vin) {
        PremiumsRequest premiumsRequest;
        if(applicantRegistrationForm.getPremiumsRequest() != null) {
            log.info("premiums request already exists");
            premiumsRequest = applicantRegistrationForm.getPremiumsRequest();
        } else {
            premiumsRequest = new PremiumsRequest(dealer.getDealerId());
            premiumsRequest.setPurchaseDate(new Date());
            premiumsRequest.setCarSaverTransaction(SecurityUtils.isAdminUser());
        }

        premiumsRequest.setVin(vin);

        UserView user = applicantRegistrationForm.getCustomer();
        if(user != null && user.tagExists(USER_TAGS_WALMART_ASSOCIATE)) {
            premiumsRequest.setWalmartEmployee(true);
        }

        return premiumsRequest;
    }

    //set common model attributes used by the 'premiumsSearch' view
    public void populatePremiumsSearchModel(Model model, DealerView dealer, ApplicantRegistrationForm applicantRegistrationForm) {
        model.addAttribute("dealerEnrollmentEffectiveDate", DateTimeFormatter.ofPattern("yyyy-MM-dd").format(applicantRegistrationForm.getDealerEffectiveEnrollmentLocalDate()));
        model.addAttribute("maxPostDatedDaysDate", LocalDate.now().plusDays(MAX_PURCHASE_POST_DATED_DAYS));
        model.addAttribute("maxPostDatedDays", MAX_PURCHASE_POST_DATED_DAYS);

        /*
            NOTE: only adminUsers can register warranties for 'PROSPECT' dealers.
            this is enforced in the searchPremiums method
         */
        model.addAttribute("isAdminUser", SecurityUtils.isAdminUser());
        model.addAttribute("isProspectDealer", DealerStatus.PROSPECT == dealer.getDealerStatus());
    }

    public void populatePremiumsSearchResults(Model model, DealerView dealer, PremiumsRequest premiumsRequest, ApplicantRegistrationForm applicantRegistrationForm, GetVehicleResponse getVehicleResponse) throws PremiumsRequestException {
        applicantRegistrationForm.setVehicleResponse(getVehicleResponse);
        List<Premium> premiumsList = warrantyService.getPremiums(premiumsRequest);

        int diff  = ZonedDateTime.now().getYear() - getVehicleResponse.getYear();
        String vehicleAge = diff > 0 ? diff + " year(s)" : "New";

        getVehicleResponse.setConditionCode(vehicleAge);
        model.addAttribute("vehicle", getVehicleResponse);

        model.addAttribute("premiums", premiumsList.stream()
            .filter(Premium::isGiveAway).collect(Collectors.toList()));

        boolean isCarSaverEmployeeTransaction = SecurityUtils.isAdminUser() && Boolean.TRUE.equals(premiumsRequest.getCarSaverTransaction());
        if(isCarSaverEmployeeTransaction || dealer.getEffectivePreferences().getOfferingOptionalVsc()) {
            model.addAttribute("premiumUpgradesQuickQuoteList", premiumsList.stream()
                .filter(premium -> !premium.isGiveAway()).collect(Collectors.toList()));
        }
    }

    public void populateRegisterApplicantModel(Model model, ApplicantRegistrationForm applicantRegistrationForm, String providerCode) throws GetFormsException, FindSurchargesException {
        ApplicantRegistration applicantRegistration = applicantRegistrationForm.getApplicantRegistration();

        Premium selectedPremium = applicantRegistrationForm.getSelectedPremium();
        Vehicle registrantVehicle = applicantRegistration.getVehicle();
        Agreement agreement = Optional.ofNullable(applicantRegistration.getAgreement())
            .orElseThrow(() -> new IllegalStateException("applicantRegistration.getAgreement() was null, applicantRegistration=" + applicantRegistration.toString()));
        Date purchaseDate = agreement.getPurchaseDate();
        if(selectedPremium != null) {
            model.addAttribute("selectedPremium", selectedPremium);

            List<Form> forms = warrantyService.getForms(new GetFormsRequest(selectedPremium.getScheduleId()));
            model.addAttribute("forms", forms);

            List<Surcharge> surcharges = warrantyService.getSurcharges(new FindSurchargesRequest(
                selectedPremium, registrantVehicle, purchaseDate));
            model.addAttribute("surchargesList", surcharges);

            applicantRegistration.applySelectedPremium(selectedPremium); //re-apply premium data on form reload
        }

        model.addAttribute("applicantRegistration", applicantRegistration);
        model.addAttribute("states", State.getStates());
        model.addAttribute("vehicleConditionCodes", Arrays.asList(VehicleConditionCode.values()));
        model.addAttribute("loanInstruments", Arrays.asList(LoanInstrument.values()));
        model.addAttribute("providerCode", providerCode);
    }

    public void populateConfirmationsModel(ModelMap model, DealerView dealer, List<WarrantyContractView> allWarrantyContractsForUserAndVin, ApplicantRegistrationForm applicantRegistrationForm) {
        WarrantyContractView warrantyContract = allWarrantyContractsForUserAndVin.get(0);
        model.addAttribute("vehicle", warrantyContract.getVehicle());

        PremiumsRequest premiumsRequest = buildUpgradePremiumsRequestForConfirmationsPage(model, dealer, warrantyContract);

        boolean hasSingleContractAndIsLifeTime = allWarrantyContractsForUserAndVin.size() == 1 && warrantyContract.isLifetimeWarranty();

        /*
            Only show upgrade premiums if there is one total registration AND it is a "Lifetime Warranty"
            AND
            when the dealer has opted IN for VSC OR if this is a carSaverTransaction=true
         */
        boolean showUpgradePremiums = premiumsRequest != null
            && hasSingleContractAndIsLifeTime
            && (dealer.getEffectivePreferences().getOfferingOptionalVsc() || Boolean.TRUE.equals(premiumsRequest.getCarSaverTransaction()));

        model.addAttribute("showUpgradePremiums", showUpgradePremiums);

        if(showUpgradePremiums) {
            populateUpgradePremiumsForConfirmationsPage(model, premiumsRequest, warrantyContract, applicantRegistrationForm);
        }
    }

    private PremiumsRequest buildUpgradePremiumsRequestForConfirmationsPage(ModelMap model, DealerView dealer, WarrantyContractView warrantyContract) {
        Registration registration = warrantyContract.getRemoteRegistration();
        Vehicle vehicle = warrantyContract.getVehicle();
        ApplicantRegistration applicantRegistration = warrantyContract.getApplicantRegistration();
        /*
        PremiumsSearch page may have auto-redirected to confirmations page if the system found a Lifetime warranty for the customer/vin combo
        entered. If that's the case, use the premiumsRequest populated in the model via a Flash Attribute
         */
        PremiumsRequest premiumsRequest = (PremiumsRequest) model.get("newPremiumsRequest");

        if(premiumsRequest != null) {
            model.addAttribute("skippedToPremiumsUpgrade", true);
            applicantRegistration.setCarSaverTransaction(premiumsRequest.getCarSaverTransaction());
        } else {
            premiumsRequest = new PremiumsRequest(dealer.getDealerId());
            premiumsRequest.setOdometer(vehicle.getOdometer());
            premiumsRequest.setVin(vehicle.getVin());

            Date purchaseDate = null;
            if(registration != null) {//favor registration as the source of truth for this information
                String purchaseDateStr = registration.getAgreementPurchaseDate();
                try {//NOTE - if this fails, will be set in onCreate() in meridian
                    DateTimeFormatter DTF = DateTimeFormatter.ofPattern(WarrantyContractView.REGISTRATION_DATE_FMT);
                    LocalDateTime ldt = LocalDateTime.parse(purchaseDateStr, DTF);
                    purchaseDate = Date.from(ldt.atZone(ZoneId.systemDefault()).toInstant());
                } catch(Exception e) {
                    log.error("error setting registration date=" + purchaseDateStr + " using format= " + WarrantyContractView.REGISTRATION_DATE_FMT , e);
                }
            //fall back to applicantRegistration data
            } else if(applicantRegistration != null && applicantRegistration.getAgreement() != null) {
                purchaseDate = warrantyContract.getApplicantRegistration().getAgreement().getPurchaseDate();
            } else {
                log.warn("could not find vehicle purchase date, cannot request premiums");
                return null;
            }
            premiumsRequest.setPurchaseDate(purchaseDate);

            /*  5/22/2018
                no applicantRegistration means it was likely created via a NWAN_POSTBACK
                Per Dan from NWAN:
                If a Lifetime Warranty comes through from a menu into your system and then, at a later time, you upgrade the customer with a VSC on the CarSaver side (not through the menu)
                then, yes, you would send us the IsCarSaverTransaction flag with a TRUE value so that the financials on the VSC are correct.
             */
            boolean isCarSaverTransaction;
            if(warrantyContract.getCreatedSource() == CreatedSource.NWAN_POSTBACK || applicantRegistration == null) {
                isCarSaverTransaction = true;
            } else { //otherwise use the value previously associated with the warranty(also sanity check that this user is allowed to send a carSaverTransaction=true)
                isCarSaverTransaction = warrantyContract.isCarSaverTransaction() && SecurityUtils.isAdminUser();
            }
            premiumsRequest.setCarSaverTransaction(isCarSaverTransaction);

            UserView user = warrantyContract.getUser();
            if(user != null && user.tagExists(USER_TAGS_WALMART_ASSOCIATE)) {
                premiumsRequest.setWalmartEmployee(true);
            }
        }

        return premiumsRequest;
    }

    private void populateUpgradePremiumsForConfirmationsPage(ModelMap model, PremiumsRequest premiumsRequest, WarrantyContractView warrantyContract, ApplicantRegistrationForm applicantRegistrationForm) {
        List<Premium> upgradePremiumsList = null;
        ApplicantRegistration applicantRegistration = warrantyContract.getApplicantRegistration();
        try {
            upgradePremiumsList = warrantyService.getPremiums(premiumsRequest).stream()
                .filter(premium -> !premium.isGiveAway()).collect(Collectors.toList());

            model.addAttribute("premiums", upgradePremiumsList);
            model.addAttribute("lifetimeWarrantyContract", warrantyContract);
            applicantRegistrationForm.setApplicantRegistration(applicantRegistration);
        } catch(PremiumsRequestException e) {
            log.error("error getting upgrade premiums", e);

            model.addAttribute("globalMessage", PortalAnnouncement.error("error getting upgrade premiums"));
        }
            /*
                if premiums are empty add some troubleshooting information to the screen
             */
        if(CollectionUtils.isEmpty(upgradePremiumsList)) {
            String premiumsRequestParams = premiumsRequest.toString();
            try {
                premiumsRequestParams = String.format("?carSaverDealerId=%s&vin=%s&odometer=%s&purchaseDate=%s&IsCarSaverTransaction=%s",
                    premiumsRequest.getCarSaverDealerId(),premiumsRequest.getVin(), premiumsRequest.getOdometer(),
                    new SimpleDateFormat("yyyy-MM-dd").format(premiumsRequest.getPurchaseDate()),
                    premiumsRequest.getCarSaverTransaction()
                );

            } catch(Exception e) {
                log.error("error parsing premiumsRequest to params", e);
            }

            model.addAttribute("premiumsRequestParams", premiumsRequestParams);
        }

    }


    public String getRemittanceUrl(DealerView dealer, List<DealerView> dealers, Principal user) {
        try {
            String url = warrantyService.buildRemittancePortalUrlFrom(user.getName(), dealers);
            return "redirect:" + url;
        } catch(Exception e) {
            log.error("error encountered getting eRemittance launch url oauth token, cannot obtain ", e);

            //send to ePortal login screen
            return "redirect:" + warrantyService.geteRemittancePortalUrl();
        }
    }

    public String getSupplyOrdering(DealerView dealer,List<DealerView> dealers, Principal user) {
        try {
            String url = warrantyService.buildSupplyOrderingUrlFrom(user.getName(), dealers);
            return "redirect:" + url;
        } catch(Exception e) {
            log.error("error encountered getting supply ordering launch url oauth token, cannot obtain ", e);

            //send to ePortal login screen
            return "redirect:" + warrantyService.getSupplyOrderPortalUrl();
        }
    }


    public ResponseEntity<byte[]> getWarrantyContractPdf(Model model, DealerView dealer, String warrantyContractId){
        WarrantyContractView warrantyContract = warrantyService.getWarrantyContract(warrantyContractId);
        ResponseEntity<byte[]> response;

        if(warrantyContract != null){
            try {
                byte[] pdf = warrantyService.getRegistrationApplicationPdf(warrantyContract);

                response = ResponseEntity.ok()
                    .headers(formPdfHttpHeaders(warrantyContract))
                    .body(decodePdfResponse(pdf));
            } catch (Exception e) {
                log.error("Error getting PDF for warranty contract {}. Exception was {}", warrantyContract.getId(), e.getMessage());
                response = ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
            }
        } else {
            log.error("Error getting PDF for warranty contract. Warranty Contract not found for id {}", warrantyContractId);
            response = ResponseEntity.notFound().build();
        }

        return response;
    }

    public ResponseEntity<byte[]> getWarrantyContractPdfPreview(Model model, DealerView dealer, @Valid ApplicantRegistration applicantRegistration, BindingResult bindingResult){

        ResponseEntity<byte[]> response;

        try {
            byte[] pdf = warrantyService.getRegistrationApplicationPdfPreview(applicantRegistration);

            response = ResponseEntity.ok()
                .headers(formPdfHttpHeaders())
                .body(decodePdfResponse(pdf));

        } catch (Exception e) {

            log.error("Error generating PDF preview. Exception was {}", e.getMessage());
            response = ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(null);
        }

        return response;
    }

    public String startLifetimeWarrantyRegistrationOver(DealerView dealer, RedirectAttributes redirectAttrs) {
        redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.error("Sorry, session has expired or is not valid, please restart the registration process"));
        return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty";
    }

    public String startUpgradeRegistrationOver(WarrantyContractView lifetimeWarrantyContract, RedirectAttributes redirectAttrs) {
        redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.error("Sorry, session has expired or is not valid, please restart the upgrade process"));
        return redirectToConfirmation(lifetimeWarrantyContract);
    }

    //NOTE: the confirmations page will present options to Upgrade existing Lifetime Warranty if applicable / allowed
    public String redirectToConfirmation(WarrantyContractView lifetimeWarrantyContract) {
        return "redirect:/stratus/dealer/" + lifetimeWarrantyContract.getDealerId() + "/warranty/confirmations?" +
            "userId=" + lifetimeWarrantyContract.getUserId() + "&vin=" + lifetimeWarrantyContract.getVin();
    }

    public void handleRegisterApplicantErrors(BindingResult bindingResult, RedirectAttributes redirectAttrs) {
        log.error("register applicant form has validation errors {}", bindingResult);

        redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.error("validation errors encountered, please fix and re-submit"));

        redirectAttrs.addFlashAttribute("org.springframework.validation.BindingResult.applicantRegistration", bindingResult);
    }


    //helper methods
    private byte[] decodePdfResponse(byte[] pdfResponse){
        String pdfString = new String(pdfResponse);
        // trim leading and trailing quotes
        String pdfWithoutQuotes = pdfString.replaceAll("(?:^\")|(?:\"$)", "");
        return Base64Utils.decode(pdfWithoutQuotes.getBytes());
    }

    private HttpHeaders formPdfHttpHeaders(){
        return formPdfHttpHeaders(Optional.empty());
    }

    private HttpHeaders formPdfHttpHeaders(WarrantyContractView warrantyContract){

        String buyerName = Optional.ofNullable(warrantyContract.getApplicantRegistration().getBuyer())
            .map(buyer -> buyer.getFirstName() + "-" + buyer.getLastName())
            .orElse("unknown-buyer");

        String fileName = new StringBuilder(buyerName)
            .append("-warranty-contract-")
            .append(warrantyContract.getRegistrationId())
            .append(".pdf")
            .toString();

        return formPdfHttpHeaders(Optional.of(fileName));
    }

    /***
     * Create the correct HTTP headers for returning the PDF.
     * If a fileName is present, the headers will instruct the
     * browser to download the file automatically.
     *
     * @param fileName
     * @return
     */
    private HttpHeaders formPdfHttpHeaders(Optional<String> fileName){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/pdf"));
        if(fileName.isPresent()) {
            headers.setContentDispositionFormData(fileName.get(), fileName.get());
        }
        headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

        return headers;
    }



}
