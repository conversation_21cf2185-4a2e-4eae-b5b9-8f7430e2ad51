package com.carsaver.portal.controller.export;

import com.carsaver.elasticsearch.criteria.VehicleSearchCriteria;
import com.carsaver.elasticsearch.model.JobTitleDoc;
import com.carsaver.elasticsearch.model.vehicle.VehicleDoc;
import com.carsaver.elasticsearch.service.VehicleDocService;
import com.carsaver.magellan.export.CsvWriter;
import com.carsaver.magellan.export.DataStreamProvider;
import com.carsaver.magellan.export.service.CsvExportService;
import com.carsaver.magellan.export.service.ExportService;
import com.carsaver.portal.elasticsearch.criteria.LocalVehicleSearchCriteria;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@Controller
public class InventoryExportController extends ExportController {

    private final VehicleDocService vehicleDocService;

    public InventoryExportController(VehicleDocService vehicleDocService) {
        this.vehicleDocService = vehicleDocService;
    }

    @PreAuthorize("hasAuthority('read:user')")
    @GetMapping(value = "/inventory/search/report.csv", produces = "text/csv")
    public void exportInventoryListToCsv(
        @ModelAttribute("searchForm") LocalVehicleSearchCriteria searchForm,
        @RequestParam List<String> columns,
        @RequestParam List<String> negativeSearchColumns,
        HttpServletResponse response
    ) throws Exception {
        super.applyNegativeSearchColumns(searchForm, negativeSearchColumns);

        CsvWriter csvWriter = super.prepareCsvWriter(response, columns)
            .addSerializer(JobTitleDoc.class, (jobTitle) -> (jobTitle != null) ? jobTitle.getName() : null)
            .build();
        try (ExportService<VehicleDoc> exportService = new CsvExportService<>(csvWriter)) {
            DataStreamProvider<VehicleDoc> dataStreamProvider = () -> vehicleDocService.scrollStream(searchForm);
            exportService.export(dataStreamProvider);
        }
    }

}
