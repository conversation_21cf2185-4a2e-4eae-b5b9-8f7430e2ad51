package com.carsaver.portal.controller.search;

import com.carsaver.elasticsearch.model.sale.VehicleSaleDoc;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.portal.elasticsearch.VehicleSaleDocService;
import com.carsaver.portal.elasticsearch.criteria.VehicleSaleSearchCriteria;
import com.carsaver.portal.elasticsearch.facets.VehicleSaleDocFacets;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.SearchResults;
import com.carsaver.search.support.StatResults;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.SortDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/sales")
public class VehicleSaleSearchController extends SearchController<VehicleSaleSearchCriteria> {

    @Getter(AccessLevel.PROTECTED)
    @Autowired
    private VehicleSaleDocService docService;

    @PostMapping("/scroll")
    public SearchResults<VehicleSaleDoc> scroll(@RequestBody VehicleSaleSearchCriteria searchForm) {
        log.info("Scrolling for {}", searchForm);
        return docService.scroll(searchForm);
    }

    @PreAuthorize("hasAuthority('read:sale')")
    @PostMapping("/search")
    public SearchResults<VehicleSaleDoc> find(@RequestBody VehicleSaleSearchCriteria searchForm, @SortDefault(value = "name") Pageable pageable) {
        log.info("Searching Sales for {}", searchForm);
        return docService.search(searchForm, PageRequestUtils.asOneBasedPage(pageable));
    }

    @PreAuthorize("hasAuthority('read:sale')")
    @PostMapping(value = "/facet_info", params = "id")
    public FacetInfoResult findFacet(@RequestBody VehicleSaleSearchCriteria searchForm, @RequestParam("id") String facet) {
        return docService.facets(searchForm, VehicleSaleDocFacets.class, facet);
    }

    @PreAuthorize("hasAuthority('read:sale')")
    @PostMapping("/stats")
    public StatResults stats(@RequestBody VehicleSaleSearchCriteria searchForm) {
        log.info("Searching Sales Stats using {}", searchForm);

        return docService.stats(searchForm);
    }

}
