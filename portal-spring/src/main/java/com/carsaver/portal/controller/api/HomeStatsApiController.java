package com.carsaver.portal.controller.api;

import com.carsaver.portal.service.home.HomeLeadDocService;
import com.carsaver.portal.service.home.HomeSalesDocService;
import com.carsaver.portal.service.home.HomeUserDocService;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/home")
public class HomeStatsApiController {

    @Autowired
    private HomeUserDocService homeUserDocService;

    @Autowired
    private HomeLeadDocService leadDocService;

    @Autowired
    private HomeSalesDocService homeSalesDocService;

    @GetMapping("/users")
    public List<? extends Histogram.Bucket> getUsersHistogram() {
        return homeUserDocService.getUserCreatedHistogram();
    }

    @GetMapping("/leads")
    public List<? extends Histogram.Bucket> getLeadsHistogram() {
        return leadDocService.getUserCreatedHistogram();
    }

    @GetMapping("/sales")
    public List<? extends Histogram.Bucket> getSalesHistogram() {
        return homeSalesDocService.getUserCreatedHistogram();
    }
}
