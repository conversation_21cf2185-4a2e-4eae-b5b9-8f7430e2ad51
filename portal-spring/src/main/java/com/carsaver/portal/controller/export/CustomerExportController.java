package com.carsaver.portal.controller.export;

import com.carsaver.elasticsearch.criteria.CustomerSearchCriteria;
import com.carsaver.elasticsearch.model.CustomerDoc;
import com.carsaver.elasticsearch.model.JobTitleDoc;
import com.carsaver.elasticsearch.service.CustomerDocService;
import com.carsaver.magellan.export.CsvWriter;
import com.carsaver.magellan.export.DataStreamProvider;
import com.carsaver.magellan.export.service.CsvExportService;
import com.carsaver.magellan.export.service.ExportService;
import com.carsaver.portal.controller.export.support.CustomerDocBufferProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@Controller
public class CustomerExportController extends ExportController {

    @Autowired
    private CustomerDocService customerDocService;

    @Autowired
    private CustomerDocBufferProcessor bufferProcessor;

    @PreAuthorize("hasAuthority('read:user')")
    @GetMapping(value="/user/customers/report.csv", produces = "text/csv")
    public void exportCustomerListToCsv(
        @ModelAttribute("searchForm") CustomerSearchCriteria searchForm,
        @RequestParam List<String> columns,
        @RequestParam List<String> negativeSearchColumns,
        HttpServletResponse response
    ) throws Exception {
        super.applyNegativeSearchColumns(searchForm, negativeSearchColumns);

        CsvWriter csvWriter = super.prepareCsvWriter(response, columns)
            .addSerializer(JobTitleDoc.class, (jobTitle) -> (jobTitle != null) ? jobTitle.getName() : null)
            .build();
        try(ExportService<CustomerDoc> exportService = new CsvExportService<>(csvWriter, bufferProcessor)) {
            DataStreamProvider<CustomerDoc> dataStreamProvider = () -> customerDocService.scrollStream(searchForm);
            exportService.export(dataStreamProvider);
        }
    }

}
