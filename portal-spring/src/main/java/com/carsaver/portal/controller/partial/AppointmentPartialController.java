package com.carsaver.portal.controller.partial;

import com.carsaver.magellan.api.GarageService;
import com.carsaver.magellan.client.AppointmentClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.lead.AppointmentRequest;
import com.carsaver.portal.domain.PortalAnnouncement;
import com.carsaver.portal.model.ScheduleAppointmentForm;
import com.carsaver.portal.util.SourceCreator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.util.UriComponentsBuilder;

import javax.validation.Valid;
import java.time.DayOfWeek;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Controller
public class AppointmentPartialController {

    @Autowired
    private AppointmentClient appointmentClient;

    @Autowired
    private GarageService garageService;

    @Autowired
    private SourceCreator sourceCreator;

    @GetMapping(value = "/appointments", params = "form")
    public String scheduleAppointment(@ModelAttribute("criteria") ScheduleAppointmentForm criteria, Model model) {
        CertificateView certificate = garageService.findCertificate(criteria.getCertificateId());
        setupForm(certificate, model);
        return "appointment/schedule";
    }

    @PostMapping("/appointments")
    public String scheduleAppointment(@ModelAttribute("criteria") @Valid ScheduleAppointmentForm criteria, BindingResult bindingResult, Model model, RedirectAttributes redirectAttrs) {
        if(bindingResult.hasErrors()){
            log.info("Error failed validation with this criteria = {}", criteria);
            CertificateView certificate = garageService.findCertificate(criteria.getCertificateId());
            setupForm(certificate, model);
            return "appointment/schedule";
        }

        CertificateView certificate = garageService.findCertificate(criteria.getCertificateId());;

        AppointmentRequest appointment = new AppointmentRequest();
        appointment.setUserId(criteria.getUserId());
        appointment.setDealerId(criteria.getDealerId());
        appointment.setCertificateId(criteria.getCertificateId());
        appointment.setLocalDate(criteria.getAppointmentDate());
        appointment.setLocalTime(criteria.getAppointmentTime());
        appointment.setSource(sourceCreator.appendSource(certificate.getSource()));
        appointmentClient.schedule(appointment);

        redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.success( "Appointment scheduled."));
        return "fragments/ajax-handler :: closeModal";
    }

    private void setupForm(CertificateView certificate, Model model) {
        model.addAttribute("certificate", certificate);
        model.addAttribute("dealer", certificate.getDealer());
        model.addAttribute("user", certificate.getUser());
        model.addAttribute("formAction", UriComponentsBuilder.fromPath("/appointments").toUriString());
        model.addAttribute("daysOfWeek", getDaysOfWeek());
    }

    private List<String> getDaysOfWeek() {
        return Arrays
            .stream(DayOfWeek.values())
            .map(dayOfWeek -> StringUtils.capitalize(dayOfWeek.name().toLowerCase()))
            .collect(Collectors.toList());
    }

}
