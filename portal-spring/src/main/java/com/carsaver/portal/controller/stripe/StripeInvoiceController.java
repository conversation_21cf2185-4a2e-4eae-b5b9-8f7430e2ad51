package com.carsaver.portal.controller.stripe;

import com.stripe.exception.InvalidRequestException;
import com.stripe.exception.StripeException;
import com.stripe.model.Invoice;
import com.stripe.model.InvoiceCollection;
import com.stripe.model.InvoiceLineItemCollection;
import com.stripe.param.InvoiceListParams;
import com.stripe.param.InvoiceUpcomingParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

@Slf4j
@RestController
public class StripeInvoiceController {

    @GetMapping("/api/stripe/customers/{cusId}/upcoming")
    public ResponseEntity upcoming(@PathVariable String cusId) throws StripeException {
        InvoiceUpcomingParams upcomingParams = InvoiceUpcomingParams.builder()
            .setCustomer(cusId)
            .build();

        try {
            InvoiceLineItemCollection lines = Invoice.upcoming(upcomingParams).getLines();
            return ResponseEntity.ok(lines.getData());
        } catch (InvalidRequestException e) {
            // ignored since upcoming will throw error if there are none for the customer instead of empty list.
        }

        return ResponseEntity.ok(Collections.emptyList());
    }

    @GetMapping("/api/stripe/customers/{cusId}/invoices")
    public ResponseEntity invoices(@PathVariable String cusId) throws StripeException {
        InvoiceListParams invoiceListParams = InvoiceListParams.builder()
            .setCustomer(cusId)
            .build();

        try {
            InvoiceCollection invoices = Invoice.list(invoiceListParams);
            return ResponseEntity.ok(invoices.getData());
        } catch (InvalidRequestException e) {
            // ignored since upcoming will throw error if there are none for the customer instead of empty list.
        }

        return ResponseEntity.ok(Collections.emptyList());
    }

}
