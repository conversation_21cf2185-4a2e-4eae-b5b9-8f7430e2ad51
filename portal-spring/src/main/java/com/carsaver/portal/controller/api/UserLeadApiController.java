package com.carsaver.portal.controller.api;

import com.carsaver.magellan.api.LeadService;
import com.carsaver.magellan.client.AppointmentClient;
import com.carsaver.magellan.client.LoanClient;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.magellan.model.AppointmentView;
import com.carsaver.magellan.model.ConnectionView;
import com.carsaver.magellan.model.finance.LoanRequestView;
import com.carsaver.portal.model.user.UserAppointment;
import com.carsaver.portal.model.user.UserConnection;
import com.carsaver.portal.model.user.UserLoanRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.hateoas.PagedModel;
import org.springframework.hateoas.CollectionModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequestMapping("/api/user")
public class UserLeadApiController {


    @Autowired
    private AppointmentClient appointmentClient;

    @Autowired
    private LeadService leadService;

    @Autowired
    private LoanClient loanClient;

    @GetMapping("{userId}/appointments")
    public Page<UserAppointment> getUserAppointments(@PathVariable String userId, @PageableDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("Getting Appointments for User: {}", userId);

        PagedModel<AppointmentView> appointments = appointmentClient.findByUser(userId, pageable);

        return PageRequestUtils.toPage(appointments, pageable).map(UserAppointment::from);
    }

    @GetMapping("/{userId}/connections")
    public Page<UserConnection> getUserConnections(@PathVariable String userId, @PageableDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("Getting connections for User {}", userId);

        Page<ConnectionView> connections = leadService.findConnectionsByUser(userId, pageable);

        return connections.map(UserConnection::from);
    }

    @GetMapping("/{userId}/finance-applications")
    public List<UserLoanRequest> getUserFinanceApps(@PathVariable String userId) {
        log.info("Getting finance applications for User {}", userId);

        CollectionModel<LoanRequestView> loanRequest = loanClient.findLoanRequestsByUser(userId);

        return loanRequest.getContent().stream().map(UserLoanRequest::from).collect(Collectors.toList());
    }
}
