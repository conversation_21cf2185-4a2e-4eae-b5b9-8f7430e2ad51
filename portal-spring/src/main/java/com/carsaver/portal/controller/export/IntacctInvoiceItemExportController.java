package com.carsaver.portal.controller.export;

import com.carsaver.elasticsearch.model.invoice.InvoiceItemDoc;
import com.carsaver.magellan.export.BufferProcessor;
import com.carsaver.magellan.export.service.CsvExportService;
import com.carsaver.magellan.util.StringUtils;
import com.carsaver.magellan.export.CsvWriter;
import com.carsaver.magellan.export.DataStreamProvider;
import com.carsaver.portal.controller.export.support.invoiceitems.IntacctInvoiceItem;
import com.carsaver.portal.controller.export.support.invoiceitems.IntacctInvoiceItemBufferProcessor;
import com.carsaver.portal.elasticsearch.InvoiceItemDocService;
import com.carsaver.portal.elasticsearch.criteria.InvoiceItemSearchCriteria;
import com.carsaver.portal.elasticsearch.facets.InvoiceItemDocFacets;
import com.carsaver.search.facet.TermFacet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Controller
public class IntacctInvoiceItemExportController extends ExportController {
    private static final List<String> COLUMNS = Arrays.asList(
        "batchTitle", "invoiceNo", "poNo", "customerId", "postingDate", "createdDate",
        "dueDate", "totalDue", "totalPaid", "paidDate", "termName", "description",
        "basecurr", "currency", "exchRateDate", "exchRateTypeId", "exchangeRate", "lineNo",
        "memo", "acctNo", "acctLabel", "locationId", "deptId", "allocationId", "amount",
        "arinvoiceitemCustomerid","arinvoiceitemVendorid", "arinvoiceitemEmployeeid", "arinvoiceitemItemid", "arinvoiceitemClassid");

    @Autowired
    private InvoiceItemDocService docService;

    @Autowired
    private IntacctInvoiceItemBufferProcessor bufferProcessor;

    @PreAuthorize("hasAuthority('invoice-item:export')")
    @GetMapping(value="/invoice-items/intacct-report.csv", produces = "text/csv")
    public void exportInvoiceItemListToIntacctCsv(
        @ModelAttribute InvoiceItemSearchCriteria searchForm,
        @RequestParam List<String> negativeSearchColumns,
        HttpServletResponse response
    ) throws Exception {
        super.applyNegativeSearchColumns(searchForm, negativeSearchColumns);

        CsvWriter csvWriter = super.prepareCsvWriter(response, COLUMNS, "intacct-report.csv")
            .labelMaker(StringUtils::snakeCaseUpper)
            .build();

        try(CsvExportService<InvoiceItemDoc, IntacctInvoiceItem> exportService = new CsvExportService<>(csvWriter, bufferProcessor)) {
            //loop through each dealer and write to csv using dateStreamProvider lambda
            for(TermFacet dealerFacet : getSearchFacets(searchForm)) {
                searchForm.setDealerIds(Collections.singletonList(dealerFacet.getId()));
                DataStreamProvider<InvoiceItemDoc> dataStreamProvider = () -> docService.scrollStream(searchForm);

                exportService.setBufferSize(dealerFacet.getCount());
                exportService.export(dataStreamProvider);
            }
        }
    }

    private List<TermFacet> getSearchFacets(InvoiceItemSearchCriteria searchForm) {
        List<TermFacet> dealerTermFacets = (List<TermFacet>) docService.facets(searchForm, InvoiceItemDocFacets.class, "dealers").getResults();
        //if user explicitly filtering with dealerIds, reduce dealerTermFacets accordingly
        if(!CollectionUtils.isEmpty(searchForm.getDealerIds())) {
            dealerTermFacets = dealerTermFacets.stream()
                .filter(facet -> searchForm.getDealerIds().contains(facet.getId()))
                .collect(Collectors.toList());
        }

        return dealerTermFacets;
    }
}
