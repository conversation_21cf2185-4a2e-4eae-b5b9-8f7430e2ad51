package com.carsaver.portal.controller.stats;

import com.carsaver.portal.elasticsearch.metrics.DealerInventoryMetrics;
import com.carsaver.portal.service.DealerInventoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/dealer-inventory-count")
public class DealerInventoryCountController {

    @Autowired
    private DealerInventoryService dealerInventoryService;

    @PostMapping("/{dealerId}")
    public DealerInventoryMetrics getMetrics(@PathVariable String dealerId) {
        return dealerInventoryService.getPublishedInventoryMetrics(dealerId);
    }
}
