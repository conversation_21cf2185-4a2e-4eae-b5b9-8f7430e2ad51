package com.carsaver.portal.controller.api;

import com.carsaver.portal.elasticsearch.criteria.LeadKpiSearchCriteria;
import com.carsaver.portal.model.store.dealer.dashboard.ClaimedCountWidget;
import com.carsaver.portal.service.dashboard.DashboardLeadDocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/leads/stats")
public class LeadStatsApiController {

    @Autowired
    private DashboardLeadDocService dashboardLeadDocService;

    @PostMapping("/claimed")
    public ClaimedCountWidget getClaimedLeads(@RequestBody LeadKpiSearchCriteria criteria) {
        return dashboardLeadDocService.getClaimedCounts(criteria, null);
    }

    @PostMapping("/contacted")
    public ClaimedCountWidget getContactedLeads(@RequestBody LeadKpiSearchCriteria criteria) {
        return dashboardLeadDocService.getContactedCounts(criteria, null);
    }
}
