package com.carsaver.portal.controller;

import com.carsaver.core.DealerLinkStatus;
import com.carsaver.elasticsearch.criteria.UserSearchCriteria;
import com.carsaver.magellan.api.DealerLinkService;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.validation.UserValidator;
import com.carsaver.portal.client.tenant.TenantClient;
import com.carsaver.portal.elasticsearch.VehicleSaleDocService;
import com.carsaver.portal.model.store.UserDetailsStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.StringTrimmerEditor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Arrays;


/**
 * Manage Users in the CarSaver system
 */
@Controller
@RequestMapping("/user")
@Slf4j
public class UserController {

    @Autowired
    private DealerLinkService dealerLinkService;

    @Autowired
    private TenantClient tenantClient;

    @Autowired
    private UserValidator userValidator;

    @Autowired
    private VehicleSaleDocService vehicleSaleDocService;

    @InitBinder(value = "user")
    public void initDealerViewBinder(WebDataBinder binder) {
        binder.addValidators(userValidator);
        binder.registerCustomEditor(String.class, new StringTrimmerEditor(true));
    }

    @GetMapping
    public String list(@ModelAttribute("searchForm") UserSearchCriteria searchForm) {
        return "user/list";
    }

    /**
     * This mapping is a fail safe when Volie doesn't have the cs_user_id key when they create the
     * links for the merge tag and will just redirect to the search page
     * @return
     */
    @GetMapping("/:cs_user_id")
    public String volieNoUserIdKey() {
        return "redirect:/user/";
    }

    @PreAuthorize("hasAnyAuthority('read:user', 'edit:user')")
    @GetMapping("/{user}")
    public String handleUserDetailsRedirect(@PathVariable UserView user, Model model) {
        if(user == null) {
            return "error/404";
        }

        if(user.isRegularUser()) {
            return "redirect:/user/customers/{user}";
        } else {
            return "redirect:/user/business-users/{user}";
        }
    }

    @PreAuthorize("hasAnyAuthority('read:user', 'edit:user')")
    @GetMapping("/business-users/{user}")
    public String businessUserDetails(@PathVariable UserView user, Model model) {
        if(user == null) {
            return "error/404";
        }

        model.addAttribute("selectedUser", UserDetailsStore.from(user, vehicleSaleDocService, tenantClient));
        model.addAttribute("dealerLinks", dealerLinkService.findUserDealerLinks(user.getId()));
        model.addAttribute("dealerLinkStatuses", Arrays.asList(DealerLinkStatus.INVALID, DealerLinkStatus.LOST, DealerLinkStatus.SOLD, DealerLinkStatus.WORKING));
        model.addAttribute("resolutions", dealerLinkService.getResolutions());

        return "user/details/business";
    }

    @PreAuthorize("hasAnyAuthority('read:user', 'edit:user')")
    @GetMapping("/customers/{user}")
    public String customerDetails(@PathVariable UserView user, Model model) {
        if(user == null) {
            return "error/404";
        }

        model.addAttribute("selectedUser", UserDetailsStore.from(user, vehicleSaleDocService, tenantClient));
        model.addAttribute("dealerLinks", dealerLinkService.findUserDealerLinks(user.getId()));
        model.addAttribute("dealerLinkStatuses", Arrays.asList(DealerLinkStatus.INVALID, DealerLinkStatus.LOST, DealerLinkStatus.SOLD, DealerLinkStatus.WORKING));
        model.addAttribute("resolutions", dealerLinkService.getResolutions());

        return "user/details/customer";
    }

    @PreAuthorize("hasAuthority('edit:user')")
    @GetMapping(value = "/business-users/{user}/form")
    public String editBusinessUserForm(@PathVariable UserView user, Model model) {
        model.addAttribute("user", user);
        model.addAttribute("isCreate", false);
        return "user/form";
    }

    @PreAuthorize("hasAuthority('edit:user')")
    @GetMapping(value = "/customers/{user}/form")
    public String editCustomerForm(@PathVariable UserView user, Model model) {
        model.addAttribute("user", user);
        model.addAttribute("isCreate", false);
        return "user/form";
    }

    @PreAuthorize("hasAuthority('edit:user')")
    @GetMapping(value = "/business-users/form")
    public String addBusinessUserForm(Model model) {
        model.addAttribute("isCreate", true);
        return "user/form";
    }

    @PreAuthorize("hasAuthority('edit:user')")
    @GetMapping(value = "/customers/form")
    public String addCustomerForm(Model model) {
        model.addAttribute("isCreate", true);
        return "user/form";
    }
}
