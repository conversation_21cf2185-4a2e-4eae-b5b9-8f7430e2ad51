package com.carsaver.portal.controller;

import com.carsaver.magellan.auth.PrincipleService;
import com.carsaver.magellan.model.UserView;
import com.carsaver.stereotype.UserIdentity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import javax.validation.Valid;
import java.security.Principal;

@Slf4j
@Controller
public class AccountController {

    @Autowired
    private PrincipleService principleService;

    @GetMapping("/account")
    public String details(Model model, Principal principal) {
        UserIdentity user = principleService.findUser(principal).orElseThrow(() -> new RuntimeException(("No User Found")));
        model.addAttribute("userView", user);
        return "account/details";
    }

    @PostMapping("/account")
    public String save(@Valid UserView userView, BindingResult result) {
        log.info("Trying to save User: {}", userView);

        if(result.hasErrors()) {
            log.info("Has Errors: {}", result.getAllErrors());
            return "account/details";
        }

        return "account/details";
    }
}
