package com.carsaver.portal.controller.stripe;

import com.carsaver.intacct.client.CustomerClient;
import com.carsaver.intacct.model.Address;
import com.carsaver.intacct.model.Contact;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.DealerGroupClient;
import com.carsaver.magellan.client.request.DealerUpdateRequest;
import com.carsaver.magellan.client.request.UpdateRequest;
import com.carsaver.magellan.model.DealerGroupView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.portal.model.billing.BillingCustomerForm;
import com.carsaver.stereotype.State;
import com.stripe.exception.StripeException;
import com.stripe.model.Customer;
import com.stripe.model.Subscription;
import com.stripe.param.CustomerCreateParams;
import com.stripe.param.SubscriptionListParams;
import com.stripe.param.common.EmptyParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController
public class StripeCustomerController {

    @Autowired
    private DealerClient dealerClient;

    @Autowired
    private DealerGroupClient dealerGroupClient;

    @Autowired
    private CustomerClient customerClient;

    @Autowired
    private Environment environment;

    @PreAuthorize("hasAuthority('edit:contracts')")
    @PostMapping("/api/stripe/customers")
    public ResponseEntity createCustomer(@RequestBody @Valid BillingCustomerForm customerForm) throws StripeException {
        DealerView dealer = dealerClient.findById(customerForm.getDealerId());
        if(dealer != null && dealer.getStripeCustomerId() != null) {
            return ResponseEntity.ok(dealer);
        }

        if(dealer != null) {
            CustomerCreateParams.Address address = null;
            if(customerForm.getAddress() != null) {
                address = CustomerCreateParams.Address.builder()
                    .setLine1(customerForm.getAddress().getLine1())
                    .setLine2(
                        Optional.ofNullable(customerForm.getAddress().getLine2())
                            .map(String::trim)
                            .filter(StringUtils::isNotBlank)
                            .orElse(null)
                    )
                    .setCity(customerForm.getAddress().getCity())
                    .setState(customerForm.getAddress().getState())
                    .setPostalCode(customerForm.getAddress().getPostalCode())
                    .build();
            }

            String invoicePrefix = null;
            if(environment.acceptsProfiles(Profiles.of("prod"))) {
                invoicePrefix = dealer.getDealerId();
            }

            CustomerCreateParams params = CustomerCreateParams.builder()
                .setName(customerForm.getName())
                .setEmail(customerForm.getEmail())
                .setInvoicePrefix(invoicePrefix)
                .setTaxExempt(CustomerCreateParams.TaxExempt.EXEMPT)
                .setDescription(customerForm.getName())
                .setPhone(customerForm.getPhoneNumber())
                .setAddress(address)
                .addPreferredLocale("en")
                .setShipping(EmptyParam.EMPTY)
                .putMetadata("WalmartStore", Optional.ofNullable(dealer.getWalmartStoreId()).map(String::valueOf).orElse(null))
                .putMetadata("DealerId", Optional.ofNullable(dealer.getDealerId()).map(String::valueOf).orElse(null))
                .build();

            Customer customer = Customer.create(params);

            StripeCustomerIdUpdate stripeCustomerIdUpdate = new StripeCustomerIdUpdate(customer.getId());
            return ResponseEntity.ok(dealerClient.update(dealer.getId(), stripeCustomerIdUpdate));
        }

        return ResponseEntity.notFound().build();
    }

    @PreAuthorize("hasAuthority('edit:contracts')")
    @PostMapping(value = "/api/stripe/customers/sync", params = "dealerGroupId")
    public ResponseEntity<?> syncIntacctCustomer(@RequestParam("dealerGroupId") DealerGroupView dealerGroup) throws StripeException {
        if(dealerGroup != null && dealerGroup.getStripeCustomerId() != null) {
            return ResponseEntity.ok(dealerGroup);
        }

        if(dealerGroup != null) {
            Optional<com.carsaver.intacct.model.Customer> intacctCustomer = customerClient.getCustomer(dealerGroup.getIntacctCustomerId());

            Contact contact = intacctCustomer
                .map(com.carsaver.intacct.model.Customer::getContact)
                .orElse(null);

            Address intacctAddress = Optional.ofNullable(contact)
                .map(Contact::getMailAddress)
                .orElse(null);

            CustomerCreateParams.Address address = null;
            if(intacctAddress != null) {
                address = CustomerCreateParams.Address.builder()
                    .setLine1(intacctAddress.getAddress1())
                    .setLine2(
                        Optional.ofNullable(intacctAddress.getAddress2())
                            .map(String::trim)
                            .filter(StringUtils::isNotBlank)
                            .orElse(null)
                    )
                    .setCity(intacctAddress.getCity())
                    .setState(intacctAddress.getState())
                    .setPostalCode(intacctAddress.getZip())
                    .build();
            }

            String invoicePrefix = null;
            if(environment.acceptsProfiles(Profiles.of("prod"))) {
                invoicePrefix = dealerGroup.getIntacctCustomerId();
            }

            if(contact != null) {
                CustomerCreateParams params = CustomerCreateParams.builder()
                    .setName(dealerGroup.getName())
                    .setEmail(contact.getEmail1())
                    .setInvoicePrefix(invoicePrefix)
                    .setTaxExempt(CustomerCreateParams.TaxExempt.EXEMPT)
                    .setDescription(dealerGroup.getName())
                    .setPhone(contact.getPhone1())
                    .setAddress(address)
                    .addPreferredLocale("en")
                    .setShipping(EmptyParam.EMPTY)
                    .putMetadata("DealerGroupId", Optional.ofNullable(dealerGroup.getId()).map(String::valueOf).orElse(null))
                    .build();

                Customer customer = Customer.create(params);

                StripeCustomerIdUpdate stripeCustomerIdUpdate = new StripeCustomerIdUpdate(customer.getId());
                return ResponseEntity.ok(dealerGroupClient.update(dealerGroup.getId(), stripeCustomerIdUpdate));
            }
        }

        return ResponseEntity.notFound().build();
    }

    @GetMapping("/api/stripe/customers/{cusId}")
    public Customer findCustomer(@PathVariable String cusId) throws StripeException {
        return Customer.retrieve(cusId);
    }

    @GetMapping("/api/stripe/customers/{cusId}/subscriptions")
    public Iterable<Subscription> findSubscriptions(@PathVariable String cusId) throws StripeException {
        SubscriptionListParams params = SubscriptionListParams.builder()
            .setCustomer(cusId)
            .setStatus(SubscriptionListParams.Status.ALL)
            .build();

        return Subscription.list(params).autoPagingIterable();
    }

    @GetMapping("/api/stripe/customers/fields")
    public ResponseEntity formFields() {
        Map<String, Object> formFields = new HashMap<>();
        formFields.put("states", State.getStates());
        return ResponseEntity.ok(formFields);
    }

    @Data
    @AllArgsConstructor
    private static class StripeCustomerIdUpdate implements DealerUpdateRequest, UpdateRequest {
        private String stripeCustomerId;
    }
}
