package com.carsaver.portal.controller.export;

import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.portal.client.financier.ExportFinancierData;
import com.carsaver.portal.client.financier.FinancierClient;
import com.carsaver.portal.client.financier.FinancierView;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.hateoas.PagedModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.supercsv.io.CsvBeanWriter;
import org.supercsv.io.ICsvBeanWriter;
import org.supercsv.prefs.CsvPreference;

import javax.servlet.http.HttpServletResponse;
import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RestController
@RequestMapping("/configs/lenders")
@Slf4j
public class FinancierExportController {

    private Pageable pageable = PageRequest.of(0, Integer.MAX_VALUE, Sort.by("name").ascending());

    @Autowired
    private FinancierClient financierClient;

    @GetMapping("/report.csv")
    public void exportToCsv(
        @RequestParam(required = false) String name,
        @RequestParam List<String> columns,
        @RequestParam List<String> negativeSearchColumns,
        HttpServletResponse response
    ) {
        response.setContentType("text/csv");

        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=report.csv";

        response.setHeader(headerKey, headerValue);

        List<FinancierView> financiers;

        PagedModel<FinancierView> financierViews;

        if (Objects.isNull(name)) {
            financierViews = financierClient.findAll(PageRequestUtils.asOneBasedPage(pageable));
        } else {
            financierViews = financierClient.findByFinancierName(name, PageRequestUtils.asOneBasedPage(pageable));
        }

        financiers = financierViews.getContent().stream().collect(Collectors.toList());

        List<ExportFinancierData> financierData = getExportableData(financiers);

        List<String> csvHeaderList = columns.stream().map(obj -> getSortKeyMapForHeader().get(obj.trim())).collect(Collectors.toList());
        String[] csvHeader = csvHeaderList.toArray(new String[csvHeaderList.size()]);

        List<String> mappingList = columns.stream().map(obj -> getSortKeyMapForMapping().get(obj.trim())).collect(Collectors.toList());
        String[] nameMapping = mappingList.toArray(new String[mappingList.size()]);

        try {
            ICsvBeanWriter csvWriter = new CsvBeanWriter(response.getWriter(), CsvPreference.STANDARD_PREFERENCE);
            csvWriter.writeHeader(csvHeader);

            for (ExportFinancierData financier : financierData) {
                csvWriter.write(financier, nameMapping);
            }
            csvWriter.close();
        } catch (Exception e) {
            log.error("Error occurred while exporting data to CSV {}", e.getMessage(), e);
        }
    }

    private List<ExportFinancierData> getExportableData(List<FinancierView> financiers) {
        List<ExportFinancierData> data = financiers.stream().map(
            obj -> {
                ExportFinancierData financier = new ExportFinancierData();
                financier.setId(obj.getId());
                financier.setName(obj.getName());
                financier.setRouteOneId(obj.getRouteOneId());
                financier.setHorizonId(obj.getHorizonId());
                financier.setPayoffPhoneNumber(obj.getPayoffPhoneNumber());
                financier.setEnabled(obj.getEnabled());
                if (Objects.nonNull(obj.getConfig())) {
                    String config = new Gson().toJson(obj.getConfig());
                    financier.setConfig(config);
                }
                return financier;
            }).collect(Collectors.toList());
        return data;
    }

    private Map<String, String> getSortKeyMapForHeader() {
        return Stream.of(
            new AbstractMap.SimpleEntry<>("id", "ID"),
            new AbstractMap.SimpleEntry<>("name", "Lender Name"),
            new AbstractMap.SimpleEntry<>("payoffPhoneNumber", "Payoff Phone Number"),
            new AbstractMap.SimpleEntry<>("enabled", "Enable?"),
            new AbstractMap.SimpleEntry<>("routeOneId", "RouteOne ID"),
            new AbstractMap.SimpleEntry<>("horizonId", "Horizon ID"),
            new AbstractMap.SimpleEntry<>("config", "Configurations")
        ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private Map<String, String> getSortKeyMapForMapping() {
        return Stream.of(
            new AbstractMap.SimpleEntry<>("id", "id"),
            new AbstractMap.SimpleEntry<>("name", "name"),
            new AbstractMap.SimpleEntry<>("payoffPhoneNumber", "payoffPhoneNumber"),
            new AbstractMap.SimpleEntry<>("enabled", "enabled"),
            new AbstractMap.SimpleEntry<>("routeOneId", "routeOneId"),
            new AbstractMap.SimpleEntry<>("horizonId", "horizonId"),
            new AbstractMap.SimpleEntry<>("config", "config")
        ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }
}
