package com.carsaver.portal.controller.stratus;

import com.carsaver.magellan.api.TradeService;
import com.carsaver.magellan.api.exception.NotFoundException;
import com.carsaver.magellan.client.AppointmentClient;
import com.carsaver.magellan.client.ConnectionClient;
import com.carsaver.magellan.model.BaseLeadView;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealJacket;
import com.carsaver.magellan.model.TradeInView;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.carsaver.portal.service.TempDealJacketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Optional;

@Slf4j
@Controller
@RequestMapping("/stratus/quickview")
public class LeadQuickViewController {

    @Autowired
    private ConnectionClient connectionClient;

    @Autowired
    private TradeService tradeService;

    @Autowired
    private AppointmentClient appointmentClient;

    @Autowired
    private TempDealJacketService tempDealJacketService;

    @GetMapping("/lead/{leadId}")
    public String quickView(@PathVariable String leadId, Model model) {
        String cleansedLeadId = cleanseLeadId(leadId);

        if(!cleansedLeadId.equals(leadId)) {
            return "redirect:/stratus/quickview/lead/" + cleansedLeadId;
        }

        BaseLeadView lead = findLead(leadId);
        List<TradeInView> tradeIns = tradeService.findTradeInByUser(lead.getUser());
        if(!CollectionUtils.isEmpty(tradeIns)) {
            model.addAttribute("tradeInfo", tradeIns.get(0));
        }

        CertificateView certificate = lead.getCertificate();

        DealJacket deal = tempDealJacketService.getDealJacket(certificate).orElse(null);
        UserVehicleView tradeVehicle = Optional.ofNullable(certificate).map(CertificateView::getTradeVehicle).orElse(null);

        model.addAttribute("deal", deal);
        model.addAttribute("tradeVehicle", tradeVehicle);
        model.addAttribute("dealer", lead.getDealer());
        model.addAttribute("lead", lead);
        model.addAttribute("certificate", certificate);
        model.addAttribute("user", lead.getUser());

        return "checkin/quickView";
    }

    @GetMapping("/lead/{leadId}/view")
    public String viewFull(@PathVariable String leadId) {
        BaseLeadView lead = findLead(leadId);
        if(lead.isAppointment()) {
            return "redirect:/stratus/dealer/" + lead.getDealerId() + "/checkin/appointment/" + lead.getId();
        } else {
            return "redirect:/stratus/dealer/" + lead.getDealerId() + "/checkin/connection/" + lead.getId();
        }
    }

    private BaseLeadView findLead(String leadId) {
        BaseLeadView lead = appointmentClient.findById(cleanseLeadId(leadId));
        if (lead == null) {
            log.info("Appointment not found for {}, looking up connection", leadId);
            lead = connectionClient.findById(leadId);
            if (lead == null){
                log.info("Connection not found for {}, throwing NotFoundException", leadId);
            }
        }

        return Optional.ofNullable(lead).orElseThrow(() -> new NotFoundException("Unable to find lead"));
    }

    private String cleanseLeadId(String leadId) {
        return Optional.ofNullable(leadId).map(id -> id.replaceAll("[^\\da-zA-Z\\-]", ""))
            .orElseThrow(() -> new IllegalArgumentException("invalid leadId=" + leadId));
    }

}
