package com.carsaver.portal.controller;

import com.carsaver.magellan.api.ImageUrlService;
import com.carsaver.magellan.model.CertificateView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/certificate")
public class CertificateController {

    @Autowired
    private ImageUrlService imageUrlService;

    @GetMapping("/{certificate}/defaultImage")
    public String modelImage(@PathVariable CertificateView certificate) {
        if (certificate == null) {
            return null;
        }

        String url = imageUrlService.getDefaultImage(certificate);

        if (url == null) {
            return null;
        }

        return "redirect:" + url;
    }
}
