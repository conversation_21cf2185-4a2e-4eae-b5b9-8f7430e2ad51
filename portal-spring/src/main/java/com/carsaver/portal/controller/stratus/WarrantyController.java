package com.carsaver.portal.controller.stratus;

import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticResponse;
import com.carsaver.elasticsearch.model.UserDoc;
import com.carsaver.magellan.api.UserService;
import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.client.DealerLinkClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.client.WarrantyContractClient;
import com.carsaver.magellan.model.DealerLinkRequest;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.WarrantyContractView;
import com.carsaver.magellan.search.UserDealerLinkSearchCriteria;
import com.carsaver.portal.domain.PortalAnnouncement;
import com.carsaver.portal.filter.DealerUserAccessFilter;
import com.carsaver.portal.model.warranty.ApplicantRegistrationForm;
import com.carsaver.portal.service.WarrantyService;
import com.carsaver.portal.util.SourceCreator;
import com.carsaver.warranty.model.ApplicantRegistration;
import com.carsaver.warranty.model.ApplicantRegistrationException;
import com.carsaver.warranty.model.FindSurchargesException;
import com.carsaver.warranty.model.GetFormsException;
import com.carsaver.warranty.model.GetRegistrationByIdException;
import com.carsaver.warranty.model.GetVehicleResponse;
import com.carsaver.warranty.model.Premium;
import com.carsaver.warranty.model.PremiumsRequest;
import com.carsaver.warranty.model.PremiumsRequestException;
import com.carsaver.warranty.model.VehicleRequest;
import com.carsaver.warranty.model.VehicleRequestException;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.SessionAttribute;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.bind.support.SessionStatus;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.security.Principal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.elasticsearch.index.query.QueryBuilders.*;

@Slf4j
@Controller("stratusWarrantyController")
@RequestMapping("/stratus/dealer/{dealer}")
@SessionAttributes({"applicantRegistrationForm"})
public class WarrantyController {

    @Value("${tenant.express-id}")
    private String expressTenantId;

    @Value("${tenant.default-id}")
    private String defaultTenantId;

    @Autowired
    private UserService userService;

    @Autowired
    private UserClient userClient;

    @Autowired
    @Qualifier("gibson")
    private ElasticClient elasticClient;

    @Autowired
    private WarrantyService warrantyService;

    @Value("${warranty-service.providerCode}")
    private String providerCode;

    @Autowired
    private WarrantyControllerHelper helper;

    @Autowired
    private WarrantyContractClient warrantyContractClient;

    @Autowired
    private DealerLinkClient dealerLinkClient;

    @Autowired
    private SourceCreator sourceCreator;

    /**
     *  add common dealer attribute to model
     * @param model
     * @param dealer
     * @return
     */
    @ModelAttribute("applicantRegistrationForm")
    public ApplicantRegistrationForm getApplicantRegistrationForm(Model model, @PathVariable("dealer") DealerView dealer) {
        model.addAttribute("dealer", dealer);
        return new ApplicantRegistrationForm();
    }


    /***************************************  WARRANTY REGISTRATION STEPS *****************************************/

    //LIFETIME-WARRANTY: STEP 1a - CUSTOMER search FORM
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @GetMapping("/warranty")
    public String customerSearchForm(@PathVariable DealerView dealer, @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm) {
        applicantRegistrationForm.setStarted();

        return "warranty/customerSearch";
    }

    /*
        Clicking the `Upgrade` button in the Portal's warranties list section lands you here
     */
    //LIFETIME-WARRANTY: Upgrade
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @GetMapping("/warranty/{warrantyContract}/upgrade")
    public String upgrade(@PathVariable DealerView dealer, @PathVariable WarrantyContractView warrantyContract, @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm) {
        log.info("Upgrading {}", warrantyContract);
        if(warrantyContract == null) {
            return "redirect:/stratus/dealer/{dealer}/warranty";
        }
        // refresh the warranty status before upgrading in case it's voided in NWAN's system
        try {
            warrantyService.refreshRemoteRegistration(warrantyContract.getId());
        } catch(GetRegistrationByIdException e) {
            log.error("Unable to refresh warranty {} to be upgraded", warrantyContract.getId());
        }

        applicantRegistrationForm.setStarted();
        applicantRegistrationForm.setCustomer(warrantyContract.getUser());

        return helper.redirectToConfirmation(warrantyContract);
    }

    //LIFETIME-WARRANTY: STEP 1b - CUSTOMER selection FORM
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @GetMapping(value = "/warranty", params = "query")
    public String findUsersWithDealerLink(
        @RequestParam("query") String keyword,
        @PathVariable DealerView dealer,
        Model model,
        @SortDefault("firstName") Pageable pageable,
        @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm,
        Principal principal,
        HttpServletRequest servletRequest,
        RedirectAttributes redirectAttrs
    ) {
        if (!applicantRegistrationForm.isRegistrationProcessStarted()) {
            return helper.startLifetimeWarrantyRegistrationOver(dealer, redirectAttrs);
        }

        UserView loggedInUser = userClient.findById(AuthUtils.retrieveUserId(principal));
        if (loggedInUser.isAdminUser()) {
            SearchSourceBuilder searchBldr = new SearchSourceBuilder();

            BoolQueryBuilder query = boolQuery();

            if (keyword.contains("@")) {
                query.must(termQuery("email", keyword));
            } else {
                query.should(multiMatchQuery(keyword, "firstName", "lastName").type(MultiMatchQueryBuilder.Type.CROSS_FIELDS));
                query.should(multiMatchQuery(keyword, "phoneNumber"));
                query.minimumShouldMatch(1);
            }

            query.must(termQuery("enabled", true));

            searchBldr.query(query);
            searchBldr.size(20);
            searchBldr.sort("_score");

            SearchRequest searchRequest = new SearchRequest("users");
            searchRequest.source(searchBldr);

            ElasticResponse<UserDoc> searchResponse = this.elasticClient.search(searchRequest, UserDoc.class);

            List<UserView> users = searchResponse.getItems()
                .stream()
                .map(userDoc -> {
                    UserView user = new UserView();

                    BeanUtils.copyProperties(userDoc, user);

                    user.setAddress(userDoc.getAddress().getStreet());
                    user.setCity(userDoc.getAddress().getCity());
                    user.setStateCode(userDoc.getAddress().getStateCode());
                    user.setZipCode(userDoc.getAddress().getZipCode());

                    return user;
                })
                .collect(Collectors.toList());

            Page<UserView> page = new PageImpl<>(users, pageable, Math.toIntExact(searchResponse.getSearchResponse().getHits().getTotalHits().value));

            model.addAttribute("page", page);
        } else {
            UserDealerLinkSearchCriteria criteria = UserDealerLinkSearchCriteria.builder()
                .tenantIds(List.of(defaultTenantId))
                .query(keyword)
                .onlySentToDealer(true)
                .build();
            Page<UserView> carsaverPage = userService.findUsersByLinkSentToDealer(criteria, dealer.getId(), pageable);

            criteria = UserDealerLinkSearchCriteria.builder()
                .tenantIds(List.of(expressTenantId))
                .query(keyword)
                .onlySentToDealer(false)
                .build();
            Page<UserView> expressPage = userService.findUsersByLinkSentToDealer(criteria, dealer.getId(), pageable);

            Page<UserView> page = mergePages(carsaverPage, expressPage);

            model.addAttribute("page", page);
        }

        model.addAttribute("dealer", dealer);
        model.addAttribute("query", keyword);

        log.info("Searching users by keyword {} with a dealerId of = {}", keyword, dealer.getId());

        applicantRegistrationForm.setUsersSearchQuery(servletRequest.getQueryString());

        return "warranty/customerSearch";
    }

    private Page<UserView> mergePages(Page<UserView> carsaverPage, Page<UserView> expressPage) {
        List<UserView> deduppedExpress = expressPage.getContent()
            .stream()
            .filter(u -> carsaverPage.getContent().stream().noneMatch(carsaverUser -> carsaverUser.getEmail().equalsIgnoreCase(u.getEmail())))
            .collect(Collectors.toUnmodifiableList());

        List<UserView> carsaverUsers = new ArrayList<>(carsaverPage.getContent());
        carsaverUsers.addAll(deduppedExpress);
        long total = carsaverPage.getTotalElements() + expressPage.getTotalElements();
        return new PageImpl<>(carsaverUsers, carsaverPage.getPageable(), total);
    }

    //LIFETIME-WARRANTY: STEP 1c - CUSTOMER selection ACTION
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @GetMapping("/warranty/select-customer/{user}")
    public String selectCustomer(@PathVariable DealerView dealer, @PathVariable("user") UserView customer, @RequestParam(value = "vin", required = false) String vin, @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm,
                                 RedirectAttributes redirectAttrs) {
        applicantRegistrationForm.setCustomer(customer);

        if(!applicantRegistrationForm.isStartedAndCustomerSelected()) {
            return helper.startLifetimeWarrantyRegistrationOver(dealer, redirectAttrs);
        }

        // refreshing all warranty statuses for customer prior to searching for premiums
        List<WarrantyContractView> warrantyContracts = new ArrayList<>(warrantyContractClient.findByUserId(customer.getId()).getContent());
        if(!warrantyContracts.isEmpty()) {
            warrantyContracts.forEach(wc -> {
                try {
                    warrantyService.refreshRemoteRegistrationAsync(wc.getId());
                } catch(Exception ex) {
                    log.error("Unable to refresh warranties for user {}, exception was {}", customer.getId(), ex.getMessage());
                }
            });
        }

        // this is for the Save-A-Deal flow
        if(vin != null) {
            return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/search-premiums?vin=" + vin;
        }

        return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/search-premiums";
    }

    //LIFETIME-WARRANTY: step 2a - PREMIUMS search FORM
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @GetMapping("/warranty/search-premiums")
    public String premiumSearchForm(@PathVariable("dealer") DealerView dealer, @RequestParam(value = "vin", required = false) String vin, Model model, @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm,
                                    RedirectAttributes redirectAttrs) {
        if(!applicantRegistrationForm.isStartedAndCustomerSelected()) {
            return helper.startLifetimeWarrantyRegistrationOver(dealer, redirectAttrs);
        }

        helper.setEffectiveDealerEnrollmentDate(dealer, applicantRegistrationForm);
        helper.populatePremiumsSearchModel(model, dealer, applicantRegistrationForm);
        model.addAttribute("premiumsRequest", helper.getPremiumsRequest(dealer, applicantRegistrationForm, vin));

        return "warranty/premiumsSearch";
    }

    //LIFETIME-WARRANTY: step 2b - PREMIUMS search ACTION
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @PostMapping("/warranty/premiums")
    public String searchPremiums(Model model, @PathVariable DealerView dealer, @Valid PremiumsRequest premiumsRequest, BindingResult bindingResult, @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm, RedirectAttributes redirectAttrs) {

        if(!applicantRegistrationForm.isStartedAndCustomerSelected()) {
            return helper.startLifetimeWarrantyRegistrationOver(dealer, redirectAttrs);
        }

        Optional<String> redirectToPremiumsSearch = helper.validatePremiumsRequest(model, dealer, premiumsRequest, bindingResult, applicantRegistrationForm, redirectAttrs);
        if(redirectToPremiumsSearch.isPresent()) {
            return redirectToPremiumsSearch.get();
        }

        applicantRegistrationForm.setPremiumsRequest(premiumsRequest);

        List<WarrantyContractView> existingContracts = new ArrayList<>(warrantyContractClient.findByUserIdAndVinAndDealerId(applicantRegistrationForm.getCustomer().getId(), premiumsRequest.getVin(), dealer.getId()).getContent());

        log.info("customerId = {}, vin = {}, found {} existingContracts", applicantRegistrationForm.getCustomer().getId(), premiumsRequest.getVin(), existingContracts.size());
        long numberOfValidContracts = existingContracts.stream()
            .filter(wc -> !wc.getRemoteRegistration().getRegistrationStatus().equalsIgnoreCase("VOIDED"))
            .filter(wc -> wc.getSelectedPremium().isGiveAway())
            .count();
        if (numberOfValidContracts > 0) {
            redirectAttrs.addFlashAttribute("newPremiumsRequest", premiumsRequest);

            return helper.redirectToConfirmation(existingContracts.get(0));
        }

        return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/select-lifetime-warranty";
    }

    //LIFETIME-WARRANTY: STEP 3a - PREMIUM selection FORM
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @GetMapping("/warranty/select-lifetime-warranty")
    public String selectLifetimeWarrantyForm(Model model, @PathVariable DealerView dealer, @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm, RedirectAttributes redirectAttrs) {
        PremiumsRequest premiumsRequest = applicantRegistrationForm.getPremiumsRequest();

        if(!applicantRegistrationForm.isStartedAndCustomerSelected() || premiumsRequest == null) {
            return helper.startLifetimeWarrantyRegistrationOver(dealer, redirectAttrs);
        }

        try {

            GetVehicleResponse getVehicleResponse = warrantyService.getVehicle(new VehicleRequest(premiumsRequest.getVin(), premiumsRequest.getOdometer()));

            if(getVehicleResponse == null) {
                redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.error("No vehicle found meeting the search criteria"));
                return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/search-premiums";
            }

            helper.populatePremiumsSearchResults(model, dealer, premiumsRequest, applicantRegistrationForm, getVehicleResponse);

            return "warranty/premiumsSearchResults";

        } catch(VehicleRequestException | PremiumsRequestException e) {
            redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.error(e.getMessage()));

            return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/search-premiums";
        }
    }

    //LIFETIME-WARRANTY: STEP 3b - PREMIUM selection ACTION
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @PostMapping("/warranty/select-lifetime-warranty")
    public String selectPremium(Model model, @PathVariable DealerView dealer, @Valid Premium selectedPremium, @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm, RedirectAttributes redirectAttrs) {
        if(!applicantRegistrationForm.isStartedAndCustomerSelected()) {
            return helper.startLifetimeWarrantyRegistrationOver(dealer, redirectAttrs);
        }

        applicantRegistrationForm.setSelectedPremium(selectedPremium);

        return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/register-applicant/lifetime-warranty";
    }

    //LIFETIME-WARRANTY: STEP 4a - REGISTER applicant FORM
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @GetMapping("/warranty/register-applicant/lifetime-warranty")
    public String registerApplicantForm(Model model, @PathVariable DealerView dealer, @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm, RedirectAttributes redirectAttrs) {
        if(!applicantRegistrationForm.isStartedAndCustomerSelected()) {
            return helper.startLifetimeWarrantyRegistrationOver(dealer, redirectAttrs);
        }

        try {
            helper.populateRegisterApplicantModel(model, applicantRegistrationForm, providerCode);

        } catch(GetFormsException | FindSurchargesException e) {
            log.error("problem loading register applicant form", e);

            redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.error(e.getMessage()));

            return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/search-premiums";
        }

        return "warranty/applicantRegistration";
    }

    //LIFETIME-WARRANTY: STEP 4b - REGISTER applicant ACTION
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @PostMapping("/warranty/register-applicant/lifetime-warranty")
    public String registerApplicant(Model model, @PathVariable DealerView dealer, @Valid ApplicantRegistration applicantRegistration, BindingResult bindingResult,
                                    @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm, RedirectAttributes redirectAttrs, SessionStatus sessionStatus) {

        if(!applicantRegistrationForm.isStartedAndCustomerSelected()) {
            return helper.startLifetimeWarrantyRegistrationOver(dealer, redirectAttrs);
        }
        applicantRegistrationForm.setApplicantRegistration(applicantRegistration);

        if(bindingResult.hasErrors()) {
            helper.handleRegisterApplicantErrors(bindingResult, redirectAttrs);
            return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/register-applicant/lifetime-warranty";
        }

        WarrantyContractView lifetimeWarrantyContract = applicantRegistrationForm.toWarrantyContractView(dealer);

        try {
            DealerLinkRequest dealerLinkRequest = DealerLinkRequest.builder()
                .dealerId(dealer.getId())
                .userId(applicantRegistrationForm.getCustomer().getId())
                .source(sourceCreator.appendSource(null))
                .build();
            dealerLinkClient.create(dealerLinkRequest);

            WarrantyContractView savedLifetimeWarrantyContract = warrantyService.registerApplicant(lifetimeWarrantyContract);
            log.info("registrationId = {}", savedLifetimeWarrantyContract.getRegistrationId());

            redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.success("Lifetime warranty Registration successful"));

            applicantRegistrationForm.setComplete();
            sessionStatus.setComplete();

            return helper.redirectToConfirmation(savedLifetimeWarrantyContract);

        } catch(ApplicantRegistrationException e) {
            log.error("error registering applicant for lifetime warranty", e);
            redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.error(e.getMessage()));
            return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/register-applicant/lifetime-warranty";
        }
    }

    //UPGRADE(VSC) STEP 1 - PREMIUMS selection ACTION  (NOTE: PREMIUMS selection FORM for upgrades is on the confirmation page presented after lifetime warranty is completed)
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @PostMapping(value = "/warranty/{lifetimeWarrantyContract}/select-upgrade-premium/{selectedPremiumIndex}")
    public String selectUpgradePremium(Model model, @PathVariable DealerView dealer, @PathVariable WarrantyContractView lifetimeWarrantyContract, @PathVariable Integer selectedPremiumIndex,
                                       @Valid Premium selectedPremium, @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm, RedirectAttributes redirectAttrs) {
        if(!applicantRegistrationForm.isUpgradeProcessStarted()) {
            return helper.startUpgradeRegistrationOver(lifetimeWarrantyContract, redirectAttrs);
        }

        applicantRegistrationForm.setSelectedPremium(selectedPremium, selectedPremiumIndex);
        applicantRegistrationForm.getApplicantRegistration().applySelectedPremium(selectedPremium);

        return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/"
            + lifetimeWarrantyContract.getId() + "/register-applicant/upgrade";
    }

    //UPGRADE(VSC) STEP 2a - REGISTER applicant FORM
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @GetMapping("/warranty/{lifetimeWarrantyContract}/register-applicant/upgrade")
    public String registerApplicantUpgradeForm(Model model, @PathVariable DealerView dealer, @PathVariable WarrantyContractView lifetimeWarrantyContract,
                                               @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm, RedirectAttributes redirectAttrs) {

        if(!applicantRegistrationForm.isUpgradeProcessStarted()) {
            return helper.startUpgradeRegistrationOver(lifetimeWarrantyContract, redirectAttrs);
        }

        try {
            helper.populateRegisterApplicantModel(model, applicantRegistrationForm, providerCode);

        } catch(GetFormsException | FindSurchargesException e) {
            log.error("problem loading register applicant form", e);
            redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.error(e.getMessage()));

            return helper.redirectToConfirmation(lifetimeWarrantyContract);
        }
        model.addAttribute("lifetimeWarrantyContract", lifetimeWarrantyContract);

        return "warranty/applicantRegistration";
    }

    //UPGRADE(VSC) step 2b - REGISTER applicant ACTION
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @PostMapping("/warranty/{lifetimeWarrantyContract}/register-applicant/upgrade")
    public String registerApplicantUpgrade(Model model, @PathVariable DealerView dealer, @PathVariable WarrantyContractView lifetimeWarrantyContract, @Valid ApplicantRegistration applicantRegistration, BindingResult bindingResult,
                                           @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm, RedirectAttributes redirectAttrs, SessionStatus sessionStatus) {

        if(!applicantRegistrationForm.isUpgradeProcessStarted()) {
            return helper.startUpgradeRegistrationOver(lifetimeWarrantyContract, redirectAttrs);
        }
        applicantRegistrationForm.setApplicantRegistration(applicantRegistration);

        if(bindingResult.hasErrors()) {
            helper.handleRegisterApplicantErrors(bindingResult, redirectAttrs);
            return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/" + lifetimeWarrantyContract.getId() + "/register-applicant/upgrade";
        }

        WarrantyContractView warrantyContract = applicantRegistrationForm.toWarrantyContractView(dealer, lifetimeWarrantyContract.getUserId());

        try {
            DealerLinkRequest dealerLinkRequest = DealerLinkRequest.builder()
                .dealerId(dealer.getId())
                .userId(lifetimeWarrantyContract.getUserId())
                .source(sourceCreator.appendSource(null))
                .build();
            dealerLinkClient.create(dealerLinkRequest);

            WarrantyContractView savedWarrantyContract = warrantyService.registerApplicant(warrantyContract);
            log.info("registrationId = {}", savedWarrantyContract.getRegistrationId());

            redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.success("Premium upgrade Registration successful"));
            applicantRegistrationForm.setComplete();
            sessionStatus.setComplete();

            return helper.redirectToConfirmation(lifetimeWarrantyContract);

        } catch(ApplicantRegistrationException e) {
            log.error("error registering applicant for upgrade premium", e);
            redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.error(e.getMessage()));

            return "redirect:/stratus/dealer/" + dealer.getId() + "/warranty/" + lifetimeWarrantyContract.getId() + "/register-applicant/upgrade";
        }
    }

    //shows confirmation messages for any warranties attached to the userId/vin/dealerId,
    ////NOTE: the confirmations page will also  present options to Upgrade existing Lifetime Warranty if applicable / allowed
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @GetMapping(value = "/warranty/confirmations", params = {"userId", "vin"})
    public String registrationConfirmation(@PathVariable DealerView dealer, @RequestParam String userId, @RequestParam String vin, @ModelAttribute ApplicantRegistrationForm applicantRegistrationForm, ModelMap model) {

        List<WarrantyContractView> allWarrantyContractsForUserAndVinAndDealer = new ArrayList<>(warrantyContractClient.findByUserIdAndVinAndDealerId(userId, vin, dealer.getId()).getContent());
        allWarrantyContractsForUserAndVinAndDealer = allWarrantyContractsForUserAndVinAndDealer.stream()
            .filter(wc -> !wc.getRemoteRegistration().getRegistrationStatus().equalsIgnoreCase("VOIDED"))
            .collect(Collectors.toList());
        model.addAttribute("warrantyContracts", allWarrantyContractsForUserAndVinAndDealer);

        if(allWarrantyContractsForUserAndVinAndDealer.size() == 0) {
            return "warranty/confirmations";
        }

        applicantRegistrationForm.startUpgradeProcess(applicantRegistrationForm.getCustomer());

        helper.populateConfirmationsModel(model, dealer, allWarrantyContractsForUserAndVinAndDealer, applicantRegistrationForm);

        return "warranty/confirmations";
    }


    //helper methods
    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @GetMapping(value="/warranty-contract/{warrantyContractId}/pdf")
    public ResponseEntity<byte[]> getWarrantyContractPdf(Model model, @PathVariable("dealer") DealerView dealer, @PathVariable("warrantyContractId") String warrantyContractId){
        return helper.getWarrantyContractPdf(model, dealer, warrantyContractId);
    }

    @PreAuthorize("hasPermission(#dealer, 'warranty:create') or hasAuthority('create:warranty')")
    @PostMapping(value="/warranty/pdf-preview")
    public ResponseEntity<byte[]> getWarrantyContractPdfPreview(Model model, @PathVariable("dealer") DealerView dealer, @Valid ApplicantRegistration applicantRegistration, BindingResult bindingResult){
       return helper.getWarrantyContractPdfPreview(model, dealer, applicantRegistration, bindingResult);
    }

    @PreAuthorize("hasPermission(#dealer, 'warranty:remit') or hasAuthority('remit:warranty')")
    @GetMapping("/warranty/remittance-url")
    public String getRemittanceUrl(@PathVariable DealerView dealer, @SessionAttribute(DealerUserAccessFilter.DEALER_LIST) List<DealerView> dealers, Principal user) {
        return helper.getRemittanceUrl(dealer, dealers, user);
    }

    @PreAuthorize("hasPermission(#dealer, 'warranty:supply_ordering') or hasAuthority('supply_ordering:warranty')")
    @GetMapping("/warranty/supply-order-url")
    public String getSupplyOrdering(@PathVariable DealerView dealer, @SessionAttribute(DealerUserAccessFilter.DEALER_LIST) List<DealerView> dealers, Principal user) {
        return helper.getSupplyOrdering(dealer, dealers, user);
    }

    @GetMapping("/warranty/sample-contracts")
    public String sampleContracts(@PathVariable DealerView dealer) {
        return "warranty/samples";
    }


}
