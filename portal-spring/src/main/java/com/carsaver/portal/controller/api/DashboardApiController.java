package com.carsaver.portal.controller.api;

import com.carsaver.portal.model.store.dealer.dashboard.ClaimedCountWidget;
import com.carsaver.portal.model.store.dealer.dashboard.DistancePieWidget;
import com.carsaver.portal.model.store.dealer.dashboard.LeadCountWidget;
import com.carsaver.portal.model.store.dealer.dashboard.MarketPricedWidget;
import com.carsaver.portal.model.store.dealer.dashboard.PercentWidget;
import com.carsaver.portal.model.store.dealer.dashboard.PricedWidget;
import com.carsaver.portal.model.store.dealer.dashboard.StockTypeCountWidget;
import com.carsaver.portal.model.store.dealer.dashboard.TimeWidget;
import com.carsaver.portal.model.store.dealer.dashboard.WarrantyCountWidget;
import com.carsaver.portal.service.dashboard.DashboardMetricsService;
import com.carsaver.portal.service.dashboard.Program;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/dealers/dashboard")
public class DashboardApiController {

    private final DashboardMetricsService dashboardMetricsService;

    @GetMapping("/searches")
    public StockTypeCountWidget getSearches(@RequestParam String dealerIds, @RequestParam LocalDate startDate, @RequestParam LocalDate endDate) {
        return dashboardMetricsService.getSearches(Collections.singletonList(dealerIds), startDate, endDate, null);
    }

    @GetMapping("/nissan-searches")
    public StockTypeCountWidget getNissanSearches(@RequestParam String dealerIds, @RequestParam LocalDate startDate, @RequestParam LocalDate endDate) {
        return dashboardMetricsService.getSearches(Collections.singletonList(dealerIds), startDate, endDate, Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/vehicle-views")
    public StockTypeCountWidget getVehicleViews(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getVehicleViews(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/nissan-vehicle-views")
    public StockTypeCountWidget getNissanVehicleViews(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getVehicleViews(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/in-garages")
    public StockTypeCountWidget getInGarages(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getInGarages(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/nissan-in-garages")
    public StockTypeCountWidget getNissanInGarages(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getInGarages(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/leads")
    public LeadCountWidget getLeads(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getLeads(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), criteria.getProductId());
    }

    @PostMapping("/claimed")
    public ClaimedCountWidget getClaimed(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getClaimed(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/nissan-claimed")
    public ClaimedCountWidget getNissanClaimed(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getClaimed(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/contacted")
    public ClaimedCountWidget getContacted(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getContacted(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/nissan-contacted")
    public ClaimedCountWidget getNissanContacted(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getContacted(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/sales")
    public StockTypeCountWidget getSales(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getSales(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/nissan-sales")
    public StockTypeCountWidget getNissanSales(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getSales(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/express-sales")
    public StockTypeCountWidget getExpressSales(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getSales(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.EXPRESS);
    }

    @PostMapping("/warranty")
    public WarrantyCountWidget getWarranty(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getWarranty(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/express-warranty")
    public WarrantyCountWidget getExpressWarranty(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getWarranty(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.EXPRESS);
    }

    @PostMapping("/nissan-warranty")
    public WarrantyCountWidget getNissanWarranty(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getWarranty(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/new-in-transit-priced")
    public PricedWidget getNewInTransitPriced(@RequestBody DealerKpiCriteria criteria) {
        return dashboardMetricsService.getNewInTransitPriced(criteria.getDealerIds(), null);
    }

    @PostMapping("/used-in-transit-priced")
    public PricedWidget getUsedInTransitPriced(@RequestBody DealerKpiCriteria criteria) {
        return dashboardMetricsService.getUsedInTransitPriced(criteria.getDealerIds(), null);
    }

    @PostMapping("/new-priced")
    public PricedWidget getNewPriced(@RequestBody DealerKpiCriteria criteria) {
        return dashboardMetricsService.getNewPriced(criteria.getDealerIds(), null);
    }

    @PostMapping("/nissan-new-priced")
    public PricedWidget getNissanNewPriced(@RequestBody DealerKpiCriteria criteria) {
        return dashboardMetricsService.getNewPriced(criteria.getDealerIds(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/used-priced")
    public PricedWidget getUsedPriced(@RequestBody DealerKpiCriteria criteria) {
        return dashboardMetricsService.getUsedPriced(criteria.getDealerIds(), null);
    }

    @PostMapping("/nissan-used-priced")
    public PricedWidget getNissanUsedPriced(@RequestBody DealerKpiCriteria criteria) {
        return dashboardMetricsService.getUsedPriced(criteria.getDealerIds(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/new-market-priced")
    public MarketPricedWidget getNewMarketPriced(@RequestBody DealerKpiCriteria criteria) {
        return dashboardMetricsService.getNewMarketPriced(criteria.getDealerIds(), null);
    }

    @PostMapping("/new-nissan-market-priced")
    public MarketPricedWidget getNewNissanMarketPriced(@RequestBody DealerKpiCriteria criteria) {
        return dashboardMetricsService.getNewMarketPriced(criteria.getDealerIds(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/used-market-priced")
    public MarketPricedWidget getUsedMarketPriced(@RequestBody DealerKpiCriteria criteria) {
        return dashboardMetricsService.getUsedMarketPriced(criteria.getDealerIds(), null);
    }

    @PostMapping("/used-nissan-market-priced")
    public MarketPricedWidget getUsedNissanMarketPriced(@RequestBody DealerKpiCriteria criteria) {
        return dashboardMetricsService.getUsedMarketPriced(criteria.getDealerIds(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/time-to-claimed")
    public TimeWidget getTimeToClaimed(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getTimeToClaimed(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/nissan-time-to-claimed")
    public TimeWidget getNissanTimeToClaimed(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getTimeToClaimed(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/time-to-called")
    public TimeWidget getTimeToCalled(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getTimeToCalled(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/nissan-time-to-called")
    public TimeWidget getNissanTimeToCalled(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getTimeToCalled(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/close-rate")
    public PercentWidget getCloseRate(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getCloseRate(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/nissan-close-rate")
    public PercentWidget getNissanCloseRate(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getCloseRate(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/appointment-close-rate")
    public PercentWidget getAppointmentCloseRate(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getCloseRateByType("APPOINTMENT", criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/nissan-appointment-close-rate")
    public PercentWidget getNissanAppointmentCloseRate(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getCloseRateByType("APPOINTMENT", criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/connection-close-rate")
    public PercentWidget getConnectionCloseRate(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getCloseRateByType("CONNECTION", criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/nissan-connection-close-rate")
    public PercentWidget getNissanConnectionCloseRate(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getCloseRateByType("CONNECTION", criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/price-request-close-rate")
    public PercentWidget getPriceRequestCloseRate(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getCloseRateByType("PRICING_REQUEST", criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/nissan-price-request-close-rate")
    public PercentWidget getNissanPriceRequestCloseRate(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getCloseRateByType("PRICING_REQUEST", criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/distance")
    public DistancePieWidget getDistance(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getDistance(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), null);
    }

    @PostMapping("/nissan-distance")
    public DistancePieWidget getNissanDistance(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getDistance(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @PostMapping("/express-distance")
    public DistancePieWidget getExpressDistance(@RequestBody @Valid DealerKpiDateRangeCriteria criteria) {
        return dashboardMetricsService.getDistance(criteria.getDealerIds(), criteria.getStartDate(), criteria.getEndDate(), Program.NISSAN_BUY_AT_HOME);
    }

    @Data
    public static class DealerKpiCriteria {
        private List<String> dealerIds;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DealerKpiDateRangeCriteria extends DealerKpiCriteria {
        @NotNull
        private LocalDate startDate;
        @NotNull
        private LocalDate endDate;

        private Integer productId;
    }
}
