package com.carsaver.portal.controller.partial;

import com.carsaver.magellan.api.GarageService;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@Slf4j
@RequestMapping("/partial/user/{user}/certificates")
public class OtherVehiclesController {

    @Autowired
    private GarageService garageService;

    @GetMapping("/recent")
    public String recentlyViewedVehicles(@RequestParam(value = "dealerId") DealerView dealer, @PathVariable UserView user, Model model, @PageableDefault(size = 5, sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("Getting recently viewed vehicles by dealer: {} for user: {}", dealer, user.getId());
        Page<CertificateView> recentVehicles = garageService.findRecentVehicleCertificatesByDealerIdAndUserId(dealer.getId(), user.getId(), pageable);

        model.addAttribute("certificates", recentVehicles);
        model.addAttribute("prefix", "recent");
        return "certificate/partial/vehicleList";
    }

    @GetMapping("/saved")
    public String savedVehicles(@RequestParam(value = "dealerId") DealerView dealer, @PathVariable UserView user, Model model, @PageableDefault(size = 5, sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("Getting saved vehicles by dealer: {} for user: {}", dealer, user.getId());
        Page<CertificateView> savedVehicles = garageService.findSavedVehicleCertificatesByDealerIdAndUserId(dealer.getId(), user.getId(), pageable);

        model.addAttribute("certificates", savedVehicles);
        model.addAttribute("prefix", "saved");
        return "certificate/partial/vehicleList";
    }

}
