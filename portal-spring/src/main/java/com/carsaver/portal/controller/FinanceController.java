package com.carsaver.portal.controller;

import com.carsaver.magellan.model.finance.LoanRequestView;
import com.carsaver.portal.elasticsearch.criteria.FinanceSearchCriteria;
import com.carsaver.portal.elasticsearch.facets.FinanceDocFacets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

@Slf4j
@Controller
@RequestMapping("/finance")
public class FinanceController {

    @GetMapping("/{loanRequest}")
    public String find(@PathVariable LoanRequestView loanRequest, Model model) {
        model.addAttribute("loanRequest", loanRequest);
        return "finance/details";
    }

    @PreAuthorize("hasAuthority('read:finance')")
    @GetMapping
    public String list(@ModelAttribute("searchForm") FinanceSearchCriteria searchForm, Model model) {
        model.addAttribute("facets", new FinanceDocFacets());
        return "finance/list";
    }

}
