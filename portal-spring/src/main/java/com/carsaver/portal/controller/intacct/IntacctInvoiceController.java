package com.carsaver.portal.controller.intacct;

import com.carsaver.intacct.client.InvoiceClient;
import com.carsaver.intacct.model.ARInvoice;
import com.carsaver.magellan.model.DealerView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class IntacctInvoiceController {

    @Autowired
    private InvoiceClient invoiceClient;

    @GetMapping("/api/intacct/customers/{dealer}/invoices")
    public ResponseEntity<List<ARInvoice>> invoices(@PathVariable DealerView dealer) {
        return ResponseEntity.ok(invoiceClient.getInvoices(dealer.getDealerId()));
    }

    @GetMapping("/api/intacct/invoices/{recordNo}")
    public ResponseEntity<ARInvoice> invoices(@PathVariable String recordNo) {
        return ResponseEntity.of(invoiceClient.getInvoice(recordNo));
    }
}
