package com.carsaver.portal.controller.search;

import com.carsaver.elasticsearch.model.WalmartStoreDoc;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.portal.elasticsearch.WalmartDocService;
import com.carsaver.portal.elasticsearch.criteria.WalmartSearchCriteria;
import com.carsaver.portal.elasticsearch.facets.WalmartStoreDocFacets;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.SearchResults;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.SortDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/walmart")
@PreAuthorize("hasAuthority('read:walmart')")
public class WalmartSearchController extends SearchController<WalmartSearchCriteria> {

    @Getter(AccessLevel.PROTECTED)
    @Autowired
    private WalmartDocService docService;

    @PostMapping("/scroll")
    public SearchResults<WalmartStoreDoc> scroll(@RequestBody WalmartSearchCriteria searchForm) {
        log.info("Scrolling for {}", searchForm);
        return docService.scroll(searchForm);
    }

    @PostMapping("/search")
    public SearchResults<WalmartStoreDoc> find(@RequestBody WalmartSearchCriteria searchForm, @SortDefault(value = "id") Pageable pageable) {
        log.info("Searching Walmart Stores for {}", searchForm);
        return docService.search(searchForm, PageRequestUtils.asOneBasedPage(pageable));
    }

    @PostMapping(value = "/facet_info", params = "id")
    public FacetInfoResult findFacet(@RequestBody WalmartSearchCriteria searchForm, @RequestParam("id") String facet) {
        return docService.facets(searchForm, WalmartStoreDocFacets.class, facet);
    }

}
