package com.carsaver.portal.controller;

import com.carsaver.elasticsearch.criteria.DealerSearchCriteria;
import com.carsaver.magellan.model.DealerGroupView;
import com.carsaver.portal.elasticsearch.criteria.DealerGroupSearchCriteria;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Collections;

@Slf4j
@Controller
@RequestMapping("/dealerGroup")
public class DealerGroupController {

    @PreAuthorize("hasAuthority('read:dealerGroup')")
    @GetMapping("/{dealerGroup}")
    public String details(@PathVariable DealerGroupView dealerGroup, ModelMap model) {
        DealerSearchCriteria dealerSearchCriteria = DealerSearchCriteria.builder()
            .dealerGroupIds(Collections.singletonList(dealerGroup.getId()))
            .build();
        model.addAttribute("dealerGroup", dealerGroup);
        model.addAttribute("dealerSearchCriteria", dealerSearchCriteria);
        return "dealer-group/details";
    }

    @PreAuthorize("hasAuthority('edit:dealerGroup')")
    @GetMapping(params={"form"})
    public String createForm() {
        return "dealer-group/form";
    }

    @PreAuthorize("hasAuthority('edit:dealerGroup')")
    @GetMapping(value = "/{dealerGroup}", params={"form"})
    public String updateForm(@PathVariable DealerGroupView dealerGroup, ModelMap model) {
        model.addAttribute("dealerGroup", dealerGroup);
        return "dealer-group/form";
    }

    @PreAuthorize("hasAuthority('read:dealerGroup')")
    @GetMapping
    public String find(@ModelAttribute("searchForm") DealerGroupSearchCriteria searchForm) {
        return "dealer-group/list";
    }

}
