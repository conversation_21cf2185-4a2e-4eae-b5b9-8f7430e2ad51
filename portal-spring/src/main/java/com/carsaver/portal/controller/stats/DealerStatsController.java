package com.carsaver.portal.controller.stats;

import com.carsaver.portal.service.DealerStatsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/stats")
public class DealerStatsController {

    @Autowired
    private DealerStatsService statsService;

    @GetMapping("dealer")
    public DealerStatsService.DealerType getStats() {
        return statsService.getStats();
    }
}
