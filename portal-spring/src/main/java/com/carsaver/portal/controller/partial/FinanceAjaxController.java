package com.carsaver.portal.controller.partial;

import com.carsaver.magellan.api.FinanceService;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.finance.LoanRequestView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;
import java.util.stream.Collectors;

@Controller
@Slf4j
public class FinanceAjaxController {

    @Autowired
    private FinanceService financeService;

    @PreAuthorize("hasAuthority('read:finance')")
    @GetMapping("/partial/user/{user}/finance-applications")
    public String userFinanceApplications(@PathVariable UserView user, Model model) {
        List<LoanRequestView> loanRequests = financeService.fetchLoanRequestsByUserId(user.getId());

        // Removing incomplete finance applications
        List<LoanRequestView> filterRequestedLoans = loanRequests.stream()
            .filter(r -> r.getStatus() != 0)
            .collect(Collectors.toList());

        model.addAttribute("loanRequests", filterRequestedLoans);
        log.info("Returning Loan Requests = {}", loanRequests);
        return "finance/fragments :: financeApplications";
    }
}
