package com.carsaver.portal.controller;

import com.carsaver.magellan.client.BasicUserAssociationClient;
import com.carsaver.magellan.filter.UserDetailsFilter;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.TerminalErrorType;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.user.BasicDealerUserRoleAssociation;
import com.carsaver.portal.filter.DealerUserAccessFilter;
import com.carsaver.portal.security.SecurityUtils;
import com.carsaver.portal.util.PermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.SessionAttribute;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

@Slf4j
@Controller
public class HomeController {

    @Autowired
    private BasicUserAssociationClient basicUserAssociationClient;

    @RequestMapping("/")
    public String loginLanding(HttpServletRequest request, @SessionAttribute(UserDetailsFilter.USER_KEY) UserView user, @SessionAttribute(DealerUserAccessFilter.DEALER_LIST) List<DealerView> dealers) {
        if (SecurityUtils.isAdminUser()) {
            return "redirect:/home";
        } else if (SecurityUtils.isDealerUser()) {
            Optional<DealerView> dealerOptional = getIfOnlyOne(dealers);
            if (dealerOptional.isPresent()) {
                DealerView dealer = dealerOptional.get();
                Optional<BasicDealerUserRoleAssociation> privileges = basicUserAssociationClient.getBasicUserAssociation(dealer.getId(), user.getId());
                if (privileges.isPresent() && PermissionUtils.hasOnlyCheckinPermission(privileges.get())) {
                    return "redirect:/stratus/dealer/" + dealer.getId() + "/checkin";
                } else {
                    log.info("User has more privileges, not auto selecting check-in");
                    return "redirect:/stratus/dealer/" + dealer.getId();
                }
            } else {
                log.info("User has more than one dealer, not auto selecting");
                return "redirect:/stratus";
            }
        }

        new SecurityContextLogoutHandler().logout(request, null, null);

        return "redirect:/login?error";
    }

    @RequestMapping("/welcome")
    public String welcome() {
        return "welcome";
    }

    @RequestMapping("/home")
    public String home() {
//        model.addAttribute("activeDealerCount", dealerService.getActiveDealerCount());
//        model.addAttribute("userCount", userService.getUserCount());
        return "home";
    }

    private <T> Optional<T> getIfOnlyOne(List<T> collection) {
        if (collection != null && collection.size() == 1) {
            return Optional.ofNullable(collection.get(0));
        }
        return Optional.empty();
    }

    @RequestMapping("/error-codes")
    public String errorCodes(ModelMap model) {

        model.addAttribute("errorCodeTypes", TerminalErrorType.values());

        return "fragments/errorCodeReferenceModal";
    }
}
