package com.carsaver.portal.controller;

import com.carsaver.magellan.api.PartnerService;
import com.carsaver.magellan.model.WalmartStoreView;
import com.carsaver.portal.domain.PortalAnnouncement;
import com.carsaver.portal.domain.WalmartStoreSearchForm;
import com.carsaver.portal.elasticsearch.criteria.WalmartSearchCriteria;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

@Slf4j
@Controller
@RequestMapping(WalmartStoreController.BASE_MAPPING)
public class WalmartStoreController {

    public static final String BASE_MAPPING = "/walmart";

    @Autowired
    private PartnerService partnerService;

    @GetMapping
    public String list(@ModelAttribute("searchForm") WalmartSearchCriteria searchForm) {
        return "walmart/list";
    }

    @GetMapping("/{walmartStore}")
    public String details(@PathVariable WalmartStoreView walmartStore, @ModelAttribute("searchForm") WalmartStoreSearchForm searchForm, ModelMap model, RedirectAttributes redirectAttrs) {

        if (walmartStore == null) {
            redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.error( "No Walmart Store Found with that ID"));
            return "redirect:/walmart";
        }

        model.addAttribute("walmart", walmartStore);

        return "walmart/details";
    }

    @GetMapping("/{walmartStore}/close")
    public String close(@PathVariable WalmartStoreView walmartStore, RedirectAttributes redirectAttrs) {
        partnerService.setStatus(walmartStore, WalmartStoreView.Status.NO_STORE);

        return redirectToWalmartDetails(walmartStore, redirectAttrs, PortalAnnouncement.success("Store marked closed"));
    }

    @PostMapping("/{walmartStore}/open")
    public String open(@PathVariable WalmartStoreView walmartStore, @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam("dateOpened") LocalDate dateOpened, RedirectAttributes redirectAttrs) {

        ZonedDateTime dateTimeOpened = ZonedDateTime.of(dateOpened, LocalTime.NOON, ZoneId.systemDefault());
        partnerService.setStatusToOpen(walmartStore, dateTimeOpened);

        return redirectToWalmartDetails(walmartStore, redirectAttrs, PortalAnnouncement.success("Store marked open"));
    }

    @GetMapping("/{walmartStore}/coming")
    public String comingSoon(@PathVariable WalmartStoreView walmartStore, RedirectAttributes redirectAttrs) {
        partnerService.setStatus(walmartStore, WalmartStoreView.Status.COMING_SOON);

        return redirectToWalmartDetails(walmartStore, redirectAttrs, PortalAnnouncement.success("Store marked coming soon"));
    }



    private String redirectToWalmartDetails(WalmartStoreView walmart, RedirectAttributes redirectAttrs, PortalAnnouncement portalAnnouncement) {
        redirectAttrs.addFlashAttribute("globalMessage", portalAnnouncement);
        return "redirect:/walmart/" + walmart.getId();
    }
}
