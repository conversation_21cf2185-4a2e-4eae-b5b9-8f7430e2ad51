package com.carsaver.portal.controller;

import com.carsaver.magellan.api.UserService;
import com.carsaver.magellan.client.WarrantyContractClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.WarrantyContractView;
import com.carsaver.magellan.validation.UserValidator;
import com.carsaver.portal.domain.PortalAnnouncement;
import com.carsaver.stereotype.State;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.validation.Valid;
import java.util.Optional;

@Slf4j
@Controller
@RequestMapping("/warranty/{warrantyContract}/user")
public class WarrantyUserController {

    @Autowired
    private UserService userService;

    @Autowired
    private UserValidator userValidator;

    @Autowired
    private WarrantyContractClient warrantyContractClient;


    //capture elements common to every request in model
    @ModelAttribute("warrantyContract")
    public void addDefaults(@PathVariable WarrantyContractView warrantyContract, ModelMap model) {
        model.addAttribute("warrantyContract", warrantyContract);
    }

    /*
     * NOTE: we currently must specify the value/name of the model attribute
     * for @InitBinder or undesired validation takes place in the list() method
     * @param binder
     */
    @InitBinder(value = "warrantyUser")
    public void initWarrantyUserBinder(WebDataBinder binder) {
        binder.addValidators(userValidator);
    }


    @GetMapping(params={"form"})
    public String createForm(ModelMap model) {
        WarrantyContractView warrantyContract = getWarrantyContract(model);

        UserView user = new UserView();

        Optional.ofNullable(warrantyContract.getRemoteRegistration()).ifPresent(registration ->
            model.addAttribute("emailQuery", registration.getCustomerEmailAddress())
        );

        user.setInternalSms(true); //default to true on create form

        return renderCreateForm(model, user, false);
    }

    /**
     * @param userView
     * @param model
     * @return
     */
    @PostMapping
    public String create(@ModelAttribute("warrantyUser") @Valid UserView userView,
                         BindingResult bindingResult, ModelMap model, RedirectAttributes redirectAttrs) {

        if(bindingResult.hasErrors()) {
            log.error("{}", bindingResult);
            model.addAttribute("globalMessage", PortalAnnouncement.error( "Uh oh, please double check the user you are adding has the required information"));
            model.addAttribute("hideAlert", true);
            return renderCreateForm(model, userView, true);
        }

        userView.setType(UserView.TYPE_USER);

        //use phonenumber as password temporarily
        if(StringUtils.isBlank(userView.getPhoneNumber())) {
            throw new IllegalArgumentException("warrantyUser phoneNumber cannot be null");
        }
        userView.setPassword(userView.getPhoneNumber().replaceAll("[^0-9]+",""));

        UserView createdUser = userService.saveUser(userView);

        warrantyContractClient.refreshWarrantyUser(getWarrantyContractId(model), createdUser.getId());

        return redirectToWarrantyDetails(model, redirectAttrs, "New user associated with warranty");
    }

//    @GetMapping(value = "/search-existing", params={"q"} )
//    public String searchExisting(@RequestParam("q") String emailQuery, ModelMap model) {
//        UserView warrantyUser = userService.findUserByEmail(emailQuery);
//
//        model.addAttribute("emailQuery", emailQuery);
//        model.addAttribute("existingWarrantyUser", warrantyUser);
//
//        UserView user = new UserView();
//        Optional.ofNullable(getWarrantyContract(model).getRemoteRegistration()).ifPresent(registration -> {
//           if(emailQuery.equalsIgnoreCase(registration.getCustomerEmailAddress())) { //if they have searched with registration email, populate defaults that are available
//               user.setFirstName(registration.getCustomerFirstName());
//               user.setLastName(registration.getCustomerLastName());
//               State.findAbbreviationByName(registration.getCustomerState()).ifPresent(user::setStateCode);
//               user.setZipCode(registration.getCustomerZip());
//               user.setPhoneNumber(ObjectUtils.firstNonNull(registration.getCustomerHomePhone(), registration.getCustomerWorkPhone()));
//               user.setCity(registration.getCustomerCity());
//               user.setAddress(registration.getCustomerStreetAddress());
//           }
//        });
//
//        user.setEmail(emailQuery);
//        user.setInternalSms(true);
//
//        return renderCreateForm(model, user,true);
//    }

    @PostMapping(value = "/{user}/associate-existing" )
    public String associateExisting(@PathVariable("user") UserView user, ModelMap model, RedirectAttributes redirectAttrs) {

        warrantyContractClient.refreshWarrantyUser(getWarrantyContractId(model), user.getId());

        return redirectToWarrantyDetails(model, redirectAttrs, "Existing user associated with warranty");
    }

    private String renderCreateForm(ModelMap model, UserView user, boolean searchAttempted) {
        model.addAttribute("warrantyUser", user);
        model.addAttribute("method", "post" );
        model.addAttribute("action", "/warranty/" + getWarrantyContractId(model) + "/user");
        model.addAttribute("searchAttempted", searchAttempted);
        model.addAttribute("availableStates", State.getStates());

        return "warranty/user/add";
    }

    private String redirectToWarrantyDetails(ModelMap model, RedirectAttributes redirectAttrs, String msg) {
        redirectAttrs.addFlashAttribute("globalMessage", PortalAnnouncement.success(msg));
        return "redirect:" + "/warranty/" + getWarrantyContractId(model);
    }

    private WarrantyContractView getWarrantyContract(ModelMap model) {
        return (WarrantyContractView) model.get("warrantyContract");
    }

    private String getWarrantyContractId(ModelMap model) {
        return getWarrantyContract(model).getId();
    }

}
