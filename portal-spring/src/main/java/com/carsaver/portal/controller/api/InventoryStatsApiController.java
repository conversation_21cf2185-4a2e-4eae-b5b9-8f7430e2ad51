package com.carsaver.portal.controller.api;

import com.carsaver.core.StockType;
import com.carsaver.portal.model.store.dealer.dashboard.PricedWidget;
import com.carsaver.portal.service.dashboard.DashboardVehicleDocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/inventory/stats")
public class InventoryStatsApiController {

    @Autowired
    private DashboardVehicleDocService dashboardVehicleDocService;

    @GetMapping("/new-priced")
    public PricedWidget getNewPriced() {
        return dashboardVehicleDocService.getPriced(StockType.NEW);
    }

    @GetMapping("/used-priced")
    public PricedWidget getUsedPriced() {
        return dashboardVehicleDocService.getPriced(StockType.USED);
    }

    @GetMapping("/all-priced")
    public PricedWidget getAllPriced() {
        return dashboardVehicleDocService.getPriced(null);
    }
}
