package com.carsaver.portal.controller.api;

import com.carsaver.core.DealerStatus;
import com.carsaver.core.SalesTxSource;
import com.carsaver.elasticsearch.criteria.DealerSearchCriteria;
import com.carsaver.elasticsearch.service.DealerImportMetricDocService;
import com.carsaver.portal.model.metrics.DealerImportMetric;
import com.carsaver.search.support.SearchResults;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.SortDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


@Slf4j
@RestController
@RequestMapping("/api")
public class ImportMetricsApiController {

    @Getter(AccessLevel.PROTECTED)
    @Autowired
    private DealerImportMetricDocService docService;

    @PreAuthorize("hasAuthority('read:dealer')")
    @PostMapping("/import-metrics/search")
    public SearchResults<DealerImportMetric> find(@RequestBody DealerSearchCriteria searchForm, @SortDefault(value = "name") Pageable pageable) {

        log.info("Searching Dealer Import Metrics for {}", searchForm);

        return docService.search(searchForm, pageable)
            .map(dealerImportMetric -> {

                return DealerImportMetric.builder()
                    .id(dealerImportMetric.getId())
                    .dealerId(dealerImportMetric.getDealerId())
                    .name(dealerImportMetric.getName())
                    .lastContainedInReportOn(dealerImportMetric.getLastContainedInReportOn())
                    .lastReportSaleCount(dealerImportMetric.getLastReportSaleCount())
                    .modifiedDate(dealerImportMetric.getModifiedDate())
                    .mostRecentSaleDate(dealerImportMetric.getMostRecentSaleDate())
                    .createdDate(dealerImportMetric.getCreatedDate())
                    .salesSource(dealerImportMetric.getSalesSource())
                    .status(dealerImportMetric.getStatus())
                    .build();
            });
    }

    @GetMapping("/import-metrics/dealer-statuses")
    public DealerStatus[] getDealerStatuses() {
        return DealerStatus.values();
    }

    @GetMapping("/import-metrics/sales-sources")
    public SalesTxSource[] getSalesTxSources() {
        return SalesTxSource.values();
    }
}
