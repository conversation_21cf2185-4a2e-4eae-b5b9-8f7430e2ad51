package com.carsaver.portal.controller.search;

import com.carsaver.elasticsearch.criteria.DealerSearchCriteria;
import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.portal.client.campaign.CampaignClient;
import com.carsaver.portal.client.campaign.CampaignView;
import com.carsaver.portal.elasticsearch.DealerDocService;
import com.carsaver.portal.elasticsearch.facets.DealerDocFacets;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.SearchResults;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.SortDefault;
import org.springframework.hateoas.CollectionModel;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/api/dealers")
public class DealerSearchController extends SearchController<DealerSearchCriteria> {

    private final CampaignClient campaignClient;

    @Getter(AccessLevel.PROTECTED)
    @Autowired
    private DealerDocService docService;

    @PostMapping("/scroll")
    public SearchResults<DealerDoc> scroll(@RequestBody DealerSearchCriteria searchForm) {
        log.info("Scrolling for {}", searchForm);
        return docService.scroll(searchForm);
    }

    @PreAuthorize("hasAuthority('read:dealer')")
    @PostMapping("/search")
    public SearchResults<DealerDoc> find(@RequestBody com.carsaver.portal.elasticsearch.criteria.DealerSearchCriteria searchForm, @SortDefault(value = "name") Pageable pageable) {
        log.info("Searching Dealers for {}", searchForm);
        //TODO: include the tenantId and transform to program
        if(StringUtils.hasText(searchForm.getTenantId())) {
            CollectionModel<CampaignView> campaigns = campaignClient.findByTenantId(searchForm.getTenantId());
            if (campaigns != null && !campaigns.getContent().isEmpty()) {
                Set<String> programIds = campaigns.getContent().stream().map(CampaignView::getProgramId).collect(Collectors.toSet());
                searchForm.setSubscriptionProgramIds(new ArrayList<>(programIds));
            }
        }
        return docService.search(searchForm, PageRequestUtils.asOneBasedPage(pageable));
    }

    @PreAuthorize("hasAuthority('read:dealer')")
    @PostMapping(value = "/facet_info", params = "id")
    public FacetInfoResult findFacet(@RequestBody DealerSearchCriteria searchForm, @RequestParam("id") String facet) {
        log.info("Searching Dealers Facet {} by {}", facet, searchForm);
        return docService.facets(searchForm, DealerDocFacets.class, facet);
    }

}
