package com.carsaver.portal.controller.intacct;

import com.carsaver.intacct.client.CustomerClient;
import com.carsaver.intacct.model.Address;
import com.carsaver.intacct.model.Contact;
import com.carsaver.intacct.model.Customer;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.portal.model.billing.BillingCustomerForm;
import com.carsaver.stereotype.State;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Optional;

@Slf4j
@RestController
public class IntacctCustomerController {

    @Autowired
    private CustomerClient customerClient;

    @Autowired
    private DealerClient dealerClient;

    @PreAuthorize("hasAuthority('read:dealer')")
    @GetMapping("/api/intacct/customers/{dealer}")
    public Optional<Customer> findCustomer(@PathVariable DealerView dealer) {
        return customerClient.getCustomer(dealer.getDealerId());
    }

    @PreAuthorize("hasAuthority('edit:dealer')")
    @PostMapping("/api/intacct/customers")
    public ResponseEntity createCustomer(@RequestBody @Valid BillingCustomerForm customerForm) {
        DealerView dealer = dealerClient.findById(customerForm.getDealerId());
        Optional<Customer> customerOpt = Optional.ofNullable(dealer)
            .map(DealerView::getDealerId)
            .flatMap(customerClient::getCustomer);

        if(customerOpt.isPresent()) {
            return ResponseEntity.of(customerOpt);
        }

        if(dealer != null) {
            Customer customer = new Customer();
            customer.setName(dealer.getName());
            customer.setCustomerId(dealer.getDealerId());
            customer.setDeliveryOptions(Customer.DELIVERY_BOTH);

            Contact contact = new Contact();
            contact.setFirstName(customerForm.getFirstName());
            contact.setLastName(customerForm.getLastName());
            contact.setEmail1(customerForm.getEmail());
            contact.setPhone1(customerForm.getPhoneNumber());

            Address address = new Address();
            address.setAddress1(customerForm.getAddress().getLine1());
            address.setAddress2(customerForm.getAddress().getLine2());
            address.setCity(customerForm.getAddress().getCity());
            address.setState(State.findAbbreviation(customerForm.getAddress().getState()).orElse(customerForm.getAddress().getState()));
            address.setZip(customerForm.getAddress().getPostalCode());

            contact.setMailAddress(address);
            customer.setContact(contact);

            return ResponseEntity.of(customerClient.createCustomer(customer));
        }

        return ResponseEntity.notFound().build();
    }

}
