package com.carsaver.portal.controller.search;

import com.carsaver.elasticsearch.criteria.QuantumLeadSearchCriteria;
import com.carsaver.elasticsearch.model.quantum.QuantumLeadDoc;
import com.carsaver.elasticsearch.service.QuantumLeadDocService;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.portal.elasticsearch.facets.QuantumLeadDocFacets;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.SearchResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.SortDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/quantum-leads")
public class QuantumLeadSearchController {

    private final QuantumLeadDocService docService;

    public QuantumLeadSearchController(QuantumLeadDocService docService) {
        this.docService = docService;
    }

    @PreAuthorize("hasAuthority('read:lead')")
    @PostMapping("/search")
    public SearchResults<QuantumLeadDoc> find(@RequestBody QuantumLeadSearchCriteria searchForm, @SortDefault(value = "createdDate") Pageable pageable) {
        SearchResults<QuantumLeadDoc> results = docService.search(searchForm, PageRequestUtils.asOneBasedPage(pageable));
        return results;
    }

    @PreAuthorize("hasAuthority('read:lead')")
    @PostMapping(value = "/facet_info", params = "id")
    public FacetInfoResult findFacet(@RequestBody QuantumLeadSearchCriteria searchForm, @RequestParam("id") String facet) {
        return docService.facets(searchForm, QuantumLeadDocFacets.class, facet);
    }
}
