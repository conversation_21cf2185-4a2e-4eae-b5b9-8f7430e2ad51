package com.carsaver.portal.controller.api;

import com.carsaver.core.StockType;
import com.carsaver.magellan.api.ImportJobService;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.ImportCarLogView;
import com.carsaver.magellan.model.YearMakeModelsView;
import com.carsaver.magellan.search.ImportCarLogSearchCriteria;
import com.carsaver.magellan.search.PriceAdjustmentSearchCriteria;
import com.carsaver.portal.model.vehiclePricing.NewVehiclePricingForm;
import com.carsaver.portal.model.vehiclePricing.PricedCarRow;
import com.carsaver.portal.model.vehiclePricing.PricedStyleRow;
import com.carsaver.portal.model.vehiclePricing.UsedVehiclePricingForm;
import com.carsaver.portal.service.VehiclePricingService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/stratus/dealer/{dealer}/vehicle-pricing")
public class VehiclePricingApiController {

    @Autowired
    private VehiclePricingService vehiclePricingService;

    @Autowired
    private ImportJobService importJobService;

    @GetMapping
    public ResponseEntity<DealerView> getDealer(@PathVariable DealerView dealer) {
        return ResponseEntity.ok(dealer);
    }

    @DeleteMapping("/{stockType}/pricing")
    public ResponseEntity deletePricingRules(@PathVariable DealerView dealer, @PathVariable StockType stockType) {
        vehiclePricingService.deletePricingRules(dealer, stockType);

        return ResponseEntity.ok().build();
    }

    @GetMapping("/{stockType}/ymm")
    public ResponseEntity<YearMakeModelsView> getYmmForDealerAndStockType(@PathVariable DealerView dealer, @PathVariable StockType stockType) {
        return ResponseEntity.ok(vehiclePricingService.getYmmForDealerAndStockType(dealer, stockType));
    }


    @PreAuthorize("hasPermission(#dealer, 'inventory:new-pricing:read') or hasAuthority('read:pricing-new')")
    @GetMapping("/new/form")
    public NewVehiclePricingForm prepareNewVehiclePricingModel(@PathVariable DealerView dealer, PriceAdjustmentSearchCriteria searchForm) {
        NewVehiclePricingForm newVehiclePricingForm = new NewVehiclePricingForm();
        newVehiclePricingForm.setSearchForm(searchForm);

        return vehiclePricingService.prepareNewVehiclePricingForm(dealer, newVehiclePricingForm);
    }

    @PreAuthorize("hasPermission(#dealer, 'inventory:new-pricing:read') or hasAuthority('read:pricing-new')")
    @GetMapping(value = "/styles", params = {"modelId"})
    public List<PricedStyleRow> getStyleWithPriceAdjustmentsFor(@PathVariable DealerView dealer,
                                                                @RequestParam Integer modelId,
                                                                @RequestParam(required=false) Integer pricingFormStyleFilter,
                                                                @RequestParam(required=false) Boolean active) {

        return vehiclePricingService.getStyleWithPriceAdjustmentsFor(dealer, modelId, pricingFormStyleFilter, active);
    }

    @PreAuthorize("hasPermission(#dealer, 'inventory:new-pricing:read') or hasAuthority('read:pricing-new')")
    @GetMapping(value = "/new/cars", params = {"styleId"})
    public List<PricedCarRow> getNewCarWithPriceAdjustmentsFor(@PathVariable DealerView dealer,
                                                               @RequestParam Integer styleId,
                                                               @RequestParam(required=false) String pricingFormInventoryIdFilter,
                                                               @RequestParam(required=false) Boolean active) {

        return vehiclePricingService.getNewCarWithPriceAdjustmentsFor(dealer, styleId, pricingFormInventoryIdFilter, active);
    }

    @PreAuthorize("hasPermission(#dealer, 'inventory:new-pricing:edit') or hasAuthority('edit:pricing-new')")
    @PostMapping("/new")
    public NewVehiclePricingForm updateNewPriceAdjustments(@PathVariable DealerView dealer, @RequestBody @Valid NewVehiclePricingForm newVehiclePricingForm) {
        return vehiclePricingService.updateNewPriceAdjustments(dealer, newVehiclePricingForm);
    }

    @PreAuthorize("hasPermission(#dealer, 'inventory:used-pricing:read') or hasAuthority('read:pricing-used')")
    @GetMapping("/used/form")
    public UsedVehiclePricingForm prepareUsedVehiclePricingModel(@PathVariable DealerView dealer, PriceAdjustmentSearchCriteria searchForm) {
        UsedVehiclePricingForm usedVehiclePricingForm = new UsedVehiclePricingForm();
        usedVehiclePricingForm.setSearchForm(searchForm);

        return vehiclePricingService.prepareUsedVehiclePricingForm(dealer, usedVehiclePricingForm);
    }

    @PreAuthorize("hasPermission(#dealer, 'inventory:used-pricing:edit') or hasAuthority('edit:pricing-used')")
    @PostMapping(value = "/used")
    public UsedVehiclePricingForm updatedUsedPriceAdjustments(@PathVariable DealerView dealer, @RequestBody @Valid UsedVehiclePricingForm usedVehiclePricingForm) {
        return vehiclePricingService.updatedUsedPriceAdjustments(dealer, usedVehiclePricingForm);
    }

    @PreAuthorize("hasPermission(#dealer, 'inventory:used-pricing:read') or hasPermission(#dealer, 'inventory:new-pricing:read') or hasAuthority('read:pricing-used') or hasAuthority('read:pricing-new')")
    @GetMapping("/metrics/{stockType}")
    public ResponseEntity<Long> getInventoryMetricsByStockType(@PathVariable DealerView dealer, @PathVariable StockType stockType, @RequestParam String type) {
        ImportCarLogSearchCriteria searchCriteria = new ImportCarLogSearchCriteria();
        searchCriteria.setType(type);
        searchCriteria.setStockType(stockType);

        Page<ImportCarLogView> carLogs = importJobService.getImportCarLogsByDealer(dealer, searchCriteria, PageRequestUtils.maxSizeRequest());

        return ResponseEntity.ok().body(carLogs.getTotalElements());
    }

    @GetMapping("/metrics")
    public ResponseEntity<ImportMetrics> getInventoryMetrics(@PathVariable DealerView dealer) {
        ImportMetrics metrics = new ImportMetrics();

        metrics.setInvalidUsedCount(getCountForSearchCriteria(dealer, "PRICING_ERROR", StockType.USED));
        metrics.setInvalidNewCount(getCountForSearchCriteria(dealer, "PRICING_ERROR", StockType.NEW));
        metrics.setDuplicateUsedCount(getCountForSearchCriteria(dealer, "DUPLICATE", StockType.USED));
        metrics.setDuplicateNewCount(getCountForSearchCriteria(dealer, "DUPLICATE", StockType.NEW));

        return ResponseEntity.ok().body(metrics);
    }

    private Long getCountForSearchCriteria(DealerView dealer, String type, StockType stockType) {
        ImportCarLogSearchCriteria searchCriteria = new ImportCarLogSearchCriteria();
        searchCriteria.setType(type);
        searchCriteria.setStockType(stockType);

        // only count import car logs from today
        return importJobService.getImportCarLogsByDealer(dealer, searchCriteria, PageRequestUtils.maxSizeRequest())
               .stream().count();
    }

    @Data
    public static class ImportMetrics {
        private Long invalidUsedCount;
        private Long invalidNewCount;
        private Long duplicateUsedCount;
        private Long duplicateNewCount;
    }
}
