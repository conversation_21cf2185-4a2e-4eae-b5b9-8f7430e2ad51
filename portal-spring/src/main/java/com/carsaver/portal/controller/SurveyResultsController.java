package com.carsaver.portal.controller;

import com.carsaver.portal.elasticsearch.criteria.SurveyResultsSearchCriteria;
import com.carsaver.portal.elasticsearch.facets.SurveyResultsDocFacets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;

@Slf4j
@Controller
@RequestMapping("/survey-results")
public class SurveyResultsController {

    @GetMapping
    public String list(@ModelAttribute("searchForm") SurveyResultsSearchCriteria searchForm, Model model) {
        model.addAttribute("facets", new SurveyResultsDocFacets());
        return "survey-results/list";
    }

}
