package com.carsaver.portal.controller.api;

import com.carsaver.magellan.client.IndexSearchFilterClient;
import com.carsaver.magellan.model.IndexSearchFilterView;
import com.carsaver.portal.model.IndexSearchFilterForm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/index-search-filters")
public class IndexSearchFilterController {

    @Autowired
    private IndexSearchFilterClient indexSearchFilterClient;

    @PostMapping
    public ResponseEntity<IndexSearchFilterView> create(@RequestBody @Valid IndexSearchFilterForm form) {
        var indexSearchFilterView = new IndexSearchFilterView();
        indexSearchFilterView.setUserId(form.getUserId());
        indexSearchFilterView.setEntityScope(form.getEntityScope());
        indexSearchFilterView.setName(form.getName());
        indexSearchFilterView.setFilter(form.getFilter());

        return ResponseEntity.ok(indexSearchFilterClient.save(indexSearchFilterView));
    }

    @GetMapping("/user/{id}")
    public ResponseEntity<List<IndexSearchFilterView>> getSavedFiltersByUserAndEntity(@PathVariable("id") String userId, @RequestParam("entityScope") String entityScope) {
        if(StringUtils.isEmpty(userId) || StringUtils.isEmpty(entityScope)) {
            return ResponseEntity.badRequest().build();
        }

        var savedFilters = new ArrayList<>(indexSearchFilterClient.getByUserAndEntityScope(userId, entityScope).getContent());

        return ResponseEntity.ok(savedFilters);
    }

    @PostMapping("{id}")
    public void deleteById(@PathVariable("id") String id) {
        indexSearchFilterClient.deleteById(id);
    }
}
