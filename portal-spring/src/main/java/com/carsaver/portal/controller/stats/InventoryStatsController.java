package com.carsaver.portal.controller.stats;

import com.carsaver.portal.service.InventoryStatsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/stats")
public class InventoryStatsController {

    @Autowired
    private InventoryStatsService inventoryStatsService;

    @GetMapping("/inventory")
    public InventoryStatsService.InventoryStats getStats() {
        return inventoryStatsService.getInventoryStats();
    }
}
