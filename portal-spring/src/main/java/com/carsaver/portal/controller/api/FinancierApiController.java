package com.carsaver.portal.controller.api;

import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.portal.client.financier.FinancierClient;
import com.carsaver.portal.client.financier.FinancierView;
import com.carsaver.portal.model.financier.FinancierForm;
import com.carsaver.portal.util.Response;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.hateoas.PagedModel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Validated
@RestController
@RequestMapping("/api/financiers")
public class FinancierApiController {

    @Autowired
    private FinancierClient financierClient;

    @GetMapping
    public PageWrapper fetchAllFinanciers(Pageable pageable) {
        if (pageable.getPageNumber() == 0) {
            pageable = PageRequestUtils.maxSizeRequest();
        }
        PagedModel<FinancierView> financiers = financierClient.findAll(PageRequestUtils.asOneBasedPage(pageable));

        return PageWrapper.builder()
            .financierViews(new ArrayList<>(financiers.getContent()))
            .page(financiers.getMetadata())
            .build();
    }

    @PostMapping
    public ResponseEntity<Response> addFinancier(@Valid @RequestBody FinancierForm financierForm) {
        var financier = new FinancierView();
        BeanUtils.copyProperties(financierForm, financier);
        financierClient.save(financier);
        return new ResponseEntity<>(new Response("Program has been added successfully"), HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public FinancierView fetchFinancier(@PathVariable("id") Long id) {
        return financierClient.findById(id);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Response> updateFinancier(@PathVariable("id") Long id, @Valid @RequestBody FinancierForm financierForm) {
        FinancierView financierView = financierClient.findById(id);
        financierView.setName(financierForm.getName());
        financierView.setRouteOneId(financierForm.getRouteOneId());
        financierView.setHorizonId(financierForm.getHorizonId());
        financierView.setPayoffPhoneNumber(financierForm.getPayoffPhoneNumber());
        financierView.setEnabled(financierForm.getEnabled());
        financierView.setConfig(financierForm.getConfig());
        financierClient.save(financierView);
        return new ResponseEntity<>(new Response("Financier has been updated successfully"), HttpStatus.OK);
    }

    @GetMapping("/search")
    public PageWrapper fetchFinanciersByName(@RequestParam("name") String name, Pageable pageable) {
        PagedModel<FinancierView> financiers = financierClient.findByFinancierName(name, PageRequestUtils.asOneBasedPage(pageable));

        return PageWrapper.builder()
            .financierViews(new ArrayList<>(financiers.getContent()))
            .page(financiers.getMetadata())
            .build();
    }

    @Data
    @Builder
    static class PageWrapper {
        private PagedModel.PageMetadata page;
        private List<FinancierView> financierViews;
    }
}
