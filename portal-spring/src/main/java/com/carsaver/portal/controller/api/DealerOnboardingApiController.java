package com.carsaver.portal.controller.api;

import com.carsaver.core.DealerStatus;
import com.carsaver.core.InventorySource;
import com.carsaver.core.PaymentStructure;
import com.carsaver.core.SalesTxSource;
import com.carsaver.magellan.api.VehicleService;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.chrome.MakeView;
import com.carsaver.magellan.model.dealer.CrmSystemView;
import com.carsaver.magellan.model.dealer.DmsSystemView;
import com.carsaver.magellan.model.dealer.FinanceSystem;
import com.carsaver.magellan.model.preferences.DealerPreferences;
import com.carsaver.magellan.util.TimeZoneUtils;
import com.carsaver.portal.elasticsearch.DealerGroupDocService;
import com.carsaver.portal.elasticsearch.criteria.DealerGroupSearchCriteria;
import com.carsaver.portal.model.dealer.ContractModel;
import com.carsaver.portal.model.dealer.DealerCreationModel;
import com.carsaver.portal.model.dealer.DealerPreferencesModel;
import com.carsaver.portal.model.dealer.FinanceIntegrationModel;
import com.carsaver.portal.model.dealer.InventoryConfigurationModel;
import com.carsaver.portal.model.dealer.LeadConfigurationModel;
import com.carsaver.portal.model.dealer.MakesConfigurationModel;
import com.carsaver.portal.model.dealer.SalesMatchingModel;
import com.carsaver.portal.service.DealerInventoryService;
import com.carsaver.stereotype.State;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequestMapping("/api/on-boarding")
public class DealerOnboardingApiController {

    @Autowired
    private DealerClient dealerClient;

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private Environment environment;

    @Autowired
    private DealerGroupDocService dealerGroupService;

    @Autowired
    private DealerInventoryService dealerInventoryService;

    @GetMapping("/{dealer}")
    public DealerView getDealer(@PathVariable DealerView dealer) {
        return dealer;
    }

    @PostMapping("/dealer-creation")
    public ResponseEntity<DealerView> createDealer(@RequestBody DealerCreationModel form) {

        var dealer = new DealerView();
        BeanUtils.copyProperties(form, dealer);

        dealer.setDealerStatus(DealerStatus.PROSPECT);

        dealer = dealerClient.save(dealer);

        return ResponseEntity.ok(dealer);
    }

    @PatchMapping("/{dealerId}/update-details")
    public ResponseEntity<DealerView> updateDetails(@PathVariable String dealerId, @RequestBody DealerCreationModel form) {
        DealerView updatedDealer = dealerClient.update(dealerId, form);
        return ResponseEntity.ok(updatedDealer);
    }

    @PostMapping("/{dealerId}/contract")
    public ResponseEntity<DealerView> contractDetailsStep(@PathVariable String dealerId, @RequestBody ContractModel form) {

        DealerView dealer = dealerClient.update(dealerId, form);

        return ResponseEntity.ok(dealer);
    }

    @PostMapping("/{dealerId}/finance-integration")
    public ResponseEntity<DealerView> financeIntegrationStep(@PathVariable String dealerId, @RequestBody FinanceIntegrationModel form) {

        DealerView dealer = dealerClient.update(dealerId, form);

        return ResponseEntity.ok(dealer);
    }

    @PostMapping("/{dealerId}/sales-matching")
    public ResponseEntity<DealerView> salesMatchingStep(@PathVariable String dealerId, @RequestBody SalesMatchingModel form) {

        DealerView dealer = dealerClient.update(dealerId, form);

        return ResponseEntity.ok(dealer);
    }

    @PostMapping("/{dealerId}/inventory-configuration")
    public ResponseEntity<DealerView> inventoryConfigurationStep(@PathVariable String dealerId, @RequestBody InventoryConfigurationModel form) {

        DealerView dealer = dealerClient.update(dealerId, form);

        return ResponseEntity.ok(dealer);
    }

    @PostMapping("/{dealerId}/lead-configuration")
    public ResponseEntity<DealerView> leadConfigurationStep(@PathVariable String dealerId, @RequestBody LeadConfigurationModel form) {

        DealerView dealer = dealerClient.update(dealerId, form);

        return ResponseEntity.ok(dealer);
    }

    @PostMapping("/{dealerId}/makes-configuration")
    public ResponseEntity<DealerView> makesConfigurationStep(@PathVariable String dealerId, @RequestBody MakesConfigurationModel form) {

        DealerView dealer = dealerClient.update(dealerId, form);

        return ResponseEntity.ok(dealer);
    }

    @PostMapping("/{dealerId}/dealer-preferences")
    public ResponseEntity<DealerView> preferencesStep(@PathVariable String dealerId, @RequestBody DealerPreferencesModel form) {

        DealerView dealer = dealerClient.update(dealerId, form);

        return ResponseEntity.ok(dealer);
    }

    @GetMapping("/payment-structures")
    public List<PaymentStructure> getAvailablePaymentStructures() {
        if(environment.acceptsProfiles(Profiles.of("prod"))) {
            return Arrays.asList(PaymentStructure.PAY_PER_SALE, PaymentStructure.SUBSCRIPTION, PaymentStructure.FREE);
        } else {
            return Arrays.asList(PaymentStructure.values());
        }
    }

    @GetMapping("/get-makes")
    public List<MakeView> getMakes(){

        return vehicleService.findMakes()
            .stream()
            .sorted(Comparator.comparing(MakeView::getName))
            .collect(Collectors.toList());
    }

    @GetMapping("/dms-types")
    public List<DmsSystemView> getDmsTypes() {
        return new ArrayList<>(dealerClient.findDmsSystems().getContent());
    }

    @GetMapping("/sales-transaction-sources")
    public List<SalesTxSource> getSalesTransactionSources() {
        return Arrays.asList(SalesTxSource.values());
    }

    @GetMapping("/crm-types")
    public List<CrmSystemView> getCrmTypes() {
        return new ArrayList<>(dealerClient.findCrmSystems().getContent());
    }

    @GetMapping("/inventory-sources")
    public List<InventorySource> getInventorySources() {
        return Arrays.asList(InventorySource.values());
    }

    @GetMapping("/time-zones")
    public List<String> getTimeZones() {
        return TimeZoneUtils.getUSTimeZones();
    }

    @GetMapping("/dealer/{dealer}/effective-preferences")
    public DealerPreferences getDealerPreferences(@PathVariable DealerView dealer) {
        return dealer.getEffectivePreferences();
    }

    @GetMapping("/dealer/{dealer}/default-preferences")
    public DealerPreferences getDealerDefaultPreferences(@PathVariable DealerView dealer) {
        return dealer.getDefaultPreferences();
    }

    @GetMapping("/google-place-id")
    public List<DealerView> getExistingDealerByPlaceId(@RequestParam String googlePlaceId) {
        log.info("inside google place id");
        return new ArrayList<>(dealerClient.findByGooglePlaceId(googlePlaceId).getContent());
    }

    @GetMapping("/dealer/{dealerId}/has-inventory")
    public boolean hasInventory(@PathVariable String dealerId) {
        return dealerInventoryService.hasInventory(dealerId);
    }

    @GetMapping("/finance-integration")
    public Map<String,Object> getFinanceIntegrationData() {
        HashMap<String,Object> map = new HashMap<>();
        map.put("financeSystemOptions", Arrays.asList(FinanceSystem.values()));
        map.put("menuProviderOptions", new ArrayList<>(dealerClient.findMenuProviders().getContent()));

        return map;
    }

    @GetMapping("/dealer-info-options")
    public Map<String,Object> getDealerInfoOptions() {
        HashMap<String,Object> map = new HashMap<>();
        map.put("states", State.getStateAbbreviations());
        map.put("timeZones", getTimeZones());

        var groupSearchCriteria = new DealerGroupSearchCriteria();
        groupSearchCriteria.setIncludes(new String[]{"id", "name"});

        List<DealerGroupOption> dealerGroups = dealerGroupService.scroll(groupSearchCriteria).getContent().stream()
            .map(g -> new DealerGroupOption(g.getId(), g.getName()))
            .sorted(Comparator.comparing(DealerGroupOption::getName))
            .collect(Collectors.toList());
        map.put("dealerGroups", dealerGroups);

        map.put("websiteProviders", dealerClient.findWebsiteProviders().getContent().stream().collect(Collectors.toList()));

        return map;
    }

    @Data
    @AllArgsConstructor
    private static class DealerGroupOption {
        private String id;
        private String name;
    }
}
