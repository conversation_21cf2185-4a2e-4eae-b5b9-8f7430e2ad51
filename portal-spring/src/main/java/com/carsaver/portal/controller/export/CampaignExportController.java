package com.carsaver.portal.controller.export;

import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.portal.client.campaign.CampaignClient;
import com.carsaver.portal.client.campaign.CampaignView;
import com.carsaver.portal.client.campaign.ExportCampaignData;
import com.carsaver.portal.client.financier.FinancierClient;
import com.carsaver.portal.client.financier.FinancierView;
import com.carsaver.portal.client.program.ProgramClient;
import com.carsaver.portal.client.program.ProgramView;
import com.carsaver.portal.client.tenant.TenantClient;
import com.carsaver.portal.client.tenant.TenantView;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.supercsv.io.CsvBeanWriter;
import org.supercsv.io.ICsvBeanWriter;
import org.supercsv.prefs.CsvPreference;

import javax.servlet.http.HttpServletResponse;
import java.util.AbstractMap;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RestController
@RequestMapping("/configs/campaigns")
@Slf4j
public class CampaignExportController {

    private static final String ID = "id";
    private static final String CAMPAIGN_NAME = "name";
    private static final String TENANT_ID = "tenantId";
    private static final String PROGRAM_ID = "programId";
    private static final String DOMAIN_NAME = "domain";
    private static final String CHANNEL = "channel";
    private static final String PHONE_NUMBER = "phoneNumber";
    private static final String PIN_ENABLED = "pinEnabled";
    private static final String UPGRADE_STRATEGY = "upgradeStrategy";
    private static final String INVENTORY_CONFIG = "inventoryConfig";

    private static final String VIEW_CONFIG = "viewConfig";
    private static final String VOLIE_CONFIG = "volieConfig";
    private static final String SECURITY_CONFIG = "securityConfig";
    private static final String WARRANTY_CONFIG = "warrantyConfig";
    private static final String FINANCE_CONFIG = "financeConfig";
    private static final String MAKE_FILTERS = "makeFilters";
    private static final String LEAD_CONFIG = "leadConfig";
    private static final String USER_TAGS = "userTags";
    private static final String WARRANTY_ENABLED = "warrantyEnabled";
    private static final String INCENTIVES = "incentives";
    private static final String LOADING_CONFIG = "loadingConfig";
    private static final String CREATED_BY = "createdBy";
    private static final String CREATED_NAME = "createdName";
    private static final String CREATED_DATE = "createdDate";
    private static final String LAST_MODIFIED_BY = "lastModifiedBy";
    private static final String LAST_MODIFIED_NAME = "lastModifiedName";
    private static final String LAST_MODIFIED_DATE = "lastModifiedDate";
    private static final String TENANT_NAME = "tenantName";
    private static final String PROGRAM_NAME = "programName";
    private static final String LENDER_NAME = "lenderName";


    @Autowired
    private CampaignClient campaignClient;

    @Autowired
    private TenantClient tenantClient;

    @Autowired
    private ProgramClient programClient;

    @Autowired
    private FinancierClient financierClient;

    @Autowired
    private UserClient userClient;

    @GetMapping("/report.csv")
    public void exportToCsv(
        @RequestParam(value = "campaignName", required = false) String campaign,
        @RequestParam(value = "tenantName", required = false) String tenant,
        @RequestParam(value = "programName", required = false) String program,
        @RequestParam(value = "lenderName", required = false) String lender,
        @RequestParam List<String> columns,
        @RequestParam List<String> negativeSearchColumns,
        Pageable pageable,
        HttpServletResponse response
    ) {
        response.setContentType("text/csv");

        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=report.csv";

        response.setHeader(headerKey, headerValue);

        List<CampaignView> campaigns;

        if (Objects.isNull(campaign) && Objects.isNull(tenant) && Objects.isNull(program) && Objects.isNull(lender)) {
            campaigns = campaignClient.findAll();
        } else {
            campaigns = campaignClient.search(campaign, tenant, program, lender);
        }

        List<ExportCampaignData> campaignData = getExportableData(campaigns, pageable);

        List<String> csvHeaderList = columns.stream().map(obj -> getSortKeyMapForHeader().get(obj.trim())).collect(Collectors.toList());
        String[] csvHeader = csvHeaderList.toArray(new String[csvHeaderList.size()]);

        List<String> mappingList = columns.stream().map(obj -> getSortKeyMapForMapping().get(obj.trim())).collect(Collectors.toList());
        String[] nameMapping = mappingList.toArray(new String[mappingList.size()]);

        try {
            ICsvBeanWriter csvWriter = new CsvBeanWriter(response.getWriter(), CsvPreference.STANDARD_PREFERENCE);
            csvWriter.writeHeader(csvHeader);

            for (ExportCampaignData details : campaignData) {
                csvWriter.write(details, nameMapping);
            }
            csvWriter.close();
        } catch (Exception e) {
            log.error("Error occurred while exporting data to CSV -> {}", e.getMessage(), e);
        }
    }

    public List<ExportCampaignData> getExportableData(List<CampaignView> campaigns, Pageable pageable) {
        List<ExportCampaignData> data = campaigns.stream().map(
            obj -> {
                ExportCampaignData details = new ExportCampaignData();
                String value;

                details.setId(obj.getId());
                details.setName(obj.getName());
                details.setTenantId(obj.getTenantId());
                details.setProgramId(obj.getProgramId());
                details.setDomain(obj.getDomain());
                details.setChannel(obj.getChannel());
                details.setPhoneNumber(obj.getPhoneNumber());
                details.setPinEnabled(obj.getPinEnabled());

                if (Objects.nonNull(obj.getUpgradeStrategy())) {
                    value = new Gson().toJson(obj.getUpgradeStrategy());
                    details.setUpgradeStrategy(value);
                }
                if (Objects.nonNull(obj.getInventoryConfig())) {
                    value = new Gson().toJson(obj.getInventoryConfig());
                    details.setInventoryConfig(value);
                }
                if (Objects.nonNull(obj.getViewConfig())) {
                    value = new Gson().toJson(obj.getViewConfig());
                    details.setViewConfig(value);
                }
                if (Objects.nonNull(obj.getVolieConfig())) {
                    value = new Gson().toJson(obj.getVolieConfig());
                    details.setVolieConfig(value);
                }
                if (Objects.nonNull(obj.getSecurityConfig())) {
                    value = new Gson().toJson(obj.getSecurityConfig());
                    details.setSecurityConfig(value);
                }
                if (Objects.nonNull(obj.getWarrantyConfig())) {
                    value = new Gson().toJson(obj.getWarrantyConfig());
                    details.setWarrantyConfig(value);
                }
                if (Objects.nonNull(obj.getFinanceConfig())) {
                    value = new Gson().toJson(obj.getFinanceConfig());
                    details.setFinanceConfig(value);
                }

                details.setMakeFilters(obj.getMakeFilters());

                if (Objects.nonNull(obj.getLeadConfig())) {
                    value = new Gson().toJson(obj.getLeadConfig());
                    details.setLeadConfig(value);
                }

                details.setUserTags(obj.getUserTags());
                details.setWarrantyEnabled(obj.getWarrantyEnabled());
                details.setIncentives(obj.getIncentives());
                if (Objects.nonNull(obj.getLoadingConfig())) {
                    value = new Gson().toJson(obj.getLoadingConfig());
                    details.setLoadingConfig(value);
                }

                details.setCreatedBy(obj.getCreatedBy());
                details.setCreatedDate(obj.getCreatedDate());
                details.setLastModifiedBy(obj.getLastModifiedBy());
                details.setLastModifiedDate(obj.getLastModifiedDate());

                if (obj.getCreatedBy().equals(obj.getLastModifiedBy())) {
                    UserView userView = userClient.findById(obj.getCreatedBy());
                    details.setCreatedName(userView.getFullName());
                    details.setLastModifiedName(userView.getFullName());
                } else {
                    if (!StringUtils.isBlank(obj.getCreatedBy())) {
                        UserView userView = userClient.findById(obj.getCreatedBy());
                        details.setCreatedName(userView.getFullName());
                    }
                    if (!StringUtils.isBlank(obj.getLastModifiedBy())) {
                        UserView userView = userClient.findById(obj.getLastModifiedBy());
                        details.setLastModifiedName(userView.getFullName());
                    }
                }

                String tenantName = getTenantName(obj.getTenantId());
                details.setTenantName(tenantName);
                String programName = getProgramName(obj.getProgramId());
                details.setProgramName(programName);
                try {
                    Long lenderId = obj.getFinanceConfig().getEnabledFinancier().longValue();
                    String lenderName = getLenderName(lenderId);
                    details.setLenderName(lenderName);
                } catch (Exception e) {
                    log.warn("Financier Id is not available");
                }

                return details;
            }
        ).collect(Collectors.toList());

        List<ExportCampaignData> sortedList = getSortedList(data, pageable);

        return sortedList;
    }

    private String getTenantName(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return null;
        }
        TenantView tenantView = tenantClient.findById(tenantId);
        return tenantView.getName();
    }

    private String getProgramName(String programId) {
        if (StringUtils.isBlank(programId)) {
            return null;
        }
        ProgramView programView = programClient.findById(programId);
        return programView.getName();
    }

    private String getLenderName(Long lenderId) {
        FinancierView financierView = financierClient.findById(lenderId);
        return financierView.getName();
    }

    private List<ExportCampaignData> getSortedList(List<ExportCampaignData> data, Pageable pageable) {
        if (pageable != null) {
            Sort sort = pageable.getSort();
            if (sort.getOrderFor(CAMPAIGN_NAME) != null) {
                Sort.Order order = sort.getOrderFor(CAMPAIGN_NAME);
                if (order != null && order.getDirection().isAscending()) {
                    data.sort(Comparator.comparing(ExportCampaignData::getName, Comparator.nullsFirst(Comparator.naturalOrder())));
                } else {
                    data.sort(Comparator.comparing(ExportCampaignData::getName, Comparator.nullsLast(Comparator.reverseOrder())));
                }
            } else if (sort.getOrderFor(TENANT_NAME) != null) {
                Sort.Order order = sort.getOrderFor(TENANT_NAME);
                if (order != null && order.getDirection().isAscending()) {
                    data.sort(Comparator.comparing(ExportCampaignData::getTenantName, Comparator.nullsFirst(Comparator.naturalOrder())));
                } else {
                    data.sort(Comparator.comparing(ExportCampaignData::getTenantName, Comparator.nullsLast(Comparator.reverseOrder())));
                }
            } else if (sort.getOrderFor(PROGRAM_NAME) != null) {
                Sort.Order order = sort.getOrderFor(PROGRAM_NAME);
                if (order != null && order.getDirection().isAscending()) {
                    data.sort(Comparator.comparing(ExportCampaignData::getProgramName, Comparator.nullsFirst(Comparator.naturalOrder())));
                } else {
                    data.sort(Comparator.comparing(ExportCampaignData::getProgramName, Comparator.nullsLast(Comparator.reverseOrder())));
                }
            } else if (sort.getOrderFor(LENDER_NAME) != null) {
                Sort.Order order = sort.getOrderFor(LENDER_NAME);
                if (order != null && order.getDirection().isAscending()) {
                    data.sort(Comparator.comparing(ExportCampaignData::getLenderName, Comparator.nullsFirst(Comparator.naturalOrder())));
                } else {
                    data.sort(Comparator.comparing(ExportCampaignData::getLenderName, Comparator.nullsLast(Comparator.reverseOrder())));
                }
            }
        }
        return data;
    }

    private Map<String, String> getSortKeyMapForHeader() {
        return Stream.of(
            new AbstractMap.SimpleEntry<>(ID, "Campaign ID"),
            new AbstractMap.SimpleEntry<>(CAMPAIGN_NAME, "Campaign Name"),
            new AbstractMap.SimpleEntry<>(TENANT_ID, "Tenant Id"),
            new AbstractMap.SimpleEntry<>(PROGRAM_ID, "Program Id"),
            new AbstractMap.SimpleEntry<>(DOMAIN_NAME, "Domain Name"),
            new AbstractMap.SimpleEntry<>(CHANNEL, "Channel"),
            new AbstractMap.SimpleEntry<>(PHONE_NUMBER, "Phone Number"),
            new AbstractMap.SimpleEntry<>(PIN_ENABLED, "Pin Enabled?"),
            new AbstractMap.SimpleEntry<>(UPGRADE_STRATEGY, "Upgrade Strategy"),
            new AbstractMap.SimpleEntry<>(INVENTORY_CONFIG, "Inventory Config"),
            new AbstractMap.SimpleEntry<>(VIEW_CONFIG, "View Config"),
            new AbstractMap.SimpleEntry<>(VOLIE_CONFIG, "Volie Config"),
            new AbstractMap.SimpleEntry<>(SECURITY_CONFIG, "Security Config"),
            new AbstractMap.SimpleEntry<>(WARRANTY_CONFIG, "Warranty Config"),
            new AbstractMap.SimpleEntry<>(FINANCE_CONFIG, "Finance Config"),
            new AbstractMap.SimpleEntry<>(MAKE_FILTERS, "Make Filters"),
            new AbstractMap.SimpleEntry<>(LEAD_CONFIG, "Lead Config"),
            new AbstractMap.SimpleEntry<>(USER_TAGS, "User Tags"),
            new AbstractMap.SimpleEntry<>(WARRANTY_ENABLED, "Warranty Enabled?"),
            new AbstractMap.SimpleEntry<>(INCENTIVES, "Incentives"),
            new AbstractMap.SimpleEntry<>(LOADING_CONFIG, "Loading Config"),
            new AbstractMap.SimpleEntry<>(CREATED_BY, "Created By"),
            new AbstractMap.SimpleEntry<>(CREATED_NAME, "Created Name"),
            new AbstractMap.SimpleEntry<>(CREATED_DATE, "Created Date"),
            new AbstractMap.SimpleEntry<>(LAST_MODIFIED_BY, "Last Modified By"),
            new AbstractMap.SimpleEntry<>(LAST_MODIFIED_NAME, "Last Modified Name"),
            new AbstractMap.SimpleEntry<>(LAST_MODIFIED_DATE, "Last Modified Date"),
            new AbstractMap.SimpleEntry<>(TENANT_NAME, "Tenant Name"),
            new AbstractMap.SimpleEntry<>(PROGRAM_NAME, "Program Name"),
            new AbstractMap.SimpleEntry<>(LENDER_NAME, "Lender Name")
        ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private Map<String, String> getSortKeyMapForMapping() {
        return Stream.of(
            new AbstractMap.SimpleEntry<>(ID, ID),
            new AbstractMap.SimpleEntry<>(CAMPAIGN_NAME, CAMPAIGN_NAME),
            new AbstractMap.SimpleEntry<>(TENANT_ID, TENANT_ID),
            new AbstractMap.SimpleEntry<>(PROGRAM_ID, PROGRAM_ID),
            new AbstractMap.SimpleEntry<>(DOMAIN_NAME, DOMAIN_NAME),
            new AbstractMap.SimpleEntry<>(CHANNEL, CHANNEL),
            new AbstractMap.SimpleEntry<>(PHONE_NUMBER, PHONE_NUMBER),
            new AbstractMap.SimpleEntry<>(PIN_ENABLED, PIN_ENABLED),
            new AbstractMap.SimpleEntry<>(UPGRADE_STRATEGY, UPGRADE_STRATEGY),
            new AbstractMap.SimpleEntry<>(INVENTORY_CONFIG, INVENTORY_CONFIG),
            new AbstractMap.SimpleEntry<>(VIEW_CONFIG, VIEW_CONFIG),
            new AbstractMap.SimpleEntry<>(VOLIE_CONFIG, VOLIE_CONFIG),
            new AbstractMap.SimpleEntry<>(SECURITY_CONFIG, SECURITY_CONFIG),
            new AbstractMap.SimpleEntry<>(WARRANTY_CONFIG, WARRANTY_CONFIG),
            new AbstractMap.SimpleEntry<>(FINANCE_CONFIG, FINANCE_CONFIG),
            new AbstractMap.SimpleEntry<>(MAKE_FILTERS, MAKE_FILTERS),
            new AbstractMap.SimpleEntry<>(LEAD_CONFIG, LEAD_CONFIG),
            new AbstractMap.SimpleEntry<>(USER_TAGS, USER_TAGS),
            new AbstractMap.SimpleEntry<>(WARRANTY_ENABLED, WARRANTY_ENABLED),
            new AbstractMap.SimpleEntry<>(INCENTIVES, INCENTIVES),
            new AbstractMap.SimpleEntry<>(LOADING_CONFIG, LOADING_CONFIG),
            new AbstractMap.SimpleEntry<>(CREATED_BY, CREATED_BY),
            new AbstractMap.SimpleEntry<>(CREATED_NAME, CREATED_NAME),
            new AbstractMap.SimpleEntry<>(CREATED_DATE, CREATED_DATE),
            new AbstractMap.SimpleEntry<>(LAST_MODIFIED_BY, LAST_MODIFIED_BY),
            new AbstractMap.SimpleEntry<>(LAST_MODIFIED_NAME, LAST_MODIFIED_NAME),
            new AbstractMap.SimpleEntry<>(LAST_MODIFIED_DATE, LAST_MODIFIED_DATE),
            new AbstractMap.SimpleEntry<>(TENANT_NAME, TENANT_NAME),
            new AbstractMap.SimpleEntry<>(PROGRAM_NAME, PROGRAM_NAME),
            new AbstractMap.SimpleEntry<>(LENDER_NAME, LENDER_NAME)
        ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }
}
