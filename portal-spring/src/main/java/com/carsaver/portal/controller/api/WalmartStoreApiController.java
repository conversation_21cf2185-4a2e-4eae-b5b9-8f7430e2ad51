package com.carsaver.portal.controller.api;

import com.carsaver.magellan.api.PartnerService;
import com.carsaver.magellan.client.InventoryClient;
import com.carsaver.magellan.client.WalmartClient;
import com.carsaver.magellan.client.request.walmart.VehicleDisplayUpdateRequest;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.WalmartStoreView;
import com.carsaver.magellan.model.WalmartVehicleDisplayView;
import com.carsaver.portal.model.walmart.VehicleDisplayCreationModel;
import com.carsaver.portal.service.walmart.WalmartLeadDocService;
import com.carsaver.portal.service.walmart.WalmartUserDocService;
import com.carsaver.portal.service.walmart.WalmartVehicleSaleDocService;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.hateoas.CollectionModel;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/walmarts")
public class WalmartStoreApiController {

    @Autowired
    private PartnerService partnerService;

    @Autowired
    private WalmartClient walmartClient;

    @Autowired
    private InventoryClient inventoryClient;

    @Autowired
    private WalmartUserDocService walmartUserDocService;

    @Autowired
    private WalmartLeadDocService walmartLeadDocService;

    @Autowired
    private WalmartVehicleSaleDocService walmartVehicleSaleDocService;

    @PostMapping("/{walmart}/open")
    public WalmartStoreView open(@PathVariable WalmartStoreView walmart, @RequestBody OpenStoreBody openStoreBody) {
        ZonedDateTime dateTimeOpened = ZonedDateTime.of(openStoreBody.getDateOpened(), LocalTime.NOON, ZoneId.systemDefault());
        return partnerService.setStatusToOpen(walmart, dateTimeOpened);
    }


    @PostMapping("/{walmartStore}/updateAccStatus")
    public WalmartStoreView updateAccStatus(@PathVariable WalmartStoreView walmartStore, @RequestBody UpdateAccStatusBody body) {
        return partnerService.updateAccStatus(walmartStore, body.getAccStatus());
    }

    @PostMapping("/{walmartStore}/updateHangtagStatus")
    public WalmartStoreView updateHangtagStatus(@PathVariable WalmartStoreView walmartStore, @RequestBody UpdateHangtagStatusBody body) {
        return partnerService.updateHangtagStatus(walmartStore, body.getHangtagStatus());
    }

    @PostMapping("/{walmartStore}/updateApprovedVehicleDisplayStatus")
    public WalmartStoreView updateApprovedVehicleDisplayStatus(@PathVariable WalmartStoreView walmartStore, @RequestBody UpdateApprovedVehicleDisplayStatusBody body) {
        return partnerService.updateApprovedVehicleDisplayStatus(walmartStore, body.getApprovedVehicleDisplayStatus());
    }

    @DeleteMapping("/{walmartId}/marketingMaterials/{name}")
    public void deleteMarketingTag(@PathVariable Integer walmartId, @PathVariable String name) {
        log.info("Deleting marketing material {} for walmart {}", name, walmartId);
        WalmartStoreView walmartStore = walmartClient.findById(walmartId);
        walmartStore.getMarketingMaterials().remove(name);
        walmartClient.save(walmartId, walmartStore);

    }

    @PostMapping("/{walmartId}/marketingMaterials/{name}")
    public void addMarketingTag(@PathVariable Integer walmartId, @PathVariable String name) {
        log.info("Adding marketing material {} for walmart {}", name, walmartId);

        WalmartStoreView walmartStore = walmartClient.findById(walmartId);
        List<String> materials = new ArrayList<>();
        if (walmartStore.getMarketingMaterials() != null) {
            materials = new ArrayList<>(walmartStore.getMarketingMaterials());
        }
        materials.add(name);
        walmartStore.setMarketingMaterials(materials);
        walmartClient.save(walmartId, walmartStore);
    }

    @PostMapping("/{walmart}/add-vehicle-display")
    public ResponseEntity<VehicleDisplay> createVehicleDisplay(@PathVariable WalmartStoreView walmart, @RequestBody VehicleDisplayCreationModel form) {
        if (walmart == null) {
            return null;
        }

        CollectionModel<VehicleView> inventoryVehicle = inventoryClient.getVehiclesByVin(form.getVin());

        if(inventoryVehicle.getContent().isEmpty()) {
            log.error("Unable to create display vehicle with VIN {}", form.getVin());
            return ResponseEntity.unprocessableEntity().build();
        }

        VehicleView firstVinVehicleFound = inventoryVehicle.iterator().next();

        WalmartVehicleDisplayView walmartVehicleDisplayView = new WalmartVehicleDisplayView();
        BeanUtils.copyProperties(form, walmartVehicleDisplayView);

        walmartVehicleDisplayView.setDealerId(firstVinVehicleFound.getDealerId());
        walmartVehicleDisplayView.setWalmartId(walmart.getId());
        walmartVehicleDisplayView.setId(walmartVehicleDisplayView.getId());

        walmartVehicleDisplayView = walmartClient.addVehicleDisplay(walmartVehicleDisplayView);

        return ResponseEntity.ok(VehicleDisplay.from(walmartVehicleDisplayView));
    }

    @PostMapping("/{walmart}/add-vehicle-display-no-vin")
    public ResponseEntity<VehicleDisplay> createVehicleDisplayNoVin(@PathVariable WalmartStoreView walmart, @RequestBody VehicleDisplayCreationModel form) {
        if(walmart == null) {
            return null;
        }

        WalmartVehicleDisplayView walmartVehicleDisplayView = new WalmartVehicleDisplayView();
        BeanUtils.copyProperties(form, walmartVehicleDisplayView);

        walmartVehicleDisplayView.setWalmartId(walmart.getId());
        walmartVehicleDisplayView.setId(walmartVehicleDisplayView.getId());

        walmartVehicleDisplayView = walmartClient.addVehicleDisplay(walmartVehicleDisplayView);

        return ResponseEntity.ok(VehicleDisplay.from(walmartVehicleDisplayView));
    }

    @GetMapping("/{walmart}/vehicle-displays")
    public Collection<VehicleDisplay> getWalmartVehicleDisplays(@PathVariable WalmartStoreView walmart) {
        if (walmart == null) {
            return null;
        }

        return walmartClient.findAllByWalmartId(walmart.getId()).getContent().stream().map(VehicleDisplay::from).collect(Collectors.toList());
    }

    @PatchMapping("/{vehicleDisplayId}/update")
    public ResponseEntity<VehicleDisplay> updateWalmartVehicleDisplay(@PathVariable String vehicleDisplayId, @RequestBody VehicleDisplayUpdateBody vehicleDisplayUpdateBody) {
        return ResponseEntity.ok(VehicleDisplay.from(walmartClient.saveVehicleDisplay(vehicleDisplayId, vehicleDisplayUpdateBody)));
    }

    @GetMapping("/users-by-hostname")
    public long getUsersByHostname(@RequestParam String hostname) {
        return walmartUserDocService.getUserCountByHostName(hostname);
    }

    @GetMapping("/users-by-walmart")
    public long getUsersByWalmartId(@RequestParam Integer walmartStoreId) {
        return walmartUserDocService.getUserCountByWalmartId(walmartStoreId);
    }

    @GetMapping("/leads")
    public HashMap<String,Long> getLeadsByHostname(@RequestParam String hostname) {
        return walmartLeadDocService.getLeadsByHostname(hostname);
    }

    @GetMapping("/vehicle-sales")
    public long getVehicleSalesByWalmartId(@RequestParam Integer walmartStoreId) {
        return walmartVehicleSaleDocService.getVehicleSaleCountByWalmartId(walmartStoreId);
    }

    @Data
    public static class OpenStoreBody {
        private LocalDate dateOpened;
    }

    @Data
    public static class UpdateAccStatusBody {
        private Boolean accStatus;
    }

    @Data
    public static class UpdateHangtagStatusBody {

        private Boolean hangtagStatus;
    }

    @Data
    public static class UpdateApprovedVehicleDisplayStatusBody {
        private Boolean approvedVehicleDisplayStatus;
    }

    @Data
    public static class VehicleDisplayUpdateBody implements VehicleDisplayUpdateRequest {
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        private ZonedDateTime deliveryDate;

        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        private ZonedDateTime removalDate;
    }

    @Data
    @Builder
    public static class VehicleDisplay {
        private String id;
        private String dealerName;
        private String vin;
        private String location;
        private ZonedDateTime deliveryDate;
        private ZonedDateTime removalDate;
        private String make;
        private Integer year;
        private String model;
        private String[] imageUrl;

        public static VehicleDisplay from(WalmartVehicleDisplayView walmartVehicleDisplayView) {
            DealerView dealer = walmartVehicleDisplayView.getDealer();
            VehicleView vehicle = walmartVehicleDisplayView.getVehicle();

            if(vehicle == null) {
                return VehicleDisplay.builder()
                    .id(walmartVehicleDisplayView.getId())
                    .location(walmartVehicleDisplayView.getLocation())
                    .deliveryDate(walmartVehicleDisplayView.getDeliveryDate())
                    .removalDate(Optional.ofNullable(walmartVehicleDisplayView.getRemovalDate()).orElse(null))
                    .year(walmartVehicleDisplayView.getYear())
                    .make(walmartVehicleDisplayView.getMake())
                    .model(walmartVehicleDisplayView.getModel())
                    .build();
            }

            return VehicleDisplay.builder()
                .id(walmartVehicleDisplayView.getId())
                .dealerName(Optional.ofNullable(dealer).map(DealerView::getName).orElse("Unknown"))
                .vin(walmartVehicleDisplayView.getVin())
                .location(walmartVehicleDisplayView.getLocation())
                .deliveryDate(walmartVehicleDisplayView.getDeliveryDate())
                .removalDate(Optional.ofNullable(walmartVehicleDisplayView.getRemovalDate()).orElse(null))
                .year(Optional.ofNullable(vehicle).map(VehicleView::getYear).orElse(null))
                .make(Optional.ofNullable(vehicle).map(VehicleView::getMake).orElse(null))
                .model(Optional.ofNullable(vehicle).map(VehicleView::getModel).orElse(null))
                .imageUrl(Optional.ofNullable(vehicle).map(VehicleView::getImageUrl).orElse(null))
                .build();
        }
    }
}
