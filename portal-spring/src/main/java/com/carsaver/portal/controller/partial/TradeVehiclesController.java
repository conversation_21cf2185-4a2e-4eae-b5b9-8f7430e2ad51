package com.carsaver.portal.controller.partial;

import com.carsaver.magellan.client.UserVehicleClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;
import java.util.stream.Collectors;

@ToString(exclude = {"userVehicleClient"})
@Controller
@Slf4j
public class TradeVehiclesController {

    @JsonIgnore
    @Autowired(required = false)
    private transient UserVehicleClient userVehicleClient;

    @GetMapping("/partial/users/{user}/vehicles")
    public String tradeVehicles(@PathVariable UserView user, Model model) {
        List<UserVehicleView> tradeVehicles = userVehicleClient.findByUser(user.getId())
            .getContent().stream().collect(Collectors.toList());
        log.info("Returning Trade Vehicles = {}", tradeVehicles);
        model.addAttribute("tradeVehicles", tradeVehicles);

        return "fragments/tradeVehicles :: tradeVehicles";
    }
}
