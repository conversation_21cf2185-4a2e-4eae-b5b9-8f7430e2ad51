package com.carsaver.portal.controller;

import com.carsaver.core.StockType;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.security.DealerPermissionEvaluator;
import com.carsaver.portal.service.VehiclePricingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.text.MessageFormat;

@Slf4j
@Controller
@RequestMapping("/stratus/dealer/{dealer}/vehicle-pricing")
public class VehiclePricingController {
    private static final String VEHICLE_PRICING_REDIRECT = "/stratus/dealer/{0}/vehicle-pricing/{1}";

    @GetMapping
    @PreAuthorize("hasPermission(#dealer, 'inventory:new-pricing:read') or hasPermission(#dealer, 'inventory:used-pricing:read') or hasAuthority('read:pricing-new')  or hasAuthority('read:pricing-used')")
    public String stratusDefaultPricing(@PathVariable DealerView dealer, Authentication auth) {
        StockType newOrUsed = DealerPermissionEvaluator
            .hasDealerPrivilege(auth, dealer.getId(), "inventory:new-pricing:edit") ? StockType.NEW : StockType.USED;

        String redirectPath = MessageFormat.format(VEHICLE_PRICING_REDIRECT, dealer.getId(), newOrUsed.toLowerCaseStr());

        return "redirect:" + redirectPath;
    }

    @GetMapping("/v2")
    @PreAuthorize("hasPermission(#dealer, 'inventory:new-pricing:read') or hasPermission(#dealer, 'inventory:used-pricing:read') or hasAuthority('read:pricing-new')  or hasAuthority('read:pricing-used')")
    public String pricingForm(@PathVariable DealerView dealer, Authentication auth) {

        StockType newOrUsed = DealerPermissionEvaluator
            .hasDealerPrivilege(auth, dealer.getId(), "inventory:new-pricing:edit") ? StockType.NEW : StockType.USED;

        String redirectPath = MessageFormat.format(VEHICLE_PRICING_REDIRECT, dealer.getId(), newOrUsed.toLowerCaseStr());

        return "redirect:" + redirectPath;
    }

    @PreAuthorize("hasPermission(#dealer, 'inventory:used-pricing:read') or hasAuthority('read:pricing-used')")
    @GetMapping("/v2/used")
    public String usedPricingFormBackwardsCompat(@PathVariable DealerView dealer, ModelMap model) {
        String redirectPath = MessageFormat.format(VEHICLE_PRICING_REDIRECT, dealer.getId(), "used");

        return "redirect:" + redirectPath;
    }

    @PreAuthorize("hasPermission(#dealer, 'inventory:used-pricing:read') or hasAuthority('read:pricing-used')")
    @GetMapping("/used")
    public String usedPricingForm(@PathVariable DealerView dealer, ModelMap model) {
        if(!VehiclePricingService.isPricingPortalSupportedBy(dealer)) {
            return "redirect:not-supported";
        }

        model.addAttribute("stockType", StockType.USED);

        return "vehicle-pricing/index";
    }

    @PreAuthorize("hasPermission(#dealer, 'inventory:new-pricing:read') or hasAuthority('read:pricing-new')")
    @GetMapping("/v2/new")
    public String newPricingFormBackwardsCompat(@PathVariable DealerView dealer, ModelMap model) {
        String redirectPath = MessageFormat.format(VEHICLE_PRICING_REDIRECT, dealer.getId(), "new");

        return "redirect:" + redirectPath;
    }

    @PreAuthorize("hasPermission(#dealer, 'inventory:new-pricing:read') or hasAuthority('read:pricing-new')")
    @GetMapping("/new")
    public String newPricingForm(@PathVariable DealerView dealer, ModelMap model) {
        if(!VehiclePricingService.isPricingPortalSupportedBy(dealer)) {
            return "redirect:not-supported";
        }

        log.info("huh!!!!!");
        log.info("I know right?!?!");
        model.addAttribute("stockType", StockType.NEW);

        return "vehicle-pricing/index";
    }

    @GetMapping("/not-supported")
    public String pricingPortalNotSupported(@PathVariable DealerView dealer) {
        return "vehicle-pricing/index";
    }
}
