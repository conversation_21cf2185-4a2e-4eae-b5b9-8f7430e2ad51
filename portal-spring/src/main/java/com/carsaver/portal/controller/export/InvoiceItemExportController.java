package com.carsaver.portal.controller.export;

import com.carsaver.elasticsearch.model.invoice.InvoiceItemDoc;
import com.carsaver.magellan.export.CsvWriter;
import com.carsaver.magellan.export.DataStreamProvider;
import com.carsaver.magellan.export.service.CsvExportService;
import com.carsaver.magellan.export.service.ExportService;
import com.carsaver.portal.elasticsearch.InvoiceItemDocService;
import com.carsaver.portal.elasticsearch.criteria.InvoiceItemSearchCriteria;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@Controller
public class InvoiceItemExportController extends ExportController {

    @Autowired
    private InvoiceItemDocService docService;

    @PreAuthorize("hasAuthority('invoice-item:export')")
    @GetMapping(value="/invoice-items/report.csv", produces = "text/csv")
    public void exportInvoiceItemListToCsv(
        @ModelAttribute InvoiceItemSearchCriteria searchForm,
        @RequestParam List<String> columns,
        @RequestParam List<String> negativeSearchColumns,
        HttpServletResponse response
    ) throws Exception {
        super.applyNegativeSearchColumns(searchForm, negativeSearchColumns);

        CsvWriter csvWriter = super.prepareCsvWriter(response, columns).build();
        try(ExportService<InvoiceItemDoc> exportService = new CsvExportService<>(csvWriter)) {
            DataStreamProvider<InvoiceItemDoc> dataStreamProvider = () -> docService.scrollStream(searchForm);
            exportService.export(dataStreamProvider);
        }
    }
}
