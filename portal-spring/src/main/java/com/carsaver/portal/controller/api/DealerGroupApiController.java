package com.carsaver.portal.controller.api;

import com.carsaver.core.InventorySource;
import com.carsaver.magellan.api.PreferencesService;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.DealerGroupClient;
import com.carsaver.magellan.model.DealerGroupView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.preferences.LeadTransport;
import com.carsaver.magellan.model.preferences.PricingRules;
import com.carsaver.portal.model.dealerGroup.DealerGroupForm;
import com.carsaver.portal.validation.DealerGroupIntacctValidator;
import com.carsaver.stereotype.State;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.CollectionModel;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/dealer-groups")
public class DealerGroupApiController {

    @Autowired
    private DealerGroupClient dealerGroupClient;

    @Autowired
    private DealerClient dealerClient;

    @Autowired
    private DealerGroupIntacctValidator dealerGroupIntacctValidator;

    @InitBinder("dealerGroupForm")
    public void initDealerApiViewBinder(WebDataBinder binder) {
        binder.addValidators(dealerGroupIntacctValidator);
    }

    @GetMapping("/fields")
    public ResponseEntity<?> getFormFields() {
        Map<String,Object> model = new HashedMap<>();
        model.put("leadTransports", LeadTransport.values());
        model.put("pricingRules", PricingRules.values());
        model.put("states", State.getStateAbbreviations());
        model.put("defaultPreferences", PreferencesService.getSystemDefaults());
        return ResponseEntity.ok(model);
    }

    @GetMapping("/{id}/dealers")
    public ResponseEntity<List<DealerView>> getHomeNetDealers(@PathVariable String id) {
        List<DealerView> sharedDealers = new ArrayList<>();

        sharedDealers =
            dealerClient
                .findByDealerGroupIdAndInventorySource(id, InventorySource.HN).getContent()
            .stream()
            .collect(Collectors.toList());

        return ResponseEntity.ok(sharedDealers);
    }

    @PreAuthorize("hasAuthority('edit:dealerGroup')")
    @PostMapping("/")
    public ResponseEntity<DealerGroupView> create(@RequestBody @Valid DealerGroupForm dealerGroupForm) {
        DealerGroupView dealerGroupView = new DealerGroupView();
        BeanUtils.copyProperties(dealerGroupForm, dealerGroupView);

        dealerGroupView = dealerGroupClient.save(dealerGroupView);

        return ResponseEntity.ok(dealerGroupView);
    }

    @PreAuthorize("hasAuthority('edit:dealerGroup')")
    @PatchMapping("/{id}")
    public ResponseEntity<DealerGroupView> update(@PathVariable String id, @RequestBody @Valid DealerGroupForm dealerGroupForm) {
        DealerGroupView patchedDealerGroupView = dealerGroupClient.update(id, dealerGroupForm);

        return ResponseEntity.ok(patchedDealerGroupView);
    }

}
