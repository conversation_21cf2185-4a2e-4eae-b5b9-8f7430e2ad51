package com.carsaver.portal.controller.api;

import com.carsaver.core.DealerStatus;
import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.client.ProgramSubscriptionClient;
import com.carsaver.magellan.client.request.subscription.ProgramSubscriptionUpdateRequest;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.magellan.model.foundation.ProgramSubscriptionView;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.magellan.model.foundation.SubscriptionConfig;
import com.carsaver.portal.model.programconfig.NissanProgramConfigModel;
import com.carsaver.portal.model.programconfig.ProgramConfigModel;
import com.carsaver.portal.service.exception.DealerChatConfigNotFoundException;
import com.carsaver.portal.service.programconfig.DealerProgramConfigService;
import com.carsaver.portal.service.programconfig.NissanBuyAtHomeProgramConfigService;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.hateoas.CollectionModel;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class DealerProgramController {

    @Autowired
    private ProgramSubscriptionClient programSubscriptionClient;

    @Autowired
    private ProgramClient programClient;

    @Autowired
    private DealerProgramConfigService dealerProgramService;

    @Autowired
    private NissanBuyAtHomeProgramConfigService nissanBuyAtHomeProgramService;

    @Value("${program-id.nissan-buy-at-home}")
    private String nissanProgramId;

    @GetMapping(value = "/api/dealer/{dealerId}/buy-at-home-program-subscription")
    public ResponseEntity<ProgramSubscriptionExistsResponse> fetchBuyAtHomeProgram(
            @PathVariable String dealerId
    ) {
        final ProgramSubscriptionExistsResponse response = new ProgramSubscriptionExistsResponse();
        programSubscriptionClient.findByProgramAndDealer(nissanProgramId, dealerId)
                .ifPresentOrElse(programSubscriptionView -> {
                            response.setProgramId(programSubscriptionView.getProgramId());
                            response.setActive(programSubscriptionView.isActive());
                            response.setExists(true);
                        },
                        () -> {
                            response.setProgramId(null);
                            response.setActive(false);
                            response.setExists(false);
                        });
        return ResponseEntity.ok(response);
    }

    @GetMapping("/api/dealer/{dealerId}/programs")
    public ResponseEntity<List<DealerProgramSubscriptionModel>> fetchPrograms(@PathVariable String dealerId) {
        CollectionModel<ProgramSubscriptionView> dealerProgramSubscriptions = programSubscriptionClient.findByDealer(dealerId);
        List<DealerProgramSubscriptionModel> subscriptionsModel = dealerProgramSubscriptions.getContent().stream()
                .map(this::toModel)
                .collect(Collectors.toList());

        return ResponseEntity.ok(subscriptionsModel);
    }

    @PostMapping("/api/dealer/{dealerId}/programs")
    public void enrollProgram(@RequestBody @Valid DealerProgramSubscriptionEnrollForm enrollForm) {
        var programSubscription = new ProgramSubscriptionView();
        programSubscription.setDealerId(enrollForm.getDealerId());
        programSubscription.setContractId(enrollForm.getContractId());
        programSubscription.setProgramId(enrollForm.getProgramId());
        programSubscription.setStatus(enrollForm.getStatus());
        programSubscription.setEnrollmentDate(enrollForm.getEnrollmentDate());
        programSubscriptionClient.create(programSubscription);
    }

    @GetMapping("/api/dealer/{dealerId}/programs/{subscriptionId}")
    public ResponseEntity<DealerProgramSubscriptionModel> fetchProgram(@PathVariable String subscriptionId) {
        Optional<DealerProgramSubscriptionModel> dealerProgramSubscriptionModel = programSubscriptionClient.findByIdNoCache(subscriptionId)
            .map(this::toModel);

        return ResponseEntity.of(dealerProgramSubscriptionModel);
    }

    @PostMapping("/api/dealer/{dealerId}/programs/{subscriptionId}")
    public ResponseEntity<DealerProgramSubscriptionModel> updateProgram(@PathVariable String subscriptionId, @RequestBody @Valid DealerProgramSubscriptionEditForm form) {
        var updateRequest = new ProgramSubscriptionUpdateRequest();
        updateRequest.setEnrollmentDate(form.getEnrollmentDate());
        updateRequest.setContractId(form.getContractId());
        updateRequest.setActivationDate(form.getActivationDate());
        updateRequest.setCancellationDate(form.getCancellationDate());
        updateRequest.setParticipationAgreementExecutedDate(form.getParticipationAgreementExecutedDate());
        updateRequest.setVendorNotifiedDate(form.getVendorNotifiedDate());
        updateRequest.setVendorAgreementToDealerDate(form.getVendorAgreementToDealerDate());
        updateRequest.setIntegrationSurveyStartedDate(form.getIntegrationSurveyStartedDate());
        updateRequest.setTrainingNotificationSentDate(form.getTrainingNotificationSentDate());
        updateRequest.setTrainingNotificationCompletedDate(form.getTrainingNotificationCompletedDate());
        updateRequest.setStatus(form.getStatus());
        return ResponseEntity.ok(toModel(programSubscriptionClient.update(subscriptionId, updateRequest)));
    }

    @GetMapping("/api/dealer/{dealerId}/programs/{subscriptionId}/config")
    public ResponseEntity fetchProgramConfig(@PathVariable String subscriptionId, @PathVariable String dealerId) {
        ProgramConfigModel programConfigModel = dealerProgramService.fetchProgramConfigurations(subscriptionId);
        ResponseEntity result = ResponseEntity.ok(programConfigModel);

        if (programConfigModel.getIsNissanBuyAtHome()) {
            NissanProgramConfigModel nissanProgramConfigModel = nissanBuyAtHomeProgramService.fetchNissanProgramConfigurations(dealerId);
            BeanUtils.copyProperties(programConfigModel, nissanProgramConfigModel);
            result = ResponseEntity.ok(nissanProgramConfigModel);
        }
        return result;
    }

    @PostMapping("/api/dealer/{dealerId}/programs/{subscriptionId}/config")
    public ResponseEntity<DealerProgramSubscriptionModel> updateProgramConfig(@PathVariable String subscriptionId, @RequestBody @Valid ProgramConfigModel form) {
        ProgramSubscriptionView programSubscriptionView = dealerProgramService.updateProgramConfig(subscriptionId, form);
        DealerProgramSubscriptionModel dealerProgramSubscriptionModel = toModel(programSubscriptionView);
        ResponseEntity<DealerProgramSubscriptionModel> result = ResponseEntity.ok(dealerProgramSubscriptionModel);
        return result;
    }

    @PostMapping("/api/dealer/{dealerId}/programs/nissan/{subscriptionId}/config")
    public ResponseEntity<DealerProgramSubscriptionModel> updateNissanProgramConfig(@PathVariable String subscriptionId, @PathVariable String dealerId,
                                                                                    @RequestBody @Valid NissanProgramConfigModel form) {
        try {
            nissanBuyAtHomeProgramService.updateProgramConfigurations(dealerId, form);
        } catch (DealerChatConfigNotFoundException e) {
            log.error("Could not save Nissan configurations for dealer {} and subscription id {}. Changes {}", dealerId, subscriptionId, form);
        }

        // this updates the Google Analytics and configurations in the subscription table
        ResponseEntity<DealerProgramSubscriptionModel> result = this.updateProgramConfig(subscriptionId, form);
        return result;
    }

    @GetMapping("/api/dealer/{dealerId}/available-programs")
    public ResponseEntity<List<ProgramView>> fetchAvailablePrograms(@PathVariable String dealerId) {
        CollectionModel<ProgramView> programs = programClient.findAll(PageRequestUtils.maxSizeRequest());

        Set<String> existingProgramIds = programSubscriptionClient.findByDealer(dealerId).getContent().stream()
            .map(ProgramSubscriptionView::getProgramId)
            .collect(Collectors.toSet());

        List<ProgramView> programModels = programs.getContent().stream()
            .filter(program -> !existingProgramIds.contains(program.getId()))
            .collect(Collectors.toList());

        return ResponseEntity.ok(programModels);
    }

    private DealerProgramSubscriptionModel toModel(ProgramSubscriptionView programSubscription) {
        ProgramView program = programClient.findById(programSubscription.getProgramId()).orElse(null);

        DealerProgramSubscriptionModel model = DealerProgramSubscriptionModel.builder()
            .id(programSubscription.getId())
            .dealerId(programSubscription.getDealerId())
            .contractId(programSubscription.getContractId())
            .status(programSubscription.getStatus())
            .config(programSubscription.getConfig())
            .program(program)
            .activationDate(programSubscription.getActivationDate())
            .enrollmentDate(programSubscription.getEnrollmentDate())
            .cancellationDate(programSubscription.getCancellationDate())
            .successManagerId(programSubscription.getSuccessManagerId())
            .accountManagerId(programSubscription.getAccountManagerId())
            .participationAgreementExecutedDate(programSubscription.getParticipationAgreementExecutedDate())
            .vendorAgreementToDealerDate(programSubscription.getVendorAgreementToDealerDate())
            .vendorNotifiedDate(programSubscription.getVendorNotifiedDate())
            .integrationSurveyStartedDate(programSubscription.getIntegrationSurveyStartedDate())
            .trainingNotificationSentDate(programSubscription.getTrainingNotificationSentDate())
            .trainingNotificationCompletedDate(programSubscription.getTrainingNotificationCompletedDate())
            .build();

        return model;
    }

    @Data
    @Builder
    public static class DealerProgramSubscriptionModel {
        private String id;
        private String dealerId;
        private Long contractId;
        private DealerStatus status;
        private SubscriptionConfig config;
        private ProgramView program;
        private LocalDate enrollmentDate;
        private LocalDate activationDate;
        private LocalDate cancellationDate;
        private LocalDate participationAgreementExecutedDate;
        private LocalDate vendorNotifiedDate;
        private LocalDate vendorAgreementToDealerDate;
        private LocalDate integrationSurveyStartedDate;
        private LocalDate trainingNotificationSentDate;
        private LocalDate trainingNotificationCompletedDate;
        private String successManagerId;
        private String accountManagerId;

        public boolean isLive() {
            return status == DealerStatus.LIVE;
        }

        public boolean isCancelled() {
            return status == DealerStatus.CANCELLED;
        }
    }

    @Data
    public static class DealerProgramSubscriptionEditForm {
        @NotNull
        private Long contractId;
        @NotNull
        private LocalDate enrollmentDate;
        private LocalDate activationDate;
        private LocalDate cancellationDate;
        private LocalDate participationAgreementExecutedDate;
        private LocalDate vendorNotifiedDate;
        private LocalDate vendorAgreementToDealerDate;
        private LocalDate integrationSurveyStartedDate;
        private LocalDate trainingNotificationSentDate;
        private LocalDate trainingNotificationCompletedDate;
        @NotNull
        private DealerStatus status;
    }

    @Data
    public static class DealerProgramSubscriptionEnrollForm {
        @NotNull
        private String dealerId;
        @NotNull
        private String programId;
        @NotNull
        private Long contractId;
        @NotNull
        private DealerStatus status;
        @NotNull
        private LocalDate enrollmentDate;
    }

    @Data
    @NoArgsConstructor
    public static class ProgramSubscriptionExistsResponse {
        String programId;
        boolean exists;
        boolean active;
    }

}
