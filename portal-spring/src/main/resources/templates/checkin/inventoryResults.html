<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout}">

<!--/* workaround for intellij bug: https://goo.gl/Qf9LGj */-->
<!--/*@thymesVar id="vehicle" type="com.carsaver.magellan.model.VehicleView"*/-->

<head>
    <link th:href="@{/css/checkin.css}" rel="stylesheet">
</head>
<body>
<div class="search-result-wrapper" layout:fragment="content">
    <!--Inventory Result Vehicle Cards-->
    <div th:fragment="listVehicles(vehicles)" class="ibox inventory-ibox-result" th:each="vehicle : ${vehicles}">
        <div class="ibox-content inventory-result-content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-6">
                        <th:block th:replace="fragments/vehicleImage :: vehicleImage(${vehicle?.vehicleView})" />
                        <div th:text="${dealerLink}"></div>
                    </div>
                    <div class="col-lg-6" style="padding: 0;">
                        <div class="optionWrapper">
                            <div class="optionLabel">
                                <div class="vehicle-title">
                                    <span th:text="${vehicle.vehicleView?.year} + ' ' + ${vehicle.vehicleView?.make} + ' ' + ${vehicle.vehicleView?.model}">Recent Vehicle Style</span>
                                    <span th:if="${vehicle.vehicleView?.trim}" th:text="${vehicle.vehicleView?.trim}"></span>
                                </div>
                            </div>
                            <div class="optionValue">
                                <div class="add-button">
                                    <button class="btn btn-primary btn-xs ladda-button"
                                            data-style="expand-right"
                                            th:disabled="${vehicle?.inGarage}"
                                            th:id="'add-to-garage-' + ${vehicle.vehicleView?.id}"
                                            th:data-userId="${link?.userId}"
                                            th:data-vehicleId="${vehicle.vehicleView?.id}"
                                            onclick="addToGarage(this.getAttribute('data-userId'), this.getAttribute('data-vehicleId'));">
                                        <span class="ladda-label" th:text="${vehicle?.inGarage} ? 'In Garage' : 'Add'"></span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <th:block th:replace="fragments/certificatePricing :: inventorySearchResults(${vehicle})" />

                        <div class="optionWrapper view-options">
                            <div class="optionLabel">
                                <strong>Vehicle Options:</strong>
                            </div>
                            <div class="optionValue">
                                <a data-toggle="modal" data-target="#optionModal" th:attrappend="data-target=${'-' + vehicle.vehicleView.id}">View</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--Mobile View-->
        <div class="ibox-footer clearfix">
            <button class="col-lg-6 btn btn-primary btn-sm ladda-button cta-button"
                    data-style="expand-right"
                    th:disabled="${vehicle?.inGarage}"
                    th:id="'add-to-garage-' + ${vehicle.vehicleView?.id}"
                    th:data-userId="${link?.userId}"
                    th:data-vehicleId="${vehicle.vehicleView?.id}"
                    onclick="addToGarage(this.getAttribute('data-userId'), this.getAttribute('data-vehicleId'));">
                <span class="ladda-label" th:text="${vehicle?.inGarage} ? 'In Garage' : 'Add'"></span>
            </button>
            <a class="col-lg-6 btn btn-primary btn-sm cta-button"  data-toggle="modal" data-target="#optionModal" th:attrappend="data-target=${'-' + vehicle.vehicleView.id}">View Options</a>
        </div>
        <!-- /Mobile View END-->

        <div th:each="vehicle : ${vehicles}" class="modal inmodal fade" id="optionModal" th:attrappend="id=${'-' + vehicle.vehicleView.id}" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <!--Content-->
                <div class="modal-content">
                    <!--Header-->
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                        <h4 class="modal-title">Vehicle Options</h4>
                    </div>
                    <!--Body-->
                    <div class="modal-body">
                        <ul>
                            <li th:each="option : ${vehicle.vehicleView?.options}" th:text="${option}">Power Steering</li>
                        </ul>
                    </div>
                    <!--Footer-->
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                    </div>
                </div>
                <!--/.Content-->
            </div>
        </div>
    </div>
</div>
</body>
</html>
