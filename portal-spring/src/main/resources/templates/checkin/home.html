<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout}">
<head>
    <link th:href="@{/css/checkin.css}" rel="stylesheet">
</head>
<body>
    <div class="main-wrapper" layout:fragment="content">
        <div class="searchWrapper container" id="step1">
            <img th:src="@{/images/crsv_logo_white_no_circle-01.png}" class="carsaver_logo" alt="carsaver_logo">
            <h3 class="dealer-header">
                <svg class="dealer-header-svg" viewBox="0 0 24 24" style="display: inline-block; color: rgba(0, 0, 0, 0.870588); fill: rgb(242, 177, 46); height: 24px; width: 24px; user-select: none; transition: all 450ms cubic-bezier(0.23, 1, 0.32, 1) 0ms;">
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"></path>
                </svg>
                <span th:text="${dealer.name}">Dealer Name</span>
                <br/>
                <span class="dealer-header-span">Customer Portal</span>
            </h3>

            <div class="home-appointment-search-wrapper">
                <form id="form_search" name="form_search" method="GET" class="form-horizontal" th:object="${searchForm}">
                    <div class="form-group">
                        <div class="input-group">
                            <input id="step2" class="form-control" name="query" placeholder="Type customer name, email or phone" type="text">
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="submit">
                                    <span class="glyphicon glyphicon-search"></span>
                                </button>
                                <button id="step3" class="btn btn-default" style="border-radius: 0;" type="submit">
                                    <span class="glyphicon glyphicon-list"></span>
                                </button>
                            </span>
                        </div>
                    </div>
                </form>
            </div>
            <a href="#" class="btn btn-primary startTour"><i class="fa fa-play"></i> Start Tour</a>
        </div>
    </div>
    <div layout:fragment="page-scripts">
        <script type="text/javascript" th:src="@{/js/tour/tour.js}"></script>
    </div>
</body>
</html>
