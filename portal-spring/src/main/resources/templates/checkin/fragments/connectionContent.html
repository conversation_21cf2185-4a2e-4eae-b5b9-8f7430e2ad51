<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<!--/* workaround for intellij bug: https://goo.gl/Qf9LGj */-->
<!--/*@thymesVar id="checkInUserRow" type="com.carsaver.portal.model.checkin.CheckInUserRow"*/-->

<body>
    <div th:fragment="connectionContent(connection)" th:assert="${checkInUserRow}">
        <div class="ibox-content card-content clearfix">
            <div class="col-md-6 content">
                <ul class="list-unstyled m-t-md appointment-info-list">
                    <li class="appointment-li">
                        <div>
                            <label class="appointment-label">Received:</label> <span class="utc-to-local" th:text="${checkInUserRow?.connectionView?.createdDate != null ? #temporals.formatISO(checkInUserRow?.connectionView?.createdDate) : ''}"></span>
                        </div>
                        <div>
                            <label class="appointment-label">Appointment Date:</label>
                            <span>N/A</span>
                        </div>
                    </li>
                    <hr class="visible-sm visible-xs li-divider" />
                    <li class="appointment-li">
                        <label class="appointment-label">Pre-approved:</label>
                        <span  th:if="${checkInUserRow?.connectionView.certificate?.finance}" th:text="${checkInUserRow?.connectionView.certificate.finance?.name}">Finance Name</span>
                        <span th:unless="${checkInUserRow.connectionView.certificate?.finance}">N/A</span>
                        <div th:if="${checkInUserRow.connectionView.certificate?.finance}">
                            <span th:text="${{checkInUserRow.connectionView.certificate?.finance.apr}} + '%' + ' | ' + ${checkInUserRow.connectionView.certificate.finance?.months} + ' ' + 'Months' + ' | ' + '$' + ${#numbers.formatDecimal(checkInUserRow.connectionView.certificate.finance?.perMonth, 0, 'COMMA', 0, 'POINT')}  + ' ' + 'month'"></span>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="col-md-6 content">
                <ul class="list-unstyled m-t-md appointment-info-list">
                    <hr class="visible-sm visible-xs li-divider" />
                    <li class="appointment-li">
                        <label class="appointment-label">Trade In:</label>
                        <span th:unless="${checkInUserRow.tradeIn}">N/A</span>
                        <span th:if="${checkInUserRow.tradeIn}">Yes</span>
                        <div th:if="${checkInUserRow.tradeIn}">
                            <span th:text="${{checkInUserRow.tradeIn.style}}"></span>
                        </div>
                    </li>
                    <hr class="visible-sm visible-xs li-divider" />
                    <li class="appointment-li">
                        <label class="appointment-label">Working with:</label>
                        <span th:text="${checkInUserRow.connectionView.programManager?.fullName} ?: _">N/A</span>
                    </li>
                </ul>
            </div>
        </div>
        <!--Connection Card Footer-->
        <div class="ibox-footer cta-wrapper clearfix">
            <div class="btn-group checkin-wrapper" role="group">
                <button class="btn btn-primary"
                        th:id="'checkin-' + ${checkInUserRow?.connectionView?.id}"
                        th:data-connection-id="${checkInUserRow?.connectionView?.id}"
                        onclick="connectionCheckin(this.getAttribute('data-connection-id'));">
                    Check In
                </button>
            </div>
            <div class="btn-group" role="group" aria-label="Button group with nested dropdown" style="text-align: center;">
                <span>Status:</span>
                <button class="btn btn-default dropdown-toggle status-dropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="status-text" th:id="@{'dealer-link-status-' + ${checkInUserRow?.connectionView?.id}}" th:text="${#strings.toLowerCase(checkInUserRow.dealerLink.status)}">Update Status</span>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="status-anchor" th:each="dealerLinkStatus : ${dealerLinkStatuses}"
                           th:text="${dealerLinkStatus}"
                           th:value="${dealerLinkStatus}"
                           th:selected="${dealerLinkStatus == checkInUserRow.dealerLink.status}"
                           th:data-property="${dealerLinkStatus}"
                           th:data-dealer-link-id="${checkInUserRow.dealerLink.id}"
                           th:data-connection-id="${checkInUserRow.connectionView.id}"
                           th:id="@{'dealer-link-' + ${checkInUserRow?.dealerLink?.id}}"
                           onclick="setDealerLinkStatus(this.getAttribute('data-connection-id'), this.getAttribute('data-dealer-link-id'), this.getAttribute('data-property'));"
                        >
                        </a>
                    </li>
                </ul>
            </div>
            <!--Hidden Resolution Select-->
            <div class="hidden-dropdown" th:classappend="${#strings.toUpperCase(checkInUserRow.dealerLink.status) == 'LOST'} ? '' : 'hidden'">
                <span>Select Reason:</span>
                <select id="resolutionId" class="selectpicker-ajax form-control hidden-select">
                    <option value="">Please Select</option>
                    <option th:each="resolution : ${resolutions}"
                            th:value="${resolution?.id}"
                            th:text="${resolution?.name}"
                            th:selected="${resolution.id == checkInUserRow.dealerLink.resolution?.id}"
                            th:id="@{'link-resolution-' + ${resolution?.id}}"
                            data:property="${resolution.id}"
                            data:url="@{|/api/dealerLinks/${checkInUserRow.dealerLink?.id}/resolution/${resolution?.id}|}"
                            data-message="Resolution was updated!"
                    >
                    </option>
                </select>
            </div>
            <!--End of Hidden Resolution Select-->
        </div>
        <!--End of Connection Card Footer-->
    </div>
</body>
</html>
