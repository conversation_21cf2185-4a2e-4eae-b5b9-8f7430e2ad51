<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout}">

<!--/* workaround for intellij bug: https://goo.gl/Qf9LGj */-->
<!--/*@thymesVar id="vehicle" type="com.carsaver.magellan.model.VehicleView"*/-->

<head>
    <link th:href="@{/css/checkin.css}" rel="stylesheet">
</head>
<body>
<!--Vin Vehicle Modal Body-->
<div th:fragment="vinVehicleDetails(vehicle)">

    <th:block th:replace="fragments/certificatePricing :: certificatePricingAlt(${certificate})" />

    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Vehicle Details:</strong>
        </div>
        <div class="optionValue">
            <a data-target="#vinVehicleOptions" data-toggle="modal" th:attrappend="data-target=${'-' + vehicle?.id}">More Details</a>
        </div>
    </div>

    <!--Start of Vehicle Modal-->
    <div class="modal inmodal fade" id="vinVehicleOptions" th:attrappend="id=${'-' + certificate.vehicle?.id}" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <!--Content-->
            <div class="modal-content">
                <!--Header-->
                <div class="modal-header info-color-dark white-text">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <div class="modal-title">
                        <span th:text="${{certificate.style}}">year make model</span>
                        <span th:text="${{certificate.style.trim}}">trim</span>
                        <th:block th:replace="fragments/vehicleImage :: certificateVehicleImage(${certificate})" />
                    </div>
                </div>
                <!--Body-->
                <div class="modal-body vehicle-details-modal-body">
                    <div class="optionWrapper">
                        <div class="optionLabel">
                            <strong>Mileage:</strong>
                        </div>
                        <div class="optionValue">
                            <span th:text="${#numbers.formatInteger(vehicle?.miles,3,'COMMA')} ?: _">-</span>
                        </div>
                    </div>
                    <hr class="vehicle-title-divider" />
                    <div class="optionWrapper">
                        <div class="optionLabel">
                            <strong>Exterior Color:</strong>
                        </div>
                        <div class="optionValue">
                            <span th:text="${vehicle?.exteriorColor} ?: _">-</span>
                        </div>
                    </div>
                    <hr class="vehicle-title-divider" />
                    <div class="optionWrapper">
                        <div class="optionLabel">
                            <strong>Interior Color:</strong>
                        </div>
                        <div class="optionValue">
                            <span th:text="${vehicle?.interiorColor} ?: _">-</span>
                        </div>
                    </div>
                    <hr class="vehicle-title-divider" />
                    <div class="optionWrapper">
                        <div class="optionLabel">
                            <strong>Doors:</strong>
                        </div>
                        <div class="optionValue">
                            <span th:text="${vehicle?.doors} ?: _"></span>
                        </div>
                    </div>
                    <hr class="vehicle-title-divider" />
                    <div class="optionWrapper">
                        <div class="optionLabel">
                            <strong>CarFax:</strong>
                        </div>
                        <div class="optionValue">
                            <a th:href="${vehicle?.carfaxUrl} ?: _" target="_blank">CarFax</a>
                        </div>
                    </div>
                    <hr class="vehicle-title-divider" />
                    <div class="optionWrapper">
                        <div class="optionLabel">
                            <strong>City Mpg:</strong>
                        </div>
                        <div class="optionValue">
                            <span th:text="${vehicle?.mpgCity} + ' Mpg'" th:unless="${vehicle?.mpgCity == null}">-</span>
                        </div>
                    </div>
                    <hr class="vehicle-title-divider" />
                    <div class="optionWrapper">
                        <div class="optionLabel">
                            <strong>Highway Mpg:</strong>
                        </div>
                        <div class="optionValue">
                            <span th:text="${vehicle?.mpgHighway} + ' Mpg'" th:unless="${vehicle?.mpgHighway == null}">-</span>
                        </div>
                    </div>
                    <hr class="vehicle-title-divider" />
                    <div class="optionWrapper">
                        <div class="optionLabel">
                            <strong>Fuel Type:</strong>
                        </div>
                        <div class="optionValue">
                            <span th:text="${vehicle?.fuelType} ?: _">-</span>
                        </div>
                    </div>
                    <hr class="vehicle-title-divider" />
                    <div class="optionWrapper">
                        <div class="optionLabel">
                            <strong>Engine:</strong>
                        </div>
                        <div class="optionValue">
                            <span th:text="${vehicle?.engine} ?: _">-</span>
                        </div>
                    </div>
                    <hr class="vehicle-title-divider" />
                    <div class="optionWrapper">
                        <div class="optionLabel">
                            <strong>Transmission:</strong>
                        </div>
                        <div class="optionValue">
                            <span th:text="${vehicle?.transmission} ?: _">-</span>
                        </div>
                    </div>
                    <hr class="vehicle-title-divider" />
                    <div class="optionWrapper">
                        <div class="optionLabel">
                            <strong>Vehicle Options:</strong>
                        </div>
                        <ul class="optionValueLi">
                            <li th:each="option : ${certificate?.vehicle?.options}" th:text="${option}">-</li>
                        </ul>
                    </div>
                </div>
                <!--Footer-->
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                </div>
            </div>
            <!--/.Content-->
        </div>
    </div>
    <!--/ End Vehicle Modal-->
</div>

</body>
</html>
