<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout}">
<!--/* workaround for intellij bug: https://goo.gl/Qf9LGj */-->
<!--/*@thymesVar id="appointment" type="com.carsaver.magellan.model.AppointmentView"*/-->
<!--/*@thymesVar id="certificate" type="com.carsaver.magellan.model.CertificateView"*/-->
<head>
    <link th:href="@{/css/checkin.css}" rel="stylesheet">
</head>
<body>
<div layout:fragment="content" class="container appointment-detail-wrapper" th:assert="${connection}">
    <div class="back-button-wrapper">
        <a th:href="@{/stratus/dealer/{dealerId}/checkin/?query=(dealerId=${dealer.id})}" ><i class="fa fa-chevron-left fa-fw" aria-hidden="true"></i>Back</a>
        <button class="btn btn-primary" onclick="topFunction()" id="to-top-btn" title="Go to top">
            <i class="fa fa-arrow-up" aria-hidden="true"></i>
        </button>
    </div>
    <!--Profile Card-->
    <div>
        <div class="ibox">
            <div th:if="${connection.user?.locale == 'es'}" class="wrapper-widget">
                <div class="checkin-ribbon-wrapper">
                    <div class="checkin-ribbon label-primary">Spanish</div>
                </div>
            </div>
            <div class="ibox-title">
                <div class="ibox-header">
                    <span class="profile-name" th:text="${connection.user?.lastName} + ', ' + ${connection.user?.firstName}">Janet Smith</span>
                </div>
            </div>
            <div class="ibox-content profile-icon-wrapper">
                <ul class="list-unstyled m-t-md appointment-info-list">
                    <li class="phone-li">
                        <div th:if="${connection.dealerLink?.proxyNumber.orElse(null) != null}" class="profile-div-wrap">
                            <i class="fa fa-phone fa-fw profile-icons" aria-hidden="true"></i>
                            <a th:if="${connection.dealerLink?.proxyNumber.orElse(null)?.number != null}" class="profile-links" th:href="'tel:'+ ${connection.dealerLink?.proxyNumber.orElse(null)?.number}" th:text="${'Proxy Phone: ' + connection.dealerLink?.proxyNumber.orElse(null)?.number}">************</a>
                            <div th:if="${connection.dealerLink?.code}" th:text="${'Pin: ' + connection.dealerLink?.code}" style="padding-left: 22px;"></div>
                        </div>
                        <div th:unless="${connection.dealerLink?.proxyNumber.orElse(null) != null}" class="profile-div-wrap">
                            <i class="fa fa-phone fa-fw profile-icons" aria-hidden="true"></i>
                            <span th:if="${connection.getUser()?.phoneNumber}" th:text="${'Phone: ' + connection.getUser().phoneNumber}" class="profile-links"></span>
                        </div>
                    </li>
                    <hr class="divider" />
                    <li class="profile-li">
                        <div class="profile-div-wrap">
                            <i class="fa fa-envelope fa-fw profile-icons" aria-hidden="true"></i>  <a class="profile-links" th:href="'mailto:'+ ${connection.user.email}" th:text="${connection.user.email}"><EMAIL></a>
                        </div>
                    </li>
                    <hr class="divider" />
                    <li class="profile-li">
                        <div class="profile-div-wrap">
                            <i class="fa fa-map-marker fa-fw profile-icons" aria-hidden="true"></i>  <a class="profile-links" target="_blank" th:href="'http://maps.google.com/?q=' + ${connection.user.addressView.fullAddress}" th:text="${connection.user.addressView.fullAddress}">900 Biscayne Blvd 33138 Miami, FL</a>
                        </div>
                    </li>
                    <hr class="divider" />
                    <li class="profile-li">
                        <div class="profile-div-wrap">
                            <i class="fa fa-clock-o fa-fw profile-icons" aria-hidden="true"></i>
                            <span id="checked-in-at" class="utc-to-local" th:if="${connection.checkedInAt != null}" th:text="${#temporals.formatISO(connection.checkedInAt)}"></span>
                            <span class="danger-text" th:unless="${connection.checkedInAt}">* Customer Not Checked In.</span>
                        </div>
                    </li>
                    <hr class="profile-dividers" th:if="${connection.certificate?.finance}" />
                    <li class="profile-li" th:if="${connection.certificate?.finance}">
                        <div class="profile-div-wrap">
                            <i class="fa fa-check-circle fa-fw" aria-hidden="true"></i>
                            <span style="padding-right: 5px;" th:text="${connection.certificate.finance?.name}"></span> <span class="label label-primary">Pre-approved</span>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="ibox-footer cta-wrapper clearfix" style="display: block !important;">
                <div class="btn-group checkin-wrapper" role="group">
                    <button class="btn btn-primary" id="checkin-btn">Check In</button>
                </div>
                <div class="btn-group" role="group" aria-label="Button group with nested dropdown" style="text-align: center;">
                    <span>Status:</span>
                    <button class="btn btn-default dropdown-toggle status-dropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="status-text" th:id="@{'dealer-link-status-' + ${connection.id}}" th:text="${#strings.toLowerCase(dealerLink?.status)}">Update Status</span>
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="status-anchor" th:each="dealerLinkStatus : ${dealerLinkStatuses}"
                               th:text="${dealerLinkStatus}"
                               th:value="${dealerLinkStatus}"
                               th:selected="${dealerLinkStatus == dealerLink?.status}"
                               th:data-property="${dealerLinkStatus}"
                               th:data-dealer-link-id="${dealerLink?.id}"
                               th:data-connection-id="${connection.id}"
                               th:id="@{'dealer-link-' + ${dealerLink?.id}}"
                               onclick="setDealerLinkStatus(this.getAttribute('data-connection-id'), this.getAttribute('data-dealer-link-id'), this.getAttribute('data-property'));"
                            >
                            </a>
                        </li>
                    </ul>
                </div>
                <!--Hidden Resolution Select-->
                <div class="hidden-dropdown" th:classappend="${#strings.toUpperCase(dealerLink?.status) == 'LOST'} ? '' : 'hidden'">
                    <span>Select Reason:</span>
                    <select id="resolutionId" class="selectpicker-ajax form-control hidden-select">
                        <option value="">Please Select</option>
                        <option th:each="resolution : ${resolutions}"
                                th:value="${resolution?.id}"
                                th:text="${resolution?.name}"
                                th:selected="${resolution?.id == dealerLink.resolution?.id}"
                                th:id="@{'link-resolution-' + ${resolution?.id}}"
                                data:property="${resolution?.id}"
                                data:url="@{|/api/dealerLinks/${connection.dealerLinkId}/resolution/${resolution?.id}|}"
                                data-message="Resolution was updated!"
                        >
                        </option>
                    </select>
                </div>
                <!--End of Hidden Resolution Select-->
            </div>
        </div>
        <!--Connection Vehicle Card-->
        <div class="ibox">
            <div class="ibox-title">
                <h2 class="ibox-header">Vehicle of Interest</h2>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-lg-6">
                        <th:block th:replace="fragments/vehicleImage :: certificateVehicleImage(${certificate})" />
                    </div>
                    <div class="col-lg-6">
                        <div th:replace="checkin/fragments/vehicleDetails :: vehicleDetails(${certificate})"></div>
                    </div>
                </div>
            </div>
        </div>
        <!--End-->
        <!--Other Vehicle Card-->
        <div class="ibox">
            <div class="ibox-title">
                <h2 class="ibox-header">Other Vehicles</h2>
            </div>
            <div class="ibox-content other-vehicle-content">
                <div class="tabs-container">
                    <ul class="nav nav-tabs">
                        <li class="active">
                            <a data-toggle="tab" href="#tab-1">SAVED VEHICLES</a>
                        </li>
                        <li class="">
                            <a data-toggle="tab" href="#tab-2">RECENTLY VIEWED</a>
                        </li>
                        <li class="">
                            <a th:href="@{/stratus/dealer/{dealerId}/checkin/link/{linkId}/inventory(dealerId=${dealer.id},linkId= ${dealerLink.id})}">INVENTORY SEARCH</a>
                        </li>
                    </ul>
                    <div class="tab-content">
                        <div id="tab-1" class="tab-pane active">
                            <div>
                                <div class="ajax-load" data:url="@{|/partial/user/${connection.userId}/certificates/saved?dealerId=${dealer.id}|}">
                                    <div>
                                        <div class="sk-spinner sk-spinner-wave">
                                            <div class="sk-rect1"></div>
                                            <div class="sk-rect2"></div>
                                            <div class="sk-rect3"></div>
                                            <div class="sk-rect4"></div>
                                            <div class="sk-rect5"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="tab-2" class="tab-pane">
                            <div>
                                <div class="ajax-load" data:url="@{|/partial/user/${connection.userId}/certificates/recent?dealerId=${dealer.id}|}">
                                    <div>
                                        <div class="sk-spinner sk-spinner-wave">
                                            <div class="sk-rect1"></div>
                                            <div class="sk-rect2"></div>
                                            <div class="sk-rect3"></div>
                                            <div class="sk-rect4"></div>
                                            <div class="sk-rect5"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--End-->
        <!--Full Connection Details Card-->
        <div class="ibox">
            <div class="ibox-title">
                <h2 class="ibox-header">Connection Details</h2>
            </div>
            <div class="ibox-content">
                <div th:replace="checkin/fragments/fullConnectionDetails :: fullConnectionDetails(${connection})"></div>
            </div>
        </div>
        <!--End-->
        <!--Finance Card-->
        <div class="ibox" th:if="${certificate?.finance}">
            <div class="ibox-title">
                <h2 class="ibox-header">Finance Details</h2>
            </div>
            <div class="ibox-content">
                <div>
                    <div th:replace="finance/fragments:: financeDetails(${certificate})"></div>
                </div>
            </div>
        </div>
        <!--End-->
        <!--Trade In Detail Card-->
        <div class="ibox" th:if="${tradeInfo}">
            <div class="ibox-title">
                <h2 class="ibox-header">Trade In</h2>
            </div>
            <div class="ibox-content">
                <div>
                    <div th:replace="fragments/tradeInDetails :: tradeInDetails(${tradeInfo})"></div>
                </div>
            </div>
        </div>
        <!--End-->

        <!--User Notes Card-->
        <div class="ajax-load" data:url="@{|/partial/user/${connection.userId}/notes?dealerId=${dealer.id}|}">
            <th:block th:insert="fragments/loaders :: iboxSpinner('Notes')" />
        </div>
        <!--End of User Notes Card -->

        <!--Events Card-->
        <div class="ajax-load" data:url="@{|/partial/user/${connection.userId}/events?dealerId=${dealer.id}&leadId=${connection.id}|}">
            <th:block th:insert="fragments/loaders :: iboxSpinner('Events')" />
        </div>
        <!--End of Events Card-->

        <div class="clearfix"></div>
    </div>
</div>
<th:block layout:fragment="body-scripts">
    <script type="text/javascript" th:src="@{/js/checkin-inventory.js}"></script>
    <script type="text/javascript" th:src="@{/js/dealer-link-status.js}"></script>
    <script>
        $('#checkin-btn').on('click', function() {
            var statusButton = $('.status-text');
            var button = $(this);
            button.prop('disabled', true);
            $.post('/api/connections/[[${connection.id}]]/checkin', {}, function(connection) {
                statusButton.text('Working');
                toastr.success('Status Updated!');
                toastr.success('[[${connection.user.fullName}]] checked in successfully');
                $('#checked-in-at').text(moment.unix(connection.checkedInAt).format('MM/DD/YYYY hh:mm a'));
            });
        });
    </script>
    <script>
        $(function() {
            var statusAnchor = $(".status-anchor");
            statusAnchor.on("click", function() {
                var hiddenDropdown = $(".hidden-dropdown");
                if ($(this).data("property") === "LOST") {
                    hiddenDropdown.removeClass('hidden').show();
                } else {
                    hiddenDropdown.hide();
                }
            });
        });
    </script>
</th:block>
</body>
</html>
