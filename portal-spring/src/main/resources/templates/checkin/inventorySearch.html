<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}" xmlns="http://www.w3.org/1999/html">
<head>
    <link th:href="@{/css/checkin.css}" rel="stylesheet">
</head>
<body>
<div layout:fragment="content">
    <div class="container">
        <div class="row">
            <div style="padding: 10px 0;">
                <a class="back-btn" th:onClick="'goBack()'">
                    <i class="fa fa-chevron-left" aria-hidden="true"></i>
                    <span>Back</span>
                </a>
            </div>
            <button class="btn btn-primary" onclick="topFunction()" id="to-top-btn" title="Go to top">
                <i class="fa fa-arrow-up" aria-hidden="true"></i>
            </button>
            <div class="ibox">
                <div class="ibox-title">
                    <h2>Inventory Search</h2>
                </div>
                <div class="ibox-content">
                    <div class="tabs-container">
                        <ul class="nav nav-tabs">
                            <li class="active">
                                <a id="newTab" data-toggle="tab" href="#tab-1">New Vehicles</a>
                            </li>
                            <li>
                                <a id="usedTab" data-toggle="tab" href="#tab-2">Used Vehicles</a>
                            </li>
                            <li>
                                <a id="vinTab" data-toggle="tab" href="#tab-3">Vin Number</a>
                            </li>
                            <li>
                                <a id="stockNumberTab" data-toggle="tab" href="#tab-4">Stock Number</a>
                            </li>
                        </ul>
                        <div class="tab-content">
                            <!--NEW INVENTORY SEARCH TAB-->
                            <div id="tab-1" class="tab-pane active">
                                <div class="panel-body">
                                    <div class="card-block">
                                        <div th:replace="checkin/inventorySearchDropDowns :: dropDownsFor('new', ${dealer}, ${searchForm})"></div>
                                    </div>
                                </div>
                            </div>
                            <!--END NEW INVENTORY SEARCH TAB-->

                            <!--USED INVENTORY SEARCH TAB-->
                            <div id="tab-2" class="tab-pane">
                                <div class="panel-body">
                                    <div class="card-block">
                                        <div th:replace="checkin/inventorySearchDropDowns :: dropDownsFor('used', ${dealer}, ${searchForm})"></div>
                                    </div>
                                </div>
                            </div>
                            <!--END USED INVENTORY SEARCH TAB-->

                            <!--VIN SEARCH TAB-->
                            <div id="tab-3" class="tab-pane">
                                <div class="panel-body">
                                    <div class="card-block">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <form th:action="@{/stratus/dealer/{dealerId}/checkin/link/{linkId}/inventory/results/#searchResults(dealerId=${dealer?.id},linkId=${link.id})}" method="GET" th:object="${searchForm}">
                                                    <div class="md-form">
                                                        <h3>Search By Vin</h3>
                                                        <hr class="divider"/>
                                                        <div class="form-group" style="padding-left: 20px;">
                                                            <label class="radio">
                                                                <input checked="checked" type="radio" name="stockType" value="new"> New Inventory
                                                            </label>
                                                            <label class="radio">
                                                                <input type="radio" name="stockType" value="used"> Used Inventory
                                                            </label>
                                                        </div>
                                                        <div class="input-group">
                                                            <input type="text" id="vin" name="vin" th:value="${searchForm?.vin}" autocomplete="off" placeholder="Enter Vin Number" class="form-control" />
                                                            <span class="input-group-btn">
                                                                <button class="btn btn-primary" type="submit">
                                                                    <i class="fa fa-search fa-fw"></i>
                                                                    Search
                                                                </button>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--END VIN SEARCH TAB-->

                            <!--STOCK NUMBER SEARCH TAB-->
                            <div id="tab-4" class="tab-pane">
                                <div class="panel-body">
                                    <div class="card-block">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <form th:action="@{/stratus/dealer/{dealerId}/checkin/link/{linkId}/inventory/results/#searchResults(dealerId=${dealer?.id},linkId=${link.id})}"  method="GET" th:object="${searchForm}">
                                                    <div class="md-form">
                                                        <h3>Search By Stock Number</h3>
                                                        <hr class="divider"/>
                                                        <div class="form-group" style="padding-left: 20px;">
                                                            <label class="radio">
                                                                <input checked="checked" type="radio" name="stockType" value="new"> New Inventory
                                                            </label>
                                                            <label class="radio">
                                                                <input type="radio" name="stockType" value="used"> Used Inventory
                                                            </label>
                                                        </div>
                                                        <div class="input-group">
                                                            <input type="text" id="stockNumber" name="stockNumber" th:value="${searchForm?.stockNumber}" autocomplete="off" placeholder="Enter Stock Number" class="form-control" />
                                                            <span class="input-group-btn">
                                                                <button class="btn btn-primary" type="submit">
                                                                    <i class="fa fa-search fa-fw"></i>
                                                                    Search
                                                                </button>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--END STOCK NUMBER SEARCH TAB-->
                        </div>
                    </div>
                </div>
                <h1 id="searchResults">Search Results</h1>
                <div th:if="${page}">Sort By:
                    <a class="sorted" sd:pagination-sort="year">Year</a> |
                    <a class="sorted" sd:pagination-sort="make">Make</a> |
                    <a class="sorted" sd:pagination-sort="model">Model</a> |
                    <a class="sorted" sd:pagination-sort="trim">Trim</a> |
                    <a class="sorted" sd:pagination-sort="exteriorColor">Exterior Color</a> |
                    <a class="sorted" sd:pagination-sort="interiorColor">Interior Color</a> |
                    <a class="sorted" sd:pagination-sort="price">Price</a> |
                    <a class="sorted" sd:pagination-sort="miles">Miles</a> |
                    <a class="sorted" sd:pagination-sort="doors">Doors</a>
                </div>
            </div>
        </div>
    </div>
    <div class="result-wrapper container">
        <div th:if="${#lists.isEmpty(vehicles)}">
            <div class="ibox">
                <div class="ibox-content">
                    <span class="no-result-msg" th:text="${'No results found. Please try searching again.'}"></span>
                </div>
            </div>
        </div>

        <div th:if="${not #lists.isEmpty(vehicles)}">
            <!--INVENTORY RESULTS FRAGMENT-->
            <div th:replace="checkin/inventoryResults :: listVehicles(${vehicles})"></div>
        </div>
    </div>

    <!--PAGINATION-->
    <div th:if="${page}">
        <th:block th:insert="fragments/pagination :: sd-controls()" />
    </div>

</div>
<div layout:fragment="page-scripts">
    <script type="text/javascript" th:src="@{/js/checkin-inventory.js}"></script>
</div>
</body>
</html>
