<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout}"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<!--/* workaround for intellij bug: https://goo.gl/Qf9LGj */-->
<!--/*@thymesVar id="inventoryMetrics" type="com.carsaver.magellan.model.metrics.InventoryMetrics"*/-->
<!--/*@thymesVar id="metric" type="com.carsaver.magellan.model.metrics.InventoryMetrics"*/-->

<th:block th:fragment="inventoryMetricsPaginated(inventoryMetrics)">

    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover dataTables-example" aria-describedby="Inventory" >
            <thead>
                <tr>
                    <th scope="col">Status</th>
                    <th scope="col">Name</th>
                    <th scope="col">State</th>
                    <th scope="col">Dealer Id</th>
                    <th scope="col"><a class="sorted" sd:pagination-sort="totalUsedPriced">% Used Priced</a></th>
                    <th scope="col"><a class="sorted" sd:pagination-sort="totalNewPriced">% New Priced</a></th>
                    <th scope="col"><a class="sorted" sd:pagination-sort="totalPercentageNewAndUsedPriced">% Total New and Used Priced</a></th>
                    <th scope="col">Manage Pricing</th>
                </tr>
            </thead>
            <tbody th:replace="fragments/inventoryMetrics :: inventoryMetricsTableBody(${inventoryMetrics})"></tbody>
        </table>
    </div>

    <th:block th:replace="fragments/pagination :: sd-controls()" />

</div>
</th:block>

<tbody th:fragment="inventoryMetricsTableBody(inventoryMetrics)">
    <tr th:each="metric : ${inventoryMetrics}">
        <td>
            <th:block th:replace="fragments/inventoryMetrics :: status(${metric})" />
        </td>
        <td>
            <a th:href="@{/dealer/{dealerId}(dealerId=${metric.dealerId})}" th:text="${metric.dealerName}?: _">-</a>
        </td>
        <td th:text="${metric.state}?: _">-</td>
        <td th:text="${metric.dealerDealerId}?: _">-</td>
        <td><span th:text="${metric.percentageUsedPriced}?: _">-</span></td>
        <td><span th:text="${metric.percentageNewPriced}?: _">-</span></td>
        <td><span th:text="${metric.totalPercentageNewAndUsedPriced}?: _">-</span></td>
        <td>
            <span>
                <a th:href="@{|/stratus/dealer/${metric.dealerId}/vehicle-pricing|}"
                class="btn btn-default btn-sm header-btn btn-block"
                sec:authorize="hasAuthority('edit:pricing-new') or hasAuthority('edit:pricing-used')">
                <i class="fa fa-pencil"></i>Manage Pricing</a>
            </span>
        </td>
    </tr>
</tbody>

<th:block th:fragment="status(metric)" th:assert="${metric}" th:switch="${{metric.dealerStatus}}">
    <span th:case="ONBOARDING" class="label label-info"  th:text="${metric.dealerStatus}">ONBOARDING</span>
    <span th:case="LIVE" class="label label-success" th:text="${metric.dealerStatus}">LIVE</span>
    <span th:case="CANCELLED" class="label" th:text="${metric.dealerStatus}">CANCELLED</span>
    <span th:case="PROSPECT" class="label label-warning" th:text="${metric.dealerStatus}">PROSPECT</span>
    <span th:case="*" class="label">UNKNOWN</span>
</th:block>

</html>
