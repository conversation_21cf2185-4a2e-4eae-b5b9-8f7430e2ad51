<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<body>

<!--
*********************************************************
* This fragment renders a logout link.
*
* If CSRF protection is enabled in Spring Security, the
* link will POST a form with the CSRF params.
*
* If CSRF protection is not enabled, it will be a simple
* link to '/logout'
*********************************************************
-->
<th:block th:fragment="renderLink(linkId, linkClass, showIcon, iconClass)"
    th:with="csrfEnabled=${_csrf != null}, linkAction = ${csrfEnabled} ? 'javascript:document.forms[\'logoutForm\'].submit();' : '/logout' ">

    <a th:id="${linkId}" th:class="${linkClass}" th:href="${linkAction}">
        <i th:class="${iconClass}" th:if="${showIcon}"></i>
        Logout
    </a>

    <!-- the csrf field is automatically added to the form because we are using th:action -->
    <form th:if="${csrfEnabled}" name="logoutForm" th:action="@{/logout}" method="post">
    </form>

</th:block>

</body>
</html>
