<th:block xmlns:th="http://www.thymeleaf.org">

<!--/* workaround for intellij bug: https://goo.gl/Qf9LGj */-->
<!--/*@thymesVar id="tradeVehicle" type="com.carsaver.magellan.model.user.UserVehicleView"*/-->

<body>
    <div class="ibox" id="tradeVehicles" th:assert="${tradeVehicles}" th:fragment="tradeVehicles(tradeVehicles)">
        <div class="ibox-title">
            <h5>Trade Vehicles</h5>
            <div class="ibox-tools">
                <a class="collapse-link">
                    <i class="fa fa-chevron-up"></i>
                </a>
            </div>
        </div>
        <div class="ibox-content" >
            <div th:if="${#lists.isEmpty(tradeVehicles)}">No Trade Vehicles found for this user.</div>
            <table th:unless="${#lists.isEmpty(tradeVehicles)}" class="table table-striped" aria-describedby="Trade Vehicles">
                <thead>
                <tr>
                    <th scope="col">Vehicle</th>
                    <th scope="col">VIN</th>
                    <th scope="col">Mileage</th>
                    <th scope="col">Offer Price</th>
                    <th scope="col">Offer Expiration</th>
                    <th scope="col">Monthly Payment</th>
                    <th scope="col">Purchase Type</th>
                </tr>
                </thead>
                <tbody>
                <tr th:each="tradeVehicle : ${tradeVehicles}">
                    <td th:text="${tradeVehicle.getYearMakeModel()} ?: _">-</td>
                    <td th:text="${tradeVehicle.vin} ?: _">-</td>
                    <td th:text="${tradeVehicle.mileage} ?: _">-</td>
                    <td th:text="'$'+ ${#numbers.formatDecimal(tradeVehicle.offer.vehicleOfferPrice,1,'COMMA',0,'COMMA')} ?: _">-</td>
                    <td th:if="${tradeVehicle.offerExpirationDate}" class="utc-to-local-date" th:text="${#temporals.formatISO(tradeVehicle.offerExpirationDate)} ?:_">-</td>
                    <td th:unless="${tradeVehicle.offerExpirationDate}" th:text="${'-'}">-</td>
                    <td th:text="${tradeVehicle.monthlyPayment} ? ${{tradeVehicle.monthlyPayment}} : '-'">-</td>
                    <td th:text="${tradeVehicle.purchaseType} ?: _">-</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</th:block>
