<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<body>
<div th:fragment="certificatePricing(certificate)" th:assert="${certificate}">
    <div class="ibox">
        <div class="ibox-title">
            <h5><i class="fa fa-car" aria-hidden="true"></i> Vehicle of Interest</h5>
        </div>
        <div class="ibox-content">
            <div class="row">
                <div class="col-md-8">
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Vehicle:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${{certificate?.style != null}}? ${{certificate?.style}} : '-'"></span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Trim:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${certificate.style?.name} ?: _">-</span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Type:</strong>
                        </div>
                        <div class="detailValue">
                            <span class="label label-primary" th:if="${certificate.vehicle?.vin}">In-stock Vehicle</span>
                            <span class="label label-primary" th:unless="${certificate.vehicle?.vin}">Configured Vehicle</span>
                        </div>
                    </div>
                    <div class="optionWrapper">
                        <div class="optionLabel">
                            <strong>Lifetime Warranty:</strong>
                        </div>
                        <div class="optionValue">
                            <span th:text="${certificate.isLifetimeWarrantyEligible()} ? 'ELIGIBLE' : 'NOT ELIGIBLE'">-</span>
                        </div>
                    </div>
                    <div class="detailWrapper" th:if="${certificate.vehicle?.vin}">
                        <div class="detailLabel">
                            <strong>VIN:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${certificate.vehicle?.vin}">In-stock Vehicle</span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>MSRP:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${{certificate.vehiclePrices?.msrp != null}}? ${{certificate.vehiclePrices?.msrp}} : '-'"></span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>CarSaver Price:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${{certificate.vehiclePrices?.carsaverPrice != null}}? ${{certificate.vehiclePrices?.carsaverPrice}} : '-'"></span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Invoice:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${{certificate.vehiclePrices?.invoicePrice != null}}? ${{certificate.vehiclePrices?.invoicePrice}} : '-'"></span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Average Market Price:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${{certificate.vehiclePrices?.averageMarketPrice != null}}? ${{certificate.vehiclePrices?.averageMarketPrice}} : '-'"></span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Total Dealer Fees:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${{certificate.vehiclePrices?.totalDealerFees != null}}? ${{certificate.vehiclePrices?.totalDealerFees}} : '-'"></span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Destination Fees:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${{certificate.vehiclePrices?.destinationFees != null}}? ${{certificate.vehiclePrices?.destinationFees}} : '-'"></span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Rebates:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${{certificate.vehiclePrices?.rebates != null}}? ${{certificate.vehiclePrices?.rebates}} : '-'"></span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Sales Tax:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${{certificate.vehiclePrices?.salesTax != null}}? ${{certificate.vehiclePrices?.salesTax}} : '-'"></span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Title License Fee:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${{certificate.vehiclePrices?.titleLicenseFee != null}}? ${{certificate.vehiclePrices?.titleLicenseFee}} : '-'"></span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Out The Door Price:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${{certificate.vehiclePrices?.outTheDoorPrice != null}}? ${{certificate.vehiclePrices?.outTheDoorPrice}} : '-'"></span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Savings:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${{certificate.prices?.prices?.savings != null}}? ${{certificate.prices?.prices?.savings}} : '-'">-</span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Interior Color:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${certificate.vehicle?.interiorColor} ?: _">-</span>
                        </div>
                    </div>
                    <div class="detailWrapper">
                        <div class="detailLabel">
                            <strong>Exterior Color:</strong>
                        </div>
                        <div class="detailValue">
                            <span th:text="${certificate.vehicle?.exteriorColor} ?: _">-</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4" th:if="${certificate}">
                    <th:block th:replace="fragments/vehicleImage :: certificateVehicleImage(${certificate})" />
                </div>
            </div>
        </div>
    </div>

</div>

<div th:fragment="certificatePricingAlt(certificate)" th:assert="${certificate}">
    <span class="vehicle-title" th:text="${{certificate.style}}">-</span>
    <hr class="vehicle-title-divider" />
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Type:</strong>
        </div>
        <div class="optionValue">
            <span th:if="${certificate.vehicle?.vin}" class="label label-primary" th:text="${vehicle?.stockType} ?: _">-</span>
            <span th:unless="${certificate.vehicle?.vin}" class="label label-primary">Configured Vehicle</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Lifetime Warranty:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${certificate.isLifetimeWarrantyEligible()} ? 'ELIGIBLE' : 'NOT ELIGIBLE'">-</span>
        </div>
    </div>
    <div class="optionWrapper" th:if="${certificate.vehicle?.vin}">
        <div class="optionLabel">
            <strong>Vin Number:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle?.vin} ?: _">-</span>
        </div>
    </div>
    <div class="optionWrapper" th:if="${certificate.vehicle?.vin}">
        <div class="optionLabel">
            <strong>Stock Number:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle?.stockNumber} ?: _">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>MSRP:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{certificate.vehiclePrices?.msrp}} ?: _">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Average Market Price:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{certificate.vehiclePrices?.averageMarketPrice != null}}? ${{certificate.vehiclePrices?.averageMarketPrice}} : '-'"></span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>CarSaver Price:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{certificate.vehiclePrices?.carsaverPrice}} ?: _">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Invoice:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{certificate.vehiclePrices?.invoicePrice != null}}? ${{certificate.vehiclePrices?.invoicePrice}} : '-'"></span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Total Dealer Fees:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{certificate.vehiclePrices?.totalDealerFees != null}}? ${{certificate.vehiclePrices?.totalDealerFees}} : '-'"></span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Destination Fees:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{certificate.vehiclePrices?.destinationFees != null}}? ${{certificate.vehiclePrices?.destinationFees}} : '-'"></span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Rebates:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{certificate.vehiclePrices?.rebates != null}}? ${{certificate.vehiclePrices?.rebates}} : '-'"></span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Sales Tax:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{certificate.vehiclePrices?.salesTax != null}}? ${{certificate.vehiclePrices?.salesTax}} : '-'"></span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Title License Fee:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{certificate.vehiclePrices?.titleLicenseFee != null}}? ${{certificate.vehiclePrices?.titleLicenseFee}} : '-'"></span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong th:if="${certificate.vehiclePrices?.salesTax == null}">Price without taxes:</strong>
            <strong th:if="${certificate.vehiclePrices?.salesTax != null}">Out The Door Price:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{certificate.vehiclePrices?.outTheDoorPrice != null}}? ${{certificate.vehiclePrices?.outTheDoorPrice}} : '-'"></span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Savings:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{certificate.prices?.prices?.savings != null}}? ${{certificate.prices?.prices?.savings}} : '-'">-</span>
        </div>
    </div>
    <div class="optionWrapper" th:if="${certificate.vehicle?.vin}">
        <div class="optionLabel">
            <strong>CarFax:</strong>
        </div>
        <div class="optionValue">
            <a th:href="${certificate.vehicle?.carfaxUrl} ?: _" target="_blank">CarFax</a>
        </div>
    </div>
    <div class="optionWrapper" th:if="${certificate.vehicle?.vin}">
        <div class="optionLabel">
            <strong>Mileage:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${certificate.vehicle?.miles} ?: _">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Exterior Color:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${certificate.vehicle?.exteriorColor} ?: _">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Interior Color:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${certificate.vehicle?.interiorColor} ?: _">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Doors:</strong>
        </div>
        <div class="optionValue">
            <span th:if="${certificate.vehicle?.vin}" th:text="${certificate.vehicle?.doors} ?: _">-</span>
            <span th:unless="${certificate.vehicle?.vin}" th:text="${certificate.style?.passengerDoors} ?: _">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Condition:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${certificate.vehicle?.stockType} ?: _">-</span>
        </div>
    </div>
    <div class="optionWrapper" th:if="${certificate.vehicle?.vin}">
        <div class="optionLabel">
            <strong>City Mpg:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle?.mpgCity} + ' Mpg'" th:unless="${vehicle?.mpgCity == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper" th:if="${certificate.vehicle?.vin}">
        <div class="optionLabel">
            <strong>Highway Mpg:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle?.mpgHighway} + ' Mpg'" th:unless="${vehicle?.mpgHighway == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper" th:if="${certificate.vehicle?.vin}">
        <div class="optionLabel">
            <strong>Fuel Type:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${certificate.vehicle?.fuelType} ?: _">-</span>
        </div>
    </div>
    <div class="optionWrapper" th:if="${certificate.vehicle?.vin}">
        <div class="optionLabel">
            <strong>Engine:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${certificate.vehicle?.engine} ?: _">-</span>
        </div>
    </div>
    <div class="optionWrapper" th:if="${certificate.vehicle?.vin}">
        <div class="optionLabel">
            <strong>Transmission:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${certificate.vehicle?.transmission} ?: _">-</span>
        </div>
    </div>
</div>

<div th:fragment="inventorySearchResults(vehicle)" th:assert="${vehicle}">
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Vin:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle.vehicleView?.vin}" th:unless="${vehicle.vehicleView?.vin == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Stock:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle.vehicleView?.stockNumber}" th:unless="${vehicle.vehicleView?.stockNumber == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Condition:</strong>
        </div>
        <div class="optionValue">
            <span class="label label-primary" th:text="${vehicle.vehicleView?.stockType}" th:unless="${vehicle.vehicleView?.stockType == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper" th:if="${vehicle.vehicleView?.carfaxUrl}">
        <div class="optionLabel">
            <strong>CarFax:</strong>
        </div>
        <div class="optionValue">
            <a th:href="${vehicle.vehicleView?.carfaxUrl}" target="_blank">CarFax</a>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Mileage:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${#numbers.formatInteger(vehicle.vehicleView?.miles,3,'COMMA')}" th:unless="${vehicle.vehicleView?.miles == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>MSRP:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{vehicle.pricing?.msrp}}" th:unless="${vehicle.pricing?.msrp == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>CarSaver Price:</strong>
        </div>
        <div class="optionValue">
            <i class="fa fa-info-circle" data-toggle="tooltip" title="* The CarSaver price has rebates included and is based on a cash deal scenario."></i>
            <span th:text="${{vehicle.pricing?.carsaverPrice}}" th:unless="${vehicle.pricing?.carsaverPrice == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Rebate:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{vehicle.pricing?.rebates}}" th:unless="${vehicle.pricing?.rebates == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Savings:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${{vehicle.pricing?.savings != null}}? ${{vehicle.pricing?.savings}} : '-'">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Exterior Color:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle.vehicleView?.exteriorColor}" th:unless="${vehicle.vehicleView?.exteriorColor == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Interior Color:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle.vehicleView?.interiorColor}" th:unless="${vehicle.vehicleView?.interiorColor == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Doors:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle.vehicleView?.doors}" th:unless="${vehicle.vehicleView?.doors == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>City Mpg:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle.vehicleView?.mpgCity} + ' Mpg'" th:unless="${vehicle.vehicleView?.mpgCity == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Highway Mpg:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle.vehicleView?.mpgHighway} + ' Mpg'" th:unless="${vehicle.vehicleView?.mpgHighway == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Fuel Type:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle.vehicleView?.fuelType}" th:unless="${vehicle.vehicleView?.fuelType == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Engine:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle.vehicleView?.engine}" th:unless="${vehicle.vehicleView?.engine == null}">-</span>
        </div>
    </div>
    <div class="optionWrapper">
        <div class="optionLabel">
            <strong>Transmission:</strong>
        </div>
        <div class="optionValue">
            <span th:text="${vehicle.vehicleView?.transmission}" th:unless="${vehicle.vehicleView?.transmission == null}">-</span>
        </div>
    </div>
</div>
</body>
</html>
