<th:block xmlns:th="http://www.thymeleaf.org">

    <div class="ibox" th:fragment="iboxSpinner(header)">
        <div class="ibox-title">
            <h5 th:text="${header}">Notes</h5>
        </div>
        <div class="ibox-content">
            <div>
                <div class="sk-spinner sk-spinner-wave">
                    <div class="sk-rect1"></div>
                    <div class="sk-rect2"></div>
                    <div class="sk-rect3"></div>
                    <div class="sk-rect4"></div>
                    <div class="sk-rect5"></div>
                </div>
            </div>
        </div>
    </div>

    <div th:fragment="spinnerOnly">
        <div class="sk-spinner sk-spinner-wave">
            <div class="sk-rect1"></div>
            <div class="sk-rect2"></div>
            <div class="sk-rect3"></div>
            <div class="sk-rect4"></div>
            <div class="sk-rect5"></div>
        </div>
    </div>

</th:block>
