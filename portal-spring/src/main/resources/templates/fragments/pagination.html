<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<!--/* workaround for intellij bug: https://goo.gl/Qf9LGj */-->
<!--/*@thymesVar id="page" type="com.carsaver.magellan.api.util.PageableResponse" */-->


<body>
    <div th:fragment="header(page)" th:assert="${page}" th:inline="text">
        Page [[${page.number + 1}]] of [[${page.totalPages}]]
    </div>
    <div th:fragment="controls(page, additionalParamsStr)" th:assert="${page}">
        <nav th:if="${page.totalElements > 0}">
            <ul class="pagination">
                <li th:class="${page.first}?'page-item disabled':'page-item'">
                    <a class="page-link" th:href="@{'?'+ ${additionalParamsStr}(page=${page.number} - 1, size=${page.size})}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                        <span class="sr-only">Previous</span>
                    </a>
                </li>
                <li class="page-item"  th:each="pageNo : ${#numbers.sequence(0, page.totalPages - 1)}" th:classappend="${page.number eq pageNo}? 'active' : ''">
                    <a class="page-link" th:href="@{'?'+ ${additionalParamsStr}(page=${pageNo}, size=${page.size})}" th:inline="text">[[${pageNo + 1}]]</a>
                </li>
                <li th:class="${page.last}?'page-item disabled':'page-item'">
                    <a class="page-link" th:href="@{'?'+ ${additionalParamsStr}(page=${page.number} + 1, size=${page.size})}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                        <span class="sr-only">Next</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>

    <div class="row" th:fragment="sd-controls()">
        <div class="col-sm-6">
            <div sd:pagination-summary="">info</div>
        </div>
        <div class="col-sm-6">
            <nav class="pull-right">
                <ul class="pagination" sd:pagination="full">
                    <!-- Pagination created by SpringDataDialect, this content is just for mockup -->
                    <li class="disabled"><a href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>
                    <li class="active"><a href="#">1 <span class="sr-only">(current)</span></a></li>
                </ul>
            </nav>
        </div>
    </div>

    <!--  POR-2049: quick fix for checkin portal searchResults page  -->
    <div class="row" th:fragment="sd-controls-no-summary()">
        <div class="col-sm-6">
            <nav class="pull-right">
                <ul class="pagination" sd:pagination="full">
                    <!-- Pagination created by SpringDataDialect, this content is just for mockup -->
                    <li class="disabled"><a href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>
                    <li class="active"><a href="#">1 <span class="sr-only">(current)</span></a></li>
                </ul>
            </nav>
        </div>
    </div>
</body>
</html>
