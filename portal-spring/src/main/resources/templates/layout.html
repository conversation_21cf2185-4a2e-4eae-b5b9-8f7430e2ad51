<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<!--/* workaround for intellij bug: https://goo.gl/Qf9LGj */-->
<!--/*@thymesVar id="dealer" type="com.carsaver.magellan.model.DealerView"*/-->

<head>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5WCBK49');</script>
    <!-- End Google Tag Manager -->

    <title layout:title-pattern="$LAYOUT_TITLE - $CONTENT_TITLE">CarSaver Portal</title>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <th:block th:if="${_csrf}">
        <meta name="_csrf" th:content="${_csrf.token}"/>
        <!-- default header name is X-CSRF-TOKEN -->
        <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
    </th:block>

    <!-- Bootstrap core CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css"
          integrity="sha256-916EbMg70RQy9LHiGkXzG8hSg9EdNy97GazNG/aiY1w=" crossorigin="anonymous"/>
    <!-- Latest compiled and minified CSS -->
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/jasny-bootstrap/3.1.3/css/jasny-bootstrap.min.css">
    <!--Bootstrap tour CSS-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tour/0.11.0/css/bootstrap-tour.min.css" integrity="sha256-r7R5NjcsiLK3ubv/Ee7yBl5n9zV+xoj1K6kzJ4E9jBU=" crossorigin="anonymous" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
          integrity="sha256-eZrrJcwDc/3uDhsdt61sL2oOBY362qM3lon1gyExkL0=" crossorigin="anonymous"/>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.3/toastr.min.css" integrity="sha256-R91pD48xW+oHbpJYGn5xR0Q7tMhH4xOrWn1QqMRINtA=" crossorigin="anonymous" />

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.5.2/animate.min.css"
          integrity="sha256-j+P6EZJVrbXgwSR5Mx+eCS6FvP9Wq27MBRC/ogVriY0=" crossorigin="anonymous"/>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" />

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ion-rangeslider/2.1.7/css/ion.rangeSlider.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ion-rangeslider/2.1.7/css/ion.rangeSlider.skinHTML5.min.css" />

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Ladda/1.0.1/ladda-themeless.min.css" />

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/iCheck/1.0.2/skins/square/blue.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.6.2/chosen.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.6.4/css/bootstrap-datepicker.min.css"/>

    <link rel="stylesheet" th:href="@{/css/jquery.steps.css}"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.6.4/css/bootstrap-datepicker3.min.css"/>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/clockpicker/0.0.7/bootstrap-clockpicker.min.css" integrity="sha256-lBtf6tZ+SwE/sNMR7JFtCyD44snM3H2FrkB/W400cJA=" crossorigin="anonymous" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Ladda/1.0.1/ladda-themeless.min.css" integrity="sha256-qj+2zCT2uHI/NSTv7154sqdbmbNaxKFIfVJN4pvSv0M=" crossorigin="anonymous" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vue-multiselect@2.1.0/dist/vue-multiselect.min.css" integrity="sha256-3f/B+1hX1WQ8ARPmJNAT5negBTgYRhaHfbziEqu7/EE=" crossorigin="anonymous">

    <script src="https://cdn.rawgit.com/google/code-prettify/master/loader/run_prettify.js"></script>

    <!-- Your custom styles (optional) -->
    <link th:href="@{/css/style.css}" rel="stylesheet">
    <link th:href="@{/css/overrides.css}" rel="stylesheet">
    <link th:href="@{/css/pricing-table.css}" rel="stylesheet">
    <link th:href="@{/dist/css/vendor.css}" rel="stylesheet">
    <link th:href="@{/dist/css/dealer.css}" rel="stylesheet">
    <link th:href="@{/dist/css/vehicleSale.css}" rel="stylesheet">

    <link th:src="@{/dist/js/vendor.js}" rel="prefetch" as="script">
    <link th:src="@{/dist/js/dealer.js}" rel="prefetch" as="script">
    <link th:src="@{/dist/js/event.js}" rel="prefetch" as="script">
    <link th:src="@{/dist/js/allNotes.js}" rel="prefetch" as="script">
    <link th:src="@{/dist/js/user.js}" rel="prefetch" as="script">
    <link th:src="@{/dist/js/quantum.js}" rel="prefetch" as="script">

    <link th:src="@{/dist/css/allNotes.css}" rel="prefetch" as="style">
    <link th:src="@{/dist/css/event.css}" rel="prefetch" as="style">
    <link th:src="@{/dist/css/user.css}" rel="prefetch" as="style">
    <link th:src="@{/dist/css/quantum.css}" rel="prefetch" as="style">
</head>

<body>

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5WCBK49"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

<div id="wrapper">
    <nav th:replace="fragments/navigation :: navigation"></nav>

    <div id="page-wrapper" class="gray-bg">
        <div class="row border-bottom">
            <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
                <div class="navbar-header">
                    <a class="navbar-minimalize minimalize-styl-2 btn btn-primary" href="#"><i class="fa fa-bars"></i> </a>
                    <span class="navbar-brand">
                    <a href="/">
                    <img src="/images/header_logo.png" style="height: 30px;">
                    </a>
                </span>
                </div>
                <ul class="nav navbar-top-links navbar-right">
                    <li th:with="dealerAccessList=${#session.getAttribute('userDealerAccessList')}">
                        <!--/* 'selectedDealerId' attribute is determined and set in DealerSelectInterceptor */-->
                        <select id="dealerSwitcher"
                                class="form-control"
                                th:unless="${dealerAccessList == null}"
                                th:if="${#lists.size(dealerAccessList) > 1}">
                            <option value="">Choose a Dealership</option>
                            <option th:selected="${#request.getAttribute('selectedDealerId') == dealer.id}" th:each="dealer : ${#session.getAttribute('userDealerAccessList')}" th:value="${dealer.id}" th:text="${dealer.name}"></option>
                        </select>
                    </li>
                    <li>
                        <headway></headway>
                    </li>
                    <li>
                        <a th:replace="fragments/logout :: renderLink('navbar-right-logout', '', true, 'fa fa-sign-out')"></a>
                    </li>
                </ul>
            </nav>
        </div>

        <div layout:fragment="content"></div>
    </div>
</div>

<!-- JQuery -->
<script src="//cdnjs.cloudflare.com/ajax/libs/jquery/3.1.1/jquery.min.js"
        integrity="sha256-hVVnYaiADRTO2PzUGmuLJr8BLUSjGIZsDYGmIJLv2b8=" crossorigin="anonymous"></script>

<!-- Bootstrap core JavaScript -->
<script src="//cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"
        integrity="sha256-U5ZEeKfGNOja007MMD3YBI0A3OSZOQbeG6z2f2Y0hu8=" crossorigin="anonymous"></script>

<!-- Inspinia core JavaScript -->
<script type="text/javascript" th:src="@{/js/inspinia.js}"></script>
<script type="text/javascript" th:src="@{/js/common.js}"></script>

<script src="//cdnjs.cloudflare.com/ajax/libs/pace/1.0.2/pace.min.js" integrity="sha256-EPrkNjGEmCWyazb3A/Epj+W7Qm2pB9vnfXw+X6LImPM=" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/1000hz-bootstrap-validator/0.11.9/validator.min.js" integrity="sha256-dHf/YjH1A4tewEsKUSmNnV05DDbfGN3g7NMq86xgGh8=" crossorigin="anonymous"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/jasny-bootstrap/3.1.3/js/jasny-bootstrap.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/metisMenu/2.6.1/metisMenu.min.js" integrity="sha256-WEO1l3uZIPsj/wIs7+ypcoYAWjfDvCzLNdpELrSAX4o=" crossorigin="anonymous"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/jQuery-slimScroll/1.3.8/jquery.slimscroll.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.17.1/moment.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.17/moment-timezone-with-data.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/datatables/1.10.13/js/jquery.dataTables.min.js" integrity="sha256-yWA356lDhruy1J8jGncaMWKAPYDbK47OKb0uT/aELLc=" crossorigin="anonymous"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.3/toastr.min.js" integrity="sha256-yNbKY1y6h2rbVcQtf0b8lq4a+xpktyFc3pSYoGAY1qQ=" crossorigin="anonymous"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.0.6/handlebars.min.js" integrity="sha256-1O3BtOwnPyyRzOszK6P+gqaRoXHV6JXj8HkjZmPYhCI=" crossorigin="anonymous"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js" integrity="sha256-egVvxkq6UBCQyKzRBrDHu8miZ5FOaVrjSqQqauKglKc=" crossorigin="anonymous"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/ion-rangeslider/2.1.7/js/ion.rangeSlider.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/Ladda/1.0.1/spin.min.js" integrity="sha256-oNtUmAZAsXy3Pg53xwfYE1YYgfdktOImDPd57g6Ldek=" crossorigin="anonymous"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/Ladda/1.0.1/ladda.min.js" integrity="sha256-5HH0pMiF5FysoeSnW31gSRNgytTvXvjudDLOUe5KdY0=" crossorigin="anonymous"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/Ladda/1.0.1/ladda.jquery.min.js" integrity="sha256-2174N0YRTiLgYMSg3QVCqweYaBx0FgeOEvFhW1mJlfo=" crossorigin="anonymous"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/iCheck/1.0.2/icheck.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/chosen/1.6.2/chosen.jquery.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/floatthead/2.0.3/jquery.floatThead.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/jquery-steps/1.1.0/jquery.steps.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.16.0/jquery.validate.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.6.4/js/bootstrap-datepicker.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/peity/3.2.1/jquery.peity.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/clockpicker/0.0.7/bootstrap-clockpicker.js" integrity="sha256-OIIIhnfeTrz7TTtNh60DMH8U7bNMBH+noeMzo2uSJTI=" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tour/0.11.0/js/bootstrap-tour.min.js" integrity="sha256-AFoIN2Z5u5QDw8n9X0FoP/p1ZhE1xDnsgHlCMWE0yYQ=" crossorigin="anonymous"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/jquery.form/4.2.2/jquery.form.min.js" integrity="sha384-FzT3vTVGXqf7wRfy8k4BiyzvbNfeYjK+frTVqZeNDFl8woCbF0CYG6g2fMEFFo/i" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/URI.js/1.19.0/URI.min.js" integrity="sha256-r1xY9ZVKxg3yQJup3KeUJLtYqrqUt9aeF7KHaprys34=" crossorigin="anonymous"></script>

<script type="application/javascript" th:src="@{/dist/js/vendor.js}"></script>

<script th:if="${globalMessage != null}" th:inline="javascript">
    /*<![CDATA[*/
    var portalActionMessage = /*[[${globalMessage}]]*/
        toastr[portalActionMessage.type.toLowerCase()](portalActionMessage.message);
    /*]]>*/
</script>

<script th:if="${session.user}" th:inline="javascript">
if(typeof FS !== 'undefined') {
    FS.identify(/*[[${session.user.id}]]*/, {
        displayName: /*[[${session.user.fullName}]]*/,
        email: /*[[${session.user.email}]]*/
    });
}
</script>

<script th:inline="javascript" type="application/javascript">
    window._CS_AUTHORIZATIONS = /*[[${#authentication.authorities}]]*/;
    window._CS_DEALER_PERMISSIONS = /*[[${#authentication?.principal?.dealerPermissions}]]*/;
    window._CS_USER = {
        id: /*[[${session.user.id}]]*/,
        email: /*[[${session.user.email}]]*/
    };
    window._CS_ENV = /*[[${@environment.getActiveProfiles()[0]}]]*/;
    window._CS_FEATURE_FLAGS = /*[[${#session.getAttribute('featureFlags')}]]*/;
</script>

<div layout:fragment="page-scripts">
</div>

<th:block layout:fragment="body-scripts"></th:block>

<!-- This is a placeholder div for anything that wants to display in a modal -->
<div id="modal-target" class="modal fade" tabindex="-1" role="dialog"></div>

</body>
</html>
