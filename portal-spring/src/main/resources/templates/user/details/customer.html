<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout_b4}">

<!--/*@thymesVar id="user" type="com.carsaver.magellan.model.UserView"*/-->

<head>
    <title>Customer User Details</title>
    <link th:href="@{/dist/css/user.css}" rel="stylesheet">
</head>

<body>

<div layout:fragment="content" id="root">

    <user-banner :is-details="true" :is-list="false"></user-banner>

    <div class="wrapper wrapper-content">
        <div class="row">
            <div class="col-md-6 col-xl-3">
                <manage-links></manage-links>
            </div>
            <div class="col-xl-9">
                <details-customer-user></details-customer-user>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <detail-sections-customer-user></detail-sections-customer-user>
            </div>
        </div>

    </div>
    <schedule-appointment-modal></schedule-appointment-modal>
    <send-lead-modal></send-lead-modal>
</div>
<th:block layout:fragment="body-scripts">
    <script type="application/javascript" th:inline="javascript">
        window._CS_SELECTED_USER = [[${selectedUser}]];
    </script>
    <script type="application/javascript" th:src="@{/dist/js/user.js}"></script>
    <script>
        $(function() {
            var statusAnchor = $(".status-anchor");

            statusAnchor.on("click", function() {
                var resolutionDropdown = $(this).closest('td').next().find(".resolution-dropdown");
                var statusButton = $(this).closest('td').find(".dealerLink-status-dropdown");

                statusButton.removeClass("btn-default").removeClass("btn-danger").removeClass("btn-primary");

                if ($(this).data("property") === "LOST") {
                    resolutionDropdown.removeClass('hidden').show();
                    statusButton.addClass("btn-danger");
                } else {
                    resolutionDropdown.hide();
                    statusButton.addClass("btn-primary");
                }
            });
        });
    </script>
    <script type="text/javascript" th:src="@{/js/dealer-link-status.js}"></script>
</th:block>
</body>
</html>
