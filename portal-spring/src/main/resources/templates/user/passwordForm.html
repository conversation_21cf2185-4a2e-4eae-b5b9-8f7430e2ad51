<div id="notes-form-container" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{fragments/modal.html}" th:with="containerId='notes-form-container'">
    <h4 layout:fragment="modal-title">
        <span th:inline="text">Change [[${user.firstName}]] [[${user.lastName}]]'s Password</span>
    </h4>
    <div layout:fragment="modal-body">
        <form data-target="notes-form-container" id="passwordForm" th:action="${formAction}" class="form-horizontal ajax-form" method="post" th:fragment="form">
            <div class="row">
                <div class="form-group col-md-12">
                    <label for="passwordInput" class="control-label">New Password</label>
                    <input type="password" name="password" id="passwordInput" required data-required-error="Password is required." class="form-control" />
                    <div class="help-block with-errors">
                        <p class="has-error text-danger"></p>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group col-md-12">
                    <label for="passwordConfirmationInput" class="control-label">Confirm New Password</label>
                    <input type="password" name="passwordConfirmation" id="passwordConfirmationInput" required data-required-error="Password Confirmation is required." data-match="#passwordInput" data-match-error="Passwords do not match." class="form-control" />
                    <div class="help-block with-errors">
                        <p class="has-error text-danger"></p>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 pull-right">
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
                    <button class="btn btn-primary" type="submit">Update</button>
                </div>
            </div>
        </form>

        <script>
            // using jquery-validate not bootstrap validator
            $( "#passwordForm" ).validate({
                rules: {
                    password: "required",
                    passwordConfirmation: {
                        equalTo: "#passwordInput"
                    }
                }
            });
        </script>
    </div>
</div>
