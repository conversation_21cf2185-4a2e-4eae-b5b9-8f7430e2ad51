<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout}">
<!--/* workaround for intellij bug: https://goo.gl/Qf9LGj */-->
<!--/*@thymesVar id="loanRequest" type="com.carsaver.magellan.model.finance.LoanRequestView"*/-->
<head>
    <title>Finance Details</title>
</head>
<body>
<div layout:fragment="content">
    <div class="row wrapper border-bottom white-bg page-heading">
        <div class="col-lg-10">
            <h2><a th:href="@{/user/{userId}(userId=${loanRequest.userId})}"  th:text="${loanRequest.user?.fullName}">Appointment Name</a></h2>
            <ol class="breadcrumb">
                <li>
                    <a href="/finance">Finance</a>
                </li>
                <li>
                    <a href="/finance">List</a>
                </li>
                <li class="active">
                    <strong  th:text="${loanRequest.user?.fullName}">User Name</strong>
                </li>
            </ol>
        </div>
    </div>
    <div class="wrapper wrapper-content">
        <div class="row">
            <div class="col-lg-6">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5><i class="fa fa-money fa-fw" aria-hidden="true"></i> Finance Details</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-md-6">
                                <p>
                                    <strong>ID:</strong>
                                    <span th:text="${loanRequest.id} ?: _"></span>
                                </p>
                                <p>
                                    <strong>Status:</strong>
                                    <span th:text="${loanRequest.getStatusEnum().description} ?: _">-</span>
                                </p>
                                <p th:if="${loanRequest.response}">
                                    <strong>Status Response:</strong>
                                    <span th:text="${#strings.capitalizeWords(loanRequest.response?.appStatus)}"></span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p>
                                    <strong>Vehicle:</strong>
                                    <span th:text="${{loanRequest.vehicle}} ?: _"></span>
                                </p>
                                <p>
                                    <strong>Phone Number:</strong>
                                    <span th:text="${{loanRequest.user?.phoneNumber}} ?: _"></span>
                                </p>
                                <p th:if="${loanRequest.response}">
                                    <strong>Horizon App ID:</strong>
                                    <span th:text="${{loanRequest.response?.appId}} ?: _"></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div th:replace="finance/fragments :: tradeVehicle(${loanRequest.certificate})"></div>
            </div>

            <div th:if="${loanRequest?.certificate}" class="col-lg-6">
                <th:block th:replace="fragments/certificatePricing :: certificatePricing(${loanRequest.certificate})" />
            </div>

        </div>

        <hr />

        <div class="row">
            <div class="col-lg-12">
                <h3>Loan Responses:</h3>

                <div th:if="${#lists.isEmpty(loanRequest.responses)}" class="alert alert-info" role="alert">
                    <span class="glyphicon glyphicon-warning-sign" aria-hidden="true"></span>&nbsp;
                    <span>No loan responses currently</span>
                </div>

            </div>

            <div th:if="${loanRequest.responses}" class="col-lg-12" th:each="loanResponse : ${loanRequest.responses}">
                <div class="panel panel-default">
                    <div class="panel-heading" style="background-color: unset;">
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" th:href="${'#collapseOne' + '-' + loanResponse.id}">
                                <strong><span th:text="${loanResponse.data.application.decisions[0].properties.lenderName} ?: _">-</span></strong>&nbsp;
                                <span class="label"th:classappend="${loanResponse.approved ? 'label-success' : 'label-danger'}" th:text="${loanResponse.getLenderStatusEnum().description}"></span>&nbsp;
                                <i class="fa fa-info-circle" aria-hidden="true" data-toggle="tooltip" data-placement="right" title="" data:original-title="@{|Response Id: ${loanResponse.id}, MAL App ID: ${loanResponse.lenderApplicationId}|}"></i>
                            </a>
                        </h5>
                    </div>
                    <div th:id="${'collapseOne' + '-' + loanResponse.id}" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <table class="table table-striped" aria-describedby="Financial details">
                                <thead>
                                <tr>
                                    <th scope="col">NAME</th>
                                    <th scope="col">VALUE</th>
                                </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Lender App ID</td>
                                        <td th:text="${{loanResponse.data.application.decisions[0].lenderAppId}}"></td>
                                    </tr>
                                    <tr>
                                        <td>Monthly Income</td>
                                        <td th:text="${{loanResponse.data.application.monthlyIncome[0]}}"></td>
                                    </tr>
                                    <tr>
                                        <td>Cash Down</td>
                                        <td th:text="${{loanResponse.data.application.cashDown}}"></td>
                                    </tr>
                                    <tr>
                                        <td>Monthly Payment</td>
                                        <td th:text="${{loanResponse.data.application.decisions[0].monthlyPayment[0]}}"></td>
                                    </tr>
                                    <tr>
                                        <td>Minimum Model Year</td>
                                        <td th:text="${loanResponse.data.application.decisions[0].minModelYear[0]}"></td>
                                    </tr>
                                    <tr>
                                        <td>Term</td>
                                        <td th:text="${loanResponse.data.application.decisions[0].term[0]}"></td>
                                    </tr>
                                    <tr>
                                        <td>Rate</td>
                                        <td th:text="${loanResponse.data.application.decisions[0].rate[0]}"></td>
                                    </tr>
                                    <tr>
                                        <td>Minimum Loan Amount</td>
                                        <td th:text="${loanResponse.data.application.decisions[0].minLoanAmount[0]}"></td>
                                    </tr>
                                    <tr>
                                        <td>Max Loan Amount</td>
                                        <td th:text="${loanResponse.data.application.decisions[0].maxLoanAmount[0]}"></td>
                                    </tr>
                                    <tr>
                                        <td>Requested Price</td>
                                        <td th:text="${{loanResponse.requestedPrice}}"></td>
                                    </tr>
                                    <tr>
                                        <td>Expiration Date</td>
                                        <td th:text="${{loanResponse.data.application.decisions[0].expirationDate}}"></td>
                                    </tr>
                                    <tr>
                                        <td>Tier: </td>
                                        <td th:text="${loanResponse.data.application.decisions[0].tier}"></td>
                                    </tr>
                                    <tr>
                                        <td>Conditional Approval</td>
                                        <td th:text="${loanResponse.data.application.decisions[0].conditionalApproval}"></td>
                                    </tr>
                                </tbody>
                                <!--/*
                                <tr>
                                    <td>Stips</td>
                                    <td th:if="${!loanResponse.data.application.decisions[0].stips.isEmpty()}">
                                        <ul>
                                            <li th:each="stip : ${loanResponse.data.application.decisions[0].stips}" th:text="${stip}">Bring Money</li>
                                        </ul>
                                    </td>
                                </tr>
                                */-->
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <hr />

        <div class="row">
            <div class="col-lg-12">
                <h3>Funded Loans:</h3>

                <div th:if="${#lists.isEmpty(loanRequest.funded)}" class="alert alert-info" role="alert">
                    <span class="glyphicon glyphicon-warning-sign" aria-hidden="true"></span>&nbsp;
                    <span>No funded loans currently</span>
                </div>

            </div>
            <div class="col-lg-12" th:each="loanFunded : ${loanRequest.funded}">
                <div class="panel panel-default" >
                    <div class="panel-heading" style="background-color: unset;">
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" th:href="${'#collapseOne' + '-' + loanFunded.id}">
                                <strong><span th:text="${loanFunded.lenderName} ?: _">-</span></strong>
                                <span class="label label-success">Funded</span>
                            </a>
                        </h5>
                    </div>
                    <div th:id="${'collapseOne' + '-' + loanFunded.id}" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <table class="table table-striped" aria-describedby="Loan Info">
                                <tr>
                                    <td>Funding Date</td>
                                    <td th:text="${{loanFunded.fundingDate}}"></td>
                                </tr>
                                <tr>
                                    <td>Loan Amount</td>
                                    <td th:text="${{loanFunded.loanAmount}}"></td>
                                </tr>
                                <tr>
                                    <td>Term</td>
                                    <td th:text="${{loanFunded.term}}"></td>
                                </tr>
                                <tr>
                                    <td>APR</td>
                                    <td th:text="${loanFunded.rate}"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <hr />

        <div class="row">
            <div id="note-list" class="col-lg-6 ajax-load" data:url="@{|/partial/user/${loanRequest.userId}/notes|}">
                <th:block th:insert="fragments/loaders :: iboxSpinner('Notes')" />
            </div>
            <div class="col-lg-6 ajax-load" data:url="@{/partial/user/{userId}/events(userId=${loanRequest.userId})}">
                <th:block th:insert="fragments/loaders :: iboxSpinner('Events')" />
            </div>
        </div>
    </div>
</div>
</body>
</html>

