<div xmlns:th="http://www.thymeleaf.org" id="events-list" sd:page-object="${eventsPage}">
    <div class="ibox">
        <div class="ibox-title">
            <h5>Events</h5>
        </div>
        <div class="ibox-content">
            <div class="activity-stream">
                <div class="stream" th:each="event : ${events}">
                    <div class="stream-badge">
                        <th:block th:switch="${event.eventType}">
                            <i th:case="sms" class="fa fa-keyboard-o bg-primary"></i>
                            <i th:case="email" class="fa fa-envelope bg-primary"></i>
                            <i th:case="directions" class="fa fa-map-marker"></i>
                            <i th:case="checkin" class="fa fa-check-square-o"></i>
                            <i th:case="call" class="fa fa-phone"></i>
                            <i th:case="appt-request" class="fa fa-mail-forward"></i>
                            <i th:case="appt-confirm" class="fa fa-mail-reply bg-primary"></i>
                            <i th:case="site" class="fa fa-car bg-primary"></i>
                            <i th:case="*" class="fa fa-pencil"></i>
                        </th:block>
                    </div>
                    <div class="stream-panel">
                        <div class="stream-info">
                            <span>
                                <img th:src="@{https://www.gravatar.com/avatar/{hash}(hash=${event.triggeredByUser?.gravitarEmailHash},s=32)}" class="img-circle">
                                <span th:text="${event.triggeredByUser?.fullName}">Karen Miggel</span>
                                <span th:text="${#temporals.formatISO(event.eventTime)}" class="date utc-to-local">Today at 01:32:40 am</span>
                                <small class="pull-right" th:text="${{event.durationSinceCreatedDate}}">2h ago</small>
                            </span>
                        </div>
                        <span th:if="${event.description != null}" th:text="${event.description}">Event Description Here</span>
                        <span th:if="${event.description == null}" th:text="${event.eventType + ' - ' + event.eventName}">Event Description Here</span>
                        <audio controls th:if="${event.metadata?.get('recordingUrl') != null}">
                            <source th:src="${event.metadata?.get('recordingUrl')}" type="audio/mpeg">
                            Your browser does not support the audio element.
                        </audio>
                        <span th:if="${event.metadata?.get('errorCode') != null}"> | reference code:
                                <a th:href="@{/error-codes}" class="modal-action"><span th:text="${event.metadata?.get('errorCode')}"></span></a>
                        </span>
                        <a href="#" th:if="${event.metadata?.get('dealerLeadTransactionId') != null}" data-toggle="modal" th:with="dealerLeadTransaction=${event.metadata?.get('dealerLeadTransactionId')}" th:attr="data-target=${'#adf-modal-' + dealerLeadTransaction}">View Lead</a>

                        <div th:id="${'adf-modal-' + event.metadata?.get('dealerLeadTransactionId')}" th:if="${event.metadata?.get('dealerLeadTransactionId') != null}" class="modal fade" tabindex="-1" role="dialog">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                        <h4 class="modal-title">ADF Source</h4>
                                    </div>
                                    <div class="modal-body">
                                        <pre class="pre-scrollable prettyprint lang-xml" th:text="${event.getDealerLeadTransaction()?.getPayload()}"></pre>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                                    </div>
                                </div><!-- /.modal-content -->
                            </div><!-- /.modal-dialog -->
                        </div><!-- /.modal -->
                    </div>
                </div>
            </div>
            <div id="no-events-message" th:if="${#lists.isEmpty(events)}">No Events found for this user.</div>
            <div class="row">
                <div class="col-sm-12">
                    <nav th:unless="${#lists.isEmpty(events)}">
                        <span sd:pagination-summary="compact">info</span>
                        <div id="event-pagination-actions" class="btn-group pager-compact" sd:pagination="compact-pager">
                            <!-- Pagination created by SpringDataDialect, this content is just for mockup -->
                            <a href="#" class="btn btn-default disabled"><span class="glyphicon glyphicon-chevron-left"></span></a>
                            <a href="#" class="btn btn-default"><span class="glyphicon glyphicon-chevron-right"></span></a>
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <script>
        $('#event-pagination-actions > div > a').click(function(event) {
            event.preventDefault();
            var url = event.currentTarget.href;
            $.get(url, function(data) {
                $('#events-list').replaceWith(data);
            });
        });
        $(function () {
            formatTime();
        });
    </script>
</div>

