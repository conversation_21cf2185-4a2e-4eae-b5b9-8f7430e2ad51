<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:data="http://www.thymeleaf.org/extras/data-attribute" layout:decorate="~{layout_b4}">

<!--/* workaround for intellij bug: https://goo.gl/Qf9LGj */-->
<!--/*@thymesVar id="warranty" type="com.carsaver.magellan.model.WarrantyContractView"*/-->

<head>
    <title>Warranties</title>

    <link th:href="@{/dist/css/warranty.css}" rel="stylesheet">
</head>

<body>

<div layout:fragment="content" id="root">
    <div class="row wrapper border-bottom white-bg page-heading">
        <div class="col-lg-10">
            <h2>Warranties</h2>
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="/warranty">Warranties</a>
                </li>
                <li class="breadcrumb-item active">
                    <strong>List</strong>
                </li>
            </ol>
        </div>
    </div>

    <div class="container-fluid">
        <warranty-search></warranty-search>

        <div class="row">
            <div class="col-12">
                <warranty-list></warranty-list>
            </div>
        </div>
    </div>

</div>


<th:block layout:fragment="body-scripts">
    <script type="application/javascript" th:inline="javascript">
        window._CS_SEARCH_CRITERIA = [[${searchForm}]];
    </script>

    <script type="application/javascript" th:src="@{/dist/js/warranty.js}"></script>
</th:block>
</body>
</html>
