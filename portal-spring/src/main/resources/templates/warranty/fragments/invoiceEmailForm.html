<div id="email-form-container" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{fragments/modal.html}" th:with="containerId='email-form-container'">

    <div layout:fragment="modal-body" style="padding: 0 30px;">
        <form id="email-form" data-target="email-form-container" class="send-invoice-form ajax-form" th:object="${invoiceRequest}" th:action="@{|/partial/warranty/${warranty.id}/invoice|}" method="post" th:fragment="form">
            <div>
                <h2>
                    <i class="fa fa-paper-plane-o" aria-hidden="true"></i> Send Invoice
                </h2>
                <hr class="divider" />
                <div class="form-group">
                    <label>* Enter the email you wish to send the invoice to.</label>
                    <input type="text" th:field="*{email}" class="form-control">
                    <div class="help-block with-errors" th:if="${#fields.hasErrors('email')}">
                        <p class="has-error text-danger" th:errors="*{email}">Error Text</p>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div layout:fragment="modal-footer">
        <button th:unless="${warranty.paid}" data-submit="email-form" class="premium-invoice btn btn-primary btn-sm ajax-submit">Email Invoice</button>
        <button th:if="${warranty.paid}" disabled >Paid</button>
        <button id="email-modal-close" type="button" class="btn btn-danger btn-sm" data-dismiss="modal">Close</button>
    </div>

    <!--/* This fragment will only be rendered by itself on a successful form post from the partial ajax form submission above */-->
    <script th:fragment="closeModal" th:inline="javascript">
        $('#modal-target').modal('hide');

        /*<![CDATA[*/
        var portalActionMessage = /*[[${globalMessage}]]*/
            toastr[portalActionMessage.type.toLowerCase()](portalActionMessage.message);
        /*]]>*/
    </script>

</div>
