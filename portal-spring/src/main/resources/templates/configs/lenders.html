<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout_b4}">

<head>
    <title>Lenders</title>
    <link th:href="@{/dist/css/lenders.css}" rel="stylesheet">
</head>

<body>

<div layout:fragment="content" id="root">
    <div class="row wrapper border-bottom white-bg page-heading">
        <div class="col-lg-10">
            <h1>Lenders</h1>
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="/configs/lenders">Lender</a>
                </li>
                <li class="breadcrumb-item active">
                    <strong>Search</strong>
                </li>
            </ol>
        </div>
        <div class="header-button-wrapper">
            <a class="btn btn-primary btn-md" th:href="@{'/configs/lenders?form'}">
                <em class="fa fa-plus fa-fw"></em> Add Lender
            </a>
        </div>
    </div>

    <div class="container-fluid">
        <search-lender></search-lender>
        <div class="row">
            <div class="col-lg-12">
                <lender-list />
            </div>
        </div>

    </div>
</div>

<th:block layout:fragment="page-scripts">
    <script type="application/javascript" th:src="@{/dist/js/lenders.js}"></script>
</th:block>
</body>
</html>
