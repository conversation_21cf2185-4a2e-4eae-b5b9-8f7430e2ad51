<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout_b4}">

<head>
    <title>Programs</title>
    <link th:href="@{/dist/css/programs.css}" rel="stylesheet">
</head>

<body>

<div layout:fragment="content" id="root">
    <div class="row wrapper border-bottom white-bg page-heading">
        <div class="col-lg-10">
            <h2>Programs</h2>
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="/configs/programs">Program</a>
                </li>
                <li class="active breadcrumb-item">
                    <strong>Search</strong>
                </li>
            </ol>
        </div>
        <div class="header-button-wrapper">
            <a class="btn btn-primary btn-md" th:href="@{'/configs/programs?form'}">
                <em class="fa fa-plus fa-fw"></em> Add Program
            </a>
        </div>
    </div>

    <div class="container-fluid">
        <program-search></program-search>
        <div class="row">
            <div class="col-12">
                <program-list />
            </div>
        </div>

    </div>
</div>

<th:block layout:fragment="page-scripts">
    <script type="application/javascript" th:src="@{/dist/js/programs.js}"></script>
</th:block>
</body>
</html>
