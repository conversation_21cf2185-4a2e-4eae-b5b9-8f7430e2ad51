<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout_b4}">

<!--/* workaround for intellij bug: https://goo.gl/Qf9LGj */-->
<!--/*@thymesVar id="vehicleSale" type="com.carsaver.elasticsearch.model.sale.VehicleSaleDoc"*/-->

<head>
    <title>Vehicle Sales</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/c3/0.7.0/c3.min.css" integrity="sha256-RSMIOX07BNUXyf71hwmYRrUZ8RmiBxMTpqI0GOAy0GM=" crossorigin="anonymous" />
</head>

<body>

<div layout:fragment="content" id="root">
    <div class="row wrapper border-bottom white-bg page-heading">
        <div class="col-lg-10">
            <h2>Sales Stats</h2>
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    Sales
                </li>
                <li class="active breadcrumb-item">
                    <a href="/sale/stats"><strong>Stats</strong></a>
                </li>
            </ol>
        </div>
    </div>

    <vehicle-sale-stats></vehicle-sale-stats>

</div>
<th:block layout:fragment="body-scripts">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/5.9.2/d3.min.js" integrity="sha256-k9RyZhvMj2bg0fd8L3IE41t0HpTXnivMoePN/Ksq250=" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/c3/0.7.0/c3.min.js" integrity="sha256-TWSsNUpvg6NO1kRGAQV01Aqit3mr/IguqEzgTECFU+I=" crossorigin="anonymous"></script>
    <script type="application/javascript" th:inline="javascript">
        window._CS_SEARCH_CRITERIA = [[${searchForm}]];
    </script>
    <script type="application/javascript" th:src="@{/dist/js/vehicleSale.js}"></script>
</th:block>
</body>
</html>

