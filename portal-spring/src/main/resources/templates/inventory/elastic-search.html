<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout}">
<!--/* workaround for intellij bug: https://goo.gl/Qf9LGj */-->
<!--/*@thymesVar id="facets" type="com.carsaver.upgrade.model.inventory.InventoryFacets"*/-->
<!--/*@thymesVar id="criteria" type="com.carsaver.upgrade.model.inventory.InventoryFilterCriteria"*/-->
<head>
    <title>Inventory | Search</title>
</head>
<body>

<div layout:fragment="content" id="inventory-desktop">

    <div class="container">
        <div th:unless="${vehiclesFound}" align="center">
           <span>Sorry, no vehicles were found matching the specified search criteria:
               <a href="javascript:history.back()"><strong>Go Back</strong></a>
            </span>
        </div>
        <div th:if="${vehiclesFound}">
            <div class="row">
                <div class="col-lg-4 filters">
                    <div class="location-wrapper">
                        Vehicles near <span class="location" th:text="${location}">MIAMI, FL</span>
                    </div>
                </div>
                <div class="col-lg-8">
                    <div class="row justify-content-between sorting">
                        <div class="col-lg-10 text-right">
                            <div class="sort-by">
                                <strong>SORT BY</strong>
                                <select onchange="updateSort()" id="sort-selector">
                                    <option th:selected="${vehicleDetails.sort != null && vehicleDetails.sort.toString() == 'price: ASC'}" value="price,asc">Price: Low to High</option>
                                    <option th:selected="${vehicleDetails.sort != null && vehicleDetails.sort.toString() == 'price: DESC'}" value="price,desc">Price: High to Low</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3">
                    <div id="filter-by" role="tablist">
                        <div class="card">
                            <div class="card-header" role="tab">
                                <h5 class="mb-0">
                                    <i class="fa fa-filter" aria-hidden="true"></i> Filter By
                                </h5>
                            </div>

                            <div class="card-body facets" th:object="${criteria}">
                                <div class="distance">
                                    <strong>DISTANCE</strong>
                                    <select onchange="updateMiles();" id="distance-selector">
                                        <option th:each="distance : ${availableDistance}" th:selected="${selectedDistance == distance}" th:value="${distance}" th:text="${distance + ' miles'}">25 MILES</option>
                                    </select>
                                </div>
                                <div class="stockType">
                                    <strong>New/Used</strong>
                                    <ul>
                                        <li th:each="stockType : ${stockTypes}">
                                            <div class="form-radio">
                                                <label class="form-radio-label" th:inline="text">
                                                    <input class="form-radio-input" type="radio" th:field="${stockTypes}" th:value="${stockType}">
                                                    [[${stockType.toProperCaseStr()}]]
                                                </label>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                                <div class="facet" id="year-wrapper">
                                    <a class="facet-header" data-toggle="collapse" data-parent="#year-wrapper" href="#years" aria-expanded="true" aria-controls="years">
                                        <span>Year</span>
                                        <i class="fa fa-angle-up" aria-hidden="true"></i>
                                    </a>
                                    <div class="collapse show" id="years" role="tabpanel">
                                        <ul>
                                            <li th:each="year : ${facets.years}">
                                                <div class="form-check">
                                                    <label class="form-check-label" th:inline="text">
                                                        <input class="form-check-input" type="checkbox" th:field="*{years}" th:value="${year.name}" data-facet="years">
                                                        [[${year.name}]]
                                                    </label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="facet" id="makes-wrapper">
                                    <a class="facet-header" data-toggle="collapse" data-parent="#makes-wrapper" href="#makes" aria-expanded="true" aria-controls="makes">
                                        <span>Makes</span>
                                        <i class="fa fa-angle-up" aria-hidden="true"></i>
                                    </a>
                                    <div class="collapse show" id="makes" role="tabpanel">
                                        <ul>
                                            <li th:each="make : ${facets.makes}">
                                                <div class="form-check">
                                                    <label class="form-check-label" th:inline="text">
                                                        <input class="form-check-input" type="checkbox" th:field="*{makes}" th:value="${make.id}" data-facet="makes">
                                                        [[${make.name}]]
                                                    </label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="facet" id="models-wrapper">
                                    <a class="facet-header" data-toggle="collapse" data-parent="#models-wrapper" href="#models" aria-expanded="true" aria-controls="models">
                                        <span>Model</span>
                                        <i class="fa fa-angle-up" aria-hidden="true"></i>
                                    </a>
                                    <div class="collapse show" id="models" role="tabpanel">
                                        <ul>
                                            <li th:each="model : ${facets.models}">
                                                <div class="form-check">
                                                    <label class="form-check-label" th:inline="text">
                                                        <input class="form-check-input" type="checkbox" th:field="*{models}" th:value="${model.name}" data-facet="models">
                                                        [[${model.name}]] <span class="font-italic" th:inline="text">([[${model.count}]])</span>
                                                    </label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="facet" id="bodyStyles-wrapper">
                                    <a class="facet-header" data-toggle="collapse" data-parent="#bodyStyles-wrapper" href="#bodyStyles" aria-expanded="true" aria-controls="bodyStyles">
                                        <span>Body Styles</span>
                                        <i class="fa fa-angle-up" aria-hidden="true"></i>
                                    </a>
                                    <div class="collapse show" id="bodyStyles" role="tabpanel">
                                        <ul>
                                            <li th:each="bodyStyle : ${facets.bodyStyles}" th:classappend="${criteria.isBodyStylesFiltered() ? 'show' : ''}">
                                                <div class="form-check">
                                                    <label class="form-check-label">
                                                        <input class="form-check-input" type="checkbox" th:field="*{bodyStyles}" th:value="${bodyStyle.id}" data-facet="bodyStyles">
                                                        [[${bodyStyle.name}]] <span class="font-italic">([[${bodyStyle.count}]])</span>
                                                    </label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="facet" id="prices-wrapper">
                                    <a class="facet-header" data-toggle="collapse" data-parent="#prices-wrapper" href="#prices" aria-expanded="true" aria-controls="prices">
                                        <span>Price</span>
                                        <i class="fa fa-angle-up" aria-hidden="true"></i>
                                    </a>
                                    <div class="collapse show" id="prices" role="tabpanel">
                                        <select data-facet="prices" th:field="*{minPrice}">
                                            <option value="">Min Price</option>
                                            <option th:each="minPrice : ${facets.priceRange.toRangeList()}" th:value="${minPrice}" th:text="${{minPrice}}">$10,000</option>
                                        </select>
                                        <select data-facet="prices" th:field="*{maxPrice}">
                                            <option value="">Max Price</option>
                                            <option th:each="maxPrice : ${facets.priceRange.toRangeList()}" th:value="${maxPrice}" th:text="${{maxPrice}}">$10,000</option>
                                        </select>
                                        <a id="priceRanges" href="#" class="button btn-sm">Go</a>
                                    </div>
                                </div>
                                <div class="facet" id="colors-wrapper">
                                    <a class="facet-header" data-toggle="collapse" data-parent="#colors-wrapper" href="#colors" aria-expanded="true" aria-controls="colors">
                                        <span>Colors</span>
                                        <i class="fa fa-angle-up" aria-hidden="true"></i>
                                    </a>
                                    <div class="collapse show" id="colors" role="tabpanel">
                                        <ul>
                                            <li th:each="color : ${facets.exteriorColors}" th:classappend="${criteria.isExteriorColorsFiltered() ? 'show' : ''}">
                                                <div class="form-check">
                                                    <label class="form-check-label" th:inline="text">
                                                        <input class="form-check-input" type="checkbox" th:field="*{colors}" th:value="${color.name}" data-facet="colors">
                                                        [[${color.name}]] <span class="font-italic" th:inline="text">([[${color.count}]])</span>
                                                    </label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="facet" id="fuels-wrapper">
                                    <a class="facet-header" data-toggle="collapse" data-parent="#fuels-wrapper" href="#fuels" aria-expanded="true" aria-controls="fuels">
                                        <span>Fuel Type</span>
                                        <i class="fa fa-angle-down" aria-hidden="true"></i>
                                    </a>
                                    <div class="collapse" id="fuels" role="tabpanel" th:classappend="${criteria.isFuelFiltered() ? 'show' : ''}">
                                        <ul>
                                            <li th:each="fuel : ${facets.fuelTypes}">
                                                <div class="form-check">
                                                    <label class="form-check-label" th:inline="text">
                                                        <input class="form-check-input" type="checkbox" th:field="*{fuels}" th:value="${fuel.name}" data-facet="fuels">
                                                        [[${fuel.name}]] <span class="font-italic" th:inline="text">([[${fuel.count}]])</span>
                                                    </label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="facet" id="transmissions-wrapper">
                                    <a class="facet-header" data-toggle="collapse" data-parent="#transmissions-wrapper" href="#transmissions" aria-expanded="true" aria-controls="transmissions">
                                        <span>Transmission</span>
                                        <i class="fa fa-angle-down" aria-hidden="true"></i>
                                    </a>
                                    <div class="collapse" id="transmissions" role="tabpanel" th:classappend="${criteria.isTransmissionFiltered() ? 'show' : ''}">
                                        <ul>
                                            <li th:each="transmission : ${facets.transmissions}">
                                                <div class="form-check">
                                                    <label class="form-check-label" th:inline="text">
                                                        <input class="form-check-input" type="checkbox" th:field="*{transmissions}" th:value="${transmission.id}" data-facet="transmissions">
                                                        [[${transmission.name}]]
                                                    </label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="facet" id="drivetrains-wrapper">
                                    <a class="facet-header" data-toggle="collapse" data-parent="#drivetrains-wrapper" href="#drivetrains" aria-expanded="true" aria-controls="drivetrains">
                                        <span>Drivetrain</span>
                                        <i class="fa fa-angle-down" aria-hidden="true"></i>
                                    </a>
                                    <div class="collapse" id="drivetrains" role="tabpanel" th:classappend="${criteria.isDrivetrainFiltered() ? 'show' : ''}">
                                        <ul>
                                            <li th:each="drivetrain : ${facets.drivetrains}">
                                                <div class="form-check">
                                                    <label class="form-check-label" th:inline="text">
                                                        <input class="form-check-input" type="checkbox" th:field="*{drivetrains}" th:value="${drivetrain.id}" data-facet="drivetrains">
                                                        [[${drivetrain.name}]]
                                                    </label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="facet" id="cylinders-wrapper">
                                    <a class="facet-header" data-toggle="collapse" data-parent="#cylinders-wrapper" href="#cylinders" aria-expanded="true" aria-controls="cylinders">
                                        <span>Engine Cylinders</span>
                                        <i class="fa fa-angle-down" aria-hidden="true"></i>
                                    </a>
                                    <div class="collapse" id="cylinders" role="tabpanel" th:classappend="${criteria.isCylindersFiltered() ? 'show' : ''}">
                                        <ul>
                                            <li th:each="cylinder : ${facets.cylinders}">
                                                <div class="form-check">
                                                    <label class="form-check-label" th:inline="text">
                                                        <input class="form-check-input" type="checkbox" th:field="*{cylinders}" th:value="${cylinder.count}" data-facet="cylinders">
                                                        [[${cylinder.count}]] Cylinder
                                                    </label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-9 listings">
                    <div class="row">
                        <div class="col-lg-12 pagination-results" sd:pagination-summary="">
                            Showing 1-5 of 33
                        </div>
                    </div>
                    <div class="cars row">
                        <div class="car position-relative" th:each="vehicleDetail : ${vehicleDetails}">
                            <div class="car-inner">
                                <div class="vehicle-image">
                                    <span>vehicle-image vue</span>
                                    <!--<vehicle-image vue:image-url="${vehicleDetail.vehicle.defaultImageUrl}" vue:backup-image-url="${vehicleDetail.vehicle.style.smallImage}" vue:alt="${vehicleDetail.vehicle.style.fullName}" class="vehicle-image-component"></vehicle-image>-->
                                </div>
                                <div class="vehicle-details">
                                    <div class="vehicle-header">
                                        <h3 th:inline="text">[[${vehicleDetail.vehicle.year}]] [[${vehicleDetail.vehicle.make}]] [[${vehicleDetail.vehicle.model}]]</h3>
                                        <p><strong th:inline="text" th:if="${vehicleDetail.vehicle.trim}">[[${vehicleDetail.vehicle.trim}]] | </strong> <span th:text="${vehicleDetail.vehicle.engineDescription}">6.2L 8 Cylinder Engine</span></p>
                                    </div>
                                    <div class="vehicle-footer d-flex flex-row justify-content-between align-items-end">
                                        <div class="vehicle-meta position-relative">
                                            <div class="type">
                                                <span class="mr-1">Type:</span> <span th:inline="text">[[${vehicleDetail.vehicle.stockType}]]</span>
                                            </div>
                                            <div th:if="${vehicleDetail.distance != null}" class="distance mb-1">
                                                <span class="mr-1">Distance:</span> <span th:inline="text">[[${vehicleDetail.distance}]] mi</span>
                                            </div>
                                            <div class="d-flex flex-row vehicle-color-block" th:if="${vehicleDetail.vehicle.exteriorColor != null && vehicleDetail.vehicle.interiorColor != null}">
                                                <div class="vehicle-color-swatch mr-1" th:if="${!#strings.isEmpty(vehicleDetail.vehicle.exteriorColorHexCode)}" th:style="'background-color: #' + ${vehicleDetail.vehicle.exteriorColorHexCode}"></div>
                                                <div class="vehicle-color-swatch mr-1" th:if="${#strings.isEmpty(vehicleDetail.vehicle.exteriorColorHexCode)}" th:style="'background-color: ' + ${vehicleDetail.vehicle.exteriorColorGeneric}"></div>
                                                <span th:text="${vehicleDetail.vehicle.colorDescription}">Red Exterior w/ Black Interior</span>
                                            </div>
                                        </div>
                                        <div class="the-numbers">
                                            <div class="fuel-repair-savings" th:unless="${vehicleDetail.estimatedFuelAndRepairSavings} == null">
                                                Est. Fuel &amp; Repair Savings<br />
                                                <strong th:text="${{vehicleDetail.estimatedFuelAndRepairSavings}}">$2,880</strong>
                                            </div>
                                            <span>monthly-payment</span>
                                            <!--/* <monthly-payment th:attr="inventory-id=${vehicleDetail.vehicle.id},payment-type=${paymentType}" /*-->
                                        </div>
                                    </div>
                                    <div class="actions btn-group" role="group">
                                        <div>
                                            <!--/*<compare-button th:attr="inventory-id=${vehicleDetail.vehicle.id}" */-->
                                        </div>
                                        <div>
                                            <a href="#">View Details</a>
                                            <!--/* <view-details vue:inventory-id="${vehicleDetail.vehicle.id}" */-->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="disclaimer" th:inline="text">In-stock vehicle. MSRP: {{ [[${vehicleDetail.vehicle.msrp}]] | numeral('$0,0') }} VIN: [[${vehicleDetail.vehicle.vin}]]</div>
                        </div>
                        <div class="row pagination d-flex justify-content-center align-items-center m-3">
                            <div class="col-lg-12">
                                <th:block th:replace="fragments/pagination :: sd-controls()" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--/* <th:block th:replace="fragments/footer"/> */-->
</div>

<script th:inline="javascript" layout:fragment="body-scripts">
    function updatePaymentType() {
        const selectedPaymentType = $('#payment-type-selector').find(':selected').val();
        console.log('selectedPaymentType', selectedPaymentType);

        var newUrl = URI(document.location.href)
            .removeSearch("paymentType")
            .addSearch({"paymentType": selectedPaymentType});

        document.location = newUrl.toString();
    }

    function updateMiles() {
        const selectedMiles = $('#distance-selector').find(':selected').val();
        console.log('selectedMiles', selectedMiles);

        var newUrl = URI(document.location.href)
            .removeSearch("distance")
            .addSearch({"distance": selectedMiles});

        document.location = newUrl.toString();
    }

    function updateSort() {
        const sort = $('#sort-selector').find(':selected').val();
        console.log('sort', sort);

        var newUrl = URI(document.location.href)
            .removeSearch("sort")
            .addSearch({"sort": sort});

        document.location = newUrl.toString();
    }

    $(document).ready(function() {
        $('[data-toggle="tooltip"]').tooltip();

        $('#filter-by').on('click', 'input', function(e) {
            var eventTarget = $(e.currentTarget);
            var facet = eventTarget.attr('data-facet');
            if(!facet) {
                console.log('No facet defined: ' +  eventTarget.attr('id'));
                return false;
            }

            var checkedBoxes = $('#' + facet).find('input:checked');
            var values = [];
            $.each(checkedBoxes, function() {
                values.push($(this).val());
            });

            var newUrl = URI(document.location.href)
                .removeSearch(facet)
                .addSearch(facet, values);

            document.location = newUrl.toString();
        });

        $('#priceRanges').on('click', function(e) {
            e.preventDefault();
            var minPrice = $('#minPrice').val()
            var maxPrice = $('#maxPrice').val()

            var newUrl = URI(document.location.href)
                .removeSearch('minPrice')
                .removeSearch('maxPrice');

            if(minPrice) {
                newUrl.addSearch('minPrice', minPrice);
            }

            if(maxPrice) {
                newUrl.addSearch('maxPrice', maxPrice);
            }

            document.location = newUrl.toString();
        });

        function toggleFacetIcon() {
            // ties toggling to the hide and show events of facet

            $('.collapse').on('hide.bs.collapse', function(e) {
                var icon = $(this).parent().find('i');

                icon.addClass('fa-angle-down');
                icon.removeClass('fa-angle-up');
            });

            $('.collapse').on('show.bs.collapse', function(e) {
                var icon = $(this).parent().find('i');

                icon.addClass('fa-angle-up');
                icon.removeClass('fa-angle-down');
            });
        }

        toggleFacetIcon();
    });
</script>

</body>
</html>
