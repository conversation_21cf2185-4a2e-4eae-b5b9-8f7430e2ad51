<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout}">
<head>
    <title>Create User</title>
</head>

<body>

<!--Main layout-->
<div layout:fragment="content">

    <div class="row wrapper border-bottom white-bg page-heading">
        <div class="col-lg-10">
            <h2>Create User</h2>
            <ol class="breadcrumb">
                <li>
                    <a href="/user-prospects">User Prospects</a>
                </li>
                <li>
                    <a href="/user-prospects">Search</a>
                </li>
                <li class="active">
                    <strong>Create User</strong>
                </li>
            </ol>
        </div>
    </div>

    <div class="wrapper wrapper-content">
        <form th:action="@{/user-prospects/{prospectId}/create-user(prospectId=${prospectId})}" class="form-horizontal" id="userForm" data-toggle="validator" th:object="${userForm}" th:method="POST">
            <div class="row">
                <div class="'col-lg-6'" id="basicuser-form">
                    <div class="ibox">
                        <div class="ibox-title">
                            <h5>
                                <span>Create User</span>
                                <small>* Indicates required field</small>
                            </h5>
                        </div>
                        <div class="ibox-content">
                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="firstName">First Name *</label>
                                <div class="col-sm-10">
                                    <input type="text" required data-required-error="First Name is required."
                                           id="firstName" class="form-control" th:field="*{firstName}" maxlength="255"/>
                                    <div class="help-block with-errors">
                                        <p class="has-error text-danger" th:if="${#fields.hasErrors('firstName')}" th:errors="*{firstName}">Error Text</p>
                                    </div>
                                </div>
                            </div>
                            <div class="hr-line-dashed"></div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="lastName">Last Name *</label>
                                <div class="col-sm-10">
                                    <input type="text" required data-required-error="Last Name is required."
                                           id="lastName" class="form-control" th:field="*{lastName}" maxlength="255"/>
                                    <div class="help-block with-errors">
                                        <p class="has-error text-danger" th:if="${#fields.hasErrors('lastName')}" th:errors="*{lastName}">Error</p>
                                    </div>
                                </div>
                            </div>
                            <div class="hr-line-dashed"></div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="email">Email *</label>
                                <div class="col-sm-10">
                                    <input type="email" required data-required-error="Email is required."
                                           data-error="Invalid email address." id="email" class="form-control"
                                           th:field="*{email}" maxlength="255"/>
                                    <div class="help-block with-errors">
                                        <p class="has-error text-danger" th:if="${#fields.hasErrors('email')}" th:errors="*{email}">Error</p>
                                    </div>
                                </div>
                            </div>
                            <div class="hr-line-dashed"></div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="phoneNumber">Phone</label>
                                <div class="col-sm-10">
                                    <input type="text" id="phoneNumber" class="form-control"
                                           th:field="*{phoneNumber}" maxlength="10" />
                                    <div class="help-block with-errors">
                                        <p class="has-error text-danger" th:if="${#fields.hasErrors('phoneNumber')}" th:errors="*{phoneNumber}">Error</p>
                                    </div>
                                </div>
                            </div>
                            <div class="hr-line-dashed"></div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="address">Address *</label>
                                <div class="col-sm-10">
                                    <input type="text" id="address" required data-required-error="Address is required."
                                           class="form-control" th:field="*{address}" maxlength="255"/>
                                    <div class="help-block with-errors">
                                        <p class="has-error text-danger" th:if="${#fields.hasErrors('address')}" th:errors="*{address}">Error</p>
                                    </div>
                                </div>
                            </div>
                            <div class="hr-line-dashed"></div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="address2">Address 2</label>
                                <div class="col-sm-10">
                                    <input type="text" id="address2" class="form-control" th:field="*{address2}" maxlength="255"/>
                                </div>
                            </div>
                            <div class="hr-line-dashed"></div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="city">City *</label>
                                <div class="col-sm-10">
                                    <input type="text" id="city" required data-required-error="City is required."
                                           class="form-control" th:field="*{city}" maxlength="255"/>
                                    <div class="help-block with-errors">
                                        <p class="has-error text-danger" th:if="${#fields.hasErrors('city')}" th:errors="*{city}">Error</p>
                                    </div>
                                </div>
                            </div>
                            <div class="hr-line-dashed"></div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="stateCode">State *</label>
                                <div class="col-sm-10">
                                    <select id="stateCode" class="form-control" th:field="*{stateCode}"
                                            onchange="$('#state').val(this.options[this.selectedIndex].text)">
                                        <option value="">Choose a State</option>
                                        <option th:each="state : ${states}" th:value="${state.abbreviation}" th:text="${state.name}">Florida</option>
                                    </select>
                                    <!-- stores the state value. changes when the select changes -->
                                    <input type="hidden" id="state" th:field="*{state}"/>
                                    <div class="help-block with-errors">
                                        <p class="has-error text-danger" th:if="${#fields.hasErrors('stateCode')}" th:errors="*{stateCode}">Error</p>
                                    </div>
                                </div>
                            </div>
                            <div class="hr-line-dashed"></div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="zipCode">Zip Code *</label>
                                <div class="col-sm-10">
                                    <input type="text" id="zipCode" required data-required-error="Zip Code is required."
                                           class="form-control" th:field="*{zipCode}" maxlength="10"/>
                                    <div class="help-block with-errors">
                                        <p class="has-error text-danger" th:if="${#fields.hasErrors('zipCode')}" th:errors="*{zipCode}">Error</p>
                                    </div>
                                </div>
                            </div>
                            <div class="hr-line-dashed"></div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="smsEnabled">SMS Enabled</label>
                                <div class="col-sm-1">
                                    <input type="checkbox" id="smsEnabled" th:field="*{smsAllowed}" class="form-control"/>
                                </div>
                            </div>
                            <div class="hr-line-dashed"></div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="locale">Locale *</label>
                                <div class="col-sm-10">
                                    <select id="locale" class="form-control" th:field="*{locale}">
                                        <option value="" disabled>Choose a locale</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                    </select>
                                    <div class="help-block with-errors">
                                        <p class="has-error text-danger" th:if="${#fields.hasErrors('locale')}" th:errors="*{locale}">Error</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="form-group">
                        <div class="col-sm-4 col-sm-offset-2">
                            <button class="btn btn-primary">Create</button>
                        </div>
                        <div class="col-sm-4 col-sm-offset-2">
                            <button class="btn btn-danger" onclick="goBack()">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

</body>
</html>
