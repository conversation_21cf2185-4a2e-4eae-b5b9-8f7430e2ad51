<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<body>

    <form th:action="@{/user-prospects}" th:object="${searchForm}" method="GET" th:fragment="form" class="form-horizontal">
        <div class="form-group">
            <div class="col-md-2 col-lg-2" style="padding-top: 5px">
                <div class="md-form" style="padding-top: 5px">
                    <label>First Name</label>
                    <input type="text" id="firstName" class="form-control"  th:field="*{firstName}" placeholder="First Name">
                </div>
            </div>
            <div class="col-md-2 col-lg-2" style="padding-top: 5px">
                <div class="md-form" style="padding-top: 5px">
                    <label>Last Name</label>
                    <input type="text" id="lastName" class="form-control"  th:field="*{lastName}" placeholder="Last Name">
                </div>
            </div>
            <div class="col-md-4 col-lg-4" style="padding-top: 5px">
                <div class="md-form" style="padding-top: 5px">
                    <label>Email</label>
                    <input type="text" id="email" class="form-control"  th:field="*{email}" placeholder="Email">
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2 col-lg-2" style="padding-top: 5px">
                <label>City</label>
                <input type="text" id="city" class="form-control" th:field="*{city}" placeholder="City">
            </div>
            <div class="col-md-2 col-lg-2">
                <div class="md-form" style="padding-top: 5px">
                    <label for="state">State</label>
                    <select th:field="*{state}" id="state" class="selectpicker form-control">
                        <option value="">State</option>
                        <option th:each="stateAbbreviation : ${states}" th:value="${stateAbbreviation}" th:text="${stateAbbreviation}"></option>
                    </select>
                </div>
            </div>
            <div class="col-md-4 col-lg-4" style="padding-top: 5px">
                <div class="md-form" style="padding-top: 5px">
                    <label>Created Date</label>
                    <div class="input-daterange input-group" id="datepicker">
                        <input type="text" class="input-sm form-control" th:field="*{createdDateStart}">
                        <span class="input-group-addon">to</span>
                        <input type="text" class="input-sm form-control" th:field="*{createdDateEnd}">
                    </div>
                </div>
            </div>
            <div class="col-md-2 col-lg-2" style="padding: 27px 28px 15px 15px">
                <div class="md-form">
                    <div class="md-form form-group" style="padding: 0 0 0 15px">
                        <button id="search-button" class="btn btn-primary btn-block">
                            <i class="fa fa-search fa-fw"></i> Search
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-2 col-lg-2" style="padding: 27px 28px 15px 15px">
                <div class="md-form">
                    <div class="md-form form-group" style="padding: 0 0 0 15px">
                        <a id="clear-button" class="btn btn-info btn-block" th:href="@{/user}">
                            <i class="fa fa-search fa-fw"></i> Reset
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

</body>
</html>
