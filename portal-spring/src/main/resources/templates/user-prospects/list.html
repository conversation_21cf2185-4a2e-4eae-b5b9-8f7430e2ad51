<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout}">

<head>
    <title>Users</title>
</head>

<body>

<!--Main layout-->
<div layout:fragment="content">
    <div class="row wrapper border-bottom white-bg page-heading">
        <div class="col-lg-10">
            <h2>Users</h2>
            <ol class="breadcrumb">
                <li>
                    <a href="/user">User Prospects</a>
                </li>
                <li class="active">
                    <strong>Search</strong>
                </li>
            </ol>
        </div>
    </div>

    <div class="wrapper wrapper-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-content">
                        <div>
                            <div th:insert="user-prospects/search-form :: form"></div>
                        </div>
                        <h5>Search Results</h5>
                        <div class="table-responsive">
                            <table th:if="${page.totalElements > 0}" class="table table-striped table-bordered table-hover dataTables-example" aria-describedby="Search Results">
                                <thead>
                                    <tr>
                                        <th scope="col">Actions</th>
                                        <th scope="col"><a class="sorted" sd:pagination-sort="enabled">Signed-Up</a></th>
                                        <th scope="col">Campaign</th>
                                        <th scope="col"><a class="sorted" sd:pagination-sort="firstName">Name</a></th>
                                        <th scope="col">Pin</th>
                                        <th scope="col">Email</th>
                                        <th scope="col">Address</th>
                                        <th scope="col"><a class="sorted" sd:pagination-sort="createdAt">Created Date</a></th>
                                        <th scope="col">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="prospect : ${prospects}">
                                        <td>
                                            <a th:href="@{/user-prospects/{id}(id=${prospect.id})}" class="blue-text"><i class="fa fa-user"></i></a>
                                        </td>
                                        <td>
                                            <span th:if="${prospect.enabled}" class="label label-warning">No</span>
                                            <span th:unless="${prospect.enabled}" class="label label-success">Yes</span>
                                        </td>
                                        <td th:text="${prospect.campaign?.name}">Hyundai Campaign</td>
                                        <td th:text="${prospect.fullName}">John Doe</td>
                                        <td th:text="${prospect.pin}">XXYYN</td>
                                        <td th:text="${prospect.email}"><EMAIL></td>
                                        <td th:text="${prospect.address.fullAddress}">123 Justice Way, Apt 404, Salem, West Virginia</td>
                                        <td>
                                            <span th:if="${prospect.createdDate != null}" nowrap="nowrap" class="utc-to-local" th:text="${#temporals.formatISO(prospect.createdDate)}">Date Here</span>
                                        </td>
                                        <td style="text-align: center">
                                            <a th:if="${prospect.enabled}" th:href="@{|/user-prospects/${prospect.id}/create-user|}" class="btn btn-primary">
                                                <i class="fa fa-user"></i> Create User
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>

                            <div th:if="${page.totalElements == 0}">
                                No records match your search.
                            </div>

                            <th:block th:replace="fragments/pagination :: sd-controls()" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
