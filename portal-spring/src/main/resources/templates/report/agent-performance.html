<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{layout}">


<head>
    <title>Agent Performance Report</title>
</head>

<body>

<div layout:fragment="content">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>
                Show reports for dates
            </h5>
        </div>
        <div class="ibox-content">
            <form action="agent-performance-between-dates"  data-toggle="validator" class="form-inline" method="get">
                <div class="form-group">
                    <div class="input-group input-daterange">
                        <input type="text" class="form-control" name="startDate" th:value="${startDate}" required data-required-error="Start date is required." />
                        <div class="input-group-addon">to</div>
                        <input type="text" class="form-control" name="endDate" th:value="${endDate}" required data-required-error="End date is required." />
                    </div>
                </div>
                <button class="btn btn-white" type="submit">Go</button>
            </form>
        </div>
    </div>
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>
                Appointments
            </h5>
        </div>
        <div class="ibox-content">
            <canvas id="appointmentChartCanvas" height="140"></canvas>
        </div>
    </div>
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>
                Connections
            </h5>
        </div>
        <div class="ibox-content">
            <canvas id="connectionChartCanvas" height="140"></canvas>
        </div>
    </div>
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>
                Signups
            </h5>
        </div>
        <div class="ibox-content">
            <canvas id="signupChartCanvas" height="140"></canvas>
        </div>
    </div>
</div>

<th:block layout:fragment="page-scripts">
    <script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.1/Chart.bundle.min.js"></script>
    <script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/randomcolor/0.5.2/randomColor.min.js"></script>
    <script type="text/javascript" src="/js/agent-performance-report.js"></script>

    <script th:inline="javascript">
        $(function () {

            var reportDaysFormatted = [[${reportDaysFormatted}]];
            var userDailyAppointmentCounts = [[${userDailyAppointmentCounts}]];
            var userDailyConnectionCounts = [[${userDailyConnectionCounts}]];
            var userDailySignupCounts = [[${userDailySignupCounts}]];

            renderPerformanceChart(userDailyAppointmentCounts, reportDaysFormatted, 'appointmentChartCanvas', '', '', 'Appointments');
            renderPerformanceChart(userDailyConnectionCounts, reportDaysFormatted, 'connectionChartCanvas', '', '', 'Connections');
            renderPerformanceChart(userDailySignupCounts, reportDaysFormatted, 'signupChartCanvas', '', '', 'Signups');
        });
    </script>
</th:block>

</body>
</html>
