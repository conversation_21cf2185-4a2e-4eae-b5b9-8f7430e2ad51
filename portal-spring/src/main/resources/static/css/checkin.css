.ibox {
    font-family: "Open Sans";
}
.ibox-header {
    font-size: 20px;
}
.card-content {
    padding: 5px 0 0;
}
.main-wrapper {
    min-height: 100vh;
    height: 100%;
    background: url(/images/car_hands_up_green.jpg) no-repeat center center;
    background-size: cover;
    margin: 0 -15px;
}
.search-result-wrapper {
    height: 100%;
    margin: 0 -15px;
}
.result-wrapper{
    padding: 0 0 25px 0;
}
.carsaver_logo {
    width: 300px;
}
.back-button {
    margin: 25px 0 0 0;
}
.searchWrapper {
    text-align: center;
    padding-top: 100px;
}
.dealer-header {
    color: #fff;
    font-size: 18px;
}
.dealer-header-span {
    font-size: .8em;
    font-weight: 400;
    margin-left: 10px;
}
.dealer-header-svg {
    margin-bottom: -5px;
}
.home-appointment-search-wrapper {
    padding: 15px 0;
    margin: 0 150px;
}
.appointment-search-wrapper {
    padding: 15px 0 1px 0;
    margin: 0 15px;
}
hr {
    clear:both;
    display:block;
}
@media (max-width: 770px) {
    .home-appointment-search-wrapper {
        margin: 0 30px;
    }
}

/***** Checkin Appt/Connection Search Result Styles *****/
.search-ibox-result {
    margin: 15px 0 30px 0;
}
.card-header {
    padding: 15px 15px 5px;
}
.name {
    color: #666666;
    font-family: "Open Sans";
    font-weight: bold;
}
.profile-name {
    color: #666666;
    font-family: "Open Sans";
    font-weight: bold;
}
.v-title, .v-trim {
    font-size: 18px;
}
.details-link {
    margin: 5px 0 0 5px;
    font-style: italic;
}
.appointment-li{
    font-size: 15px;
    color: #666666;
    padding: 5px 10px;
}
.profile-li {
    font-size: 14px;
    color: #666666;
    padding: 5px 10px;
}
.phone-li {
    font-size: 14px;
    color: #666666;
    padding: 5px 10px 0;
}
.profile-dividers {
    margin: 5px 0;
}
.content {
    padding: 0 5px;
}
.appointment-info-list {
    margin: 0;
}
.appointment-label {
    font-weight: bold;
}
.appointment-button-wrapper {
    padding: 5px 0 0 0;
}
.btn-details {
    background-color: #DDDDDD;
    border-color: #DDDDDD;
    color: #ffffff;
}
.ibox-footer .btn-default {
    background-color: #888;
    color: #FFF;
}
.label-primary, .label-danger {
    color: #FFF !important;
}
.checkin-ribbon-wrapper {
    position: absolute;
    right: -5px; top: -9px;
    z-index: 1;
    overflow: hidden;
    width: 75px; height: 75px;
    text-align: right;
}
.checkin-ribbon {
    font-size: 10px;
    font-weight: bold;
    color: #FFF;
    text-transform: uppercase;
    text-align: center;
    line-height: 20px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    width: 100px;
    display: block;
    position: absolute;
    top: 19px; left: -3px;
}
.checkin-ribbon:before, .checkin-ribbon:after {
    content: "";
    border-top:   3px solid #007DC6;
    border-left:  3px solid transparent;
    border-right: 3px solid transparent;
    position:absolute;
    bottom: -3px;
}
.checkin-ribbon:before {
    left: 0;
}
.checkin-ribbon:after {
    right: 0;
}
/***** End of Checkin Appt/connection Search Results Styles *****/

/***** Appointment & Connection Content.html styles *****/
.status-text {
    text-transform: capitalize;
}
.checkin-wrapper{
    margin-top: 18px;
}
.cta-wrapper {
    padding: 6px 15px 20px;
}
.status-dropdown{
    background-color: #F9F9F9 !important;
    border-color: #DDDDDD !important;
    color: #999 !important;
    margin-top: 3px;
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
}

.hidden-dropdown {
    padding: 5px 2px 2px;
}
.hidden-select {
    background-color: #F9F9F9;
    border-color: #DDDDDD;
    margin-top: 3px;
    color: #888;
    border-radius: 2px;
}
/***** End of Appointment & Connection Content.html styles *****/

/***** Appointment Detail Styles *****/
html,body{
    height:100%;
}
.back-button-wrapper {
    padding: 10px 10px 10px 1px;
}
.appointment-detail-wrapper {
    height:100%;
}
.inventory-ibox-result, .search-ibox-result{
    margin: 15px 0 30px 0;
    border: solid #DDD;
    border-width: 5px 1px 1px;
}
.ibox-header {
    font-weight: 500;
    color: #666666;
}
.profile-icon-wrapper {
    padding: 5px 0;
}
.profile-icons {
    font-size: 14px;
}
.profile-links {
    color: #666666;
    text-decoration: none;
}
.profile-div-wrap {
 margin: 0 38px;
 text-indent: -23px;
}
.image-overlay {
    color: #FFF;
    position: absolute;
    left: 0px;
    right: 0px;
    bottom: 0px;
    height: 68px;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
}
.li-divider {
    margin: 0 0 5px 0;
}
.appointment-vehicle-image, .inventory-vehicles-image {
    max-width: 100%;
}
.recent-activity-card {
    margin-bottom: 15px;
}
/***** End of Appointment Detail Styles *****/


/*Appointment Vehicle Styles*/
.vehicle-title {
    font-family: "Open Sans";
    color: #666666;
    font-size: 18px;
}
.vehicle-title-divider {
    margin-top: 0;
    margin-bottom: 5px;
}
.optionWrapper {
    display: inline-block;
    width: 100%;
    padding: 5px 0;
    border-bottom: 1px solid #e4e4e4;
}
.optionLabel {
    float: left;
}
.optionValue {
    float: right;
    text-align: right;
    max-width: calc(100% - 125px);
}
.optionValueLi {
    float: right;
    text-align: right;
    max-width: 180px;
}
.vehicle-details-modal-body{
    padding: 10px 30px;
}
.add-button {
    float: right;
}
.cta-button {
    border-radius: 2px;
    font-size: 16px;
    width: 49%;
    padding: 6px;
}
.ibox-footer .btn-group {
    width: 49%;
}
.ibox-footer .btn-group button {
    width: 100%;
    border-radius: 2px;
    font-size: 16px;
    padding: 6px;
}
#btn-option{
    background-color: #999;
    border-color: #999;
    color: #FFFFFF;
}
.divide {
    padding-bottom: 15px;
    border-bottom: dashed #eee;
}
.divide:not(:first-child) {
    padding-bottom: 15px;
    border-bottom: dashed #eee;
}
.range-slider {
    position: relative;
    height: 80px;
}
.extra-controls {
    position: relative;
    padding: 10px 0 0;
}
.no-result-msg {
    color: red;
}
.inmodal .modal-title {
    font-size: 18px;
}

/***** Inventory Search Form Styles ****/
.inventory-search, .inventory-reset {
    width: 49%;
}
#to-top-btn {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 30px;
    z-index: 99;
    border: none;
    outline: none;
    background-color: #007DC6;
    color: #FFF;
    cursor: pointer;
    padding: 15px;
    border-radius: 50%;
    width: 50px;
}
.back-btn {
    color: #007DC6;
}

.inventory-result-content {
    padding-bottom: 5px;
}
/***** End of Inventory Search Form Styles ****/

/********* Filters Modal From Right Side **********/
.modal-header {
    border-bottom-color: #EEEEEE;
    background-color: #FAFAFA;
    padding: 15px 15px 5px 10px;
}
.modal-title {
    font-size: 20px;
    font-weight: bold;
}
.modal-content {
    border-radius: 0;
    border: none;
}

.filter-modal-dialog,
.modal.right .filter-modal-dialog {
    position: fixed;
    margin: auto;
    width: 320px;
    height: 100%;
    -webkit-transform: translate3d(0%, 0, 0);
    -ms-transform: translate3d(0%, 0, 0);
    -o-transform: translate3d(0%, 0, 0);
    transform: translate3d(0%, 0, 0);
}

.modal-content,
.modal.right .modal-content {
    height: 100%;
    overflow-y: auto;
}

.filter-body {
    padding: 5px 0 0;
}

.modal.right.fade .filter-modal-dialog {
    right: -320px;
    -webkit-transition: opacity 0.3s linear, right 0.3s ease-out;
    -moz-transition: opacity 0.3s linear, right 0.3s ease-out;
    -o-transition: opacity 0.3s linear, right 0.3s ease-out;
    transition: opacity 0.3s linear, right 0.3s ease-out;
}

.modal.right.fade.in .filter-modal-dialog {
    right: 0;
}
/*Filter Drop Downs*/
.accordion-header-wrapper {
    color: #676a6c;
}
.accordion-header-wrapper:hover {
    color: #676a6c;
}
.accordion-header {
    padding: 0 15px;
}
.filter-radio {
    margin: 3px 0 0 !important;
}
.todo-list.small-list {
    padding: 0 15px 5px;
}
.todo-list.small-list > li {
    border-radius: 2px;
    font-size: 14px;
}
.checkmark {
    font-size: 14px;
    line-height: 20px;
    font-style: italic;
    color: #54B848;
    padding-left: 20px;
}

/*** Pop over tour styles ***/
.popover-title{
    background: #007DC6;
    color: #fff;
}
/*** End of Pop over tour styles ***/


/* tablet, landscape iPad, lo-res laptops and desktops */
@media (min-width:801px)  {
    .ibox-footer > .cta-button {
        display: none;
    }
    .ibox-footer > .btn-group {
        width: 22%;
    }
    .hidden-dropdown {
        padding: 0 2px 2px;
        width: 50%;
        float: right;
    }
}
@media (max-width: 425px) {
    .appointment-detail-wrapper {
        padding: 0;
        margin: 0;
    }
    .optionValue {
        max-width: 160px;
    }
    .add-button, .view-options {
        display: none;
    }
}
