$(function () {
    var progressBarThreshold = 90;

    // Toggles dropdown icon on Table column Model
    $('.pricing-table tr td a').on('click', function () {
        $('i', this).toggleClass('fa-chevron-right fa-chevron-down');
    });

    /**
     * Sets the percentage width required on the progress bar
     * @param percent <String>
     * @param node JQuery node type
     * @return  true is successfully ran
     */
    function setPercent (percent, node) {
        if (percent == null) {
            node.width('0%');
            return;
        }
        if (node == null) {
            console.error('Error: DOM Node expected instead received', node);
            return;
        }

        node.width(percent + '%');

        return true;
    }

    /**
     * Enables Floating Table Header
     * ref: http://mkoryak.github.io/floatThead/examples/bootstrap3/
     * @param node is a single JQuery selector object ie. $('#myTable')
     * @param cb Optional callback function
     * @return true if successful
     */
    function floatThead(node, cb) {

        if (node instanceof $ !== true) {
            console.error('Error: expect parameter of instanceof jQuery instead received', typeof node, node);
            return;
        }

        node.floatThead({
            responsiveContainer : function($table) {
                return $table.closest(".table-responsive");
            }
        });

        if (cb) {
            cb();
        }

        return true;
    }

    /**
     * Bug fix for vin field not appropriately resizing when using floating header action on table
     */
    function tableReflowFix() {
        var element = $('.pricing-table');

        if (element.length === 0) {
            console.error('Error: element not found', element);
            return;
        }

        element.on('DOMNodeInserted', 'tr', function() {
            $(this).trigger('reflow');
        });

        return true;
    }

    // Enables Floating Table Header
    floatThead($('[data-floatThead]'), tableReflowFix);

    // Progress Bar For New and Used Vehicle Pricing Pages
    setPercent(window.progressBarPercent.percentNew, $("#percentNew"));
    setPercent(window.progressBarPercent.percentUsed, $("#percentUsed"));

    /**
     * The pricing threshold for New and Used Vehicles is 90% priced.
     * If inventory is less than 90%, this function will make progress bar red.
     * If inventory is 90% or better, the progress bar will be green.
     * @param threshold
     * @return {boolean}
     */
    function progressBarColor(threshold) {
        var thr = threshold || 0;

        if (window.progressBarPercent.percentUsed < thr) {
            $("#percentUsed").addClass("metric-warning");
        } else {
            $("#percentUsed").addClass("metric-primary");
        }

        if (window.progressBarPercent.percentNew < thr) {
            $("#percentNew").addClass("metric-warning");
        } else {
            $("#percentNew").addClass("metric-primary");
        }

        return true;
    }

    // Adjust color of progress bar based on 90% threshold
    progressBarColor(progressBarThreshold);




});

