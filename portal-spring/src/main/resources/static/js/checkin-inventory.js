/* globals toastr */

// Check-In Inventory JS
$(function() {

    var newVehicles;
    var usedVehicles;

    function getDistinctYearMakeModelTrimExteriorColorForNew(callback) {
        var endpoint = "/stratus/dealer/" + getDealerIdFromPath() + "/inventory/yearMakeModelTrimExteriorColor/new";

        return getResourceAtEndpoint(endpoint, {} , callback);
    }

    function getDistinctYearMakeModelTrimExteriorColorForUsed(callback) {
        var endpoint = "/stratus/dealer/" + getDealerIdFromPath() + "/inventory/yearMakeModelTrimExteriorColor/used";

        return getResourceAtEndpoint(endpoint, {} , callback);
    }

    function populate(id, values){
        var field = $(id);
        field.empty();
        field.append('<option value="">All</option>');
        values.forEach(function(value){
            field.append('<option value="' + value + '">' + value + '</option>');
        });
    }

    function mapPropFrom(prop, vehicles) {
        return vehicles
            .map(function(vehicle) {return vehicle[prop];})
            .filter(function(val, idx, arr){return val && arr.indexOf(val) === idx;}) // only return unique
            .sort();
    }

    function storeDropDownsState(stockType) {
        var dropDownVals = {
            "stockType" : stockType,
            "year" : $("#" + stockType + "Year").val(),
            "make" : $("#" + stockType + "Make").val(),
            "model" : $("#" + stockType + "Model").val(),
            "style" : $("#" + stockType + "Style").val(),
            "exteriorColor" : $("#" + stockType + "ExteriorColor").val(),
            "minPrice" : $("#" + stockType + "MinPrice").val(),
            "maxPrice" : $("#" + stockType + "MaxPrice").val()
        };
        sessionStorage.setItem("dropDownVals", JSON.stringify(dropDownVals));
    }

    function applyStoredDropDownsState(dropDownsStr) {
        if(dropDownsStr) {

            var dropDownVals = JSON.parse(dropDownsStr);

            setTimeout(function() { //wait a bit before updating
                var stockType = dropDownVals.stockType;

                if (dropDownVals.year) {
                    $("#" + stockType + "Year").val(dropDownVals.year).trigger("change");
                }
                if (dropDownVals.make) {
                    $("#" + stockType + "Make").val(dropDownVals.make).trigger("change");
                }
                if (dropDownVals.model) {
                    $("#" + stockType + "Model").val(dropDownVals.model).trigger("change");
                }
                if (dropDownVals.style) {
                    $("#" + stockType + "Style").val(dropDownVals.style).trigger("change");
                }
                if (dropDownVals.exteriorColor) {
                    $("#" + stockType + "ExteriorColor").val(dropDownVals.exteriorColor).trigger("change");
                }
                if (dropDownVals.minPrice) {
                    instance.update({
                        from: dropDownVals.minPrice,
                    });
                    $("#" + stockType + "MinPrice").val(dropDownVals.minPrice).trigger("change");
                }
                if (dropDownVals.maxPrice) {
                    instance.update({
                        to: dropDownVals.maxPrice,
                    });
                    $("#" + stockType + "MaxPrice").val(dropDownVals.maxPrice).trigger("change");
                }

            }, 200);
        }
    }

    $(document).ready(function() {
        getDistinctYearMakeModelTrimExteriorColorForNew(function(result) {
            newVehicles = result;

            // populates all NEW years, makes and exterior colors on page load.
            populate("#newYear", mapPropFrom("year", newVehicles).reverse());
            populate("#newMake", mapPropFrom("make", newVehicles));
            populate("#newExteriorColor", mapPropFrom("exteriorColor", newVehicles));
        });

        getDistinctYearMakeModelTrimExteriorColorForUsed(function(result) {
            usedVehicles = result;

            // populates all USED years, makes and exterior colors on page load.
            populate("#usedYear", mapPropFrom("year", usedVehicles).reverse());
            // populate("#usedMake", mapPropFrom("make", usedVehicles));
            populate("#usedExteriorColor", mapPropFrom("exteriorColor", usedVehicles));
        });

        applyStoredDropDownsState(sessionStorage.getItem("dropDownVals"));

    });

    function handleYearChange(vehicles, type) {

        $("#" + type + "Make").empty();
        $("#" + type + "Model").empty();
        $("#" + type + "Style").empty();
        $("#" + type + "ExteriorColor").empty();

        var selectedYear = $("#" + type + "Year").val();

        var filteredVehicles = vehicles
            .filter(function(vehicle){
                return ((selectedYear) ? vehicle.year === parseInt(selectedYear) : true);
            });

        populate("#" + type + "Make", mapPropFrom("make", filteredVehicles));
        populate("#" + type + "ExteriorColor", mapPropFrom("exteriorColor",  filteredVehicles));
    }

    function handleMakeChange(vehicles, type) {

        $("#" + type + "Model").empty();
        $("#" + type + "Style").empty();
        var exteriorColorField = $("#" + type + "ExteriorColor");
        exteriorColorField.empty();

        var selectedYear = $("#" + type + "Year").val();
        var selectedMake = $("#" + type + "Make").val();
        var selectedExteriorColor = exteriorColorField.val();

        var filteredVehicles = vehicles
            .filter(function(vehicle){
                return ((selectedYear) ? vehicle.year === parseInt(selectedYear) : true) //year is optional
                    && ((selectedExteriorColor) ? vehicle.exteriorColor === selectedExteriorColor : true) //exterior color is optional
                    && vehicle.make === selectedMake
            });

        if (selectedMake === null) {
            $("#" + type + "Model").empty();
            $("#" + type + "Style").empty();
            $("#" + type + "ExteriorColor").empty();
            console.log("No" + type + " models for this year, and  make");
        } else {
            populate($("#" + type + "Model"), mapPropFrom("model", filteredVehicles));
            populate($("#" + type + "ExteriorColor"), mapPropFrom("exteriorColor", filteredVehicles));

            console.log( type + " models have been populated by selected year and selected make");
        }
    }

    function handleModelChange(vehicles, type) {
        $("#" + type + "Style").empty();
        var exteriorColorField = $("#" + type + "ExteriorColor");
        exteriorColorField.empty();

        var selectedYear = $("#" + type + "Year").val();
        var selectedMake = $("#" + type + "Make").val();
        var selectedModel = $("#" + type + "Model").val();
        var selectedExteriorColor = exteriorColorField.val();

        var filteredVehicles = vehicles
            .filter(function(vehicle){
                return ((selectedYear) ? vehicle.year === parseInt(selectedYear) : true) //year is optional
                    && ((selectedExteriorColor) ? vehicle.exteriorColor === selectedExteriorColor : true) //exterior color is optional
                    && vehicle.make === selectedMake
                    && vehicle.model === selectedModel
            });

        if (selectedMake && selectedModel === null) {
            $("#" + type + "Make").empty();
            $("#" + type + "Model").empty();
            $("#" + type + "Style").empty();
            $("#" + type + "ExteriorColor").empty();
            console.log("No" + type + " models for this year, and  make");
        } else {
            populate($("#" + type + "Style"), mapPropFrom("trim", filteredVehicles));
            populate($("#" + type + "ExteriorColor"), mapPropFrom("exteriorColor", filteredVehicles));

            console.log( type + " styles have been populated by a selected year, selected make, and selected Model");
        }
    }

    function getFilteredColors() {
        var selectedYear = $("#newYear").val();
        var selectedMake = $("#newMake").val();
        var selectedModel = $("#newModel").val();
        var selectedStyle = $("#newStyle").val();

        var filteredVehicles = newVehicles
            .filter(function(vehicle){
                return ((selectedYear) ? vehicle.year === parseInt(selectedYear) : true) //year is optional
                    && vehicle.make === selectedMake
                    && vehicle.model === selectedModel
                    && vehicle.trim === selectedStyle
            });
        return filteredVehicles;
    }

    // On Year Change Clear All and populate drop downs like on page load
    $("#newYear").change(function(e){
        handleYearChange(newVehicles, "new");
    });

    $("#usedYear").change(function(e){
        handleYearChange(usedVehicles, "used");
    });

    // On Make Change Get Models
    $("#newMake").change(function (e) {
        handleMakeChange(newVehicles, "new");
    });

    $("#usedMake").change(function (e) {
        handleMakeChange(usedVehicles, "used");
    });

    // On Model Change Get Styles (Trim)
    $("#newModel").change(function (e) {
        handleModelChange(newVehicles, "new");
    });

    $("#usedModel").change(function (e) {
        handleModelChange(usedVehicles, "used");
    });

    // On Style Change Get Exterior Colors
    $("#newStyle").change(function(e){
        populate("#newExteriorColor", mapPropFrom("exteriorColor", getFilteredColors()));
    });

    // Price Range Slider
    var $range = $(".js-range-slider"),
        $inputFrom = $(".js-input-from"),
        $inputTo = $(".js-input-to"),
        instance,
        min = 0,
        max = 50000,
        from = 0,
        to = 0;

    $range.ionRangeSlider({
        type: "double",
        grid: true,
        grid_num: 10,
        min: min,
        max: max,
        from: 0,
        to: 50000,
        step: 100,
        prefix: "$",
        onStart: updateInputs,
        onChange: updateInputs,
    });
    instance = $range.data("ionRangeSlider");

    function updateInputs (data) {
        from = data.from;
        to = data.to;

        $inputFrom.prop("value", from);
        $inputTo.prop("value", to);
    }

    $inputFrom.on("input", function () {
        var val = $(this).prop("value");

        // validate
        if (val < min) {
            val = min;
        } else if (val > to) {
            val = to;
        }

        instance.update({
            from: val
        });
    });

    $inputTo.on("input", function () {
        var val = $(this).prop("value");

        // validate
        if (val < from) {
            val = from;
        } else if (val > max) {
            val = max;
        }

        instance.update({
            to: val
        });
    });
    // End of Price Range Slider

    //Preserving the State of Which Tab Inventory Search is Preformed
    var tab = "a[data-toggle=\"tab\"]";
    $(tab).on("shown.bs.tab", function (e) {

        sessionStorage.setItem("lastTab", $(this).attr("href"));
    });

    // go to the latest tab, if it exists:a
    var lastTab = sessionStorage.getItem("lastTab");
    if (lastTab) {
        var href2 = "[href=\"" + lastTab + "\"] ";
        $(href2).tab("show");
    }

    $("#newForm").submit(function(e) {
       storeDropDownsState("new");
    });
    $("#usedForm").submit(function(e) {
        storeDropDownsState("used");
    });

});

// Add inventory to users appointment garage
function addToGarage(userId, inventoryId) {
    var button = $("#add-to-garage-" + inventoryId);
    var l = button.ladda();
    l.ladda("start");
    $.post({
        url: "/api/users/" + userId + "/garage/" + inventoryId,
        success: function() {
            toastr.success("Added to garage successfully");
            button.text("In Garage");
            l.ladda("stop");
            button.prop("disabled", true);
        },
        error: function() {
            l.ladda("stop");
            button.prop("disabled", false);
        }
    });
}

// When the user scrolls down 20px from the top, display the button
window.onscroll = function() {scrollFunction()};

function scrollFunction() {
    if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
        document.getElementById("to-top-btn").style.display = "block";
    } else {
        document.getElementById("to-top-btn").style.display = "none";
    }
}

// When the user clicks on the button, scroll to the top of the document
function topFunction() {
    document.body.scrollTop = 0; // For Chrome and Safari
    document.documentElement.scrollTop = 0; // For IE and Firefox
}
