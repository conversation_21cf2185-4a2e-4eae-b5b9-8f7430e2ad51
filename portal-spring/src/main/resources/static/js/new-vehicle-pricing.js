// new vehicle pricing js

function initNewVehiclePricing(searchForm) {

    $('#save-pricing-adjustments-btn').on('click', function(e) {
        e.preventDefault();

        var btn = $(this);
        if (btn.hasClass('disabled')) {
            return false;
        }

        btn
            .prop('disabled', true)
            .addClass('disabled')
            .text('Submitting...');

        var form = $('#pricing-form')[0];

        form.submit();
    });

    /**
     * Generic function to perform an HTTP GET on an endpoint with
     * query params and pass the results to the supplied callback.
     * Handles errors in a standardized way.
     */
    function getResourceAtEndpoint(url, queryParams, successCallback){
        $.get(url, queryParams, function(data, status, xhr){
            // console.log(xhr.getAllResponseHeaders());
        }).done(function(data){
            successCallback(data);
        }).fail(function(error){
            // logout is always 200. how do we catch?

            if(error.status == 403){
                document.location.href = "/login";
            } else {
                console.log("There  was an issue." + error.status)
            }
        });
    }

    function VehiclePricingEndpoint(dealerId){
        this.endpoint = '/stratus/dealer/' + dealerId + '/vehicle-pricing';

        this.andAppend = function(appendage){
            this.endpoint += '/' + appendage;
            return this;
        };

        this.andGet = function(){
            return this.endpoint;
        }
    }

    /**
     * Get all styles from vehicle data having the specified
     * modelId combined with dealer defined pricing
     * adjustments
     */
    function getStylesWithPriceAdjustmentForModelId(dealerId, modelId, successCallback){
        var endpoint = new VehiclePricingEndpoint(dealerId)
            .andAppend('styles')
            .andGet();

        var requestParams = {
            dealer: dealerId,
            modelId: modelId,
            active: searchForm.active
        };

        getResourceAtEndpoint(endpoint, requestParams, successCallback);
    }

    /**
     * Get all vehicles from a dealer's inventory having the specified
     * styleId combined with dealer defined pricing
     * adjustments
     */
    function getCarsWithPriceAdjustmentForStyleId(dealerId, styleId, successCallback){
        var endpoint = new VehiclePricingEndpoint(dealerId)
            .andAppend('new')
            .andAppend('cars')
            .andGet();

        var requestParams = {
            dealer: dealerId,
            styleId: styleId,
            active: searchForm.active
        };

        getResourceAtEndpoint(endpoint, requestParams, successCallback);
    }

    /* UI Events */

    /**
     * When a user clicks on a model name, fetch its styles with price
     * adjustments and create a new new row for each one under the model
     * row
     */
    $('.model-description').click(function(e){
        e.preventDefault();

        var dealerId = $(this).data('dealer-id');
        var modelId = $(this).data('model-id');
        var styleIdFilter = $(this).data('style-id-filter');
        var clickedElement = $(this);
        var source   = $("#styleRow").html();
        var styleRowTemplate = Handlebars.compile(source);


        // check to see if rows already exist
        var nextRow = clickedElement.closest('tr').next('tr');
        var stylesAlreadyFetched = nextRow.hasClass('style-row');

        // if it does exist, hide all style rows
        if(stylesAlreadyFetched){
            // if next row is visible, hide all
            if(nextRow.is(":visible")){
                nextRow.hide();
                nextRow.nextUntil('.model-row').hide();
            } else {
                nextRow.show();
                nextRow.nextUntil('.model-row').show();
            }
            // fetch styles and populate
        } else {
            getStylesWithPriceAdjustmentForModelId(dealerId, modelId, function (stylesWithPriceAdjustment) {
                if (stylesWithPriceAdjustment instanceof Array) {

                    // if a styleId filter was provided, filter only for that style
                    var filteredStyles = stylesWithPriceAdjustment.filter(function(styleWithPriceAdjustment){
                       return !styleIdFilter || styleWithPriceAdjustment.style.id == styleIdFilter;
                    });

                    filteredStyles.forEach(function (styleWithPriceAdjustment) {
                        clickedElement.closest('tr').after(styleRowTemplate(styleWithPriceAdjustment));
                    });

                    // update the validator so that the new fields are validated
                    $('#pricing-form').validator('update');
                }
            });
        }

    });

    /**
     * When a user clicks on a style name, fetch matching inventory with
     * price adjustments and create a new new row for each one under the
     * style row. Uses "on('click'..." since this is a dynamically added
     * element
     */
    $('.pricing-table').on('click', '.style-description', function(e){
        e.preventDefault();

        var dealerId = $(this).data('dealer-id');
        var styleId = $(this).data('style-id');
        var inventoryIdFilter = $(this).data('inventory-id-filter');
        var clickedElement = $(this);
        var source   = $("#inventoryVehicleRow").html();
        var inventoryVehicleRowTemplate = Handlebars.compile(source);

        var nextRow = clickedElement.closest('tr').next('tr');
        var vehiclesAlreadyFetched = nextRow.hasClass('inventory-vehicle-row');

        if(vehiclesAlreadyFetched){
            $('.parent-style-' + styleId).toggle();
        } else {
            getCarsWithPriceAdjustmentForStyleId(dealerId, styleId, function (carsWithPriceAdjustment) {
                if (carsWithPriceAdjustment instanceof Array) {

                    // if a vehicle id filter was provided, filter only for that vehicle
                    var filteredCars = carsWithPriceAdjustment.filter(function(carWithPriceAdjustment){
                        return !inventoryIdFilter || carWithPriceAdjustment.vehicle.id == inventoryIdFilter;
                    });

                    filteredCars.forEach(function (carWithPriceAdjustment) {
                        clickedElement.closest('tr').after(inventoryVehicleRowTemplate(carWithPriceAdjustment));
                    });
                    // update the validator so that the new fields are validated
                    $('#pricing-form').validator('update');
                }
            });
        }
    });

    $('#stockTypePriceAdjustmentEnabler').change(function(e){
        var stockTypePriceAdjustmentEnabled = this.checked;

        enableField('#stockTypePriceAdjustmentType', stockTypePriceAdjustmentEnabled);
        enableField('#stockTypePriceAdjustmentAmount', stockTypePriceAdjustmentEnabled);
        enableField('#stockTypePriceAdjustmentLeaseType', stockTypePriceAdjustmentEnabled);
        enableField('#stockTypePriceAdjustmentLeaseAmount', stockTypePriceAdjustmentEnabled);
        enableField('#stockTypePriceAdjustmentValue', stockTypePriceAdjustmentEnabled);
    });

    /**
     * When a user checks "Enable Pricing" on a row, disable or enable
     * related pricing fields. Uses "on('change'..." since this is a dynamically added
     * element
     */
    $('.pricing-table').on('change', '.price-adjustment-enabler', function(e){

        var priceAdjustmentEnabler = $(this);
        var currentRow = priceAdjustmentEnabler.closest('.price-adjustment-row');

        changeEnablementFor(currentRow, this.checked);
    });


    /**
     * When a user checks "Fixed Price" on a row, disable or enable
     * "Carsaver Price" field. Uses "on('change'..." since this is a dynamically added
     * element
     */
    $('.pricing-table').on('change', '.adjustment-fixed-price', function(e){
        var fixedPriceEnabler = $(this);
        var currentRow = fixedPriceEnabler.closest('.price-adjustment-row');
        setFixedPriceAmountEnablement(currentRow);
    });


    /**
     * When a user changes the checkbox for fixed price on
     * an inventory vehicle, recalculate and update the displayed
     * carsaver price
     */
    $('.pricing-table').on('change', '.adjustment-fixed-price.inventory-vehicle', function(e){
        var vehicleId = $(this).data('vehicle-id');

        updateCarsaverPriceDisplayAndActiveStatus(vehicleId);
    });

    /**
     * When a user blurs the fixed price amount on
     * an inventory vehicle, recalculate and update the displayed
     * carsaver price
     */
    $('.pricing-table').on('blur', '.adjustment-fixed-price-amount.inventory-vehicle', function(e) {
        var $fixedPriceAmountTextBox = $(this);
        var fixedPriceAmount = $fixedPriceAmountTextBox.val();

        if(!fixedPriceAmount || isNaN(fixedPriceAmount) || fixedPriceAmount <= 0) {
            console.warn("invalid fixedPriceAmount, must be a valid number and cannot be <= 0");
            $fixedPriceAmountTextBox.val(null);
            $fixedPriceAmountTextBox.closest('tr').find('.adjustment-fixed-price').trigger('click');
            return;
        }
        var marketPrice = $("input[name='pricedCar[" + vehicleId + "].vehicle.averageMarketPrice']").val();
        if(marketPrice) {

        }
        //user probably mistenenly entered an adjustment value instead of fixed price, show warning confirming the value they entered
        if(fixedPriceAmount < 10000) {
            $('#low-fixed-price-warning-modal').modal('show');
        }

        var vehicleId = $fixedPriceAmountTextBox.data('vehicle-id');

        updateCarsaverPriceDisplayAndActiveStatus(vehicleId);
    });

    /**
     * When a user blurs the offset amount on
     * an inventory vehicle, recalculate and update the displayed
     * carsaver price
     */
    $('.pricing-table').on('blur', '.adjustment-amount.inventory-vehicle', function(e){
        var vehicleId = $(this).data('vehicle-id');

        updateCarsaverPriceDisplayAndActiveStatus(vehicleId);
    });

    $('.pricing-table').on('blur', '.adjustment-amount', function(e){
        handleAdjustmentAmountTextBoxBlurEvent($(e.currentTarget));
    });

    /**
     * When a user changes the adjustment type (% or $) on
     * an inventory vehicle, recalculate and update the displayed
     * carsaver price
     */
    $('.pricing-table').on('change', '.adjustment-type.inventory-vehicle', function(e){
        var vehicleId = $(this).data('vehicle-id');

        updateCarsaverPriceDisplayAndActiveStatus(vehicleId);
    });

    /**
     * When a user changes the adjustment value (INVOICE or MARKET) on
     * an inventory vehicle, recalculate and update the displayed
     * carsaver price
     */
    $('.pricing-table').on('change', '.adjustment-adjusted-value.inventory-vehicle', function(e){
        var vehicleId = $(this).data('vehicle-id');

        updateCarsaverPriceDisplayAndActiveStatus(vehicleId);
    });

    function handleAdjustmentAmountTextBoxBlurEvent($blurred) {
        var adjustmentAmount = $blurred.val();
        var originalValue = $blurred.attr('data-original-value');
        originalValue = isNaN(originalValue) ? null : parseInt(originalValue);

        if(!adjustmentAmount || isNaN(adjustmentAmount)) {
            console.warn("adjustment amount of " + adjustmentAmount + " is not valid, disabling adjustment");
            $blurred.val('');
            $blurred.closest('tr').find('.price-adjustment-enabler').click();
        } else if (adjustmentAmount <= -2000 && (originalValue == null || adjustmentAmount != originalValue)) {
            const confirmation = confirm('Are you sure you want to adjust the price by ' + numeral(adjustmentAmount).format('$0,0') + '?  NOTE: Consumer Total Cash Rebates are included automatically by CarSaver, if you are including any consumer total cash in your adjustment, this could adjust your sale price more than intended.')
            if (!confirmation) {
                console.warn("adjustment amount of " + adjustmentAmount + " is marked invalid by user, disabling adjustment");
                $blurred.val('');
                $blurred.closest('tr').find('.price-adjustment-enabler').click();
            }
        }
    }

    function updateCarsaverPriceDisplayAndActiveStatus(vehicleId){

        var carsaverPrice = calculateCarsaverPriceFor(vehicleId);
        var marketPrice = $("input[name='pricedCar[" + vehicleId + "].vehicle.averageMarketPrice']").val();
        var msrp = $("input[name='pricedCar[" + vehicleId + "].vehicle.msrp']").val();

        updateCarsaverPriceDisplay(vehicleId, carsaverPrice);
        updateActiveStatus(vehicleId, carsaverPrice, marketPrice, msrp);
    }

    function updateCarsaverPriceDisplay(vehicleId, displayValue){
        $('#carsaver-price-' + vehicleId).html(displayValue);
    }

    function isNumber(val){return !isNaN(parseInt(val));}

    function updateActiveStatus(vehicleId, carsaverPrice, marketPrice, msrp){
        var valid, isActive;
        if(valid = isNumber(carsaverPrice)) {
            if(valid = isNumber(marketPrice)) {
                isActive = carsaverPrice <= (marketPrice - 100);
            } else if(valid = isNumber(msrp)) {
                isActive = carsaverPrice <= (msrp - 100);
            }
        }
        warnIf(!valid, "could not set active status because carSaverPrice, marketPrice, or msrp were not valid");
        if(window._CS_ENFORCE_PRICING_RULES === true) {
            setActiveStatus(vehicleId, isActive);
        }
    }



    function setActiveStatus(vehicleId, active){
        var activeClass = 'glyphicon-ok';
        var notActiveClass = 'glyphicon-remove';

        if(active){
            $('#active-status-' + vehicleId).removeClass(notActiveClass);
            $('#active-status-' + vehicleId).addClass(activeClass);
            $('#active-status-' + vehicleId).closest('tr').removeClass('inactive-vehicle');
        } else {
            $('#active-status-' + vehicleId).removeClass(activeClass);
            $('#active-status-' + vehicleId).addClass(notActiveClass);
            $('#active-status-' + vehicleId).closest('tr').addClass('inactive-vehicle');
        }
    }

    function calculateCarsaverPriceFor(vehicleId){

        var isFixedPrice = $("input[name='pricedCar[" + vehicleId + "].adjustment.fixedPrice']").prop( "checked" );
        var carsaverPrice = "";

        if(isFixedPrice){
            carsaverPrice = calculateFixedPriceCarsaverPriceFor(vehicleId);
        } else {
            carsaverPrice = calculateNonFixedPriceCarsaverPriceFor(vehicleId);
        }

        return carsaverPrice;
    }

    function calculateFixedPriceCarsaverPriceFor(vehicleId){
        var fixedPriceAmount = $("input[name='pricedCar[" + vehicleId + "].adjustment.fixedPriceAmount']").val();
        return fixedPriceAmount;
    }

    function calculateNonFixedPriceCarsaverPriceFor(vehicleId){
        var carsaverPrice = "";

        var currentVehicleInputSelector = "input[name='pricedCar[" + vehicleId + "]";
        var averageMarketPrice = $(currentVehicleInputSelector + ".vehicle.averageMarketPrice']").val();
        var invoicePrice = $(currentVehicleInputSelector + ".vehicle.invoicePrice']").val();
        var adjustmentAmount = $(currentVehicleInputSelector + ".adjustment.amount']").val();
        var adjustedValue = $("input[name='pricedCar[" + vehicleId + "].adjustment.adjustedValue']").val();
        var adjustmentType = $("select[name='pricedCar[" + vehicleId + "].adjustment.type']").val();

        var valueToAdjust = invoicePrice;

        // extra + signs to cast js strings to number based on knowledge-base article
        if(adjustmentType == 'DOLLAR'){
            carsaverPrice = +valueToAdjust + +adjustmentAmount;
        } else if(adjustmentType == 'PERCENTAGE'){
            var percentageBasedAdjustmentAmount = +valueToAdjust * (+adjustmentAmount/100);
            carsaverPrice = Math.round(+valueToAdjust + +percentageBasedAdjustmentAmount);
        }

        return carsaverPrice;
    }


    function changeEnablementFor(currentRow, enabled){

        enableField(currentRow.find('.adjustment-adjusted-value'), enabled);
        enableField(currentRow.find('.adjustment-amount'), enabled); //finance
        enableField(currentRow.find('.adjustment-type'), enabled); //finance
        enableField(currentRow.find('.adjustment-lease-amount'), enabled);
        enableField(currentRow.find('.adjustment-lease-type'), enabled);

        enableField(currentRow.find('.adjustment-fixed-price'), enabled);


        setFixedPriceAmountEnablement(currentRow);
    }

    /**
     * Fixed price will editable if both "Enable Pricing"
     * and "Fixed Price" are checked. If "fixed price" is
     * checked, disable other pricing fields.
     */
    function setFixedPriceAmountEnablement(currentRow){
        var fixPricedIsChecked = currentRow.find('.adjustment-fixed-price').prop('checked');
        var pricingIsEnabled = currentRow.find('.price-adjustment-enabler').prop('checked');
        var enableFixedPricing = fixPricedIsChecked && pricingIsEnabled;
        var enableRegularPricing = pricingIsEnabled && !fixPricedIsChecked;

        // if fixed price is checked, disable other fields
        enableField(currentRow.find('.adjustment-adjusted-value'), enableRegularPricing);
        enableField(currentRow.find('.adjustment-type'), enableRegularPricing);
        enableField(currentRow.find('.adjustment-amount'), enableRegularPricing);

        enableField(currentRow.find('.adjustment-fixed-price-amount'), enableFixedPricing);
    }

    function enableField(field, enabled) {
        $(field).prop("disabled", !enabled);
    }

    Handlebars.registerHelper("disabledUnless", function (condition) {
        return condition ?  "": "disabled";
    });

    Handlebars.registerHelper("disabledUnlessAdjustmentEnabledAndNotFixedPrice", function (adjustment) {

        var shouldNotBeDisabled = false;

        if(adjustment){
            shouldNotBeDisabled = (adjustment.enabled && !adjustment.fixedPrice);
        }

        return (shouldNotBeDisabled) ?  "": "disabled";
    });

    Handlebars.registerHelper("disabledUnlessAdjustmentEnabledAndIsFixedPrice", function (adjustment) {
        var shouldNotBeDisabled = false;

        if(adjustment){
            shouldNotBeDisabled = (adjustment.enabled && adjustment.fixedPrice);
        }

        return (shouldNotBeDisabled) ?  "": "disabled";
    });

    Handlebars.registerHelper("resolveVehicleActiveClass", function (vehicle) {
        var activeClass = 'glyphicon-ok';
        var notActiveClass = 'glyphicon-remove';

        return (vehicle.active) ?  activeClass: notActiveClass;
    });

    Handlebars.registerHelper("inactiveBackgroundColor", function (vehicle) {
        var inactiveVehicle = 'inactive-vehicle';

        if (!vehicle.active){
            return inactiveVehicle;
        }

    });

    window.Handlebars.registerHelper('select', function( value, options ){
        var $el = $('<select />').html( options.fn(this) );
        $el.find('[value="' + value + '"]').attr({'selected':'selected'});
        return $el.html();
    });

    $.fn.filterByData = function(prop, val) {
        return this.filter(
            function() { return $(this).data(prop)==val; }
        );
    };

    function warnIf(condition, message){
        if(condition){
            console.warn(message);
        }
    }

}




