application:
  domain: https://portal-qa.carsaver.com
warranty-service:
  nwan-client:
    rootUri: https://qvcarsaver.naenwan.com/eBusinessApi
    username: TCS
    password: zcxUUvLJPj7QdycsQ2hGfTMcEh9N6r
  providerCode: TCS
  e-remittance-portal:
    url: https://qvcarsaver.naenwan.com/Portals/eBusiness/eRemittancePortal.aspx?oauth_token=
    role: f5b2da92-1811-4b0a-9f82-5e0b507b5077
  supply-order-portal:
    url: https://qvcarsaver.naenwan.com/Portals/Organizations/OrderSupplies.aspx?oauth_token=
    role: 5d0b33ad-6a7b-4943-8331-15be54d61a7a
stripe:
  publishableKey: pk_test_r0wnvYnfMvaNT3sCAzBXSiDj
  secretKey: sk_test_8fo6JIdp4LtUD5CE84do92Gy
  products:
    platform-product-id: prod_FqD6w5jafaU7v0
twilio:
  accountId: **********************************
  authToken: 8cf701badf19f0aed13451a3fc92afdb
  number: +***********
rewards-genius:
  platform-name: CarSaver
  api-key: qOKnsgA$q?Uvof&FOBUlxzqE!GoQgUAwoIHr!xzsluw@
  environment: SANDBOX
spring:
  redis:
    host: carsaver-portal-qa.zs5f5u.0001.use1.cache.amazonaws.com
  session:
    redis:
      namespace: spring:portal:qa:session
  security:
    oauth2:
      client:
        registration:
          carsaver:
            client-id: PORTAL
            client-secret: -Ku2Q_FCN2
  config:
    import: configserver:https://api-qa.internal.carsaver.com/config
  cloud:
    config:
      username: config
      password: Rn9cECZMu586wY@Y

intacct:
  company-id: CarSaver-sandbox
  user-id: xmlgw-CSP
  user-password: '@vq4xx2O8ap'
  sender-id: CarSaver
  sender-password: 'dj4w7Zg4DrL['
routeone:
  base-uri: https://itl.routeone.net
  partner-id: F00CSV
  sso:
    password: csv$%*HBNF3ed+
carsaver:
  cloud:
    kinesis:
      api-log-stream: api-log-stream-qa
atlas:
  url: https://atlas.qa.carsaver.com
program-id:
  nissan-buy-at-home: '5e922fe4-e1e9-468c-b100-5b8f7cffcef3'
