$base-size: 16;

@function px2em($pixels, $base: $base-size) {

    @if $pixels == 0 {
        @return 0
    }

    @return $pixels / $base + 0em;
}

@function px2rem($pixels, $base: $base-size) {

    @if $pixels == 0 {
        @return 0
    }

    @return $pixels / $base + 0rem;
}

@function px2remUnits($pixels, $base: $base-size) {

    @if $pixels == 0 {
        @return 0
    }

    @return $pixels / $base + 0;
}
