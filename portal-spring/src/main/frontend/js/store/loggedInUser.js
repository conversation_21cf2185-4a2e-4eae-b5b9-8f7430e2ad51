import _ from 'lodash';
import {make} from 'vuex-pathify';

const state = {
    authorizations: _.get(window, '_CS_AUTHORIZATIONS', []),
    dealerPermissions: _.get(window, '_CS_DEALER_PERMISSIONS', {}),
    id: _.get(window, '_CS_USER.id', null),
    email: _.get(window, '_CS_USER.email', null),
    featureFlags: _.get(window, "_CS_FEATURE_FLAGS", null),
};

const mutations = {
    ...make.mutations(state)
};

const getters = {
    hasDealerPermission: (state) => (dealerId, permission) => {
        if (_.isNil(dealerId) || _.isNil(permission)) {
            return false;
        }

        const dealerPermissions = _.get(state.dealerPermissions, dealerId);
        if (_.isNil(dealerPermissions) || _.size(dealerPermissions) === 0) {
            return false;
        }

        return _.indexOf(dealerPermissions, permission) !== -1;
    },
    hasAuthority: (state) => (authorities) => {
        if (!_.isArray(authorities)) {
            return false;
        }

        const auths = _.map(state.authorizations, (auth) => _.get(auth, 'authority', ''));
        return _.intersection(authorities, auths).length === authorities.length;
    },
    hasAnyAuthorities: (state) => (authorities) => {
        if (!_.isArray(authorities)) {
            return false;
        }

        const auths = _.map(state.authorizations, (auth) => _.get(auth, 'authority', ''));
        return _.intersection(authorities, auths).length >= 1;
    }
};

export default {
    namespaced: true,
    state,
    getters,
    mutations
};
