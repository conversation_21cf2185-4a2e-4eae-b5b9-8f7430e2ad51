import axios from 'axios';
import _ from 'lodash';

const basePath = '/api';

function getMeta(metaName) {
    const metas = document.getElementsByTagName('meta');

    for (let i = 0; i < metas.length; i++) {
        if (metas[i].getAttribute('name') === metaName) {
            return metas[i].getAttribute('content');
        }
    }

    return null;
}

export default new class ApiClient {
    constructor() {
        this.httpClient = axios.create();

        this.httpClient.interceptors.request.use(req => {
            // `req` is the Axios request config, so you can modify
            // the `headers`.
            const csrfHeader = getMeta('_csrf_header');
            if (!_.isNil(csrfHeader)) {
                req.headers[csrfHeader] = getMeta('_csrf');
            }
            return req;
        });

        this.httpClient.interceptors.response.use(
            (response) => {
                if (_.isString(response.data)) {
                    if (response.data.indexOf('new Auth0Lock') !== -1) {
                        window.location.reload();
                        return;
                    }
                }

                return response;
            },
            (error) => {
                return Promise.reject(error);
            }
        );
    }

    getAll(collection) {
        return axios.all(collection.map((item) => this.httpClient.get(basePath + item.path, item.params)))
            .then((data) => {
                console.log('Axios result: ', data);
                return data;
            });
    }

    get(path, params) {
        return this.httpClient.get(basePath + path, {params});
    }

    put(path, data, config) {
        return this.httpClient.put(basePath + path, data, config);
    }

    post(path, data, config) {
        return this.httpClient.post(basePath + path, data, config);
    }

    patch(path, data) {
        return this.httpClient.patch(basePath + path, data);
    }

    delete(path, data) {
        return this.httpClient.delete(basePath + path, data);
    }
}();
