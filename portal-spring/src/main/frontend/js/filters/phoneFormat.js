import Vue from 'vue';
import _ from 'lodash';

Vue.filter('phone', function (phone) {
    if (!_.isNil(phone)) {
        const trimPhone = phone.replace(/[^0-9]/g, '');
        if (trimPhone.length === 10) {
            return trimPhone.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        }
    }

    return phone;
});

Vue.filter('phoneFormatter', function (phoneNumber) {
    if (!phoneNumber || _.isNil(phoneNumber)) return '';

    // remove non numeric characters from phone number
    const numericPhoneNumber = phoneNumber.replace(/\D/g, '');

    if (numericPhoneNumber.length < 10) return '';

    const areaCode = numericPhoneNumber.substring(0, 3);
    const exchangeCode = numericPhoneNumber.substring(3, 6);
    const subscriberNumber = numericPhoneNumber.substring(6, 10);

    return `(${areaCode}) ${exchangeCode}-${subscriberNumber}`;
});
