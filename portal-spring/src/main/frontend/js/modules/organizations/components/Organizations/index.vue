<template>
    <div>
        <b-container fluid class="py-4">
            <b-row>
                <b-col :md="displayPane ? '8' : '12'" sm="12" order-sm="2" order-md="1">
                    <div v-if="loader.isLoading" class="d-flex justify-content-center align-items-center">
                        <h1>Loading ...</h1>
                    </div>
                    <div v-else>
                        <vue-tree-list
                            @click="setSelectedOrganization"
                            @change-name="onChangeName"
                            @delete-node="onDelete"
                            @add-node="onAddNode"
                            :model="data"
                            default-tree-node-name="New Group"
                            default-leaf-node-name="New Dealer"
                            v-bind:default-expanded="true"
                            @drop="dropAndUpdate"
                      >
                            <template v-slot:leafNameDisplay="slotProps">
                                <span @click="setSelectedOrganization(slotProps.model)">
                                  {{ slotProps.model.name }}
                                </span>
                            </template>
                            <span class="icon mr-2 font-weight-bold" slot="addTreeNodeIcon">Add Folder <i class="fa fa-plus-circle"> </i></span>
                            <template v-slot:addLeafNodeIcon="slotProps">
                                <span @click="setSelectedOrganization(slotProps.model)" class="icon mr-2 font-weight-bold" slot="addLeafNodeIcon">| Add Dealer <i class="fa fa-plus-circle"> </i></span>
                            </template>
                            <template v-slot:editNodeIcon="slotProps">
                                <span @click="setSelectedOrganization(slotProps.model)" class="icon mr-2 font-weight-bold" slot="editNodeIcon" >| Edit <i class="fa fa-edit"> </i></span>
                            </template>
                            <span class="icon font-weight-bold" slot="delNodeIcon">| Delete <i class="fa fa-trash-alt"> </i></span>
                            <span class="icon mr-2" slot="leafNodeIcon"><i class="fa fa-car"></i></span>
                            <span class="icon mr-2" slot="treeNodeIcon"><i class="fa fa-folder ml-1"></i></span>
                        </vue-tree-list>
                    </div>
                </b-col>
                <b-col v-if="displayPane" sm="12" md="4" order-sm="1" order-md="2" class="px-md-0">
                    <div class="display-pane border p-2 mb-4">
                        <div class="d-flex justify-content-between border-bottom">
                            <h3>Organization Information</h3>
                            <b-link @click="displayPane = false">
                                <i class="fa fa-window-close"></i>
                            </b-link>
                        </div>
                        <div v-for="(pair, index) in Object.entries(selectedOrganization)"
                             :key="index"
                             class="property-row d-flex justify-content-between p-2">
                            <span class="font-weight-bold">{{ pair[0] }}</span>
                            <span>{{ pair[1] }}</span>
                        </div>
                    </div>
                </b-col>
            </b-row>
        </b-container>
        <b-modal
            :visible="modalDisplay"
            id="dealer-search-modal"
            ref="dealer-search-modal"
            size="xs"
            hide-footer
            @cancel="hideModal"
            @close="hideModal"
            no-close-on-backdrop
            title="Add Dealer Node"
        >
            <dealer-list
                :search-results="searchResults"
                :is-loading="searchResultsIsLoading"
                @addDealer="setSelectedDealerAndAdd"
            />
            <div class="d-flex justify-content-end mt-3">
                <b-link variant="primary" @click="hideModal">CANCEL</b-link>
            </div>
        </b-modal>
        <delete-modal @confirm="doDelete" :node="selectedForDelete" />
    </div>
</template>

<script>
import { VueTreeList, Tree } from 'vue-tree-list'
import {call, get} from 'vuex-pathify';
import DealerList from "./DealerList.vue";
import DeleteModal from "./DeleteModal.vue";
export default {
    name: "Organizations",
    components: {
        DeleteModal,
        DealerList,
        VueTreeList
    },
    data() {
        return {
            data: null,
            displayPane: false,
            selectedOrganization: null,
            selectedDealer: null,
            parentId: null,
            organization: null,
            newNode: null,
            selectedForDelete: null,
        }
    },
    created() {
        this.fetchOrganizations();
        this.fetchDealers();
    },
    computed: {
        organizations: get("organizations/organizations@data"),
        loader: get("organizations/organizations@loader"),
        modalDisplay: get("organizations/modalDisplay"),
        searchResults: get("organizations/dealers@data"),
        searchResultsIsLoading: get("organizations/<EMAIL>"),
    },
    watch: {
        organizations(newVal) {
            if (newVal) {
                // disables deleting the root node
                this.organizations[0].delNodeDisabled = true;
                // disables dragging the root node
                this.organizations[0].dragDisabled = true;
                // disables editing the root node
                this.organizations[0].editNodeDisabled = true;
                // disables adding dealer nodes on the root node
                this.organizations[0].addLeafNodeDisabled = true;
                this.data = new Tree(this.organizations);
            }
        }
    },
    methods: {
        fetchDealers: call("organizations/fetchDealers"),
        fetchOrganizations: call("organizations/fetchOrganizations"),
        addOrganization: call("organizations/addOrganization"),
        updateOrganization: call("organizations/updateOrganization"),
        deleteOrganization: call("organizations/deleteOrganization"),
        setModalDisplay: call("organizations/setModalDisplay"),
        dropAndUpdate(params) {
            console.log('dropAndUpdate node:', params);

            const organizationParent = {
                id: params.node.parent.id,
            }
            const organizationRequest = {
                id: params.node.id,
                name: params.node.name,
                dealerId: params.node.dealerId,
                parentId: params.node.parent.id,
                parent: organizationParent,
            }

            this.updateOrganization(organizationRequest);

        },
        onDelete(node) {
            this.selectedForDelete = node;
            this.$bvModal.show("deleteModal");
        },
        doDelete(node) {
            this.displayPane = false;
            this.deleteOrganization(node);
        },
        onChangeName(params) {
            // only update on blur otherwise it would happen on every character the user enters
            if (params.eventType === 'blur') {
                const organizationParent = {
                    id: this.selectedOrganization.parentId,
                };

                const organizationRequest = {
                    id: this.selectedOrganization.id,
                    name: params.newName,
                    dealerId: this.selectedOrganization?.dealerId,
                    parent: organizationParent,
                };

                this.updateOrganization(organizationRequest);
            }

            this.selectedOrganization.name = params.newName;
        },
        onAddNode(params) {
            this.parentId = params.pid;
            this.newNode = params;
            // leaf nodes are reserved for dealers
            if (params.isLeaf) {
                this.openSearchDealersModal();
            } else {
                const organizationParent = {
                    id: this.parentId,
                }
                const organizationRequest = {
                    name: params.name,
                    dealerId: null,
                    parentId: this.parentId,
                    parent: organizationParent,
                }

                this.addOrganization(organizationRequest);
            }
        },
        setSelectedOrganization(params) {
            this.selectedOrganization = _.omit(params, [
                'parent', 'children', 'toggle', 'dragDisabled', 'addTreeNodeDisabled', 'addLeafNodeDisabled',
                'editNodeDisabled', 'delNodeDisabled', 'isLeaf',
            ]);
            this.selectedOrganization.parentId = params.parent.id;

            this.displayPane = true;
        },
        openSearchDealersModal() {
            this.setModalDisplay(true);
        },
        hideModal() {
            this.selectedDealer = null;
            this.parentId = null;
            this.setModalDisplay(false);
            this.newNode.remove();
        },
        addDealer() {
            this.newNode.changeName(this.selectedDealer.name);

            const organizationParent = {
                id: this.parentId,
            }
            const organizationRequest = {
                name: this.selectedDealer.name,
                dealerId: this.selectedDealer.id,
                parent: organizationParent,
            }
            this.addOrganization(organizationRequest);
        },
        setSelectedDealerAndAdd(dealer) {
            this.selectedDealer = dealer;

            this.addDealer();
        }
    }
};
</script>
<style lang="scss">
#page-wrapper {
    background-color: #f3f3f4 !important;
}
.vtl-node-content {
    font-weight: bold;
}
.vtl-node-main {
    height: 70px;
}

.vtl-node-main:hover {
    background-color: #FFFFFF !important;
    border: 1px solid #e7eaec;
    border-radius: 5px;
    cursor: pointer;
}
.vtl {
    .vtl-drag-disabled {
        background-color: #d0cfcf;
        &:hover {
            background-color: #d0cfcf;
        }
    }
    .vtl-disabled {
        background-color: #d0cfcf;
    }
}
.display-pane {
    border-radius: 5px;
    background-color: #FFFFFF;

    .property-row {
        border-bottom: 1px solid #e7eaec;
    }
    .property-row:last-child {
        border-bottom: none;
    }
}
</style>

<style lang="scss" scoped>
.icon {
    &:hover {
        cursor: pointer;
    }
}

.fa-plus-circle {
    color: $green,
}
.fa-edit {
    color: $blue;
}
.fa-trash-alt {
    color: $red;
}

.muted {
    color: gray;
    font-size: 80%;
}
</style>
