import _ from "lodash";
import {make} from "vuex-pathify";
import loader from "@/api/loader";
import searchUtils from "@/api/searchUtils";

const uriRoot = "/call-queue";
const initialState = {
    ...searchUtils.state(),
    initialLoad: true,
    pageable: searchUtils.parsePageableFromUrl({
        sort: "createdDate,desc",
        page: 1
    }),
    facets: null,
    filters: searchUtils.parseFiltersFromUrl(
        _.get(window, "_CS_CALL_QUEUE_SEARCH_CRITERIA", null)
    ),
    searchLoader: {
        loader: loader.defaultState(),
        data: []
    },
    pageMetadata: {
        size: 0,
        totalElements: 0,
        totalPages: 0,
        number: 0
    },
    /**
     * pills schema
     * {
                    "name": {
                        label: 'Name',
                        enabled: false,
                    },
                    "warrantyStatuses": {
                        label: 'Warranty',
                        enabled: true
                    }
                }
     *     label: label to use for display
     *     facet: the name of the search facet
     *     enabled: is the pill show be displayed
     * }
     */
    pills: {
        name: {
            enabled: false
        }
    }
};

const mutations = {
    ...make.mutations(initialState),
    ...searchUtils.mutations()
};

const actions = {
    ...make.mutations(initialState),
    ...searchUtils.actions(uriRoot, "Call Queue Search")
};

const getters = {
    ...make.getters(initialState),
    ...searchUtils.getters()
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters
};
