<template>
    <b-form inline class="call-search-form fs-ignore-formabandon" @submit.prevent="doSubmit">
        <b-form-group>
            <b-input v-model="name" placeholder="Customer Name"/>
        </b-form-group>
        <b-button id="search-button" type="submit" variant="primary"
                  size="sm"
        >
            Search
        </b-button>
        <b-button id="clear-button" variant="info" size="sm"
                  href="/call-queue"
        >
            Reset
        </b-button>
    </b-form>
</template>

<style>
.call-search-form {
    padding-left: 0;
}

#clear-button, #search-button {
    margin: 5px 0 5px 5px;
}

@media screen and (max-width: 765px) {
    .call-search-form {
        padding: 0 !important;
    }

    #clear-button, #search-button {
        margin: 5px 0 0 0;
        width: 100%;
    }
}
</style>

<script>
import {dispatch, sync} from 'vuex-pathify';

export default {
    name: 'CallQueueSearchForm',
    computed: {
        name: sync('callQueueSearch/filters@name')
    },

    methods: {
        doSubmit() {
            dispatch('callQueueSearch/doSearch');
        }
    }
};
</script>
