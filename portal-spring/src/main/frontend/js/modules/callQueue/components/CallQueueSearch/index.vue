<template>
    <search-page :store="store" :search-index="searchIndex" :exportable="false">
        <call-queue-search-form slot="searchForm"/>
    </search-page>
</template>
<script>
import SearchPage from "Components/SearchPage/index";
import CallQueueSearchForm from "Modules/callQueue/components/CallQueueSearch/CallQueueSearchForm/index";

export default {
    name: "CallQueueSearch",
    components: {
        CallQueueSearchForm,
        SearchPage
    },

    data() {
        return {
            store: "callQueueSearch",
            searchIndex: "call-queue"
        };
    }
};
</script>
