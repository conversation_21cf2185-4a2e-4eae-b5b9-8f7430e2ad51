<template>
    <div v-if="isInitialLoad">
        <table-content-loader/>
    </div>

    <div v-else class="my-4">
        <div class="ibox">
            <div class="ibox-content">
                <b-table
                    responsive
                    striped
                    hover
                    bordered
                    :fields="fields"
                    :items="searchResults"
                    :no-local-sorting="true"
                    :sort-by.sync="sortBy"
                    :sort-desc.sync="sortDesc"
                    :no-sort-reset="true"
                    @sort-changed="sortChanged"
                >
                    <template v-slot:cell(user.lastName)="data">
                        <a
                            :href="`/user/${data.item.user.id}`"
                            class="blue-text"
                        >{{ data.item.user.firstName }}
                            {{ data.item.user.lastName }}</a
                        >
                    </template>

                    <template v-slot:cell(requestedCallTime)="data">
                        <div v-if="data.item.callTime">
                            <span>{{
                                    data.item.callTime
                                        | formatDateTime("MM/DD/YYYY h:mm a z")
                                }}</span>
                        </div>
                    </template>

                    <template v-slot:cell(phoneNumber)="data">
                        <b-link :href="'tel:' + data.item.user.phoneNumber">
                            {{ data.item.user.phoneNumber | phone }}
                        </b-link>
                    </template>

                    <template v-slot:cell(source)="data">
                        <span>{{ data.item.source.hostname }}</span>
                    </template>

                    <template v-slot:cell(createdDate)="data">
                        <span>{{
                                data.item.createdDate
                                    | formatDateTime("MM/DD/YYYY h:mm a z")
                            }}</span>
                    </template>

                    <template v-slot:cell(campaignId)="data">
                        <span v-if="data.item.source.campaignId">{{
                                data.item.source.campaignId
                            }}</span>
                    </template>
                </b-table>

                <div class="pagination-wrapper">
                    <pagination
                        :show-totals="false"
                        :page-metadata="pageMetadata"
                        :page="page"
                        @page-changed="pageChanged"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<style>
.pagination-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>

<script>
import TableContentLoader from "Components/TableContentLoader";
import Pagination from "Components/Pagination";
import {dispatch, get, sync} from "vuex-pathify";
import searchPageMixin from "@/mixins/searchPageMixin";

export default {
    name: "CallQueueList",
    components: {TableContentLoader, Pagination},
    mixins: [searchPageMixin],
    data() {
        return {
            fields: [
                {
                    key: "user.lastName",
                    label: "Customer",
                    sortable: true
                },
                {
                    key: "requestedCallTime",
                    sortable: false
                },
                {
                    key: "phoneNumber",
                    sortable: false
                },
                {
                    key: "source",
                    sortable: false
                },
                {
                    key: "certificateId",
                    sortable: false
                },
                {
                    key: "createdDate",
                    sortable: false
                }
            ]
        };
    },
    computed: {
        searchResults: get("callQueueSearch/searchLoader@data"),
        pageMetadata: get("callQueueSearch/pageMetadata"),
        isInitialLoad: get("callQueueSearch/initialLoad"),
        page: sync("callQueueSearch/pageable@page"),
        sort: sync("callQueueSearch/pageable@sort")
    },
    watch: {
        page: () => {
            dispatch("callQueueSearch/doPageLoad");
        },
        sort: () => {
            dispatch("callQueueSearch/doSort");
        }
    },
    mounted() {
        dispatch("callQueueSearch/doPageLoad");
    },
    methods: {
        pageChanged(page) {
            this.page = page;
        }
    }
};
</script>
