import _ from "lodash";
import {make} from "vuex-pathify";
import api from "../../../../api";
import loader from "@/api/loader";

const initialState = {
    selectedVehicleSaleId: _.get(window, "_CS_SELECTED_VEHICLE_SALE_ID", null),
    vehicleSale: {
        loader: loader.defaultState(),
        data: {}
    },
    vehicleSaleNotes: {
        loader: loader.defaultState(),
        data: {}
    },
    changeSets: {
        loader: loader.defaultState(),
        data: []
    }
};

const mutations = {
    ...make.mutations(initialState),

    SET_VEHICLE_SALE_STATUS: (state, value) => {
        state.vehicleSale.sale.status = value;
    }
};

const actions = {
    fetchVehicleSale({commit, state}) {
        if (state.vehicleSale.loader.isLoading) {
            return;
        }

        commit("SET_VEHICLE_SALE", {
            ...state.vehicleSale,
            loader: loader.started()
        });

        return api
            .get(`/sale/${state.selectedVehicleSaleId}/details`)
            .then(response => {
                commit("SET_VEHICLE_SALE", {
                    data: response.data,
                    loader: loader.successful()
                });
                return response;
            })
            .catch(error => {
                console.error(error);
                commit("SET_VEHICLE_SALE", {
                    data: {},
                    loader: loader.error(error)
                });
                return error;
            });
    },
    fetchVehicleSaleNotes({commit, state}) {
        if (state.vehicleSaleNotes.loader.isLoading) {
            return;
        }

        commit("SET_VEHICLE_SALE_NOTES", {
            ...state.vehicleSaleNotes,
            loader: loader.started()
        });

        api.get(`/sale/${state.selectedVehicleSaleId}/notes`)
            .then(response => {
                commit("SET_VEHICLE_SALE_NOTES", {
                    data: response.data,
                    loader: loader.successful()
                });
            })
            .catch(error => {
                console.log(error);
                commit("SET_VEHICLE_SALE_NOTES", {
                    data: {},
                    loader: loader.error(error)
                });
            });
    },

    loadChangeSets({commit, state}) {
        if (state.changeSets.loader.isLoading) {
            return;
        }

        commit("SET_CHANGE_SETS", {
            ...state.changeSets,
            loader: loader.started()
        });

        api.get(`/sale/${state.selectedVehicleSaleId}/changes`)
            .then(response => {
                commit("SET_CHANGE_SETS", {
                    data: response.data,
                    loader: loader.successful()
                });
            })
            .catch(error => {
                console.log(error);
                commit("SET_CHANGE_SETS", {
                    data: [],
                    loader: loader.error(error)
                });
            });
    },

    addVehicleSaleNote({commit, state, dispatch}, {newNoteContent, mentionedUsers, url}) {
        api.post(`/sale/${state.selectedVehicleSaleId}/notes`, {
            content: newNoteContent,
            mentionedUsers: mentionedUsers,
            url: url
        })
            .then(() => {
                dispatch("fetchVehicleSaleNotes");
            })
            .catch(error => {
                console.log(error);
            });
    },

    updateStatus({commit, state}, status) {
        return api
            .post(`/sale/${state.selectedVehicleSaleId}/status/${status}`)
            .then(() => {
                commit("SET_VEHICLE_SALE_STATUS", status);
                return true;
            })
            .catch(() => false);
    },

    clearDataField({commit, state, dispatch}, dataField) {
        return api
            .post(
                `/sale/${state.selectedVehicleSaleId}/clear?field=${dataField}`
            )
            .then(() => {
                dispatch("fetchVehicleSale");
            })
            .catch(() => false);
    },

    setWarrantyIneligible({commit, state}, ineligible) {
        return api
            .post(
                `/sale/${state.selectedVehicleSaleId}/warranty-ineligible?ineligible=${ineligible}`
            )
            .then(() => true)
            .catch(() => false);
    }
};

export default {
    namespaced: true,
    state: initialState,
    actions,
    mutations
};
