import _ from "lodash";
import {make} from "vuex-pathify";
import loader from "@/api/loader";
import searchUtils, {QueryModeEnum} from "@/api/searchUtils";

const uriRoot = "/sales";

const initialState = {
    ...searchUtils.state(),
    queryMode: QueryModeEnum.STATS,
    initialLoad: true,
    pushHistoryEnabled: true,
    pageable: searchUtils.parsePageableFromUrl({
        sort: "createdDate,desc",
        page: 1
    }),
    facets: {},
    filters: searchUtils.parseFiltersFromUrl(
        _.get(window, "_CS_SEARCH_CRITERIA", null)
    ),
    searchLoader: {
        loader: loader.defaultState(),
        data: []
    },
    pageMetadata: {
        size: 0,
        totalElements: 0,
        totalPages: 0,
        number: 0
    },
    total: 0,
    /**
     * pills schema
     * {
                    "name": {
                        label: 'Name',
                        enabled: false,
                    },
                    "warrantyStatuses": {
                        label: 'Warranty',
                        enabled: true
                    }
                }
     *     label: label to use for display
     *     facet: the name of the search facet
     *     enabled: is the pill show be displayed
     * }
     */
    pills: {
        name: {
            enabled: false
        },
        dealerName: {
            enabled: false
        },
        dealerIds: {
            label: "Dealers",
            facet: "dealers"
        },
        dealerStates: {
            label: "Dealer States"
        },
        dealerDmaCodes: {
            label: "Dealer DMAs",
            facet: "dealerDmas"
        },
        violationTypes: {
            label: "Violation Types"
        },
        userSourceHostnames: {
            label: "User Source"
        },
        userTags: {
            label: "User Tags"
        },
        userDmaCodes: {
            label: "User DMAs",
            facet: "userDmas"
        },
        certifiedDealer: {
            label: "Certified Dealers",
            facet: "certifiedDealers"
        },
        topDmas: {
            type: "range"
        }
    }
};

const mutations = {
    ...make.mutations(initialState),
    ...searchUtils.mutations()
};

const getters = {
    ...searchUtils.getters()
};

const actions = {
    ...make.actions(initialState),
    ...searchUtils.actions(uriRoot, "Vehicle Sale Stats")
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters
};
