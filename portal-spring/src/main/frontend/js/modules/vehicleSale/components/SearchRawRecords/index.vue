<template>
    <div id="search-raw-records">
        <b-button
            :size="buttonSize"
            variant="secondary"
            class="w-100"
            @click="fetchByVin()"
        >
            Find Match
        </b-button>
        <b-modal
            ref="sales-record-modal"
            size="lg"
            ok-only
            :title="title"
            header-bg-variant="info"
        >
            <div v-if="noRecordsFound">
                No raw sale records found for this vin: <strong>{{ this.vin }}</strong>
            </div>
            <b-tabs v-else>
                <b-tab v-for="(saleRecord, index) in saleRecords" :key="saleRecord.uuid"
                       :title="`Sale Record ${index + 1}`">
                    <input :id="'sale-filter-' + index" type="text" placeholder="Search sale record"
                           class="m-2 p-2"
                    >
                    <table :ref="`saleRecordTable-${index}`" class="footable table table-stripped" data-page-size="20"
                           :data-filter="'#sale-filter-' + index"
                           aria-describedby="Sale Records"
                    >
                        <thead>
                        <tr>
                            <th scope="col">
                                Name
                            </th>
                            <th scope="col">
                                Value
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="value in recordToArray(saleRecord)">
                            <td><strong>{{ value[0] }}</strong></td>
                            <td><span>{{ value[1] }}</span></td>
                        </tr>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="2">
                                <ul class="pagination justify-content-center"/>
                            </td>
                        </tr>
                        </tfoot>
                    </table>
                </b-tab>
            </b-tabs>
        </b-modal>
    </div>
</template>

<script>
import _ from 'lodash';
import api from '@/api';

export default {
    name: 'SearchRawRecords',
    props: {
        vin: {
            type: String,
            required: true
        },
        dealerId: {
            type: String,
            required: true
        },
        buttonSize: {
            type: String,
            default: 'sm'
        }
    },
    data() {
        return {
            saleRecords: []
        };
    },
    computed: {
        noRecordsFound() {
            return _.isEmpty(this.saleRecords);
        },
        title() {
            if (this.noRecordsFound) {
                return '';
            }
            return `Sale records for vin ${this.vin}`;
        }
    },
    methods: {
        fetchByVin() {
            api.get(`/sale/dealer/${this.dealerId}/searchByVin/${this.vin}`)
                .then(response => {
                    this.saleRecords = response.data;
                    this.$refs['sales-record-modal'].show();

                    this.$nextTick(() => {
                        _.each(this.saleRecords, (saleRecord, index) => {
                            const table = this.$refs[`saleRecordTable-${index}`];
                            $(table).footable();
                        });
                    });
                })
                .catch(error => {
                    console.log(error);
                    this.$refs['sales-record-modal'].show();
                });
        },
        recordToArray(record) {
            return _.toPairs(record);
        }
    }
};
</script>
