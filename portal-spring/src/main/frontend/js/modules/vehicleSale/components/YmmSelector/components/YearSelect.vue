<template>
    <div class="col-md-2 col-lg-2 pt-5">
        <div class="md-form pt-5">
            <label for="year">Year</label>
            <select id="year" v-model="selectedYear" :name="name"
                    class="form-control" @change="yearChanged"
            >
                <option :value="null">
                    Choose Year
                </option>
                <option v-for="year in years" :value="year">
                    {{ year }}
                </option>
            </select>
        </div>
    </div>
</template>

<script>
import api from '../../../../../api';

export default {
    name: 'YearSelect',
    props: {
        name: {
            type: String,
            required: true,
            default: ''
        },
        year: {
            type: Number,
            required: false,
            default: null
        }
    },
    data() {
        return {
            selectedYear: null,
            years: []
        };
    },

    mounted() {
        this.getYears();
    },
    methods: {
        yearChanged() {
            this.$emit('change', this.selectedYear);
        },
        getYears() {
            api.get('/vehicle-data/years')
                .then(response => {
                    if (!_.isEmpty(response.data)) {
                        this.years = response.data;
                        this.selectedYear = this.year;
                    }
                })
                .catch(error => {
                    console.log(error);
                });
        }
    }
};
</script>

<style scoped>
.pt-5 {
    padding-top: 5px;
}
</style>
