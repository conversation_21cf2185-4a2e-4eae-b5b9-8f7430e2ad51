<template>
    <div
        v-if="isList"
        class="row wrapper border-bottom white-bg page-heading pb-3"
    >
        <div class="col-lg-10">
            <div
                class="d-flex flex-column flex-sm-row align-items-sm-center mt-3"
            >
                <h2>Vehicle Sales</h2>
            </div>
            <b-breadcrumb>
                <b-breadcrumb-item href="/sale/list">
                    Vehicle Sales
                </b-breadcrumb-item>
                <b-breadcrumb-item :active="true">
                    <strong>Search</strong>
                </b-breadcrumb-item>
            </b-breadcrumb>
        </div>
        <div class="header-button-wrapper">
            <b-button
                v-if="$acl.hasAuthority('edit:sale')"
                variant="primary"
                size="md"
                href="/sale?search"
            >
                <i aria-hidden="true" class="fa fa-plus fa-fw"/> Add Vehicle
                Sale
            </b-button>
        </div>
    </div>

    <div v-else class="row wrapper border-bottom white-bg page-heading pb-3">
        <div v-if="isDetails" class="col-lg-12">
            <div
                class="d-flex flex-column flex-sm-row align-items-sm-center mt-3"
            >
                <h2 class="mr-2 my-1">
                    <b-link :href="`/user/${userId}`">
                        {{ userName }}
                    </b-link>
                </h2>
            </div>
            <b-breadcrumb>
                <b-breadcrumb-item href="/sale/list">
                    Vehicle Sales
                </b-breadcrumb-item>
                <b-breadcrumb-item href="/sale?search">
                    Search
                </b-breadcrumb-item>
                <b-breadcrumb-item :active="true">
                    <strong>{{ userName }}</strong>
                </b-breadcrumb-item>
            </b-breadcrumb>
        </div>

        <div v-else class="col-lg-12">
            <h2 v-if="isCreate">
                Add Vehicle Sale
            </h2>
            <h2 v-else>
                Update Vehicle Sale
            </h2>
            <b-breadcrumb>
                <b-breadcrumb-item href="/sale/list">
                    Vehicle Sales
                </b-breadcrumb-item>
                <b-breadcrumb-item href="/sale?search">
                    Search
                </b-breadcrumb-item>
                <b-breadcrumb-item v-if="isCreate" :active="true">
                    <strong>Add Vehicle Sale</strong>
                </b-breadcrumb-item>
                <b-breadcrumb-item v-else :active="true">
                    <strong>Update Vehicle Sale</strong>
                </b-breadcrumb-item>
            </b-breadcrumb>
        </div>
    </div>
</template>

<script>
import {call, get} from "vuex-pathify";

export default {
    name: "VehicleSaleBanner",
    props: {
        isDetails: {
            type: Boolean,
            required: true
        },
        isList: {
            type: Boolean,
            required: true
        }
    },
    computed: {
        sale: get("vehicleSale/<EMAIL>"),
        selectedVehicleSaleId: get("vehicleSale/selectedVehicleSaleId"),
        userId: get("vehicleSale/<EMAIL>"),
        userName: get("vehicleSale/<EMAIL>"),
        isCreate() {
            return _.isNil(this.selectedVehicleSaleId);
        }
    },
    created() {
        if (_.isNil(this.sale) && !_.isNil(this.selectedVehicleSaleId)) {
            this.fetchVehicleSale();
        }
    },
    methods: {
        fetchVehicleSale: call("vehicleSale/fetchVehicleSale")
    }
};
</script>
