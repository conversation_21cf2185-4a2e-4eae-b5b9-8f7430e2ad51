<template>
    <div class="container-fluid">
        <search-page
            store="vehicleSaleStats"
            :search-index="searchIndex"
            :exportable="false"
        >
            <vehicle-sale-search-form slot="searchForm"/>
            <vehicle-sale-facets slot="facets"/>
        </search-page>

        <b-row class="results mt-3">
            <b-col cols="12">
                <b-row>
                    <b-col>
                        <b-row>
                            <b-col>
                                <i-box
                                    title="Average Price"
                                    :loading="loading"
                                    :collapsible="false"
                                >
                                    <h2>
                                        {{
                                            averagePrice
                                                | emptyDash
                                                | numeral("$0,0")
                                        }}
                                    </h2>
                                </i-box>
                            </b-col>
                            <b-col>
                                <i-box
                                    title="Average Interest Rate"
                                    :loading="loading"
                                    :collapsible="false"
                                >
                                    <h2>
                                        {{
                                            averageInterestRate
                                                | emptyDash
                                                | numeral("0.000%")
                                        }}
                                    </h2>
                                </i-box>
                            </b-col>
                        </b-row>
                        <b-row>
                            <b-col>
                                <i-box
                                    title="Average Front Gross"
                                    :loading="loading"
                                    :collapsible="false"
                                >
                                    <h2>
                                        {{
                                            averageFrontGross
                                                | emptyDash
                                                | numeral("$0,0.00")
                                        }}
                                    </h2>
                                </i-box>
                            </b-col>
                            <b-col>
                                <i-box
                                    title="Average Back Gross"
                                    :loading="loading"
                                    :collapsible="false"
                                >
                                    <h2>
                                        {{
                                            averageBackGross
                                                | emptyDash
                                                | numeral("$0,0.00")
                                        }}
                                    </h2>
                                </i-box>
                            </b-col>
                        </b-row>
                        <b-row>
                            <b-col>
                                <i-box
                                    title="Average Actual Trade Value"
                                    :loading="loading"
                                    :collapsible="false"
                                >
                                    <h2>
                                        {{
                                            averageTradeValue
                                                | emptyDash
                                                | numeral("$0,0.00")
                                        }}
                                    </h2>
                                </i-box>
                            </b-col>
                            <b-col>
                                <i-box
                                    title="# Sales with Trade In"
                                    :loading="loading"
                                    :collapsible="false"
                                >
                                    <h2>
                                        {{
                                            salesWithTradeIn
                                                | emptyDash
                                                | numeral("0,0")
                                        }}
                                    </h2>
                                </i-box>
                            </b-col>
                        </b-row>
                        <b-row>
                            <b-col>
                                <i-box
                                    title="Average Buy Rate"
                                    :loading="loading"
                                    :collapsible="false"
                                >
                                    <h2>
                                        {{
                                            averageBuyRate
                                                | emptyDash
                                                | numeral("0.000%")
                                        }}
                                    </h2>
                                </i-box>
                            </b-col>
                            <b-col>
                                <i-box
                                    title="Average Sell Rate"
                                    :loading="loading"
                                    :collapsible="false"
                                >
                                    <h2>
                                        {{
                                            averageSellRate
                                                | emptyDash
                                                | numeral("0.000%")
                                        }}
                                    </h2>
                                </i-box>
                            </b-col>
                        </b-row>
                        <b-row>
                            <b-col>
                                <i-box
                                    title="Warranty Enrollments"
                                    :loading="facetLoading('warrantyStatuses')"
                                    :collapsible="false"
                                >
                                    <div
                                        v-if="!facetLoading('warrantyStatuses')"
                                    >
                                        <p>
                                            <strong>Standard:</strong>
                                            {{
                                                enrolledStandardCount
                                                    | emptyDash
                                                    | numeral("0,0")
                                            }}
                                            ({{
                                                (enrolledStandardCount / total)
                                                    | numeral("0%")
                                            }})
                                        </p>
                                        <p>
                                            <strong>VSC:</strong>
                                            {{
                                                enrolledVscCount
                                                    | emptyDash
                                                    | numeral("0,0")
                                            }}
                                            ({{
                                                (enrolledVscCount / total)
                                                    | numeral("0%")
                                            }})
                                        </p>
                                    </div>
                                </i-box>
                            </b-col>
                            <b-col>
                                <i-box
                                    title="Average Trade Equity"
                                    :loading="loading"
                                    :collapsible="false"
                                >
                                    <h2>
                                        {{
                                            averageTradeEquity
                                                | emptyDash
                                                | numeral("$0,0")
                                        }}
                                    </h2>
                                </i-box>
                            </b-col>
                        </b-row>
                        <b-row>
                            <b-col>
                                <i-box
                                    title="Average Monthly Payment"
                                    :loading="loading"
                                    :collapsible="false"
                                >
                                    <h2>
                                        {{
                                            averageFinancePayment
                                                | emptyDash
                                                | numeral("$0,0")
                                        }}
                                    </h2>
                                </i-box>
                            </b-col>
                            <b-col>
                                <i-box
                                    title="Average Buying Time"
                                    :loading="loading"
                                    :collapsible="false"
                                >
                                    <h2>
                                        {{ averageBuyingTime | numeral("0,0") }}
                                        days
                                    </h2>
                                </i-box>
                            </b-col>
                        </b-row>
                        <b-row>
                            <b-col>
                                <i-box
                                    title="Average Buying Time by Source"
                                    :loading="loading"
                                    :collapsible="false"
                                >
                                    <bar-chart
                                        :columns="
                                            averageBuyingTimeBySourceColumn
                                        "
                                    />
                                </i-box>
                            </b-col>
                        </b-row>
                    </b-col>
                    <b-col>
                        <i-box
                            title="New vs Used"
                            :loading="facetLoading('stockTypes')"
                            :collapsible="false"
                        >
                            <div class="text-center">
                                <pie-chart
                                    v-if="!facetLoading('stockTypes')"
                                    :colors="stockTypes.colors"
                                    :columns="stockTypes.columns"
                                />
                            </div>
                        </i-box>
                        <i-box
                            title="Sale Type"
                            :loading="facetLoading('saleTypes')"
                            :collapsible="false"
                        >
                            <div class="text-center">
                                <pie-chart
                                    v-if="!facetLoading('saleTypes')"
                                    :colors="saleTypes.colors"
                                    :columns="saleTypes.columns"
                                />
                            </div>
                        </i-box>
                        <i-box
                            title="Finance Terms"
                            :loading="facetLoading('financingTerms')"
                            :collapsible="false"
                        >
                            <div class="text-center">
                                <pie-chart
                                    v-if="!facetLoading('financingTerms')"
                                    :colors="financeTerms.colors"
                                    :columns="financeTerms.columns"
                                />
                            </div>
                        </i-box>
                        <i-box
                            title="Makes"
                            :loading="facetLoading('makes')"
                            :collapsible="false"
                        >
                            <div class="text-center">
                                <pie-chart
                                    v-if="!facetLoading('makes')"
                                    :colors="makes.colors"
                                    :columns="makes.columns"
                                />
                            </div>
                        </i-box>
                    </b-col>
                </b-row>
            </b-col>
        </b-row>
    </div>
</template>

<script>
import _ from "lodash";
import IBox from "Components/IBox";
import {call, get} from "vuex-pathify";
import SearchPage from "Components/SearchPage";
import VehicleSaleSearchForm from "./VehicleSaleSearchForm";
import VehicleSaleFacets from "./VehicleSaleFacets";
import PieChart from "./PieChart";
import BarChart from "./BarChart";

export default {
    name: "VehicleSaleStats",
    data() {
        return {
            searchIndex: "vehicle-sale-stats"
        };
    },
    components: {
        BarChart,
        PieChart,
        VehicleSaleFacets,
        VehicleSaleSearchForm,
        SearchPage,
        IBox
    },

    computed: {
        loading: get("vehicleSaleStats/<EMAIL>"),
        averageFinanceRate: get(
            "vehicleSaleStats/<EMAIL>"
        ),
        averageFrontGross: get(
            "vehicleSaleStats/<EMAIL>"
        ),
        averageBackGross: get(
            "vehicleSaleStats/<EMAIL>"
        ),
        averageTradeValue: get(
            "vehicleSaleStats/<EMAIL>"
        ),
        salesWithTradeIn: get(
            "vehicleSaleStats/<EMAIL>"
        ),
        averageBuyRate: get(
            "vehicleSaleStats/<EMAIL>"
        ),
        averageSellRate: get(
            "vehicleSaleStats/<EMAIL>"
        ),
        averageSavings: get(
            "vehicleSaleStats/<EMAIL>"
        ),
        averageTradeEquity: get(
            "vehicleSaleStats/searchLoader@data.averageTrade1Equity"
        ),
        averagePrice: get("vehicleSaleStats/<EMAIL>"),
        averageFinancePayment: get(
            "vehicleSaleStats/<EMAIL>"
        ),
        averageBuyingTimeBySource: get(
            "vehicleSaleStats/<EMAIL>"
        ),
        averageBuyingTime: get(
            "vehicleSaleStats/<EMAIL>"
        ),
        stockTypesFacet: get("vehicleSaleStats/facets@stockTypes"),
        makesFacet: get("vehicleSaleStats/facets@makes"),
        financeTermsFacet: get("vehicleSaleStats/facets@financingTerms"),
        saleTypesFacet: get("vehicleSaleStats/facets@saleTypes"),
        warrantyStatusesFacet: get("vehicleSaleStats/facets@warrantyStatuses"),
        facets: get("vehicleSaleStats/facets"),
        total: get("vehicleSaleStats/total"),

        enrolledVscCount() {
            return this.getFacetCount(
                this.warrantyStatusesFacet,
                "ENROLLED_VSC"
            );
        },

        enrolledStandardCount() {
            return this.getFacetCount(
                this.warrantyStatusesFacet,
                "ENROLLED_STANDARD"
            );
        },

        averageInterestRate() {
            if (_.isNil(this.averageFinanceRate)) {
                return null;
            }

            return this.averageFinanceRate / 100;
        },

        averageBuyingTimeBySourceColumn() {
            return _.map(this.averageBuyingTimeBySource, (value, key) => {
                if (_.isNumber(value)) {
                    return [key, value.toFixed(2)];
                } else {
                    return [key, 0];
                }
            });
        },

        stockTypes() {
            const newUsedCount = this.getFacetCount(
                this.stockTypesFacet,
                "USED"
            );
            const newNewCount = this.getFacetCount(this.stockTypesFacet, "NEW");

            return {
                columns: [
                    ["New", newNewCount],
                    ["Used", newUsedCount]
                ],
                colors: {
                    New: "#1ab394",
                    Used: "#BABABA"
                }
            };
        },

        makes() {
            const columns = _.map(_.get(this.makesFacet, "data"), make => {
                return [make.name, make.count];
            });

            return {
                columns,
                colors: {}
            };
        },

        saleTypes() {
            const financeCount = this.getFacetCount(
                this.saleTypesFacet,
                "FINANCE"
            );
            const leaseCount = this.getFacetCount(this.saleTypesFacet, "LEASE");
            const cashCount = this.getFacetCount(this.saleTypesFacet, "CASH");
            const unknownCount = this.getFacetCount(
                this.saleTypesFacet,
                "UNKNOWN"
            );

            return {
                columns: [
                    ["Finance", financeCount],
                    ["Lease", leaseCount],
                    ["Cash", cashCount],
                    ["Unknown", unknownCount]
                ],
                colors: {
                    Finance: "#1ab394",
                    Lease: "#BABABA",
                    Cash: "#79d2c0",
                    Unknown: "#d3d3d3"
                }
            };
        },
        financeTerms() {
            const columns = _.map(
                _.get(this.financeTermsFacet, "data"),
                make => {
                    return [make.name, make.count];
                }
            );

            return {
                columns,
                colors: {}
            };
        }
    },

    methods: {
        getFacetCount(facets, type) {
            const newFacet = _.find(_.get(facets, "data"), ["id", type]);
            return _.get(newFacet, "count", 0);
        },
        facetLoading(facetName) {
            return _.get(this.facets, `${facetName}.loader.isLoading`, true);
        },
        doSearch: call("vehicleSaleStats/doSearch"),
        loadFacetInfo: call("vehicleSaleStats/loadFacetInfo")
    },

    mounted() {
        this.doSearch();
        this.loadFacetInfo("makes");
        this.loadFacetInfo("stockTypes");
        this.loadFacetInfo("financingTerms");
        this.loadFacetInfo("saleTypes");
        this.loadFacetInfo("warrantyStatuses");
    }
};
</script>

<style lang="scss" scoped>
.results {
    margin-top: 10px;
}
</style>
