<template>
    <div>
        <div v-if="showInTransit">
            In-Transit:
            <strong>
                {{ inTransitMetrics.pricedCount }} / {{ inTransitMetrics.total }}
            </strong>
        </div>
        <div v-if="newMetrics">
            New:
            <strong>
                {{ newMetrics.pricedCount }} / {{ newMetrics.total }}
            </strong>
        </div>
        <div v-if="usedMetrics">
            Used:
            <strong>
                {{ usedMetrics.pricedCount }} / {{ usedMetrics.total }}
            </strong>
        </div>
        <div v-if="newMetrics && usedMetrics">
            Total:
            <strong>
                {{ totalPriced }} /
                {{ total }}
            </strong>
        </div>
        <div v-if="invalidMetrics">
            <div>
                Invalid New:
                <strong>
                    {{ invalidMetrics.invalidNewCount }}
                </strong>
            </div>
            <div>
                Invalid Used:
                <strong>
                    {{ invalidMetrics.invalidUsedCount }}
                </strong>
            </div>
        </div>
        <div ref="chart"/>
    </div>
</template>

<script>
export default {
    props: {
        columns: {
            type: Array,
            required: true
        },
        colors: {
            type: Object,
            required: true
        },
        newMetrics: {
            type: Object,
            required: false,
            default: null
        },
        usedMetrics: {
            type: Object,
            required: false,
            default: null
        },
        inTransitMetrics: {
            type: Object,
            required: false,
            default: null
        },
        invalidMetrics: {
            type: Object,
            required: false,
            default: null
        }
    },

    watch: {
        columns() {
            this.updateChart();
        }
    },

    mounted() {
        this.updateChart();
    },

    methods: {
        updateChart() {
            c3.generate({
                bindto: this.$refs.chart,
                data: {
                    columns: this.columns,
                    colors: this.colors,
                    type: "pie"
                },
                pie: {
                    label: {
                        format: (value, ratio, id) => {
                            return id;
                        }
                    }
                },
                tooltip: {
                    format: {
                        value: (value, ratio, id) => {
                            const valueFormat = d3.format(",");
                            const ratioFormat = d3.format(".0%");
                            return `${ratioFormat(ratio)} | ${valueFormat(
                                value
                            )}`;
                        }
                    }
                }
            });
        }
    },
    computed: {
        showInTransit() {
            return this.inTransitMetrics && this.inTransitMetrics.total > 0
        },
        totalPriced() {
            return this.usedMetrics.pricedCount + this.newMetrics.pricedCount + this.inTransitMetrics.pricedCount
        },
        total() {
            return this.usedMetrics.total + this.newMetrics.total + this.inTransitMetrics.total
        }
    }
};
</script>
