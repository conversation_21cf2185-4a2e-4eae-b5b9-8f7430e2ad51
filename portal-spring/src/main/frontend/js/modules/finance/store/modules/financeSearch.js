import _ from 'lodash';
import {make} from 'vuex-pathify';
import loader from '@/api/loader';
import searchUtils from '@/api/searchUtils';

const uriRoot = '/finance';

const initialState = {
    ...searchUtils.state(),
    initialLoad: true,
    pageable: searchUtils.parsePageableFromUrl({
        sort: 'createdDate,desc',
        page: 1
    }),
    facets: null,
    filters: searchUtils.parseFiltersFromUrl(
        _.get(window, '_CS_SEARCH_CRITERIA', null)
    ),
    searchLoader: {
        loader: loader.defaultState(),
        data: []
    },
    displayFields: [
        'status',
        'user',
        'dealer.name',
        'appId',
        'certificateId',
        'loanResponses',
        'createdDate',
        'actions'
    ],
    pageMetadata: {
        size: 0,
        totalElements: 0,
        totalPages: 0,
        number: 0
    },
    /**
     * pills schema
     * {
                    "name": {
                        label: 'Name',
                        enabled: false,
                    },
                    "warrantyStatuses": {
                        label: 'Warranty',
                        enabled: true
                    }
                }
     *     label: label to use for display
     *     facet: the name of the search facet
     *     enabled: is the pill show be displayed
     * }
     */
    pills: {
        name: {
            enabled: false
        },
        stockType: {
            label: 'Stock Type',
            facet: 'stockTypes'
        },
        statuses: {
            label: 'Loan Request Statuses',
            facet: 'statuses'
        },
        createdDate: {
            label: 'Created Date',
            facet: 'createdDate'
        },
        vehicleMakes: {
            label: 'Vehicle Makes',
            facet: 'vehicleMakes'
        },
        vehicleModels: {
            label: 'Vehicle Models',
            facet: 'vehicleModels'
        },
        vehicleYears: {
            label: 'Vehicle Years',
            facet: 'vehicleYears'
        },
        states: {
            label: 'User States',
            facet: 'states'
        },
        userDmaCodes: {
            label: 'User DMAs',
            facet: 'userDmas'
        }
    }
};

const mutations = {
    ...make.mutations(initialState),
    ...searchUtils.mutations()
};

const actions = {
    ...make.mutations(initialState),
    ...searchUtils.actions(uriRoot, 'Finance Search')
};

const getters = {
    ...make.getters(initialState),
    ...searchUtils.getters()
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters
};
