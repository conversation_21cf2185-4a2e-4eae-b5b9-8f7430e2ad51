<template>
    <b-card>
        <b-row>
            <b-col>
                <h3 class="p-2">Finance</h3>
            </b-col>
            <b-col>
                <span class="float-right mt-3">* indicates required field</span>
            </b-col>
        </b-row>
        <div class="p-2">
            <validation-observer ref="observer" v-slot="{ invalid }">
                <b-form novalidate @submit.prevent="next()">
                    <b-row>
                        <b-col lg="6">
                            <b-select-validate
                                label="*Enabled Lender"
                                name="Enabled Lender"
                                v-model="form.enabledFinancier"
                                @input="setEnabledLenderName"
                                autocomplete="off"
                                :options="lenders"
                                value-field="id"
                                text-field="name"
                                rules="required"
                            />
                        </b-col>
                        <b-col lg="6">
                            <b-form-group label="*Enabled Dealer Lender">
                                <b-form-checkbox v-model="form.enableDealerFinanciers" rules="required" switch/>
                            </b-form-group>
                        </b-col>
                    </b-row>
                    <b-row>
                        <b-col lg="6">
                            <b-input-validate
                                v-model="form.horizonSource"
                                label="*Horizon Source"
                                name="Horizon Source"
                                autocomplete="off"
                                rules="required"
                            />
                        </b-col>
                        <b-col lg="6">
                            <b-form-group label="*Allow Rate markups">
                                <b-form-checkbox v-model="form.allowRateMarkups" switch/>
                            </b-form-group>
                        </b-col>
                    </b-row>
                    <b-row>
                        <b-col lg="6">
                            <b-form-group label="Enable Deal Types">
                                <b-form-select
                                label="Enable Deal Types"
                                name="Enable Deal Types"
                                v-model="form.enabledDealTypes"
                                autocomplete="off"
                                :options="enableDealTypesList"
                                value-field="value"
                                text-field="name"
                            />
                            </b-form-group>
                        </b-col>
                    </b-row>
                    <b-card>
                        <span class="text-info font-weight-bold">Down Payment Rules</span>
                        <hr/>
                        <div class="d-flex justify-content">
                            <b-input-validate
                                class="mr-2"
                                v-model="form.downPaymentRules.newFinancePercent"
                                label="*New Finance Percent"
                                name="New Finance Percent"
                                type="number"
                                :rules="{
                                            required: true,
                                            integer: true,
                                        }"
                            />
                            <b-input-validate
                                class="mr-2"
                                v-model="form.downPaymentRules.usedFinancePercent"
                                label="*Used Finance Percent"
                                name="Used Finance Percent"
                                type="number"
                                :rules="{
                                            required: true,
                                            integer: true,
                                        }"
                            />
                            <b-input-validate
                                v-model="form.downPaymentRules.newLeasePercent"
                                label="*New Lease Percent"
                                name="New Lease Percent"
                                type="number"
                                :rules="{
                                            required: true,
                                            integer: true,
                                        }"
                            />
                        </div>
                    </b-card>
                    <b-card class="mt-2">
                        <span class="text-info font-weight-bold">Loan Markup</span>
                        <hr/>
                        <div class="d-flex justify-content">
                            <b-input-validate
                                class="mr-2"
                                v-model="form.loanMarkupPctOverride"
                                label="*Loan Markup Percent Override"
                                name="Loan Markup Percent Override"
                                autocomplete="off"
                                type="number"
                                rules="required|integer"
                            />
                            <b-input-validate
                                v-model="form.leaseMarkupPctOverride"
                                label="*Lease Markup Percent Override"
                                name="Lease Markup Percent Override"
                                autocomplete="off"
                                type="number"
                                :rules="{
                                            required: true,
                                            integer: true,
                                        }"
                            />
                        </div>
                    </b-card>
                    <b-row class="float-right mt-2 mr-1">
                        <b-button
                            size="lg"
                            type="submit"
                            class="mr-2"
                            variant="primary"
                            :disabled="invalid"
                        >
                            Save & Next
                        </b-button>
                        <b-button
                           size="lg"
                            title="Cancel"
                            @click="cancel()"
                        >
                            Cancel
                        </b-button>
                    </b-row>
                </b-form>
            </validation-observer>
        </div>
    </b-card>
</template>
<script>
import BInputValidate from 'Components/FormValidation/BInputValidate';
import BSelectValidate from "Components/FormValidation/BSelectValidate";
import api from '@/api';
import {sync} from "vuex-pathify";

export default {
    components: {
        BInputValidate, BSelectValidate
    },
    props: {
        campaignDetails : {
            type: Object,
            required: false,
            default: null
        }
    },
    data() {
        return {
            lenders: null,
            enableDealTypesList: [
                {value: null, name: 'Select Enable Deal Types'},
                {value: 'FINANCE', name: 'Finance'},
                {value: 'LEASE', name: 'Lease'},
                {value: 'CASH', name: 'Cash'}
            ],
            form: {
                enabledFinancier: null,
                enabledLenderName: null,
                enableDealerFinanciers: null,
                horizonSource: null,
                allowRateMarkups: false,
                downPaymentRules: {
                    newFinancePercent: 0,
                    newLeasePercent: 0,
                    usedFinancePercent: 0
                },
                loanMarkupPctOverride: 0,
                leaseMarkupPctOverride: 0,
                enabledDealTypes: null
            }
        }
    },
    watch: {
        campaignDetails(newValue) {
            this.setData()
        }
    },
    computed: {
        tabIndex: sync("campaigns/tabIndex")
    },
    mounted() {
        api.get('/financiers')
            .then(response => {
                this.lenders = response?.data?.financierViews;
            });
    },

    created() {
        if(this.campaignDetails){
            this.setData();
        }
    },
    methods: {
        next() {
            this.$store.commit("campaigns/SET_FINANCE_FORM_DATA", this.form);
            this.$store.commit("campaigns/SET_TAB_INDEX", Number(this.tabIndex) + 1)
        },
        setData() {
            if(this.campaignDetails.financeConfig) {
                this.form = {
                    enabledFinancier: this.campaignDetails.financeConfig.enabledFinancier,
                    enabledLenderName: this.campaignDetails.financeConfig.enabledLenderName,
                    enableDealerFinanciers: this.campaignDetails.financeConfig.enableDealerFinanciers,
                    horizonSource: this.campaignDetails.financeConfig.horizonSource,
                    allowRateMarkups: this.campaignDetails.financeConfig.allowRateMarkups,
                    loanMarkupPctOverride: this.campaignDetails.financeConfig.loanMarkupPctOverride,
                    leaseMarkupPctOverride: this.campaignDetails.financeConfig.leaseMarkupPctOverride,
                    enabledDealTypes: this.campaignDetails.financeConfig.enabledDealTypes?this.campaignDetails.financeConfig.enabledDealTypes[0]:null,
                    downPaymentRules: this.campaignDetails.financeConfig.downPaymentRules
                }
            }
        },
        setEnabledLenderName(lender) {
            let enabledLenderName = this.lenders.filter(e => e.id == lender);
            if(enabledLenderName && enabledLenderName.length === 1) {
                this.form.enabledLenderName = enabledLenderName[0]?.name;
            }
        },
        cancel() {
            this.$bvModal.show('cancel-modal');
        }
    }
}
</script>
