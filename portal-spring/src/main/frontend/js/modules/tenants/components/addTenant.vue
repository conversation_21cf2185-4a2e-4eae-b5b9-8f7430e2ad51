<template>
    <div>
        <b-row class="justify-content-center">
            <b-col lg="10">
                <i-box :title="title" class="pt-2" :collapsible="false">
                    <b-form novalidate @submit.prevent="moveToPreview">
                        <b-input-validate
                            v-model="form.name"
                            rules="required"
                            name="name"
                            label="Tenant Name *"
                            autocomplete="off"
                            :readonly="readonly"
                        />
                        <b-form-group label="Marketing Config">
                            <b-form-input
                                v-model="form.marketingConfig"
                                name="marketingConfig"
                                label="Marketing Config"
                                autocomplete="off"
                                :readonly="readonly"
                            />
                        </b-form-group>
                        <b-form-group label="IDP Config">
                            <b-form-input
                                v-model="form.idpConfig"
                                name="idpConfig"
                                label="IDP Config"
                                autocomplete="off"
                                :readonly="readonly"
                            />
                        </b-form-group>
                        <b-row class="justify-content-center mt-3" v-if="previewScreen">
                            <b-col lg="12" class="text-center">
                                <div>
                                    <b-button
                                        size="lg"
                                        type="submit"
                                        variant="primary"
                                        class="mr-2"
                                        :disabled="loading"
                                        :loading="loading"
                                        @click="onSubmit"
                                        title="Creates Tenant ID and Adds Tenant to Tenant List"
                                    >
                                        Submit
                                    </b-button>
                                    <b-button
                                        size="lg"
                                        class="mr-2"
                                        variant="outline-primary"
                                        title="Go back to previous screen to make any changes"
                                        @click="backToEdit"
                                    >
                                        Go Back
                                    </b-button>
                                    <b-button
                                        size="lg"
                                        variant="danger"
                                        title="Cancel and return to the Tenant List page. Tenant will not be added"
                                        @click="cancel"
                                    >
                                        Cancel
                                    </b-button>
                                </div>
                            </b-col>
                        </b-row>
                        <b-row class="justify-content-center mt-3" v-else>
                            <b-col lg="12" class="text-center">
                                <div>
                                    <b-button
                                        size="lg"
                                        type="submit"
                                        variant="primary"
                                        class="mr-2"
                                        :disabled="disabled"
                                        @click="moveToPreview"
                                        title="Save data and preview before submitting"
                                    >
                                        Save
                                    </b-button>
                                    <b-button
                                        size="lg"
                                        variant="danger"
                                        title="Cancel and return to the Tenant List page. Tenant will not be added"
                                        @click="cancel"
                                    >
                                        Cancel
                                    </b-button>
                                </div>
                            </b-col>
                        </b-row>
                    </b-form>
                </i-box>
            </b-col>
        </b-row>
    </div>
</template>

<script>
import IBox from 'Components/IBox';
import {validationMixin} from 'vuelidate';
import BInputValidate from 'Components/FormValidation/BInputValidate';
import {dispatch, get} from "vuex-pathify";

export default {
    name: 'AddTenant',
    components: {BInputValidate, IBox,},
    mixins: [validationMixin],
    data() {
        return {
            addTitle: "Add Tenant",
            previewTitle: "Preview - Add Tenant",
            form: {
                name: null,
                marketingConfig: null,
                idpConfig: null,
            },
            readonly: false,
            previewScreen: false,
        }
    },
    watch: {
        addTenant() {
            if (this.addTenant.loader.isError) {
                this.$toastr.e(this.addTenant.loader.errorMessages);
            }
        }
    },
    computed: {
        addTenant: get("tenants/addTenant"),
        disabled() {
            let disabled = false;
            if (this.form.name == null || this.form.name.trim().length === 0) {
                disabled = true;
            }
            return disabled;
        },
        loading() {
            return this.addTenant.loader.isLoading;
        },
        title() {
            let title = this.addTitle;
            if (this.previewScreen) {
                title = this.previewTitle;
            }
            return title;
        },
        marketingConfigObj() {
            let config = null;
            if (this.form.marketingConfig != null) {
                config = {"apiKey": this.form.marketingConfig};
            }
            return config;
        },
        idpConfigObj() {
            let config = null;
            if (this.form.idpConfig != null) {
                config = {"provider": this.form.idpConfig};
            }
            return config;
        }
    },
    methods: {
        moveToPreview() {
            this.readonly = true;
            this.previewScreen = true;
        },
        cancel() {
            window.location = "/configs/tenants"
        },
        backToEdit() {
            this.readonly = false;
            this.previewScreen = false;
        },
        onSubmit() {
            let data = {
                name: this.form.name,
                marketingConfig: this.marketingConfigObj,
                identityProviderConfig: this.idpConfigObj

            }
            dispatch("tenants/addTenant", data);
        }
    }
}
</script>

<style lang="scss" scoped>
</style>
