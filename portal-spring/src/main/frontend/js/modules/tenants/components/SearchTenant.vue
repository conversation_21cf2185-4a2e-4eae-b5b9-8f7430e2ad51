<template>
    <search-page :store="store" :search-index="searchIndex" :exportable="false">
        <b-form
            slot="searchForm"
            inline
            class="lead-search-form fs-ignore-formabandon"
            @submit.prevent="doSubmit"
        >
            <b-form-group>
                <b-input
                    v-model="name"
                    name="name"
                    placeholder="Tenant Name"
                />
            </b-form-group>
            <b-button class="mr-2 ml-2" id="search-button" type="submit" variant="primary" size="sm">
                Search
            </b-button>
            <b-button
                id="clear-button"
                variant="info"
                size="sm"
                @click="clearFilters"
            >
                Reset
            </b-button>
        </b-form>
    </search-page>
</template>

<script>
import SearchPage from "../../../components/SearchPage";
import {dispatch, sync} from "vuex-pathify";
export default {
    name: "SearchTenant",
    components : {SearchPage},
    data() {
        return {
            store: "tenants",
            searchIndex: "tenants",
            name : ""
        }
    },
    computed: {
        isTenantSearch: sync("tenants/isTenantSearch"),
    },
    methods: {
        doSubmit() {
            if(this.name != "") {
                this.$store.commit('tenants/updateTenantSearch', true);
                this.$store.commit('tenants/updateSearchString', this.name);
                this.$store.commit('tenants/updatePageonSearch', 1);
                dispatch('tenants/searchTenants');
            }else {
                this.$store.commit('tenants/updateTenantSearch', false);
                this.$store.commit('tenants/updateSearchString', null);
                this.$store.commit('tenants/updatePageonSearch', 1);
                dispatch("tenants/loadTenantList");
            }
        },
        clearFilters() {
            this.name= "";
            this.$store.commit('tenants/updateTenantSearch', false);
            this.$store.commit('tenants/updateSearchString', null);
            dispatch("tenants/loadTenantList");
        },
    },

};
</script>
