<template>
    <quantum-search-page :store="store" :search-index="searchIndex">
        <quantum-lead-search-form slot="searchForm"/>
        <quantum-lead-facets slot="facets"/>
    </quantum-search-page>
</template>

<script>
import QuantumSearchPage from "../../../../components/SearchPage";
import QuantumLeadFacets from "./QuantumLeadFacets";
import QuantumLeadSearchForm from "./QuantumLeadSearchForm";

export default {
    name: "QuantumLeadSearch",
    components: {QuantumLeadSearchForm, QuantumLeadFacets, QuantumSearchPage},

    data() {
        return {
            store: "quantumLeadSearch",
            searchIndex: "quantum-leads"
        };
    }
};
</script>
