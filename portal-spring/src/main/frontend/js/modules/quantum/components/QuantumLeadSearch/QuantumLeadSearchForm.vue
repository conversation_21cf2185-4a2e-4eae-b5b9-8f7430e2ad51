<template>
    <b-form
        inline
        class="lead-search-form fs-ignore-formabandon"
        @submit.prevent="doSubmit"
    >
        <b-form-group>
            <b-input v-model="nameOrEmail" placeholder="Name or Email"/>
        </b-form-group>

        <b-form-group>
            <b-input v-model="vins" placeholder="VIN"/>
        </b-form-group>

        <b-form-group>
            <b-button-toolbar>
                <date-range-picker2
                    v-model="createdDate"
                    :masks="{ input: 'YYYY-MM-DD' }"
                    :max-date="new Date()"
                    placeholder="Last Modified Date"
                />
            </b-button-toolbar>
        </b-form-group>

        <b-form-group>
            <b-button-toolbar>
                <date-range-picker2
                    v-model="offerCreationDateTime"
                    :masks="{ input: 'YYYY-MM-DD' }"
                    :max-date="new Date()"
                    placeholder="Offer Created Date"
                />
            </b-button-toolbar>
        </b-form-group>
        <b-button id="search-button" type="submit" variant="primary" size="sm">
            Search
        </b-button>
        <b-button
            id="clear-button"
            variant="info"
            size="sm"
            @click="clearFilters"
        >
            Reset
        </b-button>
    </b-form>
</template>

<script>
import {call, sync} from "vuex-pathify";
import DateRangePicker2 from "Components/DateRangePicker2";

export default {
    name: "QuantumLeadSearchForm",
    components: {DateRangePicker2},
    computed: {
        nameOrEmail: sync("quantumLeadSearch/filters@nameOrEmail"),
        vins: sync("quantumLeadSearch/filters@vins"),
        createdDate: sync("quantumLeadSearch/filters@createdDate"),
        offerCreationDateTime: sync("quantumLeadSearch/filters@offerCreationDateTime")
    },

    methods: {
        doSubmit: call("quantumLeadSearch/doSearch"),
        clearFilters: call("quantumLeadSearch/clearFilters")
    }
};
</script>

<style lang="scss" scoped>
.form-group {
    margin-right: 5px;
}

.lead-search-form {
    padding-left: 0;
}

#clear-button,
#search-button {
    margin: 5px 0 5px 5px;
}

@media screen and (max-width: 765px) {
    .lead-search-form {
        padding: 0 !important;
    }
    #clear-button,
    #search-button {
        margin: 5px 0 0 0;
        width: 100%;
    }
}
</style>
