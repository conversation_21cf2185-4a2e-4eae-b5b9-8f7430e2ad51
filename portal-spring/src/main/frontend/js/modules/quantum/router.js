import Vue from 'vue';
import VueRouter from 'vue-router';
import {configureRouter, routerOptions} from '@/lib/routerHelper';
// import QuantumDetailsPage from 'Modules/quantum/components/QuantumDetailsPage';
import QuantumLeadSearchPage from 'Modules/quantum/views/QuantumLeadSearchPage';

Vue.use(VueRouter);

const PATH_PREFIX = '/quantum-leads';

const routes = [
    {
        path: PATH_PREFIX,
        name: 'quantum-lead-search',
        component: QuantumLeadSearchPage,
        meta: {
            title: 'CarSaver Portal - Quantum Lead Search'
        }
    },
    // {
    //     path: PATH_PREFIX + '/:leadId',
    //     name: 'quantum-lead-details',
    //     component: QuantumDetailsPage,
    //     meta: {
    //         title: 'CarSaver Portal - Quantum Lead Details'
    //     },
    //     props: (route) => {
    //         return {
    //             leadId: route.params.leadId
    //         };
    //     }
    // }
];

const router = new VueRouter({
    mode: 'history',
    routes,
    ...routerOptions
});

configureRouter(router, PATH_PREFIX);

export default router;
