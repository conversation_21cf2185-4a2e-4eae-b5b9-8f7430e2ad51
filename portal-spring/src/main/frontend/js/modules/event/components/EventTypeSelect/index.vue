<template>
    <div>
        <multiselect
            v-model="selectedEventTypes"
            :multiple="true"
            :options="options"
            placeholder="Pick event"
            @select="addFilter"
            @remove="removeFilter"
        />
    </div>
</template>

<style lang="scss" scoped>
* {
    font-family: 'Lato', 'Avenir', sans-serif;
}
</style>

<script>
import Multiselect from 'vue-multiselect';
import {sync} from 'vuex-pathify';

export default {
    name: 'EventTypeSelect',
    components: {Multiselect},

    data() {
        return {
            options: [
                'call',
                'email',
                'elastic',
                'spring-batch',
                'system',
                'warranty'
            ]
        };
    },

    computed: {
        selectedEventTypes: sync('event/filters@eventTypes')
    },

    methods: {
        addFilter(selectedOption) {
            this.selectedEventTypes = this.selectedEventTypes.concat(selectedOption);
        },
        removeFilter(selectedOption) {
            this.selectedEventTypes = _.pull(this.selectedEventTypes.filter, selectedOption);
        }
    }
};
</script>
