import Vuex from 'vuex';
import Vue from 'vue';
import loggedInUser from '@/store/loggedInUser';
import storeHelper from '@/store/storeHelper';
import dealer from './modules/dealer';

const debug = process.env.NODE_ENV !== 'production';

Vue.use(Vuex);

const plugins = storeHelper.plugins('dealerStratusSearch');

export default new Vuex.Store({
    plugins,
    modules: {
        dealer,
        loggedInUser
    },
    strict: debug
});
