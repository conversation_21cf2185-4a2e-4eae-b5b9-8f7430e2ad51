<template>
    <div>
        <dealer-banner/>

        <b-container fluid>
            <b-row class="p-2 mb-4">
                <b-col lg="3">
                    <div class="panel panel-info">
                        <div class="panel-heading">
                            <i aria-hidden="true" class="fa fa-info-circle"/> Program Support
                        </div>
                        <div class="panel-body">
                            <div class="feed-activity-list">
                                <div class="feed-element">
                                    <div>
                                        <strong>CarSaver Program</strong>
                                        <div>
                                            Email: <a href="mailto:<EMAIL>"><EMAIL></a>
                                            <br>
                                            Phone: <a href="tel:************">************</a>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="showNissanContactInfo" class="feed-element">
                                    <div>
                                        <strong>Nissan Program</strong>
                                        <div>
                                            Email: <a href="mailto:<EMAIL>"><EMAIL></a>
                                            <br>
                                            Phone: <a href="tel:************">************</a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                To update any of your <strong>Managers</strong> please contact <strong>Client
                                Services</strong>.
                            </div>
                        </div>
                    </div>

                    <dealer-google-maps/>

                    <hours-of-operation/>
                </b-col>

                <b-col lg="5">
                    <dealership-details :admin-view="false"/>
                </b-col>

                <b-col lg="4">
                    <pricing-metrics :dealer-id="dealerId"/>

                    <dealer-metrics :dealer-ids="[dealerId]"/>
                </b-col>
            </b-row>

            <dealer-fees :dealer-id="dealerId"/>

            <dealer-users :is-stratus="true"/>

            <dealer-leads :hide-fields="['actions', 'leadType']" :dealer-id="dealerId"/>
        </b-container>
    </div>
</template>

<script>
import {get} from 'vuex-pathify';
import DealerGoogleMaps from 'Modules/dealer/components/DealerGoogleMaps';
import HoursOfOperation from 'Modules/dealer/components/HoursOfOperation';
import DealershipDetails from 'Modules/dealer/components/DealershipDetails';
import PricingMetrics from 'Modules/dealer/components/PricingMetrics';
import DealerMetrics from 'Modules/dealer/components/DealerMetrics';
import DealerUsers from 'Modules/dealer/components/DealerUsers';
import DealerLeads from 'Modules/dealer/components/DealerLeads';
import DealerBanner from 'Modules/dealer/components/DealerBanner';
import DealerFees from 'Modules/dealer/components/DealerFees';

export default {
    name: 'DealerDetailsStratus',
    components: {
        DealerFees,
        DealerBanner,
        DealerLeads,
        DealerMetrics,
        PricingMetrics,
        DealerUsers,
        DealershipDetails,
        HoursOfOperation,
        DealerGoogleMaps
    },

    computed: {
        dealerId: get('dealer/selectedDealer@id'),
        nnaDealerId: get('dealer/selectedDealer@nnaDealerId'),
        showNissanContactInfo() {
            return !_.isNil(this.nnaDealerId);
        }
    }
};
</script>

<style lang="scss">
td {
    overflow: auto;
}
</style>
