<template>
    <b-container fluid>
        <h1 class="text-center welcome-home">
            Welcome!
        </h1>
        <b-row>
            <b-col>
                <div class="error-desc text-center">
                    <p>Welcome to the CarSaver Dealer Portal. From here you can manage your CarSaver dealership
                        features.</p>
                    <p style="font-style: italic">
                        Use the menu to the left to get started.
                    </p>
                </div>
            </b-col>
        </b-row>

        <b-row>
            <b-col class="text-center">
                <b-img src="/images/homePageImage.png" alt="home_page_image" class="animated fadeInDown"/>
            </b-col>
        </b-row>

        <div class="ibox">
            <div class="ibox-title">
                <h5>Support</h5>
            </div>
            <div class="ibox-content">
                <h4>If you have any questions please reach out to Client Services by emailing or calling your program
                    support below.</h4>
                <b-row>
                    <b-col>
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                CarSaver Program
                            </div>
                            <div class="panel-body">
                                <div>
                                    Email: <a href="mailto:<EMAIL>"><EMAIL></a>
                                    <br>
                                    Phone: <a href="tel:************">************</a>
                                </div>
                            </div>
                        </div>
                    </b-col>
                    <b-col v-if="showNissanContactInfo">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                Nissan Program
                            </div>
                            <div class="panel-body">
                                <div>
                                    Email: <a href="mailto:<EMAIL>"><EMAIL></a>
                                    <br>
                                    Phone: <a href="tel:************">************</a>
                                </div>
                            </div>
                        </div>
                    </b-col>
                </b-row>
            </div>
        </div>
        <b-row class="justify-content-around">
            <b-col>
                <div class="feed-element"/>
            </b-col>
            <b-col>
                <div class="feed-element">
                    <div/>
                </div>
            </b-col>
        </b-row>

        <b-table striped bordered hover
                 :fields="fields" :items="dealers"
        >
            <template v-slot:cell(name)="row">
                <b-link :href="`/stratus/dealer/${row.item.id}`">
                    {{ row.item.name }}
                </b-link>
            </template>
            <template v-slot:cell(phoneNumber)="row">
                {{ row.item.phoneNumber | phone }}
            </template>
            <template v-slot:cell(address)="row">
                {{ displayAddress(row.item.address) }}
            </template>
            <template v-slot:cell(totalInventoryPriced)="row">
                {{ row.item.totalInventoryPriced }}%
            </template>
            <template v-slot:cell(active)="row">
                <span v-if="row.item.active" class="label label-success">true</span>
                <span v-else class="label label-danger">false</span>
            </template>
        </b-table>

        <b-row>
            <b-col cols="6" class="mb-5">
                <dealer-metrics :dealer-ids="dealerIds"/>
            </b-col>
        </b-row>
    </b-container>
</template>

<script>
import _ from 'lodash';
import api from '@/api';
import DealerMetrics from 'Modules/dealer/components/DealerMetrics';

export default {
    name: 'DealerDashboardStratus',
    components: {DealerMetrics},
    data() {
        return {
            dealers: null,
            showNissanContactInfo: false,
            fields: [
                'name',
                'phoneNumber',
                'address',
                'totalInventoryPriced',
                'active'
            ]
        };
    },
    computed: {
        dealerIds() {
            return _.get(window, '_CS_STRATUS_DEALER_IDS', null);
        }
    },
    created() {
        this.fetchDealers();
    },
    methods: {
        displayAddress(address) {
            return `${address.street}, ${address.city}, ${address.state}, ${address.zipCode}`;
        },
        fetchDealers() {
            api.get('/stratus/dealers')
                .then(response => {
                    this.dealers = response.data.dealers;
                    this.showNissanContactInfo = response.data.atLeastOneDealerIsNissanDigitalRetail;
                })
                .catch(error => {
                    console.log(error);
                });
        }
    }
};
</script>

<style lang="scss" scoped>

</style>
