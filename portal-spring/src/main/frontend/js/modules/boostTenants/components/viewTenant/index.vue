<template>
    <div>
        <search-page :store="store" :search-index="searchIndex">
            <dealer-search-form slot="searchForm"/>
        </search-page>

        <div class="row">
            <div class="col-12">
                <dealer-list/>
            </div>
        </div>
    </div>
</template>

<script>
import DealerSearchForm from "./components/DealerSearchForm";
import DealerList from "./components/DealerList";
import SearchPage from "Components/SearchPage";
import {sync} from "vuex-pathify";

export default {
    name: "ViewTenant",
    components: {SearchPage, DealerSearchForm, DealerList},
    props: {
        tenantId: {
            type: String,
            required: false,
            default: null
        }
    },

    data() {
        return {
            store: "dealerSearch",
            searchIndex: "dealers"
        };
    }
};
</script>
