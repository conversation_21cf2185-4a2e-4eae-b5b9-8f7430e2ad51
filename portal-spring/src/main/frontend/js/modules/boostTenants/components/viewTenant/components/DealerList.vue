<template>
    <div>
        <div v-if="isLoading" class="loader-wrapper">
            <table-content-loader/>
        </div>

        <div v-else>
            <div class="ibox">
                <div class="ibox-title">
                    <h5>Search Results</h5>
                    <div class="ibox-tools">
                        <b-button v-b-modal.table-column-config size="sm">
                            <i aria-hidden="true" class="fas fa-cog"/>
                            <span class="list-action">Show / Hide Columns</span>
                        </b-button>
                        <table-column-selector
                            id="table-column-config"
                            v-model="displayFields"
                            :fields="fields"
                        />

                        <b-button v-b-modal.export-column-config size="sm">
                            <i aria-hidden="true" class="fas fa-file-export"/>
                            <span class="list-action">Export</span>
                        </b-button>
                        <export-column-selector
                            id="export-column-config"
                            v-model="exportFields"
                            :display-fields="displayFields"
                            :fields="fields"
                            @export="doExport"
                        />
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="result-count">
                        <pagination
                            :show-pager="false"
                            :page-metadata="pageMetadata"
                            :page="page"
                            @page-changed="pageChanged"
                        />
                    </div>

                    <b-table
                        responsive
                        striped
                        hover
                        bordered
                        :fields="fieldsForDisplay()"
                        :items="searchResults"
                        :no-local-sorting="true"
                        :sort-by.sync="sortBy"
                        :sort-desc.sync="sortDesc"
                        :no-sort-reset="true"
                        @sort-changed="sortChanged"
                    >
                        <template v-slot:cell(name)="row">
                            <a
                                :href="`/dealer/${row.item.id}`"
                                class="blue-text"
                            >
                                {{ row.item.name }}
                            </a>
                        </template>
                        <template v-slot:cell(certified)="row">
                            <span :class="typeCss(row.item)">
                                {{ typeDisplay(row.item) }}
                            </span>
                        </template>
                        <template v-slot:cell(successManager)="row">
                            <div
                                v-for="subscription in row.item.subscriptions"
                                :key="subscription.id"
                            >
                                <div
                                    class="d-flex justify-content-between text-nowrap p-1"
                                >
                                    <strong class="mr-2">
                                        {{ subscription.program.name }}
                                    </strong>
                                    <span class="align-self-end ml-1">
                                        <span
                                            v-if="
                                                hasManager(
                                                    subscription.successManager
                                                )
                                            "
                                        >
                                            {{
                                                subscription.successManager
                                                    .firstName
                                            }}
                                            {{
                                                subscription.successManager
                                                    .lastName
                                            }}
                                        </span>
                                        <span v-else><em>None</em></span>
                                    </span>
                                </div>
                            </div>
                        </template>
                        <template v-slot:cell(accountManager)="row">
                            <div
                                v-for="subscription in row.item.subscriptions"
                                :key="subscription.id"
                            >
                                <div
                                    class="d-flex justify-content-between text-nowrap p-1"
                                >
                                    <strong class="mr-2">
                                        {{ subscription.program.name }}
                                    </strong>
                                    <span class="align-self-end ml-1">
                                        <span
                                            v-if="
                                                hasManager(
                                                    subscription.accountManager
                                                )
                                            "
                                        >
                                            {{
                                                subscription.accountManager
                                                    .firstName
                                            }}
                                            {{
                                                subscription.accountManager
                                                    .lastName
                                            }}
                                        </span>
                                        <span v-else><em>None</em></span>
                                    </span>
                                </div>
                            </div>
                        </template>
                        <template v-slot:cell(subscriptions.contract.paymentStructure)="row">
                            <div v-for="subscription in row.item.subscriptions" :key="subscription.id">
                                <div
                                    v-if="inSelectedProgramFilters(subscription.program.id)"
                                    class="d-flex justify-content-between text-nowrap p-1"
                                >
                                    <span v-if="subscription.contract.id" class="mr-2">
                                        {{ subscription.program.name }}
                                    </span>
                                    <span class="font-bold">
                                        {{ subscription.contract.paymentStructure }}
                                    </span>
                                </div>
                            </div>
                        </template>
                        <template v-slot:cell(subscriptions.contract.billingInterval)="row">
                            <div v-for="subscription in row.item.subscriptions" :key="subscription.id">
                                <div
                                    v-if="inSelectedProgramFilters(subscription.program.id)"
                                    class="d-flex justify-content-between text-nowrap p-1"
                                >
                                    <span v-if="subscription.contract.id" class="mr-2">
                                        {{ subscription.program.name }}
                                    </span>
                                    <span v-if="subscription.contract.billingInterval" class="font-bold">
                                        {{ subscription.contract.billingInterval }}
                                    </span>
                                    <span v-else>N/A</span>
                                </div>
                            </div>
                        </template>
                        <template v-slot:cell(subscriptions.contract.payPerSaleAmount)="row">
                            <div v-for="subscription in row.item.subscriptions" :key="subscription.id">
                                <div
                                    v-if="inSelectedProgramFilters(subscription.program.id)"
                                    class="d-flex justify-content-between text-nowrap p-1"
                                >
                                    <span v-if="subscription.contract.id" class="mr-2">
                                        {{ subscription.program.name }}
                                    </span>
                                    <span class="font-bold">
                                        {{ subscription.contract.payPerSaleAmount }}
                                    </span>
                                </div>
                            </div>
                        </template>
                        <template v-slot:cell(subscriptions.contract.lifetimeWarrantyAmount)="row">
                            <div v-for="subscription in row.item.subscriptions" :key="subscription.id">
                                <div
                                    v-if="inSelectedProgramFilters(subscription.program.id)"
                                    class="d-flex justify-content-between text-nowrap p-1"
                                >
                                    <span v-if="subscription.contract.id" class="mr-2">
                                        {{ subscription.program.name }}
                                    </span>
                                    <span class="font-bold">
                                        {{ subscription.contract.lifetimeWarrantyAmount }}
                                    </span>
                                </div>
                            </div>
                        </template>
                        <template v-slot:cell(subscriptions.contract.expressWarrantyAmount)="row">
                            <div v-for="subscription in row.item.subscriptions" :key="subscription.id">
                                <div
                                    v-if="inSelectedProgramFilters(subscription.program.id)"
                                    class="d-flex justify-content-between text-nowrap p-1"
                                >
                                    <span v-if="subscription.contract.id" class="mr-2">
                                        {{ subscription.program.name }}
                                    </span>
                                    <span class="font-bold">
                                        {{ subscription.contract.expressWarrantyAmount }}
                                    </span>
                                </div>
                            </div>
                        </template>
                        <template v-slot:cell(subscriptions.contract.vscOptOutAmount)="row">
                            <div v-for="subscription in row.item.subscriptions" :key="subscription.id">
                                <div
                                    v-if="inSelectedProgramFilters(subscription.program.id)"
                                    class="d-flex justify-content-between text-nowrap p-1"
                                >
                                    <span v-if="subscription.contract.id" class="mr-2">
                                        {{ subscription.program.name }}
                                    </span>
                                    <span class="font-bold">
                                        {{ subscription.contract.vscOptOutAmount }}
                                    </span>
                                </div>
                            </div>
                        </template>
                        <template v-slot:cell(subscriptions.contract.walmartStoreId)="row">
                            <div v-for="subscription in row.item.subscriptions" :key="subscription.id">
                                <div
                                    v-if="inSelectedProgramFilters(subscription.program.id)"
                                    class="d-flex justify-content-between text-nowrap p-1"
                                >
                                    <span v-if="subscription.contract.id" class="mr-2">
                                        {{ subscription.program.name }}
                                    </span>
                                    <span v-if="subscription.contract.walmartStoreId" class="font-bold">
                                        {{ subscription.contract.walmartStoreId }}
                                    </span>
                                    <span v-else>N/A</span>
                                </div>
                            </div>
                        </template>
                        <template
                            v-slot:cell(preferences.uniqueProxyNumbers)="row"
                        >
                            <span>{{ proxyTypeDisplay(row.item) }}</span>
                        </template>
                        <template v-slot:cell(makes)="row">
                            <span v-if="row.item.makes">
                                {{ row.item.makes.join(", ") }}
                            </span>
                        </template>
                        <template v-slot:cell(programs)="row">
                            <div
                                v-for="subscription in row.item.subscriptions"
                                :key="subscription.id"
                            >
                                <div
                                    v-if="inSelectedProgramFilters(subscription.program.id)"
                                    class="d-flex justify-content-between text-nowrap p-1"
                                >
                                    <span>
                                        {{ subscription.program.name }}
                                    </span>
                                    <span class="align-self-end ml-1">
                                        <dealer-status-chip
                                            :status="subscription.status"
                                        />
                                    </span>
                                </div>
                            </div>
                        </template>
                        <template v-slot:cell(status)="data">
                            <dealer-status-chip :status="data.value"/>
                        </template>
                        <template v-slot:cell(phoneNumber)="row">
                            {{ row.item.phoneNumber | phone }}
                        </template>
                        <template v-slot:cell(address)="row">
                            <div class="text-nowrap">
                                {{ row.item.address.street }}
                            </div>
                            <div class="text-nowrap">
                                {{ row.item.address.city }}
                                <span
                                    v-if="
                                        row.item.address.city &&
                                            (row.item.address.stateCode ||
                                                row.item.address.zipCode)
                                    "
                                >,</span
                                >
                                {{ row.item.address.stateCode }}
                                {{ row.item.address.zipCode }}
                            </div>
                        </template>
                        <template v-slot:cell(tags)="data">
                            <span v-if="data.item.tags">
                                {{ data.item.tags.join(", ") }}
                            </span>
                        </template>
                        <template v-slot:cell(subscriptions.contract.startDate)="row">
                            <div v-for="subscription in row.item.subscriptions" :key="subscription.id">
                                <div
                                    v-if="inSelectedProgramFilters(subscription.program.id)"
                                    class="d-flex justify-content-between text-nowrap p-1"
                                >
                                    <span class="mr-2">
                                        {{ subscription.program.name }}
                                    </span>
                                    <span v-if="subscription.contract.startDate">
                                        <strong>{{ subscription.contract.startDate }}</strong>
                                    </span>
                                    <span v-else>
                                        <strong>N/A</strong>
                                    </span>
                                </div>
                            </div>
                        </template>
                        <template v-slot:cell(subscriptions.contract.endDate)="row">
                            <div v-for="subscription in row.item.subscriptions" :key="subscription.id">
                                <div
                                    v-if="inSelectedProgramFilters(subscription.program.id)"
                                    class="d-flex justify-content-between text-nowrap p-1"
                                >
                                    <span class="mr-2">
                                        {{ subscription.program.name }}
                                    </span>
                                    <span v-if="subscription.contract.endDate">
                                        <strong>{{ subscription.contract.endDate }}</strong>
                                    </span>
                                    <span v-else>
                                        <strong>N/A</strong>
                                    </span>
                                </div>
                            </div>
                        </template>
                        <template v-slot:cell(activeDate)="row">
                            <span>
                                {{
                                    row.item.activeDate
                                        | formatDateTime("MM/DD/YYYY")
                                }}
                            </span>
                        </template>
                        <template v-slot:cell(lastContactDate)="row">
                            <span>
                                {{
                                    row.item.lastContactDate
                                        | formatDateTime("MM/DD/YYYY")
                                }}
                            </span>
                        </template>
                        <template v-slot:cell(actions)="row">
                            <b-button-group>
                                <b-button
                                    variant="white"
                                    size="xs"
                                    :to="`/dealer/${row.item.id}`"
                                >
                                    View
                                </b-button>
                                <b-button
                                    v-if="$acl.hasAuthority('edit:dealer')"
                                    variant="white"
                                    size="xs"
                                    :href="
                                        `/on-boarding/${row.item.id}/details`
                                    "
                                >
                                    Edit
                                </b-button>
                            </b-button-group>
                        </template>

                        <template v-slot:cell(invalidVehicleMetrics)="row">
                            <div
                                v-if="row.item.invalidVehicleMetrics.invalidNewCount + row.item.invalidVehicleMetrics.invalidUsedCount > 0">
                                <div>
                                    {{ row.item.invalidVehicleMetrics.invalidNewCount }} |
                                    {{ row.item.invalidVehicleMetrics.invalidUsedCount }}
                                </div>
                            </div>
                        </template>

                    </b-table>

                    <div class="pagination-wrapper">
                        <pagination
                            :show-totals="false"
                            :page-metadata="pageMetadata"
                            :page="page"
                            @page-changed="pageChanged"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style>
.result-count {
    padding: 10px 0;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}

.loader-wrapper {
    margin-top: 20px;
    margin-bottom: 20px;
}
</style>

<script>
import _ from "lodash";
import {call, dispatch, get, sync} from "vuex-pathify";
import Pagination from "Components/Pagination";
import TableContentLoader from "Components/TableContentLoader";
import TableColumnSelector from "Components/TableColumnSelector";
import searchPageMixin from "@/mixins/searchPageMixin";
import ExportColumnSelector from "Components/ExportColumnSelector";
import DealerStatusChip from "Modules/dealer/components/DealerStatusChip";

export default {
    name: "DealerList",
    components: {
        DealerStatusChip,
        ExportColumnSelector,
        TableColumnSelector,
        TableContentLoader,
        Pagination
    },
    mixins: [searchPageMixin],
    props: {
        pushHistoryEnabled: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            fields: [
                {
                    key: "id",
                    sortable: false
                },
                {
                    key: "name",
                    label: "Dealer Name",
                    sortable: true
                },
                {
                    key: "status",
                    sortable: true
                },
                {
                    key: "programs",
                    sortable: false
                },
                {
                    key: "certified",
                    label: "Certification",
                    sortable: true
                },
                {
                    key: "subscriptions.contract.paymentStructure",
                    label: "Payment Structure",
                    sortable: true
                },
                {
                    key: "subscriptions.contract.billingInterval",
                    label: "Billing Interval"
                },
                {
                    key: "subscriptions.contract.payPerSaleAmount",
                    label: "Pay Per Sale Amount"
                },
                {
                    key: "subscriptions.contract.lifetimeWarrantyAmount",
                    label: "Lifetime Warranty Amount"
                },
                {
                    key: "subscriptions.contract.expressWarrantyAmount",
                    label: "Express Warranty Amount"
                },
                {
                    key: "subscriptions.contract.vscOptOutAmount",
                    label: "VSC Opt-Out Amount"
                },
                {
                    key: "subscriptions.contract.walmartStoreId",
                    label: "Walmart Store Id"
                },
                {
                    key: "subscriptions.contract.startDate",
                    label: "Contract Start Date",
                    sortable: false
                },
                {
                    key: "subscriptions.contract.endDate",
                    label: "Contract End Date",
                    sortable: false
                },
                "activeDate",
                "dealerId",
                {
                    key: "nnaDealerId",
                    label: "Nissan Dealer Code"
                },
                {
                    key: "group.name",
                    label: "Dealer Group"
                },
                "tags",
                "successManager",
                "accountManager",
                "vastDealerId",
                "phoneNumber",
                "makes",
                {
                    key: "preferences.offeringOptionalVsc",
                    label: "Offering Vsc"
                },
                "address",
                {
                    key: "address.street",
                    label: "Street"
                },
                {
                    key: "address.city",
                    label: "City"
                },
                {
                    key: "address.stateCode",
                    label: "State"
                },
                {
                    key: "address.zipCode",
                    label: "Zip"
                },
                {
                    key: "address.dma.name",
                    label: "DMA Name"
                },
                {
                    key: "address.dma.rank",
                    label: "DMA Rank"
                },
                {
                    key: "address.dma.code",
                    label: "DMA Code"
                },
                "dms",
                "crm",
                "salesTxSource",
                "inventorySource",
                "leadTransport",
                {
                    key: "preferences.uniqueProxyNumbers",
                    label: "Phone Proxy"
                },
                {
                    key: "inventoryPricingMetrics.percentageNewPriced",
                    label: "% New Inventory Priced",
                    sortable: true
                },
                {
                    key: "inventoryPricingMetrics.percentageUsedPriced",
                    label: "% Used Inventory Priced",
                    sortable: true
                },
                {
                    key:
                        "inventoryPricingMetrics.totalPercentageNewAndUsedPriced",
                    label: "% Total Inventory Priced",
                    sortable: true
                },
                {
                    key: "lastContactDate",
                    label: "Last Contact Date",
                    sortable: true
                },
                {
                    key: "invalidVehicleMetrics",
                    label: "Invalid Vehicles (New | Used)",
                },
                {
                    key: "actions",
                    sortable: false,
                    exportable: false
                }
            ]
        };
    },

    computed: {
        searchResults: get("dealerSearch/searchLoader@data"),
        pageMetadata: get("dealerSearch/pageMetadata"),
        isLoading: get("dealerSearch/<EMAIL>"),
        page: sync("dealerSearch/pageable@page"),
        sort: sync("dealerSearch/pageable@sort"),
        displayFields: sync("dealerSearch/displayFields"),
        exportFields: sync("dealerSearch/exportFields"),
        currentProgramFilters: get("dealerSearch/filters@subscriptionProgramIds"),
        selectedTenantId: get("dealerSearch/filters@tenantId"),
    },

    watch: {
        page: () => {
            dispatch("dealerSearch/doPageLoad");
        },
        sort: () => {
            dispatch("dealerSearch/doSort");
        }
    },

    created() {
        dispatch("dealerSearch/pushHistoryEnabled", this.pushHistoryEnabled);
    },

    mounted() {
        dispatch("dealerSearch/doPageLoad");
    },

    methods: {
        doExport: call("dealerSearch/doExport"),
        hasManager(manager) {
            return (
                !_.isNil(_.get(manager, "id")) && _.get(manager, "id") !== ""
            );
        },
        typeCss(dealer) {
            const type = _.get(dealer, "certified");
            return type ? "label label-success" : "label";
        },
        typeDisplay(dealer) {
            const type = _.get(dealer, "certified");
            return type ? "Certified" : "Non-Certified";
        },
        proxyTypeDisplay(dealer) {
            const type = _.get(dealer, "preferences.uniqueProxyNumbers");
            return type ? "Unique" : "Shared";
        },
        inSelectedProgramFilters(programId) {
            //if no program filters selected
            if (!this.currentProgramFilters || this.currentProgramFilters.length === 0) {
                return true;
            }
            return _.includes(this.currentProgramFilters, programId);
        }
    }
};
</script>
