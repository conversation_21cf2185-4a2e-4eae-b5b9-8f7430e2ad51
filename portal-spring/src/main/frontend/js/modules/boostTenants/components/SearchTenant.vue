<template>
    <search-page :store="store" :search-index="searchIndex" :exportable="false">
        <b-form
            slot="searchForm"
            inline
            class="lead-search-form fs-ignore-formabandon"
            @submit.prevent="doSubmit"
        >
            <b-form-group>
                <b-input
                    v-model="name"
                    name="name"
                    placeholder="Tenant Name"
                />
            </b-form-group>
            <b-button class="mr-2 ml-2" id="search-button" type="submit" variant="primary" size="sm">
                Search
            </b-button>
            <b-button
                id="clear-button"
                variant="info"
                size="sm"
                @click="clearFilters"
            >
                Reset
            </b-button>
        </b-form>
    </search-page>
</template>

<script>
import SearchPage from "../../../components/SearchPage";
import {dispatch, sync} from "vuex-pathify";
export default {
    name: "SearchTenant",
    components : {SearchPage},
    data() {
        return {
            store: "boostTenants",
            searchIndex: "tenants",
            name : ""
        }
    },
    computed: {
        isTenantSearch: sync("boostTenants/isTenantSearch"),
    },
    methods: {
        doSubmit() {
            if(this.name != "") {
                this.$store.commit('boostTenants/updateTenantSearch', true);
                this.$store.commit('boostTenants/updateSearchString', this.name);
                this.$store.commit('boostTenants/updatePageonSearch', 1);
                dispatch('boostTenants/searchTenants');
            }else {
                this.$store.commit('boostTenants/updateTenantSearch', false);
                this.$store.commit('boostTenants/updateSearchString', null);
                this.$store.commit('boostTenants/updatePageonSearch', 1);
                dispatch("boostTenants/loadTenantList");
            }
        },
        clearFilters() {
            this.name= "";
            this.$store.commit('boostTenants/updateTenantSearch', false);
            this.$store.commit('boostTenants/updateSearchString', null);
            dispatch("boostTenants/loadTenantList");
        },
    },

};
</script>
