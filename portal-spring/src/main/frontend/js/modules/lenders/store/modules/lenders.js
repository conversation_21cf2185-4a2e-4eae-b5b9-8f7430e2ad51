import {make} from 'vuex-pathify';
import searchUtils from '@/api/searchUtils';
import api from '../../../../api';
import loader from '@/api/loader';

const uriRoot = '/lenders';

const state = {
    ...searchUtils.state(),
    initialLoad: true,
    pageable: searchUtils.parsePageableFromUrl({
        sort: 'name,asc',
        page: 1,
        size: 20
    }),
    pageMetadata: {
        size: 0,
        totalElements: 0,
        totalPages: 0,
        number: 0
    },
    lenders: [],
    addLender: {
        loader: loader.defaultState(),
        data: null,
    },
    lender: {
        loader: loader.defaultState(),
        data: {}
    },
    searchString: null,
    isLenderSearch: false,
    isProgramSearch: false,
    displayFields: [
        "id",
        "name",
        "payoffPhoneNumber",
        "enabled",
        "routeOneId",
        "horizonId",
        "config",
        "actions",
    ],
    filters: {
        name: null
    },
};

const mutations = {
    ...make.mutations(state),
    updateLenderSearch(state, isLenderSearch) {
        state.isLenderSearch = isLenderSearch
    },
    updateSearchString(state, searchString) {
        state.searchString = searchString
        state.filters.name = searchString
    },
    updatePageonSearch(state, page) {
        state.pageable.page = 1
    }
};

const actions = {
    ...searchUtils.actions(uriRoot, "Lenders"),
    loadLenderList({commit, state}) {
        api.get(`/financiers?page=${state.pageable.page}&sort=${state.pageable.sort}`)
            .then(response => {

                commit('SET_LENDERS', {
                    data: response.data.financierViews,
                    loader: loader.successful()
                });
                commit('SET_PAGE_METADATA', response.data.page);
            })
            .catch(error => {
                console.log(error);
            });
    },

    addLender({commit}, data) {
        commit("SET_ADD_LENDER", {...state.addLender, loader: loader.started()});
        api.post(`/financiers`, data)
            .then(response => {
                commit("SET_ADD_LENDER", {...state.addLender, loader: loader.successful()});
                window.location = "/configs/lenders"
            })
            .catch(error => {
                commit("SET_ADD_LENDER",
                    {
                        ...state.addLender,
                        loader: loader.error('Some error occurred. Please try again later.')
                    });
            });
    },

    lender({commit},id) {
        commit("SET_LENDER", {...state.lender, loader: loader.started()});
        api.get(`/financiers/${id}`)
            .then(response => {
                commit("SET_LENDER", {...state.lender, loader: loader.successful(), data: response.data});
            })
            .catch(error => {
                commit("SET_LENDER",
                    {
                        ...state.lender,
                        loader: loader.error(error.message),
                    });
            });
    },

    editLender({commit},data) {
        commit("SET_LENDER", {...state.lender, loader: loader.started()});
        api.put(`/financiers/${data.id}`, data)
            .then(response => {
                commit("SET_LENDER", {...state.lender, loader: loader.successful()});
                window.location = "/configs/lenders"
            })
            .catch(error => {
                commit("SET_LENDER",
                    {
                        ...state.lender,
                        loader: loader.error(error.message),
                    });
            });
    },
    searchLenders({commit, state}) {
        let name = state.searchString ? state.searchString : "";
        commit('SET_LENDERS', {...state.lenders, loader: loader.started()});
        api.get(`/financiers/search?name=${name}&page=${state.pageable.page}&sort=${state.pageable.sort}`)
            .then(response => {
                commit('SET_LENDERS',
                    {
                        data: response.data.financierViews,
                        loader: loader.successful()
                    });
                commit('SET_PAGE_METADATA', response.data.page);
            })
            .catch(error => {
                console.log(error);
                commit('SET_LENDERS',
                    {
                        ...state.lenders,
                        loader: loader.error(error.response.data.message)
                    });
            });
    },
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
