import Vue from "vue";
import "es6-promise/auto";
import CarSaverPlugin from "@/lib/CarSaverPlugin";
import UniqueId from "vue-unique-id";
import {extend, ValidationObserver, ValidationProvider} from "vee-validate";
import {required, regex, max_value, min_value} from "vee-validate/dist/rules";
import Vue2Filters from "vue2-filters";
import LenderList from './components/LenderList';
import addLender from './components/addLender';
import editLender from './components/editLender';
import SearchLender from "./components/SearchLender";

import store from "./store";

import "../../directives";
import "../../filters";

Vue.use(CarSaverPlugin);
Vue.use(require("vue-moment"));
Vue.use(Vue2Filters);
Vue.use(UniqueId);

Vue.component("ValidationObserver", ValidationObserver);
Vue.component("ValidationProvider", ValidationProvider);
extend("required", required);
extend("regex", regex);
extend("max_value", max_value);
extend("min_value", min_value);



new Vue({
    el: "#wrapper",
    store,
    components: {
        LenderList,
        addLender,
        editLender,
        SearchLender
    }
});
