<template>
    <div class="ibox">
        <div class="ibox-title">
            <h5>Lender List</h5>
             <div class="ibox-tools">
                        <b-button v-b-modal.table-column-config size="sm">
                            <i aria-hidden="true" class="fas fa-cog"/>
                            <span class="list-action">Show / Hide Columns</span>
                        </b-button>
                        <table-column-selector
                            id="table-column-config"
                            v-model="displayFields"
                            :fields="fields"
                        />
                        <b-button v-b-modal.export-column-config size="sm">
                            <i
                                aria-hidden="true"
                                class="fas fa-file-export"
                            />
                            <span class="list-action">Export</span>
                        </b-button>
                        <export-column-selector
                            id="export-column-config"
                            v-model="exportFields"
                            :display-fields="displayFields"
                            :fields="fields"
                            @export="doExport"
                        />
            </div>
        </div>
        <div class="ibox-content">
            <div class="result-count">
                <pagination
                    :show-pager="false"
                    :page-metadata="pageMetadata"
                    :page="page"
                    @page-changed="pageChanged"
                />
            </div>

            <b-table
                responsive
                striped
                hover
                bordered
                 :fields="fieldsForDisplay()"
                :items="lenders"
            >
                <template v-slot:cell(config)="row">
                    <pre v-if="row.item.config" class="max-height-90">{{ row.item.config }}</pre>
                </template>
                <template v-slot:cell(actions)="row">
                    <b-button-group>
                        <b-button :to="`/configs/lenders/${row.item.id}?form`"
                                  variant="white"
                                  size="xs"
                        >
                            Edit
                        </b-button>
                    </b-button-group>
                </template>
            </b-table>
            <div class="pagination-wrapper">
                <pagination
                    :show-totals="false"
                    :page-metadata="pageMetadata"
                    :page="page"
                    @page-changed="pageChanged"
                />
            </div>
        </div>
    </div>
</template>

<script>
import {dispatch, get, sync, call} from "vuex-pathify";
import Pagination from 'Components/Pagination/index';
import ExportColumnSelector from 'Components/ExportColumnSelector';
import TableColumnSelector from 'Components/TableColumnSelector/index';
import tableUtils from '@/api/tableUtils';

export default {
    name: 'LenderList',
    components: {Pagination,TableColumnSelector, ExportColumnSelector},
    computed: {
        lenders: get("lenders/lenders@data"),
        pageMetadata: get("lenders/pageMetadata"),
        page: sync("lenders/pageable@page"),
        buildEditUrl() {
            return (id) => `/configs/lenders/${id}?form`
        },
        isLenderSearch: sync("lenders/isLenderSearch"),
        searchString: sync("lenders/searchString"),
        displayFields: sync("lenders/displayFields"), 
        exportFields: sync("lenders/exportFields"),  
    },
    mounted() {
        dispatch("lenders/loadLenderList")
    },
    watch: {
        page() {
            if (this.isLenderSearch && this.searchString) {
                dispatch('lenders/searchLenders');
            } else {
                dispatch("lenders/loadLenderList");
            }
        }
    },
    data() {
        return {
            title: 'Lenders',
            fields: [
                {
                    key: 'id',
                    label: 'ID'
                },
                {
                    key: 'name',
                    label: 'Lender Name'
                },
                {
                    key: 'payoffPhoneNumber',
                    label: 'Payoff Phone Number'
                },
                {
                    key: 'enabled',
                    label: 'Enable?'
                },
                {
                    key: 'routeOneId',
                    label: 'RouteOne ID'
                },
                {
                    key: 'horizonId',
                    label: 'Horizon ID'
                },
                {
                    key: 'config',
                    label: 'Configurations'
                },
                {
                    key: 'actions',
                    label: 'Action',               
                    hideable: false,
                    exportable: false
                }
            ],
        };
    },
    methods: {
        doExport: call("lenders/doExport"),
        pageChanged(page) {
            this.page = page;
        }, 
        fieldsForDisplay() {
            return tableUtils.intersectFieldsForDisplay(this.fields, this.displayFields);
        }
    }
}
</script>

<style lang="scss" scoped>
.result-count {
    padding: 10px 0;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}

.max-height-90 {
    max-height: 90px;
}
</style>
