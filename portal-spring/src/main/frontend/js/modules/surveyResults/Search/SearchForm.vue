<template>
    <b-form inline class="search-form fs-ignore-formabandon" @submit.prevent="doSubmit">
        <b-form-group>
            <b-input v-model="name" placeholder="Customer Name"/>
        </b-form-group>
        <b-form-group>
            <date-range-picker2 v-model="createdDate" :masks="{ input: 'YYYY-MM-DD'}" :max-date="new Date()"
                                placeholder="Created Date"
            />
        </b-form-group>
        <b-button id="search-button" type="submit" variant="primary"
                  size="sm"
        >
            Search
        </b-button>
        <b-button id="clear-button" variant="info" size="sm"
                  href="/survey-results"
        >
            Reset
        </b-button>
    </b-form>
</template>

<script>
import {dispatch, sync} from 'vuex-pathify';
import DateRangePicker2 from 'Components/DateRangePicker2';

export default {
    components: {DateRangePicker2},
    computed: {
        name: sync('surveyResults/filters@name'),
        createdDate: sync('surveyResults/filters@createdDate')
    },

    methods: {
        doSubmit() {
            dispatch('surveyResults/doSearch');
        }
    }
};
</script>

<style>
.search-form {
    padding-left: 0;
}

#clear-button, #search-button {
    margin: 5px 0 5px 5px;
}

@media screen and (max-width: 765px) {
    .search-form {
        padding: 0 !important;
    }

    #clear-button, #search-button {
        margin: 5px 0 0 0;
        width: 100%;
    }
}
</style>
