import Vue from 'vue';
import 'es6-promise/auto';
import CarSaverPlugin from '@/lib/CarSaverPlugin';
import DmaPerformance from './components/DmaPerformance';
import DealerPerformance from './components/DealerPerformance';
import ImportMetrics from './components/SaleImportMetrics';
import store from './store';

import '../../directives';
import '../../filters';

Vue.use(require('vue-moment'));
Vue.use(CarSaverPlugin);

new Vue({
    el: '#wrapper',
    store,

    components: {
        DmaPerformance,
        DealerPerformance,
        ImportMetrics
    }
});
