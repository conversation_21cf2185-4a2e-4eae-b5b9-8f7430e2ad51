<template>
    <div class="container-fluid">
        <search-page :store="store" :search-index="searchIndex">
            <import-metrics-search-form slot="searchForm"/>
        </search-page>
        <div class="row">
            <div class="col-12">
                <import-metrics-table/>
            </div>
        </div>
    </div>
</template>

<script>
import ImportMetricsSearchForm from './ImportMetricsSearchForm';
import SearchPage from "Components/SearchPage";
import ImportMetricsTable from "./ImportMetricsTable";

export default {
    name: "ImportMetrics",
    components: {
        ImportMetricsTable,
        SearchPage,
        ImportMetricsSearchForm,
    },
    data() {
        return {
            store: "importMetrics",
            searchIndex: "dealer-import-metrics"
        };
    }
}
</script>
