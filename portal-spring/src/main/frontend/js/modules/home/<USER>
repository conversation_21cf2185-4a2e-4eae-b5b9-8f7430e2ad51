import Vue from 'vue';
import 'es6-promise/auto';
import numeral from 'numeral';
import CarSaverPlugin from '@/lib/CarSaverPlugin';

import DealerInventoryStats from './components/DealerInventoryStats';
import GrowthStats from './components/GrowthStats';
import Widget from './components/Widget';
import store from './store';

import '../../directives';
import '../../filters';

Vue.use(CarSaverPlugin);

Vue.filter('numeral', function (val, format) {
    if (!_.isNumber(val)) {
        return val;
    }

    return numeral(val).format(format);
});

new Vue({
    el: '#wrapper',
    store,
    components: {
        DealerInventoryStats,
        GrowthStats,
        Widget
    }
});
