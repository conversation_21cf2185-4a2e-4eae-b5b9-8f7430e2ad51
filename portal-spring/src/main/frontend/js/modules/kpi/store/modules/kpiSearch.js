import _ from "lodash";
import {make} from "vuex-pathify";
import loader from "@/api/loader";
import searchUtils from "@/api/searchUtils";

const uriRoot = "/kpi";
const initialState = {
    ...searchUtils.state(),
    initialLoad: true,
    pageable: searchUtils.parsePageableFromUrl({
        sort: "createdDate,desc",
        page: 1
    }),
    facets: null,
    filters: searchUtils.parseFiltersFromUrl(
        _.get(window, "_CS_SEARCH_CRITERIA", null)
    ),
    searchLoader: {
        loader: loader.defaultState(),
        data: []
    },
    pageMetadata: {
        size: 0,
        totalElements: 0,
        totalPages: 0,
        number: 0
    },
    /**
     * pills schema
     * {
                    "name": {
                        label: 'Name',
                        enabled: false,
                    },
                    "warrantyStatuses": {
                        label: 'Warranty',
                        enabled: true
                    }
                }
     *     label: label to use for display
     *     facet: the name of the search facet
     *     enabled: is the pill show be displayed
     * }
     */
    pills: {
        createdDate: {
            enabled: false
        }
    },
    leadDistanceData: null,
    vehicleSaleDistanceData: null
};

const mutations = {
    ...make.mutations(initialState),
    ...searchUtils.mutations()
};

const actions = {
    ...make.actions(initialState),
    ...searchUtils.actions(uriRoot, "KPI Search"),

    updateLeadDistanceData({commit, state}, payload) {
        commit("SET_LEAD_DISTANCE_DATA", payload);
    },

    updateVehicleSaleDistanceData({commit, state}, payload) {
        commit("SET_VEHICLE_SALE_DISTANCE_DATA", payload);
    }
};

const getters = {
    ...make.getters(initialState),
    ...searchUtils.getters()
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters
};
