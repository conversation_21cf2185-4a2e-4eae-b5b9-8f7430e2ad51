<template>
    <b-form inline class="kpi-search-form" @submit.prevent="doSubmit">
        <b-form-group>
            <date-range-picker2 v-model="createdDate" :masks="{ input: 'YYYY-MM-DD'}" :max-date="new Date()"
                                placeholder="Created Date"
            />
        </b-form-group>
        <b-button id="search-button" type="submit" variant="primary"
                  size="sm"
        >
            Search
        </b-button>
        <b-button id="clear-button" variant="info" size="sm"
                  href="/kpi"
        >
            Reset
        </b-button>
    </b-form>
</template>

<script>
import {dispatch, sync} from 'vuex-pathify';
import DateRangePicker2 from 'Components/DateRangePicker2';

export default {
    name: 'KpiSearchForm',
    components: {DateRangePicker2},
    computed: {
        createdDate: sync('kpiSearch/filters@createdDate')
    },

    methods: {
        doSubmit() {
            dispatch('kpiSearch/doSearch');
        }
    }
};
</script>

<style>
.kpi-search-form {
    padding-left: 0;
}

#clear-button, #search-button {
    margin: 5px 0 5px 5px;
}

@media screen and (max-width: 765px) {
    .kpi-search-form {
        padding: 0 !important;
    }

    #clear-button, #search-button {
        margin: 5px 0 0 0;
        width: 100%;
    }
}
</style>
