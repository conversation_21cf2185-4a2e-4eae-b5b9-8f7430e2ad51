<template>
    <i-box
        title="Vehicle Sale Close Rates by Distance"
        description="The ratio of sales to leads, broken down into buckets by distance from user to dealer"
    >
        <div>
            <b-table bordered :items="items"></b-table>
        </div>
    </i-box>
</template>

<script>
import IBox from "Components/IBox";
import _ from "lodash";
import {get} from "vuex-pathify";

export default {
    name: "CloseRateByDistanceWidget",
    components: {IBox},
    computed: {
        leadDistanceData: get("kpiSearch/leadDistanceData"),
        vehicleSaleDistanceData: get("kpiSearch/vehicleSaleDistanceData"),
        items() {
            return [
                this.closeRateZeroToThirty,
                this.closeRateThirtyToSixty,
                this.closeRateSixtyPlus
            ];
        },
        closeRateZeroToThirty() {
            const leads = this.getLeadsByDistance("values.30");
            const sales = this.getSalesByDistance("zeroToThirty");
            const percent = (sales / leads) * 100;
            const value = _.round(percent, 1) + "%";

            return {
                distance: "0 - 30 mi",
                percentage: value
            };
        },
        closeRateThirtyToSixty() {
            const leads = this.getLeadsByDistance("values.60");
            const sales = this.getSalesByDistance("thirtyToSixty");
            const percent = (sales / leads) * 100;
            const value = _.round(percent, 1) + "%";

            return {
                distance: "30 - 60 mi",
                percentage: value
            };
        },
        closeRateSixtyPlus() {
            const leads = this.getLeadsByDistance("values.-1");
            const sales = this.getSalesByDistance("sixtyPlus");
            const percent = (sales / leads) * 100;
            const value = _.round(percent, 1) + "%";

            return {
                distance: "60+ mi",
                percentage: value
            };
        }
    },
    methods: {
        safeNumber(num) {
            return _.isNumber(num) ? num : 0;
        },
        getLeadsByDistance(distance) {
            const leads = _.get(this.leadDistanceData, distance, 0);

            return this.safeNumber(leads);
        },
        getSalesByDistance(distance) {
            const sales = _.get(this.vehicleSaleDistanceData, distance, 0);

            return this.safeNumber(sales);
        }
    }
};
</script>
