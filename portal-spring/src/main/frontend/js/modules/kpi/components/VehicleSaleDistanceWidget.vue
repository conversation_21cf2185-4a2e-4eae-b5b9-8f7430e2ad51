<template>
    <i-box :title="title" :description="description" :collapsible="false">
        <div ref="vehicleSaleDistanceChart"/>
        <div v-if="!hasValues">
            <no-data :title="title"/>
        </div>
    </i-box>
</template>

<script>
import _ from "lodash";
import IBox from "Components/IBox";
import NoData from "Modules/dealer/components/Dashboard/components/NoData";
import {call} from "vuex-pathify";

export default {
    components: {NoData, IBox},

    props: {
        vehicleSales: {
            type: Object,
            required: true
        }
    },

    data() {
        return {
            title: "Vehicle Sale Distance",
            description:
                "The distance between dealer and customer for total unique sales."
        };
    },

    computed: {
        hasValues() {
            return (
                !_.isNil(this.zeroToThirty) ||
                !_.isNil(this.thirtyToSixty) ||
                !_.isNil(this.sixtyPlus) ||
                !_.isNil(this.noDistance)
            );
        },

        zeroToThirty() {
            return _.get(this.vehicleSales, "zeroToThirtyMiles");
        },

        thirtyToSixty() {
            return _.get(this.vehicleSales, "thirtyToSixtyMiles");
        },

        sixtyPlus() {
            return _.get(this.vehicleSales, "sixtyPlusMiles");
        },

        noDistance() {
            return _.get(this.vehicleSales, "noDistance");
        }
    },

    watch: {
        vehicleSales() {
            this.loadChart();
            this.updateStore();
        }
    },

    methods: {
        updateVehicleSaleDistanceData: call(
            "kpiSearch/updateVehicleSaleDistanceData"
        ),
        loadChart() {
            c3.generate({
                bindto: this.$refs.vehicleSaleDistanceChart,
                data: {
                    columns: [
                        [`0 - 30 (${this.zeroToThirty})`, this.zeroToThirty],
                        [`30 - 60 (${this.thirtyToSixty})`, this.thirtyToSixty],
                        [`60+ (${this.sixtyPlus})`, this.sixtyPlus],
                        [`Unknown (${this.noDistance})`, this.noDistance]
                    ],
                    type: "pie",
                    color: function (color, data) {
                        if (_.startsWith(data, "0")) {
                            return "#60B044";
                        } else if (_.startsWith(data, "3")) {
                            return "#ffb237";
                        } else if (_.startsWith(data, "6")) {
                            return "#dc3545";
                        }

                        return "#000";
                    }
                }
            });
        },
        updateStore() {
            this.updateVehicleSaleDistanceData({
                zeroToThirty: this.zeroToThirty,
                thirtyToSixty: this.thirtyToSixty,
                sixtyPlus: this.sixtyPlus,
                noDistance: this.noDistance
            });
        }
    }
};
</script>
