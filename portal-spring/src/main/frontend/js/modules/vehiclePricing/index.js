import Vue from 'vue';
import 'es6-promise/auto';
import Toastr from 'vue-toastr';
import 'vue-toastr/src/vue-toastr.scss';
import CarSaverPlugin from '@/lib/CarSaverPlugin';
import store from './store';
import router from './router.js';

import '../../directives';
import '../../filters';
import Vue2Filters from 'vue2-filters';

Vue.use(require('vue-moment'));
Vue.use(Vue2Filters);
Vue.use(Toastr);
Vue.use(CarSaverPlugin);

new Vue({
    el: '#wrapper',
    router,
    store
});
