export default {
    methods: {
        /**
         * Enables Floating Table Header
         * ref: http://mkoryak.github.io/floatThead/examples/bootstrap3/
         * @param node is a single JQuery selector object ie. $('#myTable')
         * @param cb Optional callback function
         * @return true if successful
         */
        floatThead(node, cb) {
            if (node instanceof $ !== true) {
                console.trace('Error: expect parameter of instanceof jQuery instead received', typeof node, node);
                return;
            }

            node.floatThead({
                responsiveContainer: function ($table) {
                    return $table.closest('.table-responsive');
                }
            });

            if (cb) {
                cb();
            }

            return true;
        },

        /**
         * Bug fix for vin field not appropriately resizing when using floating header action on table
         */
        tableReflowFix() {
            var element = $('.pricing-table');

            if (element.length === 0) {
                console.trace('Warn: element not found', element);
                return;
            }

            element.on('DOMNodeInserted', 'tr', function () {
                $(this).trigger('reflow');
            });

            return true;
        },

        initFloatThead() {
            // Enables Floating Table Header
            this.floatThead($('[data-floatThead]'), this.tableReflowFix);
        }
    },

    mounted() {
        const node = document.querySelector('body');

        node.arrive('.pricing-table', () => {
            this.initFloatThead();
        });
    }
};
