<template>
    <div class="panel blank-panel">
        <pricing-nav/>

        <low-fixed-price-warning-modal/>

        <div class="panel-body pb-5">
            <search-form @search-completed="handleSearchCompleted"/>

            <div class="panel-title m-b-md animated">
                <h4>Vehicle Listings</h4>
            </div>
            <div class="panel panel-success animated">
                <div class="panel-heading">
                    <h4>{{ dealer.name }}</h4>
                </div>
                <b-form role="form" class="form-horizontal">
                    <b-container fluid>
                        <b-row class="pricing-row">
                            <stock-type-price-adjustment-form/>
                            <b-col lg="3">
                                <priced-widget
                                    :key="metricsRefreshKey"
                                    name="used-priced"
                                    title="% Used Priced"
                                    description="Percent of used vehicles with valid pricing"
                                    :dealer-id="dealer.id"
                                />
                                <b-row class="justify-content-center pb-4">
                                    <invalid-vehicles/>
                                </b-row>
                            </b-col>
                        </b-row>
                    </b-container>
                    <div class="d-flex justify-content-end pr-3">
                        <b-button @click="handlePrint"
                        ><i
                            class="fa fa-print pr-1"
                            aria-hidden="true"
                        />print
                        </b-button
                        >
                    </div>
                    <div v-if="loading" class="panel-body">
                        <table-content-loader/>
                    </div>
                    <div v-if="!loading" class="panel-body">
                        <div class="table-responsive">
                            <!-- user attempted to search for a specific vehicle but none was found -->
                            <div
                                v-if="
                                    vehicleSearchAttempted &&
                                        !vehicleSearchFoundVehicle
                                "
                                class="alert alert-warning alert-dismissable"
                            >
                                <button
                                    aria-hidden="true"
                                    data-dismiss="alert"
                                    class="close"
                                    type="button"
                                >
                                    ×
                                </button>
                                <span>{{ vehicleSearchFailureMessage }}</span>
                            </div>
                            <table
                                class="pricing-table table table-condensed table-striped"
                                data-floatThead
                                aria-describedby="Pricing Table"
                            >
                                <used-priced-car-row-header/>
                                <tbody
                                    style="white-space: nowrap; text-align: center"
                                >
                                <used-priced-car-row
                                    v-for="pricedCar in renderedPricedCars"
                                    :key="
                                            'pricedCar_' + pricedCar.vehicle.id
                                        "
                                    :priced-car="pricedCar"
                                />
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div id="save-footer">
                        <delete-pricing-rules-btn
                            v-if="
                                $acl.hasAuthority('edit:pricing-used') ||
                                    $acl.hasDealerPermission(
                                        dealer.id,
                                        'inventory:used-pricing:edit'
                                    )
                            "
                            class="mr-4"
                            :disabled="loading || submitting"
                            @pricingSuccessfullyDeleted="
                                handlePricingSuccessfullyDeleted()
                            "
                        />
                        <ladda-b-btn
                            v-if="
                                $acl.hasAuthority('edit:pricing-used') ||
                                    $acl.hasDealerPermission(
                                        dealer.id,
                                        'inventory:used-pricing:edit'
                                    )
                            "
                            :loading="submitting"
                            loading-text="Saving..."
                            variant="primary"
                            :disabled="loading"
                            @click.prevent="savePricingAdjustments()"
                        >
                            Save
                        </ladda-b-btn>
                    </div>
                </b-form>
            </div>
        </div>
    </div>
</template>
<script>
import api from "@/api";
import PricingNav from "Modules/vehiclePricing/components/PricingNav.vue";
import DeletePricingRulesBtn from "Modules/vehiclePricing/components/DeletePricingRulesBtn.vue";
import LowFixedPriceWarningModal from "Modules/vehiclePricing/components/LowFixedPriceWarningModal";
import TableContentLoader from "Components/TableContentLoader";
import PricedWidget from "Modules/vehiclePricing/components/PricedWidget";
import UsedPricedCarRowHeader from "Modules/vehiclePricing/components/UsedVehiclePricing/PricedCarRowHeader.vue";
import UsedPricedCarRow from "Modules/vehiclePricing/components/UsedVehiclePricing/PricedCarRow.vue";
import SearchForm from "Modules/vehiclePricing/components/UsedVehiclePricing/SearchForm.vue";
import InvalidVehicles from "Modules/vehiclePricing/components/InvalidVehicles.vue";
import floatTheadMixin from "Modules/vehiclePricing/mixins/floatTheadMixin";
import {get, sync} from "vuex-pathify";
import StockTypePriceAdjustmentForm
    from "Modules/vehiclePricing/components/UsedVehiclePricing/StockTypePriceAdjustmentForm.vue";
import LaddaBBtn from "Components/LaddaBBtn";

export default {
    name: "UsedVehiclePricing",
    components: {
        PricingNav,
        TableContentLoader,
        DeletePricingRulesBtn,
        PricedWidget,
        UsedPricedCarRow,
        UsedPricedCarRowHeader,
        SearchForm,
        LowFixedPriceWarningModal,
        StockTypePriceAdjustmentForm,
        LaddaBBtn,
        InvalidVehicles
    },
    mixins: [floatTheadMixin],
    computed: {
        loading: sync("vehiclePricing/loading"),
        stockType: get("vehiclePricing/stockType"),
        dealer: get("vehiclePricing/dealer"),
        ymm: get("vehiclePricing/ymm"),
        searchForm: get("usedVehiclePricing/searchForm"),
        enforcePricingRules: sync("usedVehiclePricing/enforcePricingRules"),
        stockTypePriceAdjustment: sync(
            "usedVehiclePricing/stockTypePriceAdjustment"
        ),
        vehicleSearchFailureMessage: sync(
            "usedVehiclePricing/vehicleSearchFailureMessage"
        ),
        vehicleSearchAttempted: sync(
            "usedVehiclePricing/vehicleSearchAttempted"
        ),
        vehicleSearchFoundVehicle: sync(
            "usedVehiclePricing/vehicleSearchFoundVehicle"
        ),
        pricingForm() {
            return {
                searchForm: this.searchForm,
                stockTypePriceAdjustment: this.stockTypePriceAdjustment,
                pricedCarRows: this.pricedCarRows
            };
        },
        renderedPricedCars() {
            return _.map(this.pricedCarRows, pricedCar => {
                return this.renderPricedCar(pricedCar);
            });
        }
    },
    data() {
        return {
            submitting: false,
            pricedCarRows: [],
            metricsRefreshKey: 0
        };
    },
    created() {
    },
    methods: {
        handlePricingSuccessfullyDeleted() {
            this.loading = true;
            this.submitting = true;
            setTimeout(() => {
                window.location.reload();
            }, 5000);
        },

        renderPricedCar(pricedCar) {
            const {adjustment: carAdjustment, vehicle} = pricedCar;

            if (carAdjustment.enabled) {
                const fixedPriceEnabledAndValid =
                    carAdjustment.fixedPrice &&
                    carAdjustment.fixedPriceAmount &&
                    !isNaN(carAdjustment.fixedPriceAmount);
                if (fixedPriceEnabledAndValid) {
                    this.addFixedPriceAdjustmentAmount(vehicle, carAdjustment);
                } else {
                    this.addVehiclePriceAndEffectiveAdjustmentAmount(
                        vehicle,
                        carAdjustment,
                        carAdjustment,
                        "CAR"
                    );
                }
            } else if (this.stockTypePriceAdjustment.adjustment.enabled) {
                this.addVehiclePriceAndEffectiveAdjustmentAmount(
                    vehicle,
                    carAdjustment,
                    this.stockTypePriceAdjustment.adjustment,
                    "STOCK_TYPE"
                );
            } else {
                // neither individual adjustment or stockType adjustment is enabled
                carAdjustment.amount = null;
                this.addVehiclePriceAndEffectiveAdjustmentAmount(
                    vehicle,
                    carAdjustment,
                    carAdjustment,
                    null
                );
            }

            return pricedCar;
        },

        addFixedPriceAdjustmentAmount(vehicle, carAdjustment) {
            vehicle.price = carAdjustment.fixedPriceAmount;
            carAdjustment.amount =
                carAdjustment.fixedPriceAmount - vehicle.internetPrice;

            this.addActiveStatus(vehicle, carAdjustment, "FIXED_CAR");
        },

        addVehiclePriceAndEffectiveAdjustmentAmount(
            vehicle,
            carAdjustment,
            effectiveAdjustment,
            effectiveAdjustmentType
        ) {
            carAdjustment.fixedPriceAmount = null;
            carAdjustment.type = effectiveAdjustment.type;
            carAdjustment.amount = effectiveAdjustment.amount;

            let price;
            if (carAdjustment.amount != null) {
                price =
                    carAdjustment.type === "PERCENTAGE"
                        ? Math.round(
                            vehicle.internetPrice +
                            vehicle.internetPrice *
                            (carAdjustment.amount / 100)
                        )
                        : vehicle.internetPrice + carAdjustment.amount;
            }

            carAdjustment.amount =
                !price || price < 0 ? null : carAdjustment.amount;
            vehicle.price = !price || price < 0 ? null : price;

            this.addActiveStatus(
                vehicle,
                carAdjustment,
                effectiveAdjustmentType
            );
        },

        addActiveStatus(
            vehicle,
            effectiveCarAdjustment,
            effectiveAdjustmentType
        ) {
            if (this.enforcePricingRules) {
                this.addActiveStatusForEnforcePricingRulesIsTrue(
                    vehicle,
                    effectiveCarAdjustment,
                    effectiveAdjustmentType
                );
            } else if (_.isNil(effectiveAdjustmentType)) {
                vehicle.activeStatus = false;
                this.$set(
                    vehicle,
                    "activeStatusDesc",
                    "Enforce Pricing Rules is turned OFF, but no price adjustments set."
                );
            } else {
                vehicle.activeStatus = true;
                this.$set(
                    vehicle,
                    "activeStatusDesc",
                    "Enforce Pricing Rules is turned OFF."
                );
            }
        },
        addActiveStatusForEnforcePricingRulesIsTrue(
            vehicle,
            effectiveCarAdjustment,
            effectiveAdjustmentType
        ) {
            let activeStatus, activeStatusDesc;

            if (
                !effectiveAdjustmentType ||
                effectiveCarAdjustment.amount == null
            ) {
                activeStatus = false;
                activeStatusDesc = "No adjustment set";
            } else if (effectiveCarAdjustment.amount >= 0) {
                activeStatus = false;
                activeStatusDesc =
                    "Adjustment does not bring price below Internet Price";
            } else {
                activeStatus = true;
                activeStatusDesc =
                    "Active via " +
                    effectiveAdjustmentType +
                    " adjustment since it brings price below Internet Price";
            }

            vehicle.activeStatus = activeStatus;
            this.$set(vehicle, "activeStatusDesc", activeStatusDesc);
        },
        savePricingAdjustments() {
            this.submitting = true;
            api.post(
                `/stratus/dealer/${this.dealer.id}/vehicle-pricing/${this.stockType}`,
                this.pricingForm
            )
                .then(response => {
                    this.$toastr.s(
                        "Used Vehicle Pricing Rules Updated. Please give us a couple of minutes to recalculate your used vehicle prices."
                    );
                    this.handleSearchCompleted(response.data);
                    this.refreshMetrics();
                })
                .catch(error => {
                    this.$toastr.e(
                        "There was an error with your submission, price adjustments were not saved"
                    );
                    if (
                        error.response.status === 400 &&
                        error.response.data &&
                        error.response.data.errors
                    ) {
                        console.error(
                            "validation errors encountered",
                            error.response.data.errors
                        );
                    } else {
                        console.error(error.response);
                    }
                    // TODO handle validation errors
                    this.submitting = false;
                });
        },

        refreshMetrics() {
            setTimeout(() => {
                this.metricsRefreshKey++;
                this.submitting = false;
            }, 5000);
        },

        handleSearchCompleted(searchResults) {
            this.enforcePricingRules = searchResults.enforcePricingRules;
            this.stockTypePriceAdjustment =
                searchResults.stockTypePriceAdjustment;
            this.pricedCarRows = searchResults.pricedCarRows;
            this.vehicleSearchFoundVehicle =
                searchResults.vehicleSearchFoundVehicle;
            this.vehicleSearchAttempted = searchResults.vehicleSearchAttempted;
            this.vehicleSearchFailureMessage =
                searchResults.vehicleSearchFailureMessage;
        },

        handlePrint() {
            window.print();
        }
    }
};
</script>

<style lang="scss" scoped>
#save-footer {
    font-size: 18px;
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0px -2px 2px rgba(0, 0, 0, 0.09);
}

.pricing-row {
    label {
        margin-right: 3px;
        text-align: right;
    }
}

.ibox.priced-widget {
    margin-bottom: 0;
}

@media print {
    @page {
        size: A4 landscape;
        max-height: 100%;
        max-width: 100%;
    }
    body {
        width: 100%;
        -webkit-transform: scale(0.95, 0.95);
        -moz-transform: scale(0.95, 0.95);
    }
    .panel-body {
        padding: 0;
    }
}
</style>
