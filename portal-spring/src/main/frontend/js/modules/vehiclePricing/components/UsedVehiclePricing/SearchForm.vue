<template>
    <div>
        <div class="panel-title m-b-md animated">
            <h4>Search Filters</h4>
        </div>
        <div class="panel animated">
            <div class="panel-body container-fluid">
                <form class="form-inline">
                    <div class="used-price-select-wrapper my-1 form-group">
                        <label>Stock #</label>
                        <input v-model="searchFormStockNumber">
                    </div>
                    <div class="used-price-select-wrapper my-1 form-group">
                        <label>Vin #</label>
                        <input v-model="searchFormVin">
                    </div>

                    <div class="used-price-select-wrapper my-1 form-group">
                        <label>Year</label>
                        <select id="year" v-model="searchFormYear" class="mdb-select colorful-select dropdown-primary">
                            <option :value="null">
                                All Years
                            </option>
                            <option v-for="year in ymm.years" :value="year">
                                {{ year }}
                            </option>
                        </select>
                    </div>
                    <div class="used-price-select-wrapper my-1 form-group">
                        <label>Make</label>
                        <select id="make" v-model="searchFormMake" class="mdb-select colorful-select dropdown-primary">
                            <option :value="null">
                                All Makes
                            </option>
                            <option v-for="make in ymm.makes" :value="make">
                                {{ make }}
                            </option>
                        </select>
                    </div>

                    <div class="used-price-select-wrapper my-1 form-group">
                        <label>Model</label>
                        <select id="model" v-model="searchFormModel"
                                class="mdb-select colorful-select dropdown-primary">
                            <option :value="null">
                                All Models
                            </option>
                            <option v-for="model in ymm.models" :value="model">
                                {{ model }}
                            </option>
                        </select>
                    </div>

                    <div class="used-price-select-wrapper my-1 form-group">
                        <label>Type</label>
                        <select id="active" v-model="searchFormActive">
                            <option :value="null">
                                All
                            </option>
                            <option :value="true">
                                Active
                            </option>
                            <option :value="false">
                                Not Active
                            </option>
                        </select>
                    </div>
                    <div class="used-price-input-wrapper form-group">
                        <ladda-b-btn
                            :loading="submitting"
                            loading-text="Searching..."
                            variant="primary"
                            :disabled="loading"
                            @click.prevent="search()"
                        >
                            Search
                        </ladda-b-btn>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
import api from '@/api';
import {get, sync} from 'vuex-pathify';
import LaddaBBtn from 'Components/LaddaBBtn';

export default {
    name: 'UsedSearchForm',
    components: {LaddaBBtn},
    data() {
        return {
            submitting: false
        };
    },
    computed: {
        loading: sync('vehiclePricing/loading'),
        ymm: get('vehiclePricing/ymm'),
        dealer: get('vehiclePricing/dealer'),
        stockType: get('vehiclePricing/stockType'),

        searchForm: sync('usedVehiclePricing/searchForm'),
        searchFormActive: sync('usedVehiclePricing/searchForm@active'),
        searchFormStockNumber: sync('usedVehiclePricing/searchForm@stockNumber'),
        searchFormVin: sync('usedVehiclePricing/searchForm@vin'),
        searchFormYear: sync('usedVehiclePricing/searchForm@year'),
        searchFormMake: sync('usedVehiclePricing/searchForm@make'),
        searchFormModel: sync('usedVehiclePricing/searchForm@model')
    },
    created() {
        this.loading = true;
        this.search(this.$route.query);
    },

    methods: {
        search(routeQueryParams) {
            this.submitting = !this.loading;
            this.loading = true;

            const query = !_.isEmpty(routeQueryParams) ? routeQueryParams : this.searchForm;
            api.get(`/stratus/dealer/${this.dealer.id}/vehicle-pricing/${this.stockType}/form`, query)
                .then(response => {
                    this.searchForm = response.data.searchForm;
                    this.loading = false;

                    const queryStr = '?' + Object.keys(query).map(k => k + '=' + query[k]).join('&').replace(/=null/g, '');
                    window.history.pushState(null, null, queryStr);

                    this.submitting = false;
                    this.$emit('search-completed', response.data);
                })
                .catch(error => {
                    console.log(error);
                    this.loading = false;
                    this.submitting = false;
                });
        }
    }
};
</script>

<style lang="scss" scoped>
.panel > .panel-heading {
    color: #676a6c;
}
</style>
