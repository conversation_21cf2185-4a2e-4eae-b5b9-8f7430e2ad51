<template>
    <div class="d-flex align-items-center error-links">
        <div class="error-link">
            <a v-if="invalidCount > 0"
               :href="`/stratus/imports/dealer/${dealer.id}/invalid-car-logs?type=PRICING_ERROR&stockType=${this.stockType}`">
                <span class="text-danger">{{ invalidCount }} Errors Logged</span>
            </a>
        </div>
        <div class="error-link">
            <a v-if="duplicateCount > 0"
               :href="`/stratus/imports/dealer/${dealer.id}/invalid-car-logs?type=DUPLICATE&stockType=${this.stockType}`">
                <span class="text-danger">{{ duplicateCount }} Duplicate Vehicles</span>
            </a>
        </div>
    </div>

</template>

<script>
import {get} from 'vuex-pathify';

export default {
    name: 'InvalidVehicles',
    computed: {
        dealer: get('vehiclePricing/dealer'),
        stockType: get('vehiclePricing/stockType'),
        invalidUsedCount: get('vehiclePricing/invalidUsedCount'),
        invalidNewCount: get('vehiclePricing/invalidNewCount'),
        duplicateUsedCount: get('vehiclePricing/duplicateUsedCount'),
        duplicateNewCount: get('vehiclePricing/duplicateNewCount'),
        invalidCount() {
            return this.stockType === 'new' ? this.invalidNewCount : this.invalidUsedCount;
        },
        duplicateCount() {
            return this.stockType === 'new' ? this.duplicateNewCount : this.duplicateUsedCount;
        }
    }
};
</script>

<style lang="scss" scoped>
.error-link:not(:first-of-type) {
    margin-left: 10px;
}
</style>
