<template>
    <div>
        <button :disabled="disabled" type="button" class="btn btn-lg btn-danger"
                data-toggle="modal" data-target="#deletePricingModal"
        >
            Delete ALL Pricing
        </button>

        <div id="deletePricingModal" class="modal fade" tabindex="-1"
             role="dialog" aria-labelledby="deletePricingModal"
        >
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 id="myModalLabel" class="modal-title">
                            Delete Pricing
                        </h4>
                    </div>
                    <div class="modal-body">
                        <label for="confirmCheckbox" style="font-weight: 400;">
                            <input id="confirmCheckbox" v-model="confirmCheckbox" type="checkbox"> Please confirm before
                            deleting all of the pricing offsets for <strong>{{ this.stockType }}</strong> vehicles.
                        </label>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"
                                @click="notDeleted()"
                        >
                            No
                        </button>
                        <button type="button" class="btn btn-primary"
                                :disabled="!confirmCheckbox || disabledAfterSubmit"
                                @click="deletePricing()"
                        >
                            {{ disabledAfterSubmit ? 'Deleting...' : 'Yes' }}
                            <div v-if="disabledAfterSubmit" class="sk-spinner sk-spinner-circle delete-spinner">
                                <div class="sk-circle1 sk-circle"/>
                                <div class="sk-circle2 sk-circle"/>
                                <div class="sk-circle3 sk-circle"/>
                                <div class="sk-circle4 sk-circle"/>
                                <div class="sk-circle5 sk-circle"/>
                                <div class="sk-circle6 sk-circle"/>
                                <div class="sk-circle7 sk-circle"/>
                                <div class="sk-circle8 sk-circle"/>
                                <div class="sk-circle9 sk-circle"/>
                                <div class="sk-circle10 sk-circle"/>
                                <div class="sk-circle11 sk-circle"/>
                                <div class="sk-circle12 sk-circle"/>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import api from '@/api';
import {get} from 'vuex-pathify';

export default {
    name: 'DeletePricingRulesBtn',
    props: {
        disabled: {
            type: Boolean,
            required: true,
            default: false
        }
    },
    data() {
        return {
            confirmCheckbox: false,
            disabledAfterSubmit: false
        };
    },
    computed: {
        loading: get('vehiclePricing/loading'),
        stockType: get('vehiclePricing/stockType'),
        dealer: get('vehiclePricing/dealer')
    },
    methods: {
        deletePricing() {
            this.disabledAfterSubmit = true;
            api.delete(`/stratus/dealer/${this.dealer.id}/vehicle-pricing/${this.stockType}/pricing`)
                .then(() => {
                    this.$toastr.s('Pricing has been deleted');
                    this.$emit('pricingSuccessfullyDeleted');
                })
                .catch(error => {
                    console.log(error);
                    this.$toastr.e('Error deleting pricing');
                });
        },
        notDeleted() {
            this.confirmCheckbox = false;
            this.$toastr.i('Pricing not deleted');
        }
    }
};
</script>

<style lang="scss" scoped>
#deletePricingModal {

    .modal-header {
        background-color: #ed5565;
    }

    .modal-title {
        color: white;
    }
}

.sk-circle:before {
    background-color: #fff;
}

.delete-spinner {
    float: right;
    margin-left: 10px;
}
</style>
