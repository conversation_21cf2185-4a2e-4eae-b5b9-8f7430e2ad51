<template>
    <tr class="style-row price-adjustment-row">
        <td class="style-name" style="text-align: left; padding-left: 60px;">
            <a
                class="style-description"
                @click.prevent="
                    fetchPricedCars(
                        pricedModel,
                        modelIdx,
                        pricedStyle,
                        styleIdx
                    )
                "
            >{{ pricedStyle.style.name }}</a
            >
        </td>
        <td>{{ pricedStyle.inventoryCount }} Vehicles</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>
            <input
                v-model="pricedStyle.adjustment.enabled"
                type="checkbox"
                class="price-adjustment-enabler"
            />
        </td>
        <td>
            <div class="form-inline">
                <select
                    v-model="pricedStyle.adjustment.adjustedValue"
                    :disabled="!pricedStyle.adjustment.enabled"
                    class="adjustment-adjusted-value form-control"
                >
                    <option selected value="INVOICE">
                        Invoice
                    </option>
                    <option
                        v-if="enableInternetPrice === true"
                        value="INTERNET_PRICE"
                    >
                        Internet
                    </option>
                    <option value="MSRP">
                        MSRP
                    </option>
                </select>
            </div>
        </td>
        <td>
            <div class="finance-adjustment-cell">
                <div class="form-group">
                    <div class="input-group">
                        <div class="input-group-btn">
                            <select
                                v-model="pricedStyle.adjustment.type"
                                :disabled="
                                    !pricedStyle.adjustment.enabled ||
                                        pricedStyle.adjustment.fixedPrice
                                "
                                class="adjustment-type form-control"
                            >
                                <option selected value="DOLLAR">
                                    $
                                </option>
                                <option value="PERCENTAGE">
                                    %
                                </option>
                            </select>
                        </div>
                        <!-- TODO implement pattern, keep track of original value -->
                        <input
                            v-model.number="pricedStyle.adjustment.amount"
                            type="number"
                            :disabled="
                                !pricedStyle.adjustment.enabled ||
                                    pricedStyle.adjustment.fixedPrice
                            "
                            class="model-price adjustment-amount form-control"
                            style="width: 75px;"
                            pattern="^-{0,1}(?!0.)\d+$"
                            data-pattern-error="Please enter a whole number"
                            @blur="adjustmentAmountBlur(pricedStyle)"
                        />
                    </div>
                </div>
            </div>
        </td>
        <td v-if="enableLeasePricing">
            <div class="finance-adjustment-cell">
                <div class="form-group">
                    <div class="input-group">
                        <div class="input-group-btn">
                            <select
                                v-model="pricedStyle.adjustment.leaseType"
                                :disabled="
                                    !pricedStyle.adjustment.enabled ||
                                        pricedStyle.adjustment.fixedPrice
                                "
                                class="adjustment-lease-type form-control"
                            >
                                <option selected value="DOLLAR">
                                    $
                                </option>
                                <option value="PERCENTAGE">
                                    %
                                </option>
                            </select>
                        </div>
                        <input
                            v-model.number="pricedStyle.adjustment.leaseAmount"
                            :disabled="
                                !pricedStyle.adjustment.enabled ||
                                    pricedStyle.adjustment.fixedPrice
                            "
                            class="model-price adjustment-lease-amount form-control"
                            style="width: 75px;"
                            pattern="^-{0,1}(?!0.)\d+$"
                            data-pattern-error="Please enter a whole number"
                        />
                    </div>
                </div>
            </div>
        </td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
    </tr>
</template>
<script>
import _ from "lodash";

export default {
    name: "PricedStyleRow",
    props: {
        pricedModel: {
            type: Object,
            required: true
        },
        modelIdx: {
            type: Number,
            required: true
        },
        pricedStyle: {
            type: Object,
            required: true
        },
        styleIdx: {
            type: Number,
            required: true
        },
        enableLeasePricing: {
            type: Boolean,
            required: true
        },
        enableInternetPrice: {
            type: Boolean,
            required: false,
            default: false
        }
    },
    methods: {
        adjustmentAmountBlur({adjustment}) {
            const adjustmentAmountInvalid =
                _.isNil(adjustment.amount) ||
                _.isNaN(adjustment.amount) ||
                (adjustment.type === "PERCENTAGE" && adjustment.amount < -100);
            if (adjustmentAmountInvalid) {
                adjustment.amount = null;
                adjustment.enabled = false;
            }
        },
        fetchPricedCars(...args) {
            this.$emit("fetchPricedCars", ...args);
        }
    }
};
</script>
