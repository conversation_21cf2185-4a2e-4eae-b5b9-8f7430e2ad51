<template>
    <i-box
        class="priced-widget"
        :title="title"
        :description="description"
        :alert-message="alertMessage"
        :collapsible="false"
        :loading="elasticMetrics.isLoading || pricingMetricsFromDb.isLoading"
    >
        <div ref="pricedWidget"/>
    </i-box>
</template>

<style lang="scss">
.priced-widget {
    margin-bottom: -10px;
    .percent-number {
        font-size: 18px;
    }

    .ibox-title {
        padding-right: 15px !important;
    }

    .ibox-content {
        min-height: 215px;
    }
}
</style>

<script>
import _ from "lodash";
import IBox from "Components/IBox";
import api from "@/api";

export default {
    components: {IBox},

    props: {
        name: {
            type: String,
            required: true
        },
        title: {
            type: String,
            required: true
        },
        colorConfig: {
            type: Object,
            required: false
        },
        description: {
            type: String,
            required: false,
            default: null
        },
        alertMessage: {
            type: String,
            required: false,
            default: null
        },
        dealerId: {
            type: String,
            required: false,
            default: null
        }
    },

    data() {
        return {
            elasticMetrics: {
                pricedCount: 0,
                total: 0,
                isLoading: true
            },
            defaultColorConfig: {
                green: 90,
                yellow: 75
            },
            pricingMetricsFromDb: {
                totalNew: 0,
                totalNewPriced: 0,
                totalUsed: 0,
                totalUsedPriced: 0,
                isLoading: true
            }
        };
    },

    computed: {
        colorConfigInUse() {
            return _.merge(this.defaultColorConfig, this.colorConfig);
        },

        columns() {
            const pricedCount = _.get(this.metricsDisplay, "pricedCount", 0);
            if (_.isNaN(pricedCount)) {
                return [[this.title, 0]];
            }

            return [[this.title, pricedCount]];
        },

        max() {
            const total = _.get(this.metricsDisplay, "total", 100);
            if (_.isNaN(total) || total <= 0) {
                return 100;
            }

            return total;
        },

        metricsDisplay() {
            if (
                this.elasticMetrics.isLoading === false &&
                this.pricingMetricsFromDb.isLoading === false &&
                this.elasticMetrics.total === 0
            ) {
                if (this.name === "used-priced") {
                    return {
                        pricedCount: this.pricingMetricsFromDb.totalUsedPriced,
                        total: this.pricingMetricsFromDb.totalUsed
                    };
                } else if (this.name === "new-priced") {
                    return {
                        pricedCount: this.pricingMetricsFromDb.totalNewPriced,
                        total: this.pricingMetricsFromDb.totalNew
                    };
                }
            } else {
                return this.elasticMetrics;
            }
        }
    },

    created() {
        this.loadData();
    },

    methods: {
        updateChart() {
            c3.generate({
                bindto: this.$refs.pricedWidget,
                data: {
                    columns: this.columns,
                    type: "gauge"
                },
                gauge: {
                    max: this.max,
                    label: {
                        format: function (value, ratio) {
                            return _.round(ratio * 100) + "%";
                        }
                    }
                },
                color: {
                    pattern: ["#dc3545", "#ffb237", "#60B044"],
                    threshold: {
                        values: [
                            (this.colorConfigInUse.yellow / 100) *
                            this.metricsDisplay.total,
                            (this.colorConfigInUse.green / 100) *
                            this.metricsDisplay.total,
                            100
                        ]
                    }
                }
            });
        },

        loadData() {
            this.elasticMetrics.isLoading = true;
            this.pricingMetricsFromDb.isLoading = true;

            api.post(`/dealers/dashboard/${this.name}`, {
                dealerIds: [this.dealerId]
            })
                .then(response => {
                    this.elasticMetrics = response.data;
                    this.elasticMetrics.isLoading = false;
                })
                .then(() => {
                    this.updateChart();
                })
                .catch(error => {
                    console.log(error);
                    this.elasticMetrics.isLoading = false;
                });

            api.get(`/dealer/${this.dealerId}/pricing-metrics`)
                .then(response => {
                    this.pricingMetricsFromDb = response.data;
                    this.pricingMetricsFromDb.isLoading = false;
                    this.updateChart();
                })
                .catch(error => {
                    console.log(error);
                    this.pricingMetricsFromDb.isLoading = false;
                });
        }
    }
};
</script>
