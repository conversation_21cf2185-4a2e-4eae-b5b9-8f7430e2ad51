<template>
    <thead class="pricing-table-head">
    <tr>
        <th scope="col">
            Stock Number
        </th>
        <th scope="col">
            Vin
        </th>
        <th scope="col">
            Days In Stock
        </th>
        <th scope="col">
            Year
        </th>
        <th scope="col">
            Make
        </th>
        <th scope="col">
            Model
        </th>
        <th scope="col">
            Retail Price
        </th>
        <th scope="col">
            Internet Price
        </th>
        <th scope="col">
            Enable Pricing
        </th>
        <th scope="col">
            Adjustment Amount
            <b-link
                v-b-tooltip.hover
                title="If Fixed Price Amount set, Adjustment Amount is difference of Internet Price and Fixed Price."
            >
                <i aria-hidden="true" class="fas fa-info-circle"/>
            </b-link>
        </th>
        <th scope="col">
            Fixed Price Amount
        </th>
        <th scope="col">
            Enable Fixed Pricing
            <b-link
                v-b-tooltip.hover
                title="If Fixed Pricing is enabled, the Fixed Price Amount will override any other price adjustments."
            >
                <i aria-hidden="true" class="fas fa-info-circle"/>
            </b-link>
        </th>
        <th scope="col">
            CarSaver Price
        </th>
        <th scope="col">
            Active
        </th>
    </tr>
    </thead>
</template>
<script>
export default {
    name: "UsedPricedCarRowHeader"
};
</script>

<style lang="scss"></style>
