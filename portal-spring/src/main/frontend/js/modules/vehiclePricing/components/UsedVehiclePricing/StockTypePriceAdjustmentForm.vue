<template>
    <b-col lg="9" class="global-pricing-column">
        <b-form-group :disabled="loading">
            <b-row>
                <label class="col col-sm-3 control-label" for="stockTypePriceAdjustment.adjustment.enabled"
                       style="margin-left: 15px;">
                    Enable Global Pricing
                    <b-link v-b-tooltip.hover
                            title="Enable if you want adjustment amounts to apply to ALL 'Used' vehicles">
                        <i aria-hidden="true" class="fas fa-info-circle"/>
                    </b-link>
                </label>
                <div class="col col-sm-3">
                    <input
                        id="stockTypePriceAdjustment.adjustment.enabled"
                        v-model="adjustmentEnabled"
                        class="stockTypePriceAdjustmentEnabledCheckBox"
                        type="checkbox"
                    >
                </div>
            </b-row>
        </b-form-group>
        <b-form-group :disabled="loading">
            <b-row>
                <label class="col col-sm-3 control-label" for="stockTypePriceAdjustment.adjustment.type"
                       style="margin-left: 15px;">Adjustment Type</label>
                <div class="col col-sm-3">
                    <select
                        id="stockTypePriceAdjustment.adjustment.type"
                        v-model="adjustmentType"
                        :disabled="!adjustmentEnabled"
                        class="stockTypePriceAdjustmentTypeDropDown"
                    >
                        <option value="DOLLAR">
                            $
                        </option>
                        <option value="PERCENTAGE">
                            %
                        </option>
                    </select>
                </div>
            </b-row>
        </b-form-group>
        <b-form-group :disabled="loading">
            <b-row>
                <label class="col col-sm-3 control-label" for="stockTypePriceAdjustment.adjustment.amount"
                       style="margin-left: 15px;">
                    Adjustment Amount
                    <b-link v-b-tooltip.hover
                            title="This adjustment amount will apply to all USED vehicles by default; NOTE: VIN level pricing set below will override this amount">
                        <i aria-hidden="true" class="fas fa-info-circle"/>
                    </b-link>
                </label>
                <div class="col col-sm-3">
                    <input
                        id="stockTypePriceAdjustment.adjustment.amount"
                        v-model.number="adjustmentAmount"
                        :required="adjustmentEnabled"
                        :readonly="!adjustmentEnabled"
                        class="stockTypePriceAdjustmentAmountTextBox form-control"
                        type="number"
                        pattern="^-{0,1}(?!0.)\d+$"
                        max="9999"
                        data-pattern-error="Please enter a valid whole dollar amount"
                        @blur="stockTypeAdjustmentAmountBlur()"
                    >
                    <!-- TODO show validation error -->
                </div>
            </b-row>
        </b-form-group>
    </b-col>
</template>
<script>
import {sync} from 'vuex-pathify';
import _ from 'lodash';

export default {
    name: 'UsedStockTypePriceAdjustmentForm',
    computed: {
        loading: sync('vehiclePricing/loading'),
        adjustmentType: sync('usedVehiclePricing/<EMAIL>'),
        adjustmentEnabled: sync('usedVehiclePricing/<EMAIL>'),
        adjustmentAmount: sync('usedVehiclePricing/<EMAIL>')
    },
    methods: {
        stockTypeAdjustmentAmountBlur() {
            const adjustmentAmountInvalid = _.isNil(this.adjustmentAmount) || _.isNaN(this.adjustmentAmount) || (this.adjustmentType === 'PERCENTAGE' && this.adjustmentAmount < -100);
            if (adjustmentAmountInvalid) {
                this.adjustmentAmount = 0;
            }
        }
    }
};
</script>
