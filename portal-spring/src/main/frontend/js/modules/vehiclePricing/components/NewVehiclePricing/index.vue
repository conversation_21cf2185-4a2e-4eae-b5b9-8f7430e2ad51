<template>
    <div class="panel blank-panel">
        <pricing-nav/>
        <low-fixed-price-warning-modal/>
        <div class="panel-body pb-5">
            <search-form @search-completed="handleSearchCompleted"/>
            <div class="panel-title m-b-md animated">
                <h4>Vehicle Listings</h4>
            </div>
            <div class="panel panel-success animated">
                <div class="panel-heading">
                    <h4>{{ dealer.name }}</h4>
                </div>
                <form id="pricing-form" class="form-horizontal">
                    <b-container fluid>
                        <b-row class="pricing-row">
                            <stock-type-price-adjustment-form
                                :enable-lease-pricing="enableLeasePricing"
                                :enable-internet-price="internetPriceEnabled"
                            />
                            <b-col lg="3">
                                <priced-widget
                                    :key="metricsRefreshKey"
                                    name="new-priced"
                                    title="% New Priced"
                                    description="Percent of new vehicles with valid pricing"
                                    :dealer-id="dealer.id"
                                />
                                <b-row class="justify-content-center pb-4">
                                    <invalid-vehicles/>
                                </b-row>
                            </b-col>
                        </b-row>
                    </b-container>
                    <div class="d-flex justify-content-end pr-3">
                        <b-button @click="handlePrint">
                            <i
                                class="fa fa-print pr-1"
                                aria-hidden="true"
                            />print
                        </b-button>
                    </div>
                    <div v-if="loading" class="panel-body">
                        <table-content-loader/>
                    </div>
                    <div v-if="!loading" class="panel-body">
                        <!-- user attempted to search for a specific vehicle but none was found -->
                        <div
                            v-if="
                                vehicleSearchAttempted &&
                                    !vehicleSearchFoundVehicle
                            "
                            class="alert alert-warning alert-dismissable"
                        >
                            <button
                                aria-hidden="true"
                                data-dismiss="alert"
                                class="close"
                                type="button"
                            >
                                ×
                            </button>
                            <span>{{ vehicleSearchFailureMessage }}</span>
                        </div>
                        <div class="table-responsive">
                            <table
                                ref="pricing-table"
                                class="pricing-table table table-condensed table-striped"
                                aria-describedby="Vehicle Pricing Table"
                                data-floatThead
                            >
                                <new-priced-car-row-header
                                    :enable-lease-pricing="enableLeasePricing"
                                />
                                <tbody
                                    style="white-space: nowrap; text-align: center"
                                >
                                <template
                                    v-for="(pricedModel,
                                        modelIdx) in renderedPricedModels"
                                >
                                    <priced-model-row
                                        :key="
                                                'pricedModel_' +
                                                    pricedModel.model.id
                                            "
                                        :priced-model="pricedModel"
                                        :model-idx="modelIdx"
                                        :enable-lease-pricing="
                                                enableLeasePricing
                                            "
                                        :enable-internet-price="
                                                internetPriceEnabled
                                            "
                                        @fetchPricedStyles="
                                                fetchPricedStyles
                                            "
                                    />
                                    <template
                                        v-for="(pricedStyle,
                                            styleIdx) in pricedModel.renderedPricedStyles"
                                    >
                                        <priced-style-row
                                            :key="
                                                    'pricedStyle_' +
                                                        pricedStyle.style.id
                                                "
                                            :priced-model="pricedModel"
                                            :model-idx="modelIdx"
                                            :priced-style="pricedStyle"
                                            :style-idx="styleIdx"
                                            :enable-lease-pricing="
                                                    enableLeasePricing
                                                "
                                            :enable-internet-price="
                                                    internetPriceEnabled
                                                "
                                            @fetchPricedCars="
                                                    fetchPricedCars
                                                "
                                        />
                                        <new-priced-car-row
                                            v-for="pricedCar in pricedStyle.renderedPricedCars"
                                            :key="
                                                    'pricedCar_' +
                                                        pricedCar.vehicle.id
                                                "
                                            :priced-car="pricedCar"
                                            :enable-lease-pricing="
                                                    enableLeasePricing
                                                "
                                            :enable-internet-price="
                                                    internetPriceEnabled
                                                "
                                        />
                                    </template>
                                </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div id="save-footer">
                        <delete-pricing-rules-btn
                            v-if="
                                $acl.hasAuthority('edit:pricing-new') ||
                                    $acl.hasDealerPermission(
                                        dealer.id,
                                        'inventory:new-pricing:edit'
                                    )
                            "
                            class="mr-4"
                            :disabled="loading || submitting"
                            @pricingSuccessfullyDeleted="
                                handlePricingSuccessfullyDeleted()
                            "
                        />
                        <ladda-b-btn
                            v-if="
                                $acl.hasAuthority('edit:pricing-new') ||
                                    $acl.hasDealerPermission(
                                        dealer.id,
                                        'inventory:new-pricing:edit'
                                    )
                            "
                            :loading="submitting"
                            :disabled="loading"
                            loading-text="Saving..."
                            variant="primary"
                            @click.prevent="savePricingAdjustments()"
                        >
                            Save
                        </ladda-b-btn>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>
<script>
import api from "@/api";
import PricingNav from "Modules/vehiclePricing/components/PricingNav.vue";
import SearchForm from "Modules/vehiclePricing/components/NewVehiclePricing/SearchForm.vue";
import PricedModelRow from "Modules/vehiclePricing/components/NewVehiclePricing/PricedModelRow.vue";
import PricedStyleRow from "Modules/vehiclePricing/components/NewVehiclePricing/PricedStyleRow.vue";
import NewPricedCarRow from "Modules/vehiclePricing/components/NewVehiclePricing/PricedCarRow.vue";
import NewPricedCarRowHeader from "Modules/vehiclePricing/components/NewVehiclePricing/PricedCarRowHeader.vue";
import StockTypePriceAdjustmentForm
    from "Modules/vehiclePricing/components/NewVehiclePricing/StockTypePriceAdjustmentForm.vue";
import DeletePricingRulesBtn from "Modules/vehiclePricing/components/DeletePricingRulesBtn.vue";
import LowFixedPriceWarningModal from "Modules/vehiclePricing/components/LowFixedPriceWarningModal";
import TableContentLoader from "Components/TableContentLoader";
import PricedWidget from "Modules/vehiclePricing/components/PricedWidget";
import InvalidVehicles from "Modules/vehiclePricing/components/InvalidVehicles.vue";
import floatTheadMixin from "Modules/vehiclePricing/mixins/floatTheadMixin";
import {get, sync} from "vuex-pathify";
import LaddaBBtn from "Components/LaddaBBtn";
import _ from "lodash";

export default {
    name: "NewVehiclePricing",
    components: {
        PricingNav,
        TableContentLoader,
        DeletePricingRulesBtn,
        PricedWidget,
        PricedModelRow,
        PricedStyleRow,
        NewPricedCarRow,
        NewPricedCarRowHeader,
        SearchForm,
        LowFixedPriceWarningModal,
        LaddaBBtn,
        StockTypePriceAdjustmentForm,
        InvalidVehicles
    },
    mixins: [floatTheadMixin],
    computed: {
        loading: sync("vehiclePricing/loading"),
        stockType: get("vehiclePricing/stockType"),
        dealer: get("vehiclePricing/dealer"),
        enforcePricingRules: sync("newVehiclePricing/enforcePricingRules"),
        enableLeasePricing: sync("newVehiclePricing/enableLeasePricing"),
        vehicleSearchAttempted: sync(
            "newVehiclePricing/vehicleSearchAttempted"
        ),
        vehicleSearchFailureMessage: sync(
            "newVehiclePricing/vehicleSearchFailureMessage"
        ),
        vehicleSearchFoundVehicle: sync(
            "newVehiclePricing/vehicleSearchFoundVehicle"
        ),
        pricingFormStyleFilter: sync(
            "newVehiclePricing/pricingFormStyleFilter"
        ),
        pricingFormInventoryIdFilter: sync(
            "newVehiclePricing/pricingFormInventoryIdFilter"
        ),
        stockTypePriceAdjustment: sync(
            "newVehiclePricing/stockTypePriceAdjustment"
        ),
        searchForm: get("newVehiclePricing/searchForm"),
        internetPriceEnabled: get(
            "vehiclePricing/<EMAIL>"
        ),
        pricingForm() {
            return {
                searchForm: this.searchForm,
                stockTypePriceAdjustment: this.stockTypePriceAdjustment,
                pricedModelMap: this.pricedModelMap,
                pricedStyleMap: this.pricedStyleMap,
                pricedCarMap: this.pricedCarMap
            };
        },
        renderedPricedModels() {
            return _.map(this.pricedModelRows, (pricedModel, idx) => {
                return this.renderPricedModel(pricedModel, idx);
            });
        }
    },
    data() {
        return {
            pricedModelRows: [],
            pricedModelMap: {},
            pricedStyleMap: {},
            pricedCarMap: {},
            submitting: false,
            metricsRefreshKey: 0
        };
    },
    methods: {
        handlePricingSuccessfullyDeleted() {
            this.loading = true;
            this.submitting = true;
            setTimeout(() => {
                window.location.reload();
            }, 5000);
        },

        fetchPricedCars(pricedModel, modelIdx, pricedStyle, styleIdx) {
            pricedStyle.showCars = !pricedStyle.showCars; // toggle visibility

            if (pricedStyle.fetchedPricedCars) {
                pricedModel.fetchedPricedStyles[styleIdx] = pricedStyle;
                this.$set(this.pricedModelRows, modelIdx, pricedModel);
            } else {
                const query = {
                    styleId: pricedStyle.style.id,
                    active: this.searchForm.active,
                    pricingFormInventoryIdFilter: this
                        .pricingFormInventoryIdFilter
                };
                api.get(
                    `/stratus/dealer/${this.dealer.id}/vehicle-pricing/new/cars`,
                    query
                )
                    .then(response => {
                        pricedStyle.fetchedPricedCars = response.data;
                        _.each(response.data, pricedCar => {
                            this.$set(
                                this.pricedCarMap,
                                pricedCar.vehicle.id,
                                pricedCar
                            );
                        });
                        pricedModel.fetchedPricedStyles[styleIdx] = pricedStyle;
                        this.$set(this.pricedModelRows, modelIdx, pricedModel);
                    })
                    .catch(error => {
                        console.log(error);
                    });
            }
        },
        fetchPricedStyles(pricedModel, idx) {
            pricedModel.showStyles = !pricedModel.showStyles; // toggle visibility

            if (pricedModel.fetchedPricedStyles) {
                this.$set(this.pricedModelRows, idx, pricedModel);
            } else {
                const query = {
                    modelId: pricedModel.model.id,
                    active: this.searchForm.active,
                    pricingFormStyleFilter: this.pricingFormStyleFilter
                };
                api.get(
                    `/stratus/dealer/${this.dealer.id}/vehicle-pricing/styles`,
                    query
                )
                    .then(response => {
                        pricedModel.fetchedPricedStyles = response.data;
                        _.each(response.data, pricedStyle => {
                            this.$set(
                                this.pricedStyleMap,
                                pricedStyle.style.id,
                                pricedStyle
                            );
                        });
                        this.$set(this.pricedModelRows, idx, pricedModel);
                    })
                    .catch(error => {
                        console.log(error);
                    });
            }
        },

        renderPricedStyle(pricedModel, pricedStyle) {
            pricedStyle.renderedPricedCars = pricedStyle.showCars
                ? _.map(pricedStyle.fetchedPricedCars, fetchedPricedCar => {
                    return this.renderPricedCar(
                        pricedModel,
                        pricedStyle,
                        fetchedPricedCar
                    );
                })
                : [];

            return pricedStyle;
        },

        renderPricedModel(pricedModel, idx) {
            pricedModel.renderedPricedStyles = pricedModel.showStyles
                ? _.map(pricedModel.fetchedPricedStyles, fetchedPricedStyle => {
                    return this.renderPricedStyle(
                        pricedModel,
                        fetchedPricedStyle
                    );
                })
                : [];

            this.$set(this.pricedModelRows, idx, pricedModel);

            return pricedModel;
        },

        renderPricedCar(pricedModel, pricedStyle, pricedCar) {
            const {adjustment: carAdjustment, vehicle} = pricedCar;

            // consult car,style,model,stockType adjustments in that order of preference to determine effective adjustment to apply
            if (carAdjustment.enabled) {
                // individual car adjustment enabled
                const fixedPriceEnabledAndValid =
                    carAdjustment.fixedPrice &&
                    carAdjustment.fixedPriceAmount &&
                    !isNaN(carAdjustment.fixedPriceAmount);
                if (fixedPriceEnabledAndValid) {
                    this.addFixedPriceAndAdjustmentAmount(
                        vehicle,
                        carAdjustment
                    );
                } else {
                    this.addVehiclePriceAndEffectiveAdjustmentAmount(
                        vehicle,
                        carAdjustment,
                        carAdjustment,
                        "CAR"
                    );
                }
            } else if (pricedStyle.adjustment.enabled) {
                // style adjustment enabled
                this.addVehiclePriceAndEffectiveAdjustmentAmount(
                    vehicle,
                    carAdjustment,
                    pricedStyle.adjustment,
                    "STYLE"
                );
            } else if (pricedModel.adjustment.enabled) {
                // model adjustment enabled
                this.addVehiclePriceAndEffectiveAdjustmentAmount(
                    vehicle,
                    carAdjustment,
                    pricedModel.adjustment,
                    "MODEL"
                );
            } else if (this.stockTypePriceAdjustment.adjustment.enabled) {
                // stockType/global adjustment enabled
                this.addVehiclePriceAndEffectiveAdjustmentAmount(
                    vehicle,
                    carAdjustment,
                    this.stockTypePriceAdjustment.adjustment,
                    "STOCK_TYPE"
                );
            } else {
                // NO adjustment enabled
                carAdjustment.amount = null;
                this.addVehiclePriceAndEffectiveAdjustmentAmount(
                    vehicle,
                    carAdjustment,
                    carAdjustment,
                    null
                );
            }

            return pricedCar;
        },

        addFixedPriceAndAdjustmentAmount(vehicle, carAdjustment) {
            const priceToAdjust =
                carAdjustment.adjustedValue === "INVOICE"
                    ? vehicle.invoicePrice
                    : vehicle.averageMarketPrice;
            vehicle.price = carAdjustment.fixedPriceAmount;
            carAdjustment.amount =
                carAdjustment.fixedPriceAmount - priceToAdjust; // for display purposes only set this
            this.addActiveStatus(vehicle, carAdjustment, "FIXED_CAR");
        },

        addVehiclePriceAndEffectiveAdjustmentAmount(
            vehicle,
            carAdjustment,
            effectiveAdjustment,
            effectiveAdjustmentType
        ) {
            let priceToAdjust;
            let price;

            switch (effectiveAdjustment.adjustedValue) {
                case "INVOICE":
                    priceToAdjust = vehicle.invoicePrice;
                    break;
                case "MARKET":
                    priceToAdjust = vehicle.averageMarketPrice;
                    break;
                case "INTERNET_PRICE":
                    priceToAdjust = vehicle.internetPrice;
                    break;
                case "MSRP":
                    priceToAdjust = vehicle.msrp;
                    break;
                default:
                    return "";
            }

            carAdjustment.fixedPriceAmount = null;
            carAdjustment.type = effectiveAdjustment.type;
            carAdjustment.amount = effectiveAdjustment.amount;
            carAdjustment.adjustedValue = effectiveAdjustment.adjustedValue;

            if (
                priceToAdjust &&
                !isNaN(priceToAdjust) &&
                carAdjustment.amount != null
            ) {
                price =
                    carAdjustment.type === "PERCENTAGE"
                        ? Math.round(
                            priceToAdjust +
                            priceToAdjust * (carAdjustment.amount / 100)
                        )
                        : priceToAdjust + carAdjustment.amount;
            }

            carAdjustment.amount =
                !price || price < 0 ? null : carAdjustment.amount;
            vehicle.price = !price || price < 0 ? null : price;

            this.addActiveStatus(
                vehicle,
                carAdjustment,
                effectiveAdjustmentType
            );
        },

        addActiveStatus(
            vehicle,
            effectiveCarAdjustment,
            effectiveAdjustmentType
        ) {
            if (this.enforcePricingRules === true) {
                this.addActiveStatusForEnforcePricingRulesIsTrue(
                    vehicle,
                    effectiveCarAdjustment,
                    effectiveAdjustmentType
                );
            } else if (_.isNil(effectiveAdjustmentType)) {
                vehicle.activeStatus = false;
                this.$set(
                    vehicle,
                    "activeStatusDesc",
                    "Enforce Pricing Rules is turned OFF, but no price adjustments set."
                );
            } else {
                vehicle.activeStatus = true;
                this.$set(
                    vehicle,
                    "activeStatusDesc",
                    "Enforce Pricing Rules is turned OFF."
                );
            }
        },
        addActiveStatusForEnforcePricingRulesIsTrue(
            vehicle,
            effectiveCarAdjustment,
            effectiveAdjustmentType
        ) {
            let activeStatus, activeStatusDesc;

            const hasMsrp = vehicle.msrp && !isNaN(vehicle.msrp);

            if (
                !effectiveAdjustmentType ||
                effectiveCarAdjustment.amount == null
            ) {
                activeStatus = false;
                activeStatusDesc = "No Adjustment Set";
            } else if (hasMsrp) {
                const msrp = vehicle.msrp;
                const price = vehicle.price;
                const totalCashIncentive = vehicle.totalCashIncentive;
                const passesMsrpRule = price <= msrp - 100;
                const savingsLimit = msrp * 0.5;
                const savings = msrp - price + totalCashIncentive;
                const savingsValid = savings < savingsLimit;

                if (passesMsrpRule && savingsValid) {
                    activeStatus = true;
                    activeStatusDesc =
                        "Active via " +
                        effectiveAdjustmentType +
                        " adjustment since it brings price at least $100 below MSRP";
                } else {
                    activeStatus = false;
                    activeStatusDesc =
                        effectiveAdjustmentType +
                        " Adjustment does NOT bring price $100 below MSRP or vehicle savings is greater than 50% of MSRP";
                }
            } else {
                activeStatus = false;
                activeStatusDesc =
                    "Neither MSRP or Average Market Price are valid";
            }

            vehicle.activeStatus = activeStatus;
            this.$set(vehicle, "activeStatusDesc", activeStatusDesc);
        },

        savePricingAdjustments() {
            this.submitting = true;

            api.post(
                `/stratus/dealer/${this.dealer.id}/vehicle-pricing/${this.stockType}`,
                this.pricingForm
            )
                .then(response => {
                    this.$toastr.s(
                        "New Vehicle Pricing Rules Updated. Please give us a couple of minutes to recalculate your new vehicle prices."
                    );

                    this.handleSearchCompleted(response.data);
                    this.refreshMetrics();
                })
                .catch(error => {
                    if (
                        error.response.status === 400 &&
                        error.response.data &&
                        error.response.data.errors
                    ) {
                        const errors = error.response.data.errors;
                        const messages = _.map(errors, e => {
                            return e.message;
                        });
                        const toastr = _.forEach(messages, m => {
                            return m + ",";
                        });
                        this.$toastr.e(toastr);
                    } else {
                        console.error(error.response);
                    }
                    this.submitting = false;
                });
        },

        refreshMetrics() {
            setTimeout(() => {
                this.metricsRefreshKey++;
                this.submitting = false;
            }, 5000);
        },

        handleSearchCompleted(searchResults) {
            this.pricedModelRows = [];

            this.pricedModelMap = {};
            this.pricedStyleMap = {};
            this.pricedCarMap = {};

            this.vehicleSearchAttempted = searchResults.vehicleSearchAttempted;
            this.vehicleSearchFoundVehicle =
                searchResults.vehicleSearchFoundVehicle;
            this.vehicleSearchFailureMessage =
                searchResults.vehicleSearchFailureMessage;
            this.enableLeasePricing = searchResults.enableLeasePricing;
            this.enforcePricingRules = searchResults.enforcePricingRules;
            this.pricingFormStyleFilter = searchResults.pricingFormStyleFilter;
            this.pricingFormInventoryIdFilter =
                searchResults.pricingFormInventoryIdFilter;
            this.stockTypePriceAdjustment =
                searchResults.stockTypePriceAdjustment;

            _.each(searchResults.pricedModelRows, item => {
                item.showStyles = false;
                this.pricedModelRows.push(item);
                this.$set(this.pricedModelMap, item.model.id, item);
            });
        },

        handlePrint() {
            window.print();
        }
    }
};
</script>

<style lang="scss" scoped>
#save-footer {
    font-size: 18px;
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0px -2px 2px rgba(0, 0, 0, 0.09);
}
</style>
<style lang="scss">
.panel-body {
    label {
        margin-right: 3px;
    }
}

.finance-adjustment-cell {
    .form-group {
        margin: 0;
        width: 150px;
    }
}

@media print {
    @page {
        size: A4 landscape;
        max-height: 100%;
        max-width: 100%;
    }
    body {
        width: 100%;
        -webkit-transform: scale(0.95, 0.95);
        -moz-transform: scale(0.95, 0.95);
    }
    .panel-body {
        padding: 0;
    }
}
</style>
