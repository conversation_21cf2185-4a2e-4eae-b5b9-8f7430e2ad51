import Vue from 'vue';
import VueRouter from 'vue-router';
import store from './store';
import UsedVehiclePricing from './components/UsedVehiclePricing';
import NewVehiclePricing from './components/NewVehiclePricing';
import PricingPortalNotSupported from 'Modules/vehiclePricing/components/PricingPortalNotSupported'

Vue.use(VueRouter);

const doBeforeEnter = (routeTo, routeFrom, next, stockType) => {
    store.dispatch('vehiclePricing/setStockType', stockType)
        .then(() => {
            return store.dispatch('vehiclePricing/loadDealer', routeTo.params.dealerId);
        })
        .then(() => {
            return store.dispatch('vehiclePricing/fetchYmm');
        })
        .then(() => {
            store.dispatch('vehiclePricing/fetchMetrics');
            next();
        })
};

const routes = [
    {
        path: '/stratus/dealer/:dealerId/vehicle-pricing/new',
        name: 'vehicle-pricing-new',
        component: NewVehiclePricing,
        props: true,
        beforeEnter(routeTo, routeFrom, next) {
            const stockType = 'new';
            doBeforeEnter(routeTo, routeFrom, next, stockType);
        }
    },
    {
        path: '/stratus/dealer/:dealerId/vehicle-pricing/used',
        name: 'vehicle-pricing-used',
        component: UsedVehiclePricing,
        props: true,
        beforeEnter(routeTo, routeFrom, next) {
            const stockType = 'used';
            doBeforeEnter(routeTo, routeFrom, next, stockType);
        }
    },
    {
        path: '/stratus/dealer/:dealerId/vehicle-pricing/not-supported',
        name: 'pricing-portal-not-supported',
        component: PricingPortalNotSupported,
        props: true
    }
];

const router = new VueRouter({
    mode: 'history',
    routes
});

export default router;
