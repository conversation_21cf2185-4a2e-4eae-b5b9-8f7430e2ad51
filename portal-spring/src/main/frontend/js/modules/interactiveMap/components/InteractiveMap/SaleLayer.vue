<template>
    <div>
        <div class="d-flex justify-content-between w-100">
            <b-form-checkbox switch size="sm" @input="addOrRemovePins">
                <span class="pointer-cursor" v-text="enablePins ? 'Remove Sales' : 'Show Sales'"/>&nbsp;
                <small>({{ pageMetadata.totalElements }})</small>
            </b-form-checkbox>
            <b-img src="/images/sale_map_marker.svg" width="20" height="22"/>
        </div>
        <vehicle-sale-facets/>
    </div>
</template>

<script>
import {dispatch, get, sync} from 'vuex-pathify';
import VehicleSaleFacets from 'Modules/vehicleSale/components/VehicleSaleSearch/VehicleSaleFacets';

export default {
    name: 'SaleLayer',
    components: {VehicleSaleFacets},
    props: {
        map: {
            type: Object,
            require: true
        }
    },
    data() {
        return {
            layerGroup: L.layerGroup(),
            enablePins: false
        };
    },
    created() {
        dispatch('vehicleSaleSearch/queryModeScroll');
        dispatch('vehicleSaleSearch/pushHistoryEnabled', false);
        this.fetchSourceIncludes = ['id', 'user.firstName', 'user.lastName', 'user.address.geo.location'];
        dispatch('vehicleSaleSearch/loadMetadata');
    },
    computed: {
        pageMetadata: get('vehicleSaleSearch/pageMetadata'),
        searchResults: get('vehicleSaleSearch/searchLoader@data'),
        fetchSourceIncludes: sync('vehicleSaleSearch/fetchSource@includes')
    },
    watch: {
        enablePins(newVal) {
            if (newVal === true) {
                dispatch('vehicleSaleSearch/doSearch');
            }
        },
        searchResults: function () {
            this.initLayers();
        }
    },
    methods: {
        addOrRemovePins() {
            this.map.addLayer(this.layerGroup);
            this.enablePins = !this.enablePins;

            if (this.enablePins) {
                this.initLayers();
            } else {
                this.layerGroup.clearLayers();
            }
        },
        initLayers() {
            const mapIcon = L.icon({
                iconUrl: '/images/sale_map_marker.svg',
                popupAnchor: [0, -10],
                iconSize: [30, 33]
            });

            this.layerGroup.clearLayers();

            if (this.enablePins) {
                const clusterOfMarkers = new L.markerClusterGroup(
                    {
                        maxClusterRadius: 60,
                        disableClusteringAtZoom: 11,
                        iconCreateFunction: function (cluster) {
                            var markers = cluster.getAllChildMarkers();
                            var html = '<div class="d-flex flex-column justify-content-center align-items-center outline-circle sale">' + '<span class="count-circle sale d-flex justify-content-center align-items-center">' + markers.length + '</span></div>';
                            return L.divIcon({html: html, className: 'mycluster', iconSize: L.point(32, 32)});
                        }
                    }
                );

                this.searchResults.forEach((sale) => {
                    const location = _.get(sale, 'user.address.geo.location');
                    if (!_.isNil(location)) {
                        const leafletObject = L.marker([location.lat, location.lon], {icon: mapIcon})
                            .bindPopup(
                                sale.user.firstName + ' ' + sale.user.lastName +
                                '<br/> <strong>ID: </strong>' + sale.id +
                                '<br/> <a href="\/sale/' + sale.id + '">View Sale Details</a>'
                            );

                        clusterOfMarkers.addLayer(leafletObject);
                    }
                });
                this.layerGroup.addLayer(clusterOfMarkers);
            }
        }
    }
};
</script>
<style lang="scss">
.outline-circle.sale {
    background-color: rgba(181, 226, 140, 0.6);

    .count-circle.sale {
        background-color: rgba(110, 204, 57, 0.6);
    }
}
</style>
