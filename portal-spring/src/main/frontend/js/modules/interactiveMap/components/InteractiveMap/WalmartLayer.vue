<template>
    <div>
        <div class="d-flex justify-content-between w-100">
            <b-form-checkbox name="walmarts-check-button" switch size="sm"
                             @input="addOrRemovePins"
            >
                <span class="pointer-cursor" v-text="enablePins ? 'Remove Walmarts' : 'Show Walmarts'"/>&nbsp;
                <small>({{ pageMetadata.totalElements }})</small>
            </b-form-checkbox>
            <b-img src="/images/walmart_map_marker.svg" width="20" height="22"/>
        </div>
        <walmart-facets/>
    </div>
</template>

<script>
import {dispatch, get, sync} from 'vuex-pathify';
import WalmartFacets from 'Modules/walmart/components/Listings/WalmartFacets';

export default {
    name: 'WalmartLayer',
    components: {WalmartFacets},
    props: {
        map: {
            type: Object,
            require: true
        }
    },
    data() {
        return {
            layerGroup: L.layerGroup(),
            enablePins: false
        };
    },
    created() {
        dispatch('walmartSearch/queryModeScroll');
        dispatch('walmartSearch/pushHistoryEnabled', false);
        this.fetchSourceIncludes = ['id', 'name', 'address'];
        dispatch('walmartSearch/loadMetadata');
    },
    computed: {
        pageMetadata: get('walmartSearch/pageMetadata'),
        searchResults: get('walmartSearch/searchLoader@data'),
        fetchSourceIncludes: sync('walmartSearch/fetchSource@includes')
    },
    watch: {
        enablePins(newVal) {
            if (newVal === true) {
                dispatch('walmartSearch/doSearch');
            }
        },
        searchResults: function () {
            this.initLayers();
        }
    },
    methods: {
        addOrRemovePins() {
            this.map.addLayer(this.layerGroup);
            this.enablePins = !this.enablePins;

            if (this.enablePins) {
                this.initLayers();
            } else {
                this.layerGroup.clearLayers();
            }
        },
        initLayers() {
            const mapIcon = L.icon({
                iconUrl: '/images/walmart_map_marker.svg',
                popupAnchor: [0, -10],
                iconSize: [30, 33]
            });

            this.layerGroup.clearLayers();

            if (this.enablePins) {
                const clusterOfMarkers = new L.markerClusterGroup(
                    {
                        maxClusterRadius: 60,
                        disableClusteringAtZoom: 11,
                        iconCreateFunction: function (cluster) {
                            var markers = cluster.getAllChildMarkers();
                            var html = '<div class="circle d-flex flex-column justify-content-center align-items-center outline-circle walmart">' + '<span class="count-circle walmart d-flex justify-content-center align-items-center">' + markers.length + '</span></div>';
                            return L.divIcon({html: html, className: 'mycluster', iconSize: L.point(32, 32)});
                        }
                    }
                );
                this.searchResults.forEach((store) => {
                    const location = _.get(store, 'address.geo.location');
                    if (!_.isNil(location)) {
                        const fullAddress = store.address.street + ', ' + store.address.city + ', ' + store.address.zipCode;
                        const leafletObject = L.marker([location.lat, location.lon], {icon: mapIcon})
                            .bindPopup(
                                '<strong>Store Name: </strong>' + store.name +
                                '<br/> <strong>ID: </strong>' + store.id +
                                '<br/> <strong>Address: </strong>' + fullAddress +
                                '<br/> <a target="_blank" href="\/walmart/' + store.id + '">View Store Details</a>'
                            );

                        clusterOfMarkers.addLayer(leafletObject);
                    }
                });
                this.layerGroup.addLayer(clusterOfMarkers);
            }
        }
    }
};
</script>
<style lang="scss">
.outline-circle.walmart {
    background-color: rgba(240, 194, 12, 0.6);

    .count-circle.walmart {
        background-color: rgb(255, 194, 28);
    }
}
</style>
