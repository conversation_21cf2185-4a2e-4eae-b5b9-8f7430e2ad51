import Vue from 'vue';
import 'es6-promise/auto';
import CarSaverPlugin from '@/lib/CarSaverPlugin';
import VCalendar from 'v-calendar';

import store from './store';
import InteractiveMap from './components/InteractiveMap';
import UniqueId from 'vue-unique-id';

import '../../directives';
import '../../filters';

Vue.use(CarSaverPlugin);
Vue.use(require('vue-moment'));
Vue.use(UniqueId);
Vue.use(VCalendar);

new Vue({
    el: '#wrapper',
    store,
    components: {
        InteractiveMap
    }
});
