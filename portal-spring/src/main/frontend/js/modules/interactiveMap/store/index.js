import Vuex from 'vuex';
import Vue from 'vue';
import pathify from '@/lib/pathify';
import walmartSearch from 'Modules/walmart/store/modules/walmartSearch';
import dealerSearch from 'Modules/dealer/store/modules/dealerSearch';
import vehicleSaleSearch from 'Modules/vehicleSale/store/modules/vehicleSaleSearch';
import userSearch from 'Modules/user/store/modules/userSearch';
import loggedInUser from '@/store/loggedInUser';

const debug = process.env.NODE_ENV !== 'production';

Vue.use(Vuex);

export default new Vuex.Store({
    plugins: [pathify.plugin],
    modules: {
        loggedInUser,
        walmartSearch,
        dealerSearch,
        vehicleSaleSearch,
        userSearch
    },
    strict: debug
});
