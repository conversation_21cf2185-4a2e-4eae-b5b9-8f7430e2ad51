import {make} from "vuex-pathify";
import _ from "lodash";
import searchUtils from "@/api/searchUtils";
import loader from "@/api/loader";

const disableAutoSearchFields = ["name"];
const uriRoot = "/notes";
const initialState = {
    initialLoad: true,
    pageable: searchUtils.parsePageableFromUrl({
        sort: "createdDate,desc",
        page: 1
    }),
    facets: null,
    filters: searchUtils.parseFiltersFromUrl(
        _.get(window, "_CS_NOTE_SEARCH_CRITERIA", null)
    ),
    searchLoader: {
        loader: loader.defaultState(),
        data: []
    },
    pageMetadata: {
        size: 0,
        totalElements: 0,
        totalPages: 0,
        number: 0
    },
    pills: {
        name: {
            enabled: false
        }
    }
};

const mutations = {
    ...make.mutations(initialState),
    ...searchUtils.mutations("notes", disableAutoSearchFields)
};

const actions = {
    ...searchUtils.actions(uriRoot, "Notes Search")
};

export default {
    namespaced: true,
    state: initialState,
    actions,
    mutations
};
