<template>
    <search-page :store="store" :search-index="searchIndex" :exportable="false">
        <notes-search-form slot="searchForm"/>
    </search-page>
</template>
<script>
import SearchPage from "Components/SearchPage/index";
import NotesSearchForm from "Modules/notes/components/NotesSearch/NotesSearchForm/index";

export default {
    name: "NotesSearch",
    components: {
        NotesSearchForm,
        SearchPage
    },

    data() {
        return {
            store: "notes",
            searchIndex: "notes"
        };
    }
};
</script>
