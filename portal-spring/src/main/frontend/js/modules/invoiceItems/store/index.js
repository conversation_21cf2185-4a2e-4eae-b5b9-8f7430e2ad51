import Vuex from 'vuex';
import Vue from 'vue';
import loggedInUser from '@/store/loggedInUser';
import invoiceItemSearch from './modules/invoiceItemSearch';
import storeHelper from '@/store/storeHelper';

const debug = process.env.NODE_ENV !== 'production';

Vue.use(Vuex);

const plugins = storeHelper.plugins('invoiceItemSearch');

export default new Vuex.Store({
    plugins,
    modules: {
        loggedInUser,
        invoiceItemSearch
    },
    strict: debug
});
