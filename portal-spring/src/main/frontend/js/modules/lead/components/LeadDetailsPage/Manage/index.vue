<template>
    <i-box title="Manage">
        <div class="d-flex flex-column justify-content-center align-items-center">
            <b-button v-if="$acl.hasAuthority('create:notes')"
                      variant="outline-secondary"
                      size="sm"
                      block
                      @click="openAddNoteModal"
            >
                <i aria-hidden="true" class="fa fa-pencil-alt fa-fw"/> Add Note
            </b-button>
            <b-button v-if="!deliveryOnly && $acl.hasAuthority('edit:appointment')"
                      :disabled="ifLeadOlderThanSevenDays"
                      variant="outline-secondary"
                      size="sm"
                      block
                      @click="resendSms"
            >
                <i aria-hidden="true" class="fa fa-share fa-fw"/> Resend SMS
            </b-button>
            <b-button v-if="$acl.hasAuthority('edit:appointment')"
                      :disabled="ifLeadOlderThanSevenDays"
                      variant="outline-secondary"
                      size="sm"
                      block
                      @click="sendAdfLead"
            >
                <i aria-hidden="true" class="fa fa-paper-plane fa-fw"/> Send ADF
            </b-button>
            <b-button v-if="isAppointmentAndNotCancelled && $acl.hasAuthority('edit:appointment')"
                      variant="outline-danger"
                      size="sm"
                      block
                      @click="cancelAppointment"
            >
                <i aria-hidden="true" class="fa fa-ban fa-fw"/> Cancel Apt.
            </b-button>
        </div>
    </i-box>
</template>
<script>
import IBox from 'Components/IBox';
import {call, get} from 'vuex-pathify';
import moment from 'moment';
import _ from 'lodash';

export default {
    name: 'Manage',
    components: {IBox},
    computed: {
        lead: get('leadDetails/lead'),
        leadCancelled() {
            return _.get(this.lead, 'cancelled', null);
        },
        ifLeadOlderThanSevenDays() {
            const createdDate = _.get(this.lead, 'createdDate', null);
            const leadCreatedDate = moment(createdDate);
            const now = moment();
            const differenceInDays = now.diff(leadCreatedDate, 'days');

            return differenceInDays > 7;
        },
        isAppointmentAndNotCancelled() {
            return !this.leadCancelled && this.lead.type === null;
        },
        deliveryOnly() {
            return this.lead.dealer.deliveryAvailability === 'DELIVERY_ONLY';
        }
    },
    methods: {
        openAddNoteModal: call('notes/openAddNoteModal'),
        sendSms: call('leadDetails/sendSms'),
        sendAdf: call('leadDetails/sendAdf'),
        cancelApt: call('leadDetails/cancelApt'),
        resendSms() {
            this.sendSms().then(
                this.$toastr.s('Resending SMS!')
            );
        },
        sendAdfLead() {
            this.sendAdf().then(
                this.$toastr.s('Sending ADF!')
            );
        },
        cancelAppointment() {
            this.cancelApt()
                .then(
                    this.$toastr.e('Appointment Cancelled!')
                );
        }
    }
};
</script>
<style lang="scss" scoped>
.disabled {
    cursor: not-allowed;
}
</style>
