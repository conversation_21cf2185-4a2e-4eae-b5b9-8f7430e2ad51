<template>
    <div>
        <facet-group facet-group-label="Dates" facet-group-name="dates">
            <div class="facet-base">
                <div
                    v-b-toggle="'date_facet'"
                    class="facet-base-label"
                    @click="showCollapse = !showCollapse"
                >
                    <div>
                        <i
                            aria-hidden="true"
                            :class="
                                showCollapse
                                    ? 'fa fa-chevron-down'
                                    : 'fa fa-chevron-right'
                            "
                        />
                    </div>

                    <span>Visit Time</span>
                </div>
                <b-collapse id="date_facet">
                    <div class="form-group">
                        <div
                            class="md-form date-filter"
                            style="padding-top: 5px"
                        >
                            <date-range-picker2
                                v-model="visitTime"
                                :masks="{ input: 'YYYY-MM-DD' }"
                                placeholder="Appointment Date"
                                :columns="1"
                                placement="bottom"
                            />
                        </div>
                    </div>
                </b-collapse>
            </div>
        </facet-group>

        <facet-group facet-group-label="Lead" facet-group-name="lead">
            <checkbox-facet
                facet-label="Delivery Status"
                store="leadSearch"
                facet-name="deliveryStatuses"
                filter-name="deliveryStatuses"
            />
            <checkbox-facet
                facet-label="Type"
                store="leadSearch"
                facet-name="types"
                filter-name="types"
            />
            <checkbox-facet
                facet-label="Category"
                store="leadSearch"
                facet-name="leadTypes"
                filter-name="leadTypes"
            />
            <checkbox-facet
                facet-label="Stock Type"
                store="leadSearch"
                facet-name="stockTypes"
                filter-name="stockTypes"
            />
            <checkbox-facet
                facet-label="Make"
                store="leadSearch"
                facet-name="makes"
                filter-name="makes"
            />
            <checkbox-facet
                facet-label="Status"
                store="leadSearch"
                facet-name="statuses"
                filter-name="statuses"
            />
            <checkbox-facet
                facet-label="Source Host"
                store="leadSearch"
                facet-name="leadSourceHosts"
                filter-name="leadSourceHosts"
            />
        </facet-group>

        <facet-group
            facet-group-label="Lead Google Campaigns"
            facet-group-name="lead-utm"
        >
            <checkbox-facet
                facet-label="Sources"
                store="leadSearch"
                facet-name="leadUtmSources"
                filter-name="leadUtmSources"
            />
            <checkbox-facet
                facet-label="Mediums"
                store="leadSearch"
                facet-name="leadUtmMediums"
                filter-name="leadUtmMediums"
            />
            <checkbox-facet
                facet-label="Campaigns"
                store="leadSearch"
                facet-name="leadUtmCampaigns"
                filter-name="leadUtmCampaigns"
            />
            <checkbox-facet
                facet-label="Terms"
                store="leadSearch"
                facet-name="leadUtmTerms"
                filter-name="leadUtmTerms"
            />
            <checkbox-facet
                facet-label="Contents"
                store="leadSearch"
                facet-name="leadUtmContents"
                filter-name="leadUtmContents"
            />
        </facet-group>

        <facet-group facet-group-label="Dealer" facet-group-name="dealer">
            <checkbox-facet
                facet-label="Groups"
                store="leadSearch"
                facet-name="dealerGroups"
                filter-name="dealerGroupIds"
            />
            <checkbox-facet
                facet-label="Dealers"
                store="leadSearch"
                facet-name="dealers"
                filter-name="dealerIds"
            />
            <radio-facet
                facet-label="Certified"
                store="leadSearch"
                facet-name="dealerCertifiedStatus"
                filter-name="dealerCertified"
            />
            <checkbox-facet
                facet-label="Sales Source"
                store="leadSearch"
                facet-name="salesTxSources"
                filter-name="salesTxSources"
            />
            <checkbox-facet
                facet-label="Makes"
                store="leadSearch"
                facet-name="dealerMakes"
                filter-name="dealerMakes"
            />
            <checkbox-facet
                facet-label="States"
                store="leadSearch"
                facet-name="states"
                filter-name="states"
            />
            <checkbox-facet
                facet-label="DMA"
                store="leadSearch"
                facet-name="dmas"
                filter-name="dmaCodes"
            />
            <checkbox-facet
                facet-label="Success Managers"
                store="leadSearch"
                facet-name="successManagers"
                filter-name="successManagerIds"
            />
            <checkbox-facet
                facet-label="Dealer Link Status"
                store="leadSearch"
                facet-name="dealerLinkStatuses"
                filter-name="dealerLinkStatuses"
            />
        </facet-group>

        <facet-group facet-group-label="User" facet-group-name="user">
            <checkbox-facet
                facet-label="Tenant"
                store="leadSearch"
                facet-name="tenants"
                filter-name="tenants"
            />
            <checkbox-facet
                facet-label="Multi-Account"
                store="leadSearch"
                facet-name="userRefTenants"
                filter-name="userRefTenantIds"
            />
            <radio-facet
                facet-label="Top User DMAs"
                store="leadSearch"
                facet-name="topDmas"
                filter-name="topDmas.end"
                :show-count="false"
            />
            <checkbox-facet
                facet-label="Tags"
                store="leadSearch"
                facet-name="userTags"
                filter-name="userTags"
            />
            <checkbox-facet
                facet-label="Cities"
                store="leadSearch"
                facet-name="userCities"
                filter-name="userCities"
            />
            <checkbox-facet
                facet-label="States"
                store="leadSearch"
                facet-name="userStates"
                filter-name="userStates"
            />
            <checkbox-facet
                facet-label="DMA"
                store="leadSearch"
                facet-name="userDmas"
                filter-name="userDmaCodes"
            />
            <checkbox-facet
                facet-label="Source Host"
                store="leadSearch"
                facet-name="userSourceHosts"
                filter-name="userSourceHosts"
            />
        </facet-group>

        <facet-group
            facet-group-label="User Google Campaigns"
            facet-group-name="user-utm"
        >
            <checkbox-facet
                facet-label="Sources"
                store="leadSearch"
                facet-name="userUtmSources"
                filter-name="userUtmSources"
            />
            <checkbox-facet
                facet-label="Mediums"
                store="leadSearch"
                facet-name="userUtmMediums"
                filter-name="userUtmMediums"
            />
            <checkbox-facet
                facet-label="Campaigns"
                store="leadSearch"
                facet-name="userUtmCampaigns"
                filter-name="userUtmCampaigns"
            />
            <checkbox-facet
                facet-label="Terms"
                store="leadSearch"
                facet-name="userUtmTerms"
                filter-name="userUtmTerms"
            />
            <checkbox-facet
                facet-label="Contents"
                store="leadSearch"
                facet-name="userUtmContents"
                filter-name="userUtmContents"
            />
        </facet-group>

        <facet-group facet-group-label="Finance" facet-group-name="finance">
            <radio-facet
                facet-label="Applications"
                store="leadSearch"
                facet-name="financingApplications"
                filter-name="financingApplied"
            />
            <radio-facet
                facet-label="Approvals"
                store="leadSearch"
                facet-name="financingApprovals"
                filter-name="financingApproved"
            />
        </facet-group>

        <facet-group facet-group-label="Carvana" facet-group-name="carvana">
            <checkbox-facet
                facet-label="Status"
                store="leadSearch"
                facet-name="carvanaStatuses"
                filter-name="carvanaStatuses"
            />
        </facet-group>
    </div>
</template>

<script>
import {sync} from 'vuex-pathify';
import CheckboxFacet from 'Components/Facets/CheckboxFacet';
import RadioFacet from 'Components/Facets/RadioFacet';
import FacetBase from 'Components/Facets/FacetBase';
import DateRangePicker2 from 'Components/DateRangePicker2';
import FacetGroup from 'Components/Facets/FacetGroup';

export default {
    name: 'LeadFacets',
    components: {
        FacetGroup,
        DateRangePicker2,
        FacetBase,
        RadioFacet,
        CheckboxFacet
    },
    data() {
        return {
            showCollapse: false
        };
    },
    computed: {
        visitTime: sync('leadSearch/filters@visitTime')
    }
};
</script>
