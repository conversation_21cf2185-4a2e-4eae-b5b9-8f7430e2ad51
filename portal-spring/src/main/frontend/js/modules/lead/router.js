import Vue from 'vue';
import VueRouter from 'vue-router';
import {configureRouter, routerOptions} from '@/lib/routerHelper';
import LeadDetailsPage from 'Modules/lead/components/LeadDetailsPage';
import LeadSearchPage from 'Modules/lead/views/LeadSearchPage';

Vue.use(VueRouter);

const PATH_PREFIX = '/lead';

const routes = [
    {
        path: PATH_PREFIX,
        name: 'lead-search',
        component: LeadSearchPage,
        meta: {
            title: 'CarSaver Portal - Lead Search'
        }
    },
    {
        path: PATH_PREFIX + '/:leadId',
        name: 'lead-details',
        component: LeadDetailsPage,
        meta: {
            title: 'CarSaver Portal - Lead Details'
        },
        props: (route) => {
            return {
                leadId: route.params.leadId
            };
        }
    }
];

const router = new VueRouter({
    mode: 'history',
    routes,
    ...routerOptions
});

configureRouter(router, PATH_PREFIX);

export default router;
