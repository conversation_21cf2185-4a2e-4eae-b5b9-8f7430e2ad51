import Vue from 'vue';
import VueRouter from "vue-router";
import {configureRouter, routerOptions} from "@/lib/routerHelper";
import BusinessUser from "Modules/user/components/BusinessUser";
import BusinessUserDetails from "Modules/user/components/UserDetails/BusinessUser";
import Customer from "Modules/user/components/Customer";
import CustomerDetails from "Modules/user/components/UserDetails/Customer";
import AddUser from "Modules/user/components/AddUser";

Vue.use(VueRouter);

const PATH_PREFIX = '/user';

const routes = [
    {
        path: PATH_PREFIX + '/business-users',
        name: 'business-users',
        component: BusinessUser,
        meta: {
            title: 'CarSaver Portal - Business User Search'
        }
    },
    {
        path: PATH_PREFIX + '/business-users/form',
        name: 'business-user-form',
        component: AddUser,
        meta: {
            title: 'Add Business User'
        }
    },
    {
        path: PATH_PREFIX + '/business-users/:userId',
        name: 'business-user-details',
        component: BusinessUserDetails,
        meta: {
            title: 'Business User Details'
        },
        props: route => {
            return {
                userId: route.params.userId
            };
        }
    },
    {
        path: PATH_PREFIX + '/business-users/:userId/form',
        name: 'business-user-edit-form',
        component: AddUser,
        meta: {
            title: 'Edit Business User'
        },
        props: route => {
            return {
                userId: route.params.userId
            };
        }
    },

    {
        path: PATH_PREFIX + '/customers',
        name: 'customers',
        component: Customer,
        meta: {
            title: 'CarSaver Portal - Customer Search'
        }
    },
    {
        path: PATH_PREFIX + '/customers/form',
        name: 'customer-form',
        component: AddUser,
        meta: {
            title: 'Add Customer'
        }
    },
    {
        path: PATH_PREFIX + '/customers/:userId',
        name: 'customer-details',
        component: CustomerDetails,
        meta: {
            title: 'Customer Details'
        },
        props: route => {
            return {
                userId: route.params.userId
            };
        }
    },
    {
        path: PATH_PREFIX + '/customers/:userId/form',
        name: 'customer-edit-form',
        component: AddUser,
        meta: {
            title: 'Edit Customer'
        },
        props: route => {
            return {
                userId: route.params.userId
            };
        }
    },
];

const router = new VueRouter({
    mode: 'history',
    routes,
    ...routerOptions
});

configureRouter(router, PATH_PREFIX);

export default router;


