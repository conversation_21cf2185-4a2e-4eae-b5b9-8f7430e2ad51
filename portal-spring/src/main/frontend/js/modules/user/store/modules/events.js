import _ from 'lodash';
import {make} from 'vuex-pathify';
import api from '../../../../api';

const state = {
    selectedUser: _.get(window, '_CS_SELECTED_USER', null),
    events: null,
    openAdfModal: false,
    selectedEvent: null
};

const mutations = {
    ...make.mutations(state)
};

const actions = {
    fetchUserEvents({commit, state}, page) {
        const params = _.isNil(page) ? null : {page};

        api.get(`/users/${state.selectedUser.id}/events`, params)
            .then((response) => {
                commit('SET_EVENTS', response.data);
            });
    },
    openAdfModal({commit, state}, key) {
        commit('SET_SELECTED_EVENT', state.events.content[key]);
        commit('SET_OPEN_ADF_MODAL', true);
    }
};

export default {
    namespaced: true,
    state,
    actions,
    mutations
};
