import _ from "lodash";
import {make} from "vuex-pathify";
import loader from "@/api/loader";
import searchUtils from "@/api/searchUtils";

const uriRoot = '/business-users';

const initialState = {
    ...searchUtils.state(),
    initialLoad: true,
    pushHistoryEnabled: true,
    pageable: searchUtils.parsePageableFromUrl({
        sort: "createdDate,desc",
        page: 1
    }),
    facets: null,
    filters: searchUtils.parseFiltersFromUrl(
        _.get(window, "_CS_BUSINESS_USER_SEARCH_CRITERIA", null)
    ),
    searchLoader: {
        loader: loader.defaultState(),
        data: []
    },
    displayFields: [
        "fullName",
        "phoneNumber",
        "email",
        "source.hostname",
        "createdDate",
        "actions"
    ],
    pageMetadata: {
        size: 0,
        totalElements: 0,
        totalPages: 0,
        number: 0
    },
    /**
     * pills schema
     * {
                    "name": {
                        label: 'Name',
                        enabled: false,
                    },
                    "warrantyStatuses": {
                        label: 'Warranty',
                        enabled: true
                    },
                    "topDmas": {
                        type: 'range'
                    }
                }
     *     label: label to use for display
     *     facet: the name of the search facet
     *     enabled: is the pill show be displayed
     *     type: the type of filter i.e. 'range' filter
     * }
     */
    pills: {
        name: {
            enabled: false
        },
        emailOrPhone: {
            enabled: false
        },
        sourceAgentIds: {
            label: "Agents",
            facet: "agents"
        },
        createdDate: {
            enabled: false
        },
        dmaCodes: {
            label: "DMAs",
            facet: "dmas"
        },
        topDmas: {
            type: "range"
        }
    }
};

const mutations = {
    ...make.mutations(initialState),
    ...searchUtils.mutations()
};

const actions = {
    ...make.actions(initialState),
    ...searchUtils.actions(uriRoot, "Business User Search")
};

const getters = {
    ...make.getters(initialState),
    ...searchUtils.getters()
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters
};
