import api from '@/api';
import {make} from 'vuex-pathify';
import _ from 'lodash';

const state = {
    certificateId: null,
    dealer: null,
    open: false,
    vehicle: null,
    loading: true,
    message: ''
};

const mutations = {
    ...make.mutations(state)
};

const actions = {
    ...make.actions(state),

    sendLead({commit, state, rootState}) {
        const dealerId = _.get(state, 'dealer.id');
        const certificateId = _.get(state, 'certificateId');
        const message = _.get(state, 'message');
        const userId = _.get(rootState, 'user.selectedUser.id');
        if (_.isNil(dealerId) || _.isNil(certificateId) || _.isNil(userId)) {
            return false;
        }
        commit('SET_LOADING', true);

        return api.post(`/certificates/${certificateId}/sendLead`, {
            dealerId,
            message,
            userId
        })
            .then(response => {
                commit('SET_LOADING', false);
                commit('SET_OPEN', false);

                return response;
            });
    },

    openSendLeadModal({commit}, {certificateId}) {
        commit('SET_CERTIFICATE_ID', certificateId);
        commit('SET_LOADING', true);
        commit('SET_OPEN', true);

        return api.get(`/certificates/${certificateId}/sendLead`)
            .then(response => {
                const dealer = _.get(response, 'data.dealer');
                const vehicle = _.get(response, 'data.vehicle');

                commit('SET_DEALER', dealer);
                commit('SET_VEHICLE', vehicle);
                commit('SET_LOADING', false);

                return response;
            })
            .catch(response => {
                commit('SET_LOADING', false);
            });
    }
};

export default {
    namespaced: true,
    state,
    actions,
    mutations
};
