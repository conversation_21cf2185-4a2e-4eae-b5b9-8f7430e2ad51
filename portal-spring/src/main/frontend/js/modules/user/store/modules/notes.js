import api from '@/api';
import {make} from 'vuex-pathify';
import _ from 'lodash';

const state = {
    note: '',
    noteId: null,
    visibleToDealer: false,
    open: false,
    submitting: false,
    modified: false,
    notes: []
};

const mutations = {
    ...make.mutations(state)
};

const actions = {
    ...make.actions(state),
    fetchUserNotes({commit, state}, {userId, page}) {
        const params = _.isNil(page) ? null : {page};

        api.get(`/users/${userId}/notes`, params)
            .then((response) => {
                commit('SET_NOTES', response.data);
            });
    },
    openAddNoteModal({commit}) {
        commit('SET_SUBMITTING', false);
        commit('SET_NOTE', '');
        commit('SET_NOTE_ID', null);
        commit('SET_OPEN', true);
    },
    openEditNoteModal({commit, rootState, state}, noteId) {
        commit('SET_SUBMITTING', false);

        const note = _.find(state.notes.content, ['id', noteId]);

        commit('SET_NOTE', note.content);
        commit('SET_NOTE_ID', note.id);

        commit('SET_OPEN', true);
    },
    persistNote({commit, rootState, state}, {userId, visibleToDealer, mentionedUsers, url}) {
        const note = state.note;
        const noteId = state.noteId;

        const data = {
            content: note,
            dealerVisible: visibleToDealer,
            mentionedUsers: mentionedUsers,
            url: url
        };

        commit('SET_SUBMITTING', true);

        const handleResponse = () => {
            commit('SET_NOTE', '');
            commit('SET_NOTE_ID', null);
            commit('SET_SUBMITTING', false);
            commit('SET_OPEN', false);
        };
        const handleReject = response => {
            commit('SET_SUBMITTING', false);
            commit('SET_OPEN', true);
            return response;
        };

        if (_.isNil(noteId)) {
            return api.post(`/users/${userId}/notes`, data)
                .then(handleResponse)
                .catch(handleReject);
        } else {
            return api.put(`/users/${userId}/notes/${noteId}`, {
                modified: true,
                ...data
            })
                .then(handleResponse)
                .catch(handleReject);
        }
    }
};

export default {
    namespaced: true,
    state,
    actions,
    mutations
};
