<template>
    <div>
        <multiselect
            v-model="tags"
            :multiple="true"
            track-by="id"
            :options="options"
            :custom-label="tagLabel"
            placeholder="Pick tags"
            @select="addTagToUser"
            @remove="removeTagFromUser"
        />
    </div>
</template>

<style lang="scss" scoped>
* {
    font-family: 'Lato', 'Avenir', sans-serif;
}
</style>

<script>
import Multiselect from 'vue-multiselect';
import 'vue-multiselect/dist/vue-multiselect.min.css';
import {dispatch, sync} from 'vuex-pathify';
import api from '@/api';

export default {
    name: 'UserTag',
    components: {Multiselect},
    data() {
        return {
            options: []
        };
    },
    computed: {
        tags: sync('user/selectedUser@tags')
    },
    mounted() {
        api.get('/users/tags')
            .then(response => {
                this.options = response.data;
            });
    },
    methods: {
        addTagToUser(selectedOption) {
            dispatch('user/addTag', selectedOption.id);
        },
        removeTagFromUser(selectedOption) {
            dispatch('user/removeTag', selectedOption.id);
        },
        tagLabel(option) {
            return `${option.name}`;
        }
    }
};
</script>
