<template>
    <b-tabs lazy>
        <b-tab key="activity" class="bg-muted" title="Activity" active>
            <div class="row pt-3">
                <div class="col-lg-6">
                    <notes-list :user-id="user.id"/>

                    <jobs/>
                </div>
                <div class="col-lg-6">
                    <events/>
                </div>
            </div>
        </b-tab>
        <b-tab key="dealerLinks" title="Dealer Links">
            <div class="row pt-3">
                <div class="col-lg-12">
                    <dealer-links/>
                </div>
            </div>
        </b-tab>
        <b-tab key="leads" title="Leads">
            <leads/>
        </b-tab>
        <b-tab key="vehicles" title="Vehicles">
            <user-vehicles/>
        </b-tab>
        <b-tab key="sales" title="Sales">
            <vehicle-sale v-if="$acl.hasAuthority('read:sale')"/>

            <warranty v-if="$acl.hasAuthority('read:warranty')"/>

            <gift-cards/>

            <vouchers v-if="$acl.hasAuthority('read:voucher')"/>
        </b-tab>
        <b-tab key="scheduledCalls" title="Scheduled Calls">
            <scheduled-calls/>
        </b-tab>
        <b-tab
            v-if="showPermissionsTab()"
            key="permissions"
            title="Permissions"
        >
            <user-permissions :read-only="!canSetPermissions()"/>
        </b-tab>
        <b-tab key="tickets" title="Support Tickets">
            <user-freshdesk-tickets/>
        </b-tab>
    </b-tabs>
</template>
<script>
import {get} from "vuex-pathify";
import NotesList from "Modules/user/components/Notes/List";
import Jobs from "Modules/user/components/Jobs";
import Events from "Modules/user/components/Events";
import SmsResponses from "Modules/user/components/SmsResponses";
import DealerLinks from "Modules/user/components/DealerLinks";
import Leads from "Modules/user/components/Leads";
import UserVehicles from "Modules/user/components/Vehicles";
import VehicleSale from "Modules/user/components/VehicleSale";
import Warranty from "Modules/user/components/Warranty";
import Vouchers from "Modules/user/components/Vouchers";
import ScheduledCalls from "Modules/user/components/ScheduledCalls";
import UserPermissions from "Modules/user/components/UserPermissions";
import UserFreshdeskTickets from "Modules/user/components/UserFreshdeskTickets";
import GiftCards from "Modules/user/components/GiftCards/index";

export default {
    name: "UserDetailsTabs",
    components: {
        GiftCards,
        UserFreshdeskTickets,
        UserPermissions,
        ScheduledCalls,
        Events,
        Vouchers,
        Warranty,
        VehicleSale,
        UserVehicles,
        Leads,
        DealerLinks,
        SmsResponses,
        Jobs,
        NotesList
    },
    computed: {
        user: get("user/selectedUser"),
        isAdminUser() {
            const userType = _.get(this.user, "type", "");

            return userType.toLowerCase() === "admin";
        }
    },
    methods: {
        canSetPermissions() {
            return (
                this.isAdminUser &&
                this.$acl.hasAnyAuthorities(["edit:permissions"])
            );
        },
        canReadPermissions() {
            return (
                this.isAdminUser &&
                this.$acl.hasAnyAuthorities(["read:permissions"])
            );
        },
        showPermissionsTab() {
            return this.canReadPermissions() || this.canSetPermissions();
        }
    }
};
</script>
