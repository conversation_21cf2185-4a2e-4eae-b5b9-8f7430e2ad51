<template>
    <b-modal id="add-note-modal" v-model="open" :title="modalTitle"
             :busy="submitting"
    >
        <mentions :note="this.note" @noteFromMention="mentionNoteReceived"/>
        <div slot="modal-footer">
            <b-button variant="primary" @click="saveNote({visibleToDealer: false})">
                Comment Internally
            </b-button>
            <b-button variant="info" @click="saveNote({visibleToDealer: true})">
                Share with Dealers
            </b-button>
        </div>
    </b-modal>
</template>

<script>
import _ from 'lodash';
import {call, get, sync} from 'vuex-pathify';
import Mentions from 'Components/Mentions'

export default {
    components: {
        Mentions
    },
    data() {
        return {
            mentionedUsers: []
        }
    },
    props: {
        userId: {
            type: String,
            required: true
        }
    },
    computed: {
        submitting: get('notes/submitting'),
        open: sync('notes/open'),
        note: sync('notes/note'),
        page: sync('notes/note@number'),
        noteId: get('notes/noteId'),
        visibleToDealer: get('notes/visibleToDealer'),
        isEditing() {
            return !_.isNil(this.noteId);
        },
        modalTitle() {
            if (this.isEditing) {
                return 'Edit Note';
            } else {
                return 'Add Note';
            }
        }
    },
    methods: {
        addNote: call('notes/addNote'),
        fetchUserNotes: call('notes/fetchUserNotes'),
        persistNote: call('notes/persistNote'),
        mentionNoteReceived(note, mentionedUsers) {
            this.note = note
            this.mentionedUsers = mentionedUsers
        },
        saveNote(options) { // options = { visibleToDealer: <>}
            const visibleToDealer = _.get(options, 'visibleToDealer');
            const toastrMsg = {
                internalOnly: 'Note updated successfully and is only visible internally!',
                visibleToDealer: 'Note updated successfully and shared with Dealers'
            };
            const msg = visibleToDealer ? toastrMsg.visibleToDealer : toastrMsg.internalOnly;

            if (_.isNil(this.note) || _.trim(this.note) === '') {
                this.$toastr.Add({
                    title: 'Error Adding Note',
                    msg: 'Invalid Note',
                    type: 'error'
                });
                return;
            }

            this.persistNote({
                userId: this.userId,
                visibleToDealer: visibleToDealer,
                mentionedUsers: this.mentionedUsers,
                url: window.location.href
            })
                .then(() => {
                    if (_.isNil(this.noteId)) {
                        // Editing Note
                        this.fetchUserNotes({userId: this.userId, page: this.page + 1});
                        this.$toastr.Add({
                            title: 'Note Updated',
                            msg
                        });
                    } else {
                        // New Note
                        this.fetchUserNotes({userId: this.userId});
                        this.$toastr.Add({
                            title: 'Note Added',
                            msg
                        });
                    }
                })
                .catch((error) => {
                    this.$toastr.Add({
                        title: 'Error Adding Note',
                        msg: JSON.stringify(error),
                        type: 'error'
                    });
                });
        }
    }
};
</script>
