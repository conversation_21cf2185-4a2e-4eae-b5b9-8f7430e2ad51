<template>
    <div>
        <user-banner :is-details="false" :is-list="false"/>
        <user-form
            v-if="user === null"
            :is-create="true"
            :is-stratus="false"
        />
        <user-form
            v-else
            :is-create="false"
            :is-stratus="false"
        />
    </div>
</template>

<script>
import {get} from 'vuex-pathify';
import UserBanner from './UserBanner';
import UserForm from './UserForm';

export default {
    name: "AddUser",
    components: {
        UserBanner,
        UserForm
    },
    computed: {
        user: get('user/selectedUser')
    }
}
</script>
