<template>
    <content-loader
        :height="300"
        :width="540"
        primary-color="#f3f3f3"
        secondary-color="#ecebeb"
    >
        <rect x="10" y="10" rx="0"
              ry="0" width="510" height="16"
        />
        <rect x="10" y="40" rx="0"
              ry="0" width="240" height="270"
        />
        <rect x="280" y="70" rx="0"
              ry="0" width="240" height="34"
        />
        <rect x="280" y="120" rx="0"
              ry="0" width="240" height="34"
        />
    </content-loader>
</template>

<script>
import {ContentLoader} from 'vue-content-loader';

export default {
    components: {
        ContentLoader
    }
};
</script>
