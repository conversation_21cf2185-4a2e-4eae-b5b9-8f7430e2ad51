<template>
    <div v-if="isDealerUser">
        <b-card header="Atlas Preferences" class="mt-1">
            <div v-if="loading">
                <b-spinner/>
            </div>
            <div v-else>
                <div class="d-flex justify-content-between pb-2">
                    <strong>Theme</strong>
                    <b-dropdown :text="themeDisplay" size="sm">
                        <b-dropdown-item @click="handleSelect('CarSaver')"
                        >CarSaver (default)
                        </b-dropdown-item
                        >
                        <b-dropdown-item @click="handleSelect('Nissan')"
                        >Nissan
                        </b-dropdown-item
                        >
                    </b-dropdown>
                </div>

                <div class="d-flex justify-content-between">
                    <strong>Last Selected Dealer</strong>
                    <b-link
                        :href="
                            `/dealer/${form.userPreferences.atlasPreferences.lastSelectedDealerId}`
                        "
                    >{{ dealerName }}
                    </b-link
                    >
                </div>
            </div>
        </b-card>
    </div>
</template>

<script>
import {get} from "vuex-pathify";
import _ from "lodash";
import api from "@/api";

export default {
    name: "UserPreferences",
    data() {
        return {
            form: {
                userPreferences: {
                    atlasPreferences: {
                        theme: null,
                        lastSelectedDealerId: null
                    }
                }
            },
            loading: false,
            dealerName: null
        };
    },
    computed: {
        userId: get("user/selectedUser@id"),
        isDealerUser: get("user/selectedUser@dealerUser"),
        themeDisplay() {
            return _.capitalize(
                _.get(this.form, "userPreferences.atlasPreferences.theme", "")
            );
        }
    },
    mounted() {
        this.fetchUserPreferences();
    },
    methods: {
        fetchUserPreferences() {
            this.loading = true;

            api.get(`/business-users/dealer-user/${this.userId}/preferences`)
                .then(response => {
                    this.form.userPreferences.atlasPreferences.theme =
                        response.data.theme;
                    this.form.userPreferences.atlasPreferences.lastSelectedDealerId =
                        response.data.lastSelectedDealerId;
                    this.dealerName = response.data.dealerName;
                    this.loading = false;
                })
                .catch(error => {
                    console.error(error);
                    this.loading = false;
                });
        },
        handleSelect(option) {
            this.form.userPreferences.atlasPreferences.theme = option.toLowerCase();
            this.updatePrefs();
        },
        updatePrefs() {
            api.patch(
                `/business-users/dealer-user/${this.userId}/update`,
                this.form
            );
        }
    }
};
</script>
