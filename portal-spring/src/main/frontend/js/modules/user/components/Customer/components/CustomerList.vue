<template>
    <div>
        <div v-if="isLoading">
            <table-content-loader />
        </div>

        <div v-else>
            <div class="ibox">
                <div class="ibox-title">
                    <h5>Search Results</h5>
                    <div class="ibox-tools">
                        <b-button v-b-modal.table-column-config size="sm">
                            <i aria-hidden="true" class="fas fa-cog" />
                            <span class="list-action">Show / Hide Columns</span>
                        </b-button>
                        <table-column-selector
                            id="table-column-config"
                            v-model="displayFields"
                            :fields="fields"
                        />

                        <div
                            v-if="$acl.hasAuthority('export:user')"
                            class="d-inline-block"
                        >
                            <b-button v-b-modal.export-column-config size="sm">
                                <i
                                    aria-hidden="true"
                                    class="fas fa-file-export"
                                />
                                <span class="list-action">Export</span>
                            </b-button>
                            <export-column-selector
                                id="export-column-config"
                                v-model="exportFields"
                                :display-fields="displayFields"
                                :fields="fields"
                                @export="doExport"
                            />
                            <b-button
                                size="sm"
                                @click.prevent="doExportNissanKpi"
                            >
                                <i
                                    aria-hidden="true"
                                    class="fas fa-file-export"
                                />
                                <span class="list-action">Export Nissan KPI</span>
                            </b-button>
                        </div>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="result-count">
                        <pagination
                            :show-pager="false"
                            :page-metadata="pageMetadata"
                            :page="page"
                            @page-changed="pageChanged"
                        />
                    </div>

                    <b-table
                        responsive
                        striped
                        hover
                        bordered
                        :fields="fieldsForDisplay()"
                        :items="searchResults"
                        tbody-tr-class="rowClass"
                        :no-local-sorting="true"
                        :sort-by.sync="sortBy"
                        :sort-desc.sync="sortDesc"
                        :no-sort-reset="true"
                        @sort-changed="sortChanged"
                        @row-clicked="openQuickViewModal"
                    >
                        <template v-slot:cell(tenant.name)="data">
                            <div class="d-flex">
                                <b-badge
                                    v-if="data.item.tenant"
                                    pill
                                    variant="primary"
                                >
                                    {{ data.item.tenant.name }}
                                </b-badge>
                                <span
                                    v-if="isNotEmpty(data.item.userRefs)"
                                    class="pl-1"
                                >
                                    <b-badge
                                        :id="
                                            `cross-tenant-tooltip-${data.item.id}`
                                        "
                                        variant="light"
                                    >
                                        +{{ data.item.userRefs.length }}
                                    </b-badge>
                                    <b-tooltip
                                        v-if="isNotEmpty(data.item.userRefs)"
                                        :target="
                                            `cross-tenant-tooltip-${data.item.id}`
                                        "
                                        triggers="hover"
                                    >
                                        <div
                                            v-for="userRef in data.item
                                                .userRefs"
                                        >
                                            <span v-if="userRef.tenant">{{
                                                userRef.tenant.name
                                            }}</span>
                                            <span v-else>Unknown</span>
                                        </div>
                                    </b-tooltip>
                                </span>
                            </div>
                        </template>
                        <template v-slot:cell(crossTenants)="data" />
                        <template v-slot:cell(fullName)="data">
                            <b-link :href="`/user/customers/${data.item.id}`">
                                {{ data.item.firstName }}
                                {{ data.item.lastName }}
                            </b-link>
                        </template>
                        <template v-slot:cell(jobTitle)="data">
                            <span v-if="data.item.jobTitle">
                                {{ data.item.jobTitle.name }}
                            </span>
                        </template>
                        <template v-slot:cell(address)="data">
                            <div class="text-nowrap">
                                {{ data.item.address.street }},
                                <span v-if="data.item.address.street2">
                                    {{ data.item.address.street2 }},
                                </span>
                            </div>
                            <div class="text-nowrap">
                                {{
                                    data.item.address.city
                                }}<span
                                    v-if="
                                        data.item.address.city &&
                                            (data.item.address.stateCode ||
                                                data.item.address.zipCode)
                                    "
                                >,</span>
                                {{ data.item.address.stateCode }}
                                {{ data.item.address.zipCode }}
                            </div>
                        </template>
                        <template v-slot:cell(enabled)="data">
                            <b-badge
                                v-if="data.item.enabled"
                                pill
                                variant="success"
                            >
                                TRUE
                            </b-badge>
                            <b-badge
                                v-else
                                pill
                                variant="danger"
                            >
                                FALSE
                            </b-badge>
                        </template>
                        <template v-slot:cell(rankedStage)="data">
                            <user-ranked-stage-tool-tip
                                v-if="
                                    (data.item.rankedStage && data.item.stage) &&
                                        data.item.rankedStage !== 8
                                "
                                :id="data.item.id"
                                :rank="data.item.rankedStage"
                                :stage="data.item.stage"
                            />
                        </template>
                        <template v-slot:cell(repeatBuyer)="data">
                            <span class="label">{{
                                data.item.repeatBuyer
                            }}</span>
                        </template>
                        <template v-slot:cell(vehicleSaleCount)="data">
                            <span>{{ data.item.vehicleSaleCount }}</span>
                        </template>
                        <template v-slot:cell(heatScore)="data">
                            <span v-if="data.item.heatScore">
                                <i
                                    v-for="n in data.item.heatScore"
                                    aria-hidden="true"
                                    class="fa fa-fire"
                                />
                            </span>
                        </template>
                        <template v-slot:cell(signUpVehicle)="data">
                            <span v-if="data.item.signUpVehicle">
                                {{ data.item.signUpVehicle.stockType }}
                                {{ data.item.signUpVehicle.year }}
                                {{ data.item.signUpVehicle.make }}
                                {{ data.item.signUpVehicle.model }}
                            </span>
                        </template>
                        <template v-slot:cell(phoneNumber)="data">
                            <span class="text-nowrap">{{
                                data.item.phoneNumber | phone
                            }}</span>
                        </template>
                        <template v-slot:cell(phoneNumberExt)="data">
                            <span class="text-nowrap">{{ data.item.phoneNumberExt }}</span>
                        </template>
                        <template v-slot:cell(createdDate)="data">
                            <span>{{
                                data.item.createdDate
                                    | formatDateTime("MM/DD/YYYY h:mm a z")
                            }}</span>
                        </template>
                        <template v-slot:cell(lastModifiedDate)="data">
                            <span>{{
                                data.item.lastModifiedDate
                                    | formatDateTime("MM/DD/YYYY h:mm a z")
                            }}</span>
                        </template>
                        <template v-slot:cell(lastLoginAt)="data">
                            <span>{{
                                data.item.lastLoginAt
                                    | formatDateTime("MM/DD/YYYY h:mm a z")
                            }}</span>
                        </template>
                        <template v-slot:cell(tags)="data">
                            <span v-if="data.item.tags">{{
                                data.item.tags.join(", ")
                            }}</span>
                        </template>
                        <template v-slot:cell(actions)="data">
                            <b-button-group>
                                <b-button
                                    variant="white"
                                    size="xs"
                                    :href="`/user/customers/${data.item.id}`"
                                >
                                    View
                                </b-button>
                                <b-button
                                    v-if="$acl.hasAuthority('edit:user')"
                                    variant="white"
                                    size="xs"
                                    :href="`/user/customers/${data.item.id}?form`"
                                >
                                    Edit
                                </b-button>
                            </b-button-group>
                        </template>
                    </b-table>

                    <div class="pagination-wrapper">
                        <pagination
                            :show-totals="false"
                            :page-metadata="pageMetadata"
                            :page="page"
                            @page-changed="pageChanged"
                        />
                    </div>
                </div>
            </div>

            <right-side-modal
                v-if="selectedRowData"
                id="quick-view-modal"
                title="User Quick View"
                ok-only="true"
                header-bg-variant="success"
            >
                <div class="border bg-white">
                    <div
                        class="d-flex justify-content-between align-items-center border-bottom p-2"
                    >
                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">Tenant</span>
                            <b-badge
                                v-if="selectedRowData.tenant"
                                pill
                                variant="primary"
                            >
                                {{ selectedRowData.tenant.name }}
                            </b-badge>
                        </div>

                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">ID</span>
                            <span>{{ selectedRowData.id }}</span>
                        </div>
                    </div>

                    <div
                        class="d-flex justify-content-between align-items-center border-bottom p-2"
                    >
                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">Name</span>
                            <span><b-link :href="`/user/customers/${selectedRowData.id}`">{{
                                                                                              selectedRowData.firstName
                                                                                          }}
                                {{ selectedRowData.lastName }}</b-link></span>
                        </div>

                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">Email</span>
                            <b-link :href="`mailto: ${selectedRowData.email}`">
                                {{ selectedRowData.email }}
                            </b-link>
                        </div>

                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">User Since</span>
                            <span>{{
                                selectedRowData.createdDate
                                    | moment("MM/DD/YYYY hh:mm a")
                            }}</span>
                        </div>
                    </div>

                    <div class="d-flex align-items-center border-bottom p-2">
                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">Address</span>
                            <span>{{ selectedRowData.address.street }},
                                <span v-if="selectedRowData.address.street2">
                                    {{ selectedRowData.address.street2 }},
                                </span>
                                {{ selectedRowData.address.city }},
                                {{ selectedRowData.address.stateCode }}
                                {{ selectedRowData.address.zipCode }}</span>
                        </div>
                    </div>

                    <div
                        class="d-flex justify-content-between align-items-center border-bottom p-2"
                    >
                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">Phone Number</span>
                            <b-link
                                :href="`Tel: ${selectedRowData.phoneNumber}`"
                            >
                                {{ selectedRowData.phoneNumber | phone }}
                            </b-link>
                        </div>

                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">Time Zone</span>
                            <span>{{ selectedRowData.timeZone }}</span>
                        </div>

                        <div class="d-flex flex-column align-items-center">
                            <span class="font-weight-bold">SMS Enabled</span>
                            <span
                                :class="[
                                    selectedRowData.smsEnabled === true
                                        ? 'label label-success'
                                        : 'label label-danger'
                                ]"
                            >{{ selectedRowData.smsEnabled }}</span>
                        </div>
                    </div>

                    <div
                        class="d-flex justify-content-between align-items-center border-bottom p-2"
                    >
                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">User Type</span>
                            <span>{{ selectedRowData.type }}</span>
                        </div>

                        <div class="d-flex flex-column align-items-center">
                            <span class="font-weight-bold">Preferred Language</span>
                            <span
                                class="label"
                                v-text="
                                    selectedRowData.locale === 'en'
                                        ? 'English'
                                        : 'Spanish'
                                "
                            >{{ selectedRowData.locale }}</span>
                        </div>

                        <div class="d-flex flex-column align-items-center">
                            <span class="font-weight-bold">User Enabled</span>
                            <span
                                :class="[
                                    selectedRowData.enabled === true
                                        ? 'label label-success'
                                        : 'label label-danger'
                                ]"
                            >{{ selectedRowData.enabled }}</span>
                        </div>
                    </div>

                    <div
                        v-if="selectedRowData.address.dma"
                        class="d-flex justify-content-between align-items-center border-bottom p-2"
                    >
                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">DMA Name</span>
                            <span>{{ selectedRowData.address.dma.name }}</span>
                        </div>

                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">DMA Code</span>
                            <span>{{ selectedRowData.address.dma.code }}</span>
                        </div>

                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">DMA Rank</span>
                            <span>{{ selectedRowData.address.dma.rank }}</span>
                        </div>
                    </div>

                    <div
                        class="d-flex justify-content-between align-items-center border-bottom p-2"
                    >
                        <div
                            v-if="selectedRowData.heatScore"
                            class="d-flex flex-column"
                        >
                            <span class="font-weight-bold">Heat Score</span>
                            <div class="d-flex w-100">
                                <i
                                    v-for="n in selectedRowData.heatScore"
                                    aria-hidden="true"
                                    class="fa fa-fire"
                                />
                            </div>
                        </div>

                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">Tags</span>
                            <div class="d-flex justify-content-between">
                                <span
                                    v-for="tag in selectedRowData.tags"
                                    class="label"
                                >{{ tag }}</span>
                            </div>
                        </div>
                        <div
                            v-if="selectedRowData.stage"
                            class="d-flex flex-column"
                        >
                            <span class="font-weight-bold">Stage</span>
                            <span>{{ selectedRowData.stage }}</span>
                        </div>
                    </div>
                    <div
                        class="d-flex justify-content-between align-items-center p-2"
                    >
                        <div
                            v-if="selectedRowData.source"
                            class="d-flex flex-column"
                        >
                            <span class="font-weight-bold">Source</span>
                            <span>{{ selectedRowData.source.hostname }}</span>
                        </div>

                        <div class="d-flex flex-column">
                            <span class="font-weight-bold">Last Modified Date</span>
                            <span>{{
                                selectedRowData.lastModifiedDate
                                    | moment("MM/DD/YYYY hh:mm a")
                            }}</span>
                        </div>
                    </div>
                </div>
            </right-side-modal>
        </div>
    </div>
</template>

<style>
.result-count {
    padding: 10px 0;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}

.rowClass {
    cursor: pointer;
}
</style>

<script>
import { call, get, sync } from 'vuex-pathify';
import Pagination from 'Components/Pagination';
import TableContentLoader from 'Components/TableContentLoader';
import RightSideModal from 'Components/RightSideModal/index';
import TableColumnSelector from 'Components/TableColumnSelector/index';
import searchPageMixin from '@/mixins/searchPageMixin';
import ExportColumnSelector from 'Components/ExportColumnSelector';
import UserRankedStageToolTip from 'Modules/user/components/UserSearch/UserList/UserRankedStageToolTip';

export default {
    name: 'CustomerList',
    components: {
        UserRankedStageToolTip,
        ExportColumnSelector,
        TableColumnSelector,
        RightSideModal,
        TableContentLoader,
        Pagination
    },
    mixins: [searchPageMixin],
    data () {
        return {
            modalOpen: false,
            fields: [
                {
                    key: 'tenant.name',
                    label: 'Tenant',
                    sortable: false,
                    hideable: false
                },
                'firstName',
                'lastName',
                {
                    key: 'fullName',
                    label: 'Name',
                    sortable: false,
                    exportable: false
                },
                'enabled',
                {
                    key: 'rankedStage',
                    label: 'Stage',
                    value: 'rankedStage',
                    sortable: true
                },
                {
                    key: 'repeatBuyer',
                    label: 'Repeat Buyer'
                },
                {
                    key: 'vehicleSaleCount',
                    label: 'Number of Sales'
                },
                {
                    key: 'heatScore',
                    label: 'Heat Score',
                    sortable: true
                },
                {
                    key: 'phoneNumber',
                    label: 'Phone Number'
                },
                {
                    key: 'email',
                    label: 'Email',
                    sortable: false
                },
                'smsEnabled',
                'timeZone',
                'address',
                {
                    key: 'address.street',
                    label: 'Street'
                },
                {
                    key: 'address.city',
                    label: 'City'
                },
                {
                    key: 'address.stateCode',
                    label: 'State'
                },
                {
                    key: 'address.zipCode',
                    label: 'Zip',
                    sortable: true
                },
                {
                    key: 'address.dma.name',
                    label: 'DMA Name'
                },
                {
                    key: 'address.dma.rank',
                    label: 'DMA Rank'
                },
                {
                    key: 'address.dma.code',
                    label: 'DMA Code'
                },
                {
                    key: 'source.hostname',
                    label: 'Source Host',
                    sortable: true
                },
                'tags',
                {
                    key: 'createdDate',
                    sortable: true
                },
                {
                    key: 'lastModifiedDate',
                    sortable: true
                },
                {
                    key: 'lastLoginAt',
                    sortable: true
                },
                {
                    key: 'signUpVehicle',
                    label: 'Sign Up Vehicle (VOI)',
                    sortable: false,
                    exportable: false
                },
                {
                    key: 'signUpVehicle.vin',
                    label: 'VOI VIN',
                    sortable: false
                },
                {
                    key: 'signUpVehicle.stockType',
                    label: 'VOI StockType',
                    sortable: false
                },
                {
                    key: 'signUpVehicle.year',
                    label: 'VOI Year',
                    sortable: false
                },
                {
                    key: 'signUpVehicle.make',
                    label: 'VOI Make',
                    sortable: false
                },
                {
                    key: 'signUpVehicle.model',
                    label: 'VOI Model',
                    sortable: false
                },
                {
                    key: 'traits.preQualificationsCount',
                    label: 'Pre-quals'
                },
                {
                    key: 'traits.tradeInsCount',
                    label: 'Trade-ins'
                },
                {
                    key: 'traits.vehiclesInGarageCount',
                    label: 'Garage Vehicles'
                },
                {
                    key: 'traits.leadsCount',
                    label: 'Leads'
                },
                {
                    key: 'traits.financeAppsSubmittedCount',
                    label: 'Applications'
                },
                {
                    key: 'traits.salesCount',
                    label: 'Sales'
                },
                {
                    key: 'actions',
                    label: 'Actions',
                    sortable: false,
                    hideable: false,
                    exportable: false
                }
            ],
            selectedRowData: null
        };
    },

    computed: {
        searchResults: get('customerSearch/searchLoader@data'),
        pageMetadata: get('customerSearch/pageMetadata'),
        isLoading: get('customerSearch/<EMAIL>'),
        page: sync('customerSearch/pageable@page'),
        sort: sync('customerSearch/pageable@sort'),
        displayFields: sync('customerSearch/displayFields'),
        exportFields: sync('customerSearch/exportFields')
    },

    watch: {
        page () {
            this.doPageLoad();
        },
        sort () {
            this.doSort();
        }
    },

    mounted () {
        this.doPageLoad();
    },

    methods: {
        doExport: call('customerSearch/doExport'),
        doExportNissanKpi: call('customerSearch/doNissanKpiExport'),
        doPageLoad: call('customerSearch/doPageLoad'),
        doSort: call('customerSearch/doSort'),
        isNotEmpty (items) {
            return !_.isEmpty(items);
        },
        openQuickViewModal (item) {
            if (this.selectedRowData === item && this.modalOpen) {
                this.$bvModal.hide('quick-view-modal');
                this.modalOpen = false;
            } else {
                this.selectedRowData = item;

                this.$nextTick(() => {
                    this.$bvModal.show('quick-view-modal');
                    this.modalOpen = true;
                });
            }
        }
    }
};
</script>
