<template>
    <div>
        <validation-observer ref="observer" v-slot="{ passes }">
            <b-container class="mt-2">
                <b-form novalidate @submit.prevent="passes(onSubmit)">
                    <i-box :title="title" :collapsible="false">
                        <b-row>
                            <b-col>
                                <b-alert show variant="warning">
                                    Selecting the appropriate tenant is
                                    important. This is who will own this user.
                                    For regular users you will want to select
                                    CarSaver, while for Dealer or CarSaver
                                    Employees you will want to select <PERSON> as
                                    the Tenant. If you are adding an Upgrade
                                    user please select the appropriate Upgrade
                                    Program.
                                </b-alert>
                            </b-col>
                        </b-row>
                        <b-row class="justify-content-end">
                            <b-col>
                                <div class="required-msg text-right">
                                    * indicates a required field
                                </div>
                            </b-col>
                        </b-row>
                        <b-row class="justify-content-center">
                            <b-col class="pt-2">
                                <b-row class="justify-content-center">
                                    <b-col lg="5">
                                        <b-select-validate
                                            v-model="form.tenantId"
                                            :disabled="disableTenantDropDown"
                                            label="Tenant *"
                                            name="tenant"
                                            value-field="id"
                                            text-field="name"
                                            rules="required"
                                            :options="tenants"
                                        />
                                    </b-col>
                                    <b-col lg="5" class="text-center">
                                        <b-form-group label="Account Type">
                                            <b-form-radio-group
                                                v-model="form.type"
                                                @change="handleUserTypeChange"
                                            >
                                                <b-form-radio value="user">
                                                    User
                                                </b-form-radio>
                                                <b-form-radio value="dealer">
                                                    Dealer
                                                </b-form-radio>
                                                <b-form-radio value="admin">
                                                    Employee
                                                </b-form-radio>
                                                <b-form-radio value="program">
                                                    Program
                                                </b-form-radio>
                                            </b-form-radio-group>
                                        </b-form-group>
                                    </b-col>
                                </b-row>

                                <b-row class="justify-content-center">
                                    <b-col lg="5">
                                        <b-input-validate
                                            v-model="form.firstName"
                                            label="First Name *"
                                            name="firstName"
                                            rules="required"
                                        />
                                    </b-col>
                                    <b-col lg="5">
                                        <b-input-validate
                                            v-model="form.lastName"
                                            label="Last Name *"
                                            name="lastName"
                                            rules="required"
                                        />
                                    </b-col>
                                </b-row>

                                <b-row class="justify-content-center">
                                    <b-col lg="4">
                                        <b-input-validate
                                            v-model="form.email"
                                            label="Email *"
                                            name="email"
                                            rules="required|email"
                                        />
                                    </b-col>
                                    <b-col lg="3">
                                        <b-input-validate
                                            v-model="form.phoneNumber"
                                            v-mask="'(###) ###-####'"
                                            label="Phone Number *"
                                            name="phoneNumber"
                                            type="tel"
                                            :rules="{
                                                required: true,
                                                regex: phoneNumberRegex(),
                                            }"
                                        />
                                    </b-col>
                                    <b-col lg="3">
                                        <b-input-validate
                                            v-model="form.phoneNumberExt"
                                            label="Extension"
                                            name="phoneNumberExt"
                                            type="number"
                                            :rules="{
                                                required: false,
                                            }"
                                        />
                                    </b-col>
                                </b-row>

                                <b-row
                                    v-if="!isDealerUser"
                                    class="justify-content-center"
                                >
                                    <b-col lg="5">
                                        <b-form-group label="Address *">
                                            <validation-provider
                                                v-slot="validationContext"
                                                vid="address"
                                                name="Address"
                                                rules="required"
                                            >
                                                <b-form-input
                                                    ref="autocomplete"
                                                    v-model="form.address"
                                                    autocomplete="off"
                                                    name="address"
                                                    :required="true"
                                                    :state="
                                                        getValidationState(
                                                            validationContext
                                                        )
                                                    "
                                                />
                                                <b-form-invalid-feedback
                                                    id="inputLiveFeedback"
                                                >
                                                    {{
                                                        validationContext
                                                            .errors[0]
                                                    }}
                                                </b-form-invalid-feedback>
                                            </validation-provider>
                                        </b-form-group>
                                    </b-col>
                                    <b-col lg="5">
                                        <b-form-group label="Address 2">
                                            <b-form-input
                                                v-model="form.address2"
                                            />
                                        </b-form-group>
                                    </b-col>
                                </b-row>

                                <b-row
                                    v-if="!isDealerUser"
                                    class="justify-content-center"
                                >
                                    <b-col lg="5">
                                        <b-input-validate
                                            v-model="form.city"
                                            label="City *"
                                            name="city"
                                            :rules="
                                                !isDealerUser ? 'required' : ''
                                            "
                                        />
                                    </b-col>
                                    <b-col lg="2">
                                        <b-select-validate
                                            v-model="form.stateCode"
                                            label="State *"
                                            name="stateCode"
                                            :rules="
                                                !isDealerUser ? 'required' : ''
                                            "
                                            :options="stateCodes"
                                        />
                                    </b-col>
                                    <b-col lg="3">
                                        <b-input-validate
                                            v-model="form.zipCode"
                                            label="Zip Code *"
                                            name="zipCode"
                                            :rules="{
                                                required: !isDealerUser,
                                                regex: /^(\d{5})$/,
                                            }"
                                        />
                                    </b-col>
                                </b-row>

                                <b-row class="justify-content-center">
                                    <b-col lg="3">
                                        <b-form-group label="SMS Enabled">
                                            <b-form-checkbox
                                                v-model="form.smsAllowed"
                                                switch
                                            >
                                                <i
                                                    v-b-popover.hover="
                                                        'Allows SMS communication from CarSaver platform.'
                                                    "
                                                    class="fa fa-question-circle"
                                                    aria-hidden="true"
                                                />
                                            </b-form-checkbox>
                                        </b-form-group>
                                    </b-col>
                                    <b-col lg="4">
                                        <b-select-validate
                                            v-model="form.locale"
                                            label="Locale"
                                            name="locale"
                                            :options="localeOptions"
                                        />
                                    </b-col>
                                </b-row>
                            </b-col>

                            <b-col
                                lg="4"
                                class="pt-2"
                                :class="{
                                    'd-none': isBasicUser,
                                }"
                            >
                                <b-card
                                    v-if="isDealerUser"
                                    header="Dealer User"
                                >
                                    <b-form-group label="Internal SMS Enabled">
                                        <b-form-checkbox
                                            v-model="form.internalSms"
                                            switch
                                        >
                                            <small
                                            >Must be enabled in order for a
                                                dealer user to receive SMS for
                                                leads.</small
                                            >
                                        </b-form-checkbox>
                                    </b-form-group>
                                    <b-form-group label="CRM ID">
                                        <b-form-input v-model="form.crmId"/>
                                    </b-form-group>
                                    <b-form-group label="Spanish Speaking">
                                        <b-form-checkbox
                                            v-model="form.spanishSpeaking"
                                            switch
                                        >
                                            <small
                                            >Allows dealer user to accept
                                                leads for Spanish speaking
                                                customers.</small
                                            >
                                        </b-form-checkbox>
                                    </b-form-group>
                                </b-card>

                                <b-card
                                    v-if="isAdminUser"
                                    header="Admin User"
                                    class="mt-2"
                                >
                                    <b-form-group label="Employee Pin">
                                        <b-input-validate
                                            v-model="form.pin"
                                            name="pin"
                                            :rules="{
                                                regex: /^(\d{4}|\d{5})$/,
                                            }"
                                        />
                                    </b-form-group>
                                </b-card>

                                <b-card
                                    v-if="isDealerUser || isAdminUser"
                                    header="Job Title"
                                    class="mt-2"
                                >
                                    <b-form-select v-model="form.jobTitleId">
                                        <template v-slot:first>
                                            <option :value="null" disabled>
                                                Select job title
                                            </option>
                                        </template>
                                        <option
                                            v-for="option in jobTitles"
                                            :value="option.id"
                                        >
                                            {{ option.title }}
                                        </option>
                                    </b-form-select>
                                </b-card>

                                <b-card
                                    v-if="isProgramUser"
                                    header="Select Programs"
                                    class="mt-2"
                                >
                                    <b-form-group label="Programs">
                                        <b-form-select
                                            v-model="form.programs"
                                            autocomplete="false"
                                            name="programsGroup"
                                            placeholder="Programs"
                                            multiple
                                            size="10"
                                            select-size="20"
                                        >
                                            <option
                                                v-for="option in programs"
                                                :key="option.id"
                                                :value="option.id"
                                            >
                                                {{ option.name }}
                                            </option>
                                        </b-form-select>
                                    </b-form-group>
                                </b-card>
                            </b-col>
                        </b-row>

                        <b-row class="justify-content-center mt-3">
                            <b-col lg="12" class="text-center">
                                <div>
                                    <b-button
                                        size="lg"
                                        type="submit"
                                        variant="primary"
                                        class="mr-2"
                                        :disabled="disabled"
                                        @submit="onSubmit()"
                                    >
                                        {{
                                            disabled ? "Submitting..." : "Save"
                                        }}
                                    </b-button>
                                    <b-button
                                        size="lg"
                                        variant="danger"
                                        @click="onCancel()"
                                    >
                                        Cancel
                                    </b-button>
                                </div>
                            </b-col>
                        </b-row>
                    </i-box>
                </b-form>
            </b-container>
        </validation-observer>
    </div>
</template>

<script>
import api from "@/api";
import IBox from "Components/IBox";
import UserBanner from "Modules/user/components/UserBanner";
import BSelectValidate from "Components/FormValidation/BSelectValidate";
import BInputValidate from "Components/FormValidation/BInputValidate";
import veeValidateUtils from "@/api/veeValidateUtils";
import _ from "lodash";
import {mask} from "vue-the-mask";

export default {
    name: "UserForm",
    components: {BInputValidate, BSelectValidate, IBox, UserBanner},
    directives: {mask},
    props: {
        isCreate: {
            type: Boolean,
            required: true,
        },
        userSearchEmail: {
            type: String,
            required: false,
        },
        dealerUserId: {
            type: String,
            required: false,
        },
        dealerId: {
            type: String,
            required: false,
        },
    },
    data() {
        return {
            dealerJobTitles: [],
            adminJobTitles: [],
            tenants: [],
            programs: [],
            autocomplete: null,
            stateCodes: [],
            disabled: false,
            isAddressValid: null,
            selectedUser: _.get(window, "_CS_SELECTED_USER"),
            localeOptions: [
                {text: "English", value: "en"},
                {text: "Spanish", value: "es"},
            ],
            form: {
                type: "user",
                firstName: null,
                lastName: null,
                email: null,
                phoneNumber: null,
                phoneNumberExt: null,
                address: null,
                address2: null,
                city: null,
                stateCode: null,
                zipCode: null,
                smsAllowed: null,
                locale: null,
                tenantId: null,

                // dealer only
                internalSms: true,
                crmId: null,
                spanishSpeaking: null,

                // admin only
                pin: null,

                // admin and dealer
                jobTitleId: null,

                // program only
                programs: [],

                // need to send this when updating a user for validation checks
                userId: null,
            },
        };
    },
    computed: {
        isDealerUser() {
            return this.form.type === "dealer";
        },
        isAdminUser() {
            return this.form.type === "admin";
        },
        isBasicUser() {
            return this.form.type === "user";
        },
        isProgramUser() {
            return this.form.type === "program";
        },
        jobTitles() {
            return this.isDealerUser
                ? this.dealerJobTitles
                : this.adminJobTitles;
        },
        title() {
            return this.isCreate ? "Add User" : "Update User";
        },
        disableTenantDropDown() {
            return (
                !this.isCreate ||
                this.isDealerUser ||
                this.isAdminUser ||
                this.isProgramUser
            );
        },
    },
    mounted() {
        if (window.google === undefined) {
            this.$gmapApiPromiseLazy()
                .then(() => {
                    this.setAutocomplete();
                    this.setFormAddressValues();
                })
                .catch((error) => {
                    console.error(error);
                });
        } else {
            this.setAutocomplete();
            this.setFormAddressValues();
        }
    },
    created() {
        const userId = this.setUserId();

        if (!this.isCreate && this.selectedUser) {
            this.form = {
                ...this.selectedUser,
            };
        }

        api.get("/users/form-data", {userId})
            .then((response) => {
                this.adminJobTitles = response.data.adminJobTitles;
                this.dealerJobTitles = response.data.dealerJobTitles;
                this.stateCodes = response.data.stateCodes;
                this.dealerPermissions = response.data.dealerPermissions;
                this.tenants = response.data.tenants;
                this.programs = response.data.programs;
                this.form.programs = response.data.existingUserProgramIds;

                if (!_.isNil(this.dealerId)) {
                    this.form.type = "dealer";
                    this.handleUserTypeChange("dealer");
                }
            })
            .catch((error) => {
                console.error(error);
            });

        if (!_.isNil(this.userSearchEmail)) {
            this.form.email = this.userSearchEmail;
        }

        if (!_.isNil(this.dealerUserId)) {
            api.get(`/users/${this.dealerUserId}`)
                .then((response) => {
                    this.form = response.data;
                })
                .catch((error) => {
                    console.error(error);
                });
        }
    },
    methods: {
        onSubmit() {
            this.disabled = true;
            const userType = this.form.type;
            const userId = this.setUserId();
            const url = this.setUrl(userType, this.isCreate, userId);

            if (userType === "user") {
                this.doesAddressExist().finally(() => {
                    if (this.isAddressValid) {
                        this.shouldSaveUser(url, userId);
                    }
                });
            } else if (
                userType === "dealer" ||
                userType === "admin" ||
                userType === "program"
            ) {
                this.shouldSaveUser(url, userId);
            }
        },
        doesAddressExist() {
            const address = {
                street1: this.form.address,
                street2: this.form.address2,
                city: this.form.city,
                state: this.form.stateCode,
                zipCode: this.form.zipCode,
            };
            return api
                .post("/users/validate-address", address)
                .then((response) => {
                    const addressComponent = response.data.results[0];
                    const { streetNumber, route, city, state, zipCode, route_long } =
                        this.addressComponentValues(addressComponent);
                    this.form.address = `${streetNumber} ${route}`;
                    const address_long = `${streetNumber} ${route_long}`;
                    this.form.city = city;
                    this.form.stateCode = state;
                    this.form.zipCode = zipCode;

                    if (this.form.address !== address.street1) {
                         if (address_long !== address.street1) {
                            this.form.address = '';
                            this.$toastr.e('Physical address was not found.');
                            this.disabled = false;
                            this.isAddressValid = false;
                        } else {
                            this.form.address = address_long;
                            this.isAddressValid = true;
                        }
                    } else {
                        this.isAddressValid = true;
                    }
                })
                .catch((error) => {
                    this.form.address = "";
                    this.form.city = "";
                    this.form.stateCode = null;
                    this.form.zipCode = "";
                    this.$toastr.e("Physical address was not found.");
                    this.disabled = false;
                    this.isAddressValid = false;
                });
        },
        onCancel() {
            this.$emit("cancel");
            if (this.isCreate && _.isNil(this.selectedUser)) {
                if (window.location.href.indexOf("/business-users") > -1) {
                    return (window.location = "/user/business-users");
                }
                return (window.location = "/user/customers");
            }

            if (!this.isCreate && this.selectedUser) {
                return (window.location = `/user/${this.selectedUser.id}`);
            }
        },
        cleanPhoneNumber() {
            if (!_.isNil(this.form.phoneNumber)) {
                this.form.phoneNumber = _.replace(
                    this.form.phoneNumber,
                    /[^0-9]+/g,
                    ""
                );
            }
        },
        phoneNumberRegex() {
            return !this.disabled
                ? /^((\(\d{3}\) ?)|(\d{3}-))?\d{3}-\d{4}/
                : "";
        },
        redirectToUserDetails(userId) {
            const userType = this.form.type;

            if (userType === "admin" || userType === "dealer") {
                if (!_.isNil(userId)) {
                    return (window.location = `/user/business-users/${userId}`);
                }
                return (window.location = "/user/business-users");
            } else {
                if (!_.isNil(userId)) {
                    return (window.location = `/user/customers/${userId}`);
                }
                return (window.location = "/user/customers");
            }
        },
        handleUserTypeChange(type) {
            const portalTenant = _.find(
                this.tenants,
                (tenant) => tenant.name === "Portal"
            );
            const portalTenantId = _.get(portalTenant, "id", null);
            if (type === "dealer" || type === "admin" || type === "program") {
                if (!_.isNil(portalTenantId)) {
                    this.form.tenantId = portalTenantId;
                }
            } else {
                if (this.form.tenantId === portalTenantId) {
                    this.form.tenantId = null;
                }
            }
        },
        handleError(error) {
            this.disabled = false;
            this.$toastr.e("Please check form errors!");
            this.$refs.observer.setErrors(
                veeValidateUtils.convertErrorToObserverErrors(error)
            );
        },
        isValidUserAddressError(error) {
            const errorMessage = "user address not found";
            return errorMessage !== error.response.data.message;
        },
        createUser(url) {
            api.post(url, this.form)
                .then((response) => {
                    console.log("success");
                    this.$emit("done", response.data);
                    if (_.isNil(this.dealerId)) {
                        this.redirectToUserDetails(response.data.id);
                    }
                    console.log("response: ", response);
                })
                .catch((error) => {
                    console.log("error", error.response.data.message);
                    this.handleError(error);
                });
        },
        updateUser(url, userId) {
            api.patch(url, this.form)
                .then(() => {
                    this.$emit("done", userId);

                    if (_.isNil(this.dealerId)) {
                        this.redirectToUserDetails(userId);
                    }
                })
                .catch((error) => {
                    this.handleError(error);
                });
        },
        getGoogleMapsAddressField(place, fieldName, type = 'short') {
            for (var i = 0; i < place.address_components.length; i++) {
                for (
                    var j = 0;
                    j < place.address_components[i].types.length;
                    j++
                ) {
                    var addressType = place.address_components[i].types[j];
                    if (addressType === fieldName) {
                         if (type == 'long') {
                            return place.address_components[i].long_name;
                        }
                        return place.address_components[i].short_name;
                    }
                }
            }

            return "";
        },
        setAutocomplete() {
            this.autocomplete = new google.maps.places.Autocomplete(
                this.$refs.autocomplete.$el,
                {
                    types: ["address"],
                    componentRestrictions: {
                        country: "us",
                    },
                }
            );
        },
        addressComponentValues(place) {
            const streetNumber = this.getGoogleMapsAddressField(
                place,
                "street_number"
            );
            const route = this.getGoogleMapsAddressField(place, "route");
            const route_long = this.getGoogleMapsAddressField(place, 'route', 'long');
            let locality = this.getGoogleMapsAddressField(place, "locality");
            if (!locality) {
                locality = this.getGoogleMapsAddressField(place, "sublocality");
            }
            const city = locality;
            const state = this.getGoogleMapsAddressField(
                place,
                "administrative_area_level_1"
            );
            const zipCode = this.getGoogleMapsAddressField(
                place,
                "postal_code"
            );
            return {streetNumber, route, city, state, zipCode, route_long};
        },
        setFormAddressValues() {
            this.autocomplete.addListener("place_changed", () => {
                const place = this.autocomplete.getPlace();
                const { streetNumber, route, city, state, zipCode, route_long } =
                    this.addressComponentValues(place);

                this.form.address = `${streetNumber} ${route}`;
                this.form.city = city;
                this.form.stateCode = state;
                this.form.zipCode = zipCode;
            });
        },
        setUserId() {
            let result;
            if (this.selectedUser) {
                result = this.selectedUser.id;
                this.form.userId = result;
            } else if (this.dealerUserId) {
                result = this.dealerUserId;
            }
            return result;
        },
        setUrl(userType, isCreate, userId) {
            let result;
            if (isCreate) {
                if (userType === "user") {
                    result = "/users?type=user";
                } else if (userType === "admin") {
                    result = "/business-users?type=admin";
                } else if (userType === "dealer") {
                    result = `/business-users?type=dealer&dealerId=${this.dealerId}`;
                } else if (userType === "program") {
                    result = "/business-users?type=program";
                }
            } else {
                if (userType === "user") {
                    result = `/users/${userId}?type=user`;
                } else if (userType === "admin") {
                    result = `/business-users/${userId}?type=admin`;
                } else if (userType === "dealer") {
                    result = `/business-users/${userId}?type=dealer&dealerId=${this.dealerId}`;
                } else if (userType === "program") {
                    result = `/business-users/${userId}?type=program`;
                }
            }

            return result;
        },
        getValidationState({errors, passed}) {
            return errors[0] ? false : passed ? true : null;
        },
        shouldSaveUser(url, userId) {
            this.cleanPhoneNumber();
            this.isCreate ? this.createUser(url) : this.updateUser(url, userId);
        },
    },
};
</script>
