<template>
    <div>
        <business-user-banner :is-list="true" :is-details="false"/>

        <div class="container-fluid">
            <business-user-search/>
            <div class="row">
                <div class="col-12">
                    <business-user-list/>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import BusinessUserBanner from "./components/BusinessUserBanner";
import BusinessUserSearch from "./components/BusinessUserSearch";
import BusinessUserList from "./components/BusinessUserList";

export default {
    name: "BusinessUser",
    components: {
        BusinessUserSearch,
        BusinessUserBanner,
        BusinessUserList
    }
}
</script>
