<template>
    <tr>
        <td>
            <b-button variant="primary" size="xs" :href="`/lead/${lead.id}`">
                View
            </b-button>
        </td>
        <td v-if="lead.visitTime">
            {{ lead.visitTime | moment('MM/DD/YYYY h:mm a') }} {{ lead.dealer.timezone }}
        </td>
        <td v-if="lead.type">
            {{ lead.createdDate | formatDateTime('MM/DD/YYYY h:mm a z', lead.dealer.timezone) }}
        </td>
        <td v-if="lead.type">
            {{ lead.type }}
        </td>
        <td>
            <b-link :href="`/dealer/${lead.dealer.id}`">
                {{ lead.dealer.name }}
            </b-link>
        </td>
        <td>
            <span v-if="lead.programManager">{{ lead.programManager.name }}</span>
            <span v-else>-</span>
        </td>
    </tr>
</template>
<script>
export default {
    props: ['lead']
};
</script>
