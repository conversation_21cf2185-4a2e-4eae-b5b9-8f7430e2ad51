<template>
    <div class="mb-1">
        <b-button
            size="sm"
            block
            class="header-btn"
            @click="viewLog()"
        >
            <i aria-hidden="true" class="fa fa-satellite-dish"/>
            API Logs
        </b-button>

        <b-modal
            id="api-log-modal"
            size="xl"
            header-bg-variant="primary"
            title="API Log"
            ok-only
        >
            <b-container fluid>
                <div :disabled="loading?'disabled':''">
                    <b-form-group label="Select Service Provider" class="d-flex">
                        <b-form-radio-group
                            v-model="serviceProvider"
                        >
                            <b-form-radio name="offerlogixSelect" value="offerlogix">
                                Offerlogix
                            </b-form-radio>
                            <b-form-radio name="finservSelect" value="finserv">
                                Finserv
                            </b-form-radio>
                            <b-form-radio name="finservSelect" value="atc">
                                ATC
                            </b-form-radio>
                            <b-form-radio name="blackbook" value="blackbook">
                                Blackbook
                            </b-form-radio>
                        </b-form-radio-group>
                    </b-form-group>
                    <b-button
                        size="sm"
                        variant="primary"
                        block
                        class="header-btn"
                        @click="loadApiLogs()"
                    >
                        <i aria-hidden="true" class="fa fa-satellite-dish"/>
                        Search
                    </b-button>
                </div>

                <div v-if="loading" class="text-center">
                    <h1>
                        Loading
                        &nbsp;
                        <b-spinner label="Spinning"/>
                    </h1>
                </div>

                <div v-else>
                    <div v-if="isLogEmpty">
                        <h2>Click the search button to fetch User's api logs by service provider.</h2>
                    </div>

                    <div v-else>
                        <b-table stacked="true" striped hover responsive
                                 bordered :fields="fields" :items="apiLogs"
                                 :no-local-sorting="true"
                        />
                    </div>
                </div>
            </b-container>
        </b-modal>
    </div>
</template>

<script>
import {call, get} from 'vuex-pathify';
import LoadingBlock from 'Modules/onBoarding/components/LoadingBlock';

import _ from 'lodash';

export default {
    name: 'UserApiLog',
    components: {LoadingBlock},
    data() {
        return {
            loading: false,
            serviceProvider: 'offerlogix',
            fields: [
                {
                    key: 'caller',
                    label: 'Caller'
                },
                {
                    key: 'requestBody',
                    label: 'Request Body'
                },
                {
                    key: 'requestParams',
                    label: 'Request Params'
                },
                {
                    key: 'requestURL',
                    label: 'Request URL'
                },
                {
                    key: 'responseStatus',
                    label: 'Response Status'
                },
                {
                    key: 'responseBody',
                    label: 'Response Body'
                },
                {
                    key: 'timestamp',
                    label: 'Timestamp'
                },
            ]
        };
    },
    computed: {
        selectedUser: get("user/selectedUser"),
        apiLogs: get('user/apiLogs'),
        isLogEmpty() {
            return _.isEmpty(this.apiLogs);
        }
    },
    methods: {
        callLoadApiLogs: call("user/loadApiLogs"),
        viewLog() {
            this.$bvModal.show('api-log-modal');
        },
        loadApiLogs() {
            const userId = this.selectedUser.id;
            const serviceProvider = this.serviceProvider;

            this.loading = true;
            this.callLoadApiLogs({userId, serviceProvider})
                .then(() => this.loading = false);
        },
    }
};
</script>
<style lang="scss">
#audit-log-modal {
    .modal-title {
        font-size: 20px;
    }
}
</style>
