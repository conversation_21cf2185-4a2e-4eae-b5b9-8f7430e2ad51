<template>
    <b-modal
        id="vehicleSaleModal"
        ref="modal"
        title="Create Vehicle Sale"
        @show="setWalmartId"
        @hidden="resetModal"
        @ok="handleOk"
    >
        <validation-observer ref="observer" v-slot="{ passes }">
            <div>
                <div v-if="submitting" class="d-flex flex-column justify-content-center align-items-center">
                    <b-spinner class="mb-2"/>
                    <span>Submitting Form</span>
                </div>

                <div v-else class="d-flex mb-2">
                    <span>Please fill out any additional information.</span>
                </div>

                <b-form novalidate @submit.prevent="passes(handleSubmit)">
                    <b-form-group label="Customer *" label-for="userName">
                        <b-form-input
                            id="userName"
                            :value="user.firstName + ' ' + user.lastName"
                            name="userName"
                            disabled
                        />
                    </b-form-group>
                    <input required type="hidden" name="userId"
                           :value="user.id"
                    >

                    <b-form-group v-if="dealerLink" label="Dealer *" label-for="dealer">
                        <b-form-input
                            id="dealer"
                            :value="dealerLink.dealer.name"
                            name="dealerName"
                            disabled
                        />
                    </b-form-group>
                    <input v-if="dealerLink" required type="hidden"
                           name="dealerId"
                           :value="dealerLink.dealer.id"
                    >

                    <b-input-validate
                        v-model="form.walmartStoreId"
                        label="Walmart Store"
                        name="walmartStoreId"
                        rules="numeric"
                    />

                    <b-input-validate
                        v-model="form.vin"
                        label="VIN"
                        :input-size="'md'"
                        name="vin"
                        :rules="{regex: /^[a-zA-Z0-9]{17}$/}"
                    />
                </b-form>

                <div class="d-flex mt-2">
                    <span>If you do not have any additional information clicking "Ok" will still create a vehicle sale.</span>
                </div>
            </div>
        </validation-observer>
    </b-modal>
</template>
<script>
import {get} from 'vuex-pathify';
import BInputValidate from 'Components/FormValidation/BInputValidate';
import api from '@/api';
import dealerLinksMixin from '@/mixins/dealerLinksMixin';
import veeValidateUtils from '@/api/veeValidateUtils';

export default {
    name: 'VehicleSaleModal',
    components: {BInputValidate},
    mixins: [dealerLinksMixin],
    props: {
        dealerLink: {
            type: Object,
            required: false,
            default: null
        }
    },
    data() {
        return {
            form: {
                userId: '',
                dealerId: '',
                walmartStoreId: null,
                vin: null
            },
            submitting: false
        };
    },
    mounted() {
        this.setWalmartId();
    },
    computed: {
        user: get('user/selectedUser')
    },
    methods: {
        setWalmartId() {
            if (this.user.walmartStoreId != null) {
                this.form.walmartStoreId = this.user.walmartStoreId;
            }
        },
        resetModal() {
            this.form.walmartStoreId = null;
            this.form.vin = null;
        },
        handleOk(bvModalEvt) {
            bvModalEvt.preventDefault();
            this.handleSubmit();
        },
        closeModal() {
            this.$bvModal.hide('vehicleSaleModal');
        },
        handleSubmit() {
            this.submitting = true;
            this.form.userId = this.user.id;
            this.form.dealerId = this.dealerLink.dealer.id;

            api.post('/sale/', this.form)
                .then(response => {
                    this.submitting = false;
                    this.$toastr.s('Sale Created!');
                    this.updateDealerLinkStatus('SOLD');
                    this.closeModal();
                })
                .catch(error => {
                    this.submitting = false;
                    console.error(error);
                    this.$toastr.e('Internal Server Error');
                    this.$refs.observer.setErrors(veeValidateUtils.convertErrorToObserverErrors(error));
                });
        }
    }
};
</script>
