<template>
    <b-modal v-model="open" :title="modalTitle" :busy="submitting"
             @ok.prevent="saveNewPassword"
    >
        <label for="newPassword">New Password</label>
        <b-form-input id="newPassword" v-model="newPassword" type="password"
                      @keyup.enter="saveNewPassword"
        />
        <label for="confirmPassword">Confirm Password</label>
        <b-form-input id="confirmPassword" v-model="confirmPassword" type="password"
                      @keyup.enter="saveNewPassword"
        />
    </b-modal>
</template>

<script>
import _ from 'lodash';
import {call, get, sync} from 'vuex-pathify';

export default {
    name: 'UpdatePasswordModal',
    data() {
        return {
            confirmPassword: ''
        };
    },

    computed: {
        selectedUser: get('user/selectedUser'),
        submitting: get('updatePassword/submitting'),
        open: sync('updatePassword/open'),
        newPassword: sync('updatePassword/newPassword'),

        modalTitle() {
            return `Change ${this.selectedUser.firstName} ${this.selectedUser.lastName}'s Password`;
        }
    },

    methods: {
        updatePassword: call('updatePassword/updatePassword'),
        saveNewPassword() {
            if (this.newPassword === '' || this.newPassword !== this.confirmPassword) {
                this.$toastr.Add({
                    title: 'Error Updating Password',
                    msg: 'Passwords do not match',
                    type: 'error'
                });
                return;
            }

            this.updatePassword()
                .then((response) => {
                    const type = _.get(response, 'data.type');
                    const message = _.get(response, 'data.message');

                    this.$toastr.Add({
                        title: 'Password Updated',
                        msg: message,
                        type: _.toLower(type)
                    });
                })
                .catch((error) => {
                    this.$toastr.Add({
                        title: 'Error Updating Password',
                        msg: JSON.stringify(error)
                    });
                });
        }
    }
};
</script>
