<template>
    <div class="dealer-associations">
        <admin-user-dealers v-if="isAdminUser" :user-id="userId"/>
        <dealer-user-dealers v-if="isDealerUser" :user-id="userId"/>
    </div>
</template>

<script>
import AdminUserDealers from "./AdminUserDealers";
import DealerUserDealers from "./DealerUserDealers";
import _ from "lodash";
import {get} from "vuex-pathify";

export default {
    name: "DealerAssociations",
    components: {AdminUserDealers, DealerUserDealers},
    computed: {
        user: get("user/selectedUser"),
        userId: get("user/selectedUser@id"),
        isAdminUser() {
            return _.lowerCase(_.get(this.user, "type", "")) === "admin";
        },
        isDealerUser() {
            return _.lowerCase(_.get(this.user, "type", "")) === "dealer";
        }
    }
};
</script>
