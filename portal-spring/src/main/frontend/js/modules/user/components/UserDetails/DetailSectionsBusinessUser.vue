<template>
    <div>
        <notes-list :user-id="user.id"/>

        <sms-responses v-if="isDealerUser"/>

        <i-box v-if="showPermissionsTab()" title="Permissions">
            <user-permissions :read-only="!canSetPermissions()"/>
        </i-box>

        <events/>
    </div>
</template>
<script>
import {get} from "vuex-pathify";
import NotesList from "Modules/user/components/Notes/List";
import Events from "Modules/user/components/Events";
import UserPermissions from "Modules/user/components/UserPermissions";
import IBox from "Components/IBox";
import SmsResponses from "Modules/user/components/SmsResponses";

export default {
    name: "DetailSectionsBusinessUser",
    components: {
        SmsResponses,
        UserPermissions,
        Events,
        NotesList,
        IBox
    },
    computed: {
        user: get("user/selectedUser"),
        isAdminUser() {
            const userType = _.get(this.user, "type", "");

            return userType.toLowerCase() === "admin";
        },
        isDealerUser() {
            const type = _.get(this.user, "type", "");

            return _.lowerCase(type) === "dealer";
        }
    },
    methods: {
        canSetPermissions() {
            return (
                this.isAdminUser &&
                this.$acl.hasAnyAuthorities(["edit:permissions"])
            );
        },
        canReadPermissions() {
            return (
                this.isAdminUser &&
                this.$acl.hasAnyAuthorities(["read:permissions"])
            );
        },
        showPermissionsTab() {
            return (
                this.isAdminUser &&
                (this.canReadPermissions() || this.canSetPermissions())
            );
        }
    }
};
</script>
