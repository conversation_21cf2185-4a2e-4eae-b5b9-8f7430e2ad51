<template>
    <i-box title="Marketing Materials">
        <multiselect
            v-model="marketingMaterials"
            :multiple="true"
            :options="options"
            placeholder="Pick Marketing Materials"
            @select="addMarketingMaterial"
            @remove="removeMarketingMaterial"
        />
    </i-box>
</template>

<style lang="scss" scoped>
* {
    font-family: 'Lato', 'Avenir', sans-serif;
}
</style>

<script>
import Multiselect from 'vue-multiselect';
import 'vue-multiselect/dist/vue-multiselect.min.css';
import {call, sync} from 'vuex-pathify';
import IBox from 'Components/IBox';

export default {
    components: {IBox, Multiselect},
    data() {
        return {
            options: [
                'External Vehicle',
                'Internal Vehicle',
                'Vestibule Signs',
                'Point Of Purchase',
                'Fourth Wall'
            ]
        };
    },
    computed: {
        marketingMaterials: sync('walmartDetails/selectedWalmart@marketingMaterials')
    },
    methods: {
        addMarketingMaterial: call('walmartDetails/addMarketingMaterial'),
        removeMarketingMaterial: call('walmartDetails/removeMarketingMaterial')
    }
};
</script>
