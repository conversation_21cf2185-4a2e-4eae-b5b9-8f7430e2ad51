<template>
    <i-box :title="'User Sign Ups'" :description="'Users that physically signed up at this Walmart store'">
        <h1 class="text-center">
            {{ count }}
        </h1>
        <span class="label label-primary pull-left">last 30 days</span>
    </i-box>
</template>

<script>
import api from '@/api';
import IBox from 'Components/IBox';

export default {
    name: 'UsersByHostname',

    components: {IBox},

    props: {
        walmartStoreId: {
            type: Number,
            required: true
        }
    },

    data() {
        return {
            count: 0
        };
    },

    computed: {
        hostName() {
            return `wm${this.walmartStoreId}.carsaver.com`;
        }
    },

    mounted() {
        api.get('/walmarts/users-by-hostname', {hostname: this.hostName})
            .then(response => {
                this.count = response.data;
            })
            .catch(error => {
                console.error(error);
            });
    }
};
</script>
