<template>
    <b-form>
        <b-container v-if="submitting" class="h-100">
            <b-row>
                <b-col cols="12">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <h3>Submitting Form </h3>
                        <div class="d-flex">
                            <b-spinner type="grow" label="Spinning"/>
                            <b-spinner type="grow" label="Spinning"/>
                            <b-spinner type="grow" label="Spinning"/>
                        </div>
                    </div>
                </b-col>
            </b-row>
        </b-container>
        <b-container v-else class="pt-3">
            <b-row>
                <b-col cols="12">
                    <b-form-group label="Delivery Date">
                        <v-date-picker
                            v-model="form.deliveryDate"
                            :popover="datePickerProps.popover"
                            :input-props="datePickerProps.input"
                            :columns="$screens({default: 1})"
                        />
                    </b-form-group>
                </b-col>
            </b-row>
            <b-row>
                <b-col cols="12">
                    <b-form-group label="Removal Date">
                        <v-date-picker
                            v-model="form.removalDate"
                            :min-date="vehicleDisplay.deliveryDate"
                            :popover="datePickerProps.popover"
                            :input-props="datePickerProps.input"
                            :columns="$screens({default: 1})"
                        />
                    </b-form-group>
                </b-col>
            </b-row>
            <b-row>
                <b-col cols="6">
                    <b-button variant="danger" block @click="resetForm">
                        Clear
                    </b-button>
                </b-col>
                <b-col cols="6">
                    <b-button variant="success" block @click="submitEdit()">
                        Submit
                    </b-button>
                </b-col>
            </b-row>
        </b-container>
    </b-form>
</template>
<script>
import api from '../../../../api';
import {call} from 'vuex-pathify';
import moment from 'moment';

export default {
    props: {
        vehicleDisplay: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            form: {
                removalDate: null,
                deliveryDate: new Date(this.vehicleDisplay.deliveryDate)
            },
            dateToday: new Date(),
            datePickerProps: {
                popover: {
                    visibility: 'click'
                },
                input: {
                    readonly: true
                }
            },
            submitting: false
        };
    },
    methods: {
        updateVehicleDisplay: call('walmartDetails/updateVehicleDisplay'),
        submitEdit() {
            this.submitting = true;
            api.patch(`/walmarts/${this.vehicleDisplay.id}/update`, {
                removalDate: this.form.removalDate,
                deliveryDate: moment(this.form.deliveryDate).format()
            })
                .then((response) => {
                    this.submitting = false;
                    this.form.removalDate = null;
                    this.form.deliveryDate = null;
                    this.updateVehicleDisplay(response.data);
                });
        },
        resetForm() {
            this.form.removalDate = null;
            this.form.deliveryDate = null;
        }
    }
};
</script>
