<template>
    <i-box title="Vehicle Display">
        <div class="mb-2 text-center">
            Add New Vehicle Display
        </div>
        <b-button v-b-toggle="'collapse-display-vehicle-form'" block class="mb-2">
            Display Vehicle Form
        </b-button>
        <b-collapse id="collapse-display-vehicle-form" class="mt-2 mb-2">
            <b-card body-class="py-2 px-1">
                <vehicle-display-form/>
            </b-card>
        </b-collapse>
        <hr>
        <div>
            <div v-if="activeDisplayVehicles.length > 0">
                <div class="mb-2 text-center">
                    <span>Current vehicles on display</span> <strong>({{ activeDisplayVehicles.length }})</strong>
                </div>
                <div v-for="( vehicleDisplay, key) in activeDisplayVehicles">
                    <b-button v-b-toggle="'collapse' + key" block class="mb-2"
                              variant="primary"
                    >
                        <i aria-hidden="true" class="fas fa-car"/>&nbsp;
                        <span v-if="vehicleDisplay.year && vehicleDisplay.make && vehicleDisplay.model">
                            {{ vehicleDisplay.year }} {{ vehicleDisplay.make }} {{ vehicleDisplay.model }}
                        </span>
                    </b-button>
                    <b-collapse v-if="vehicleDisplay.vin" :id="'collapse' + key" class="mt-2 mb-2">
                        <b-card body-class="py-2 px-1">
                            <div class="card-text">
                                <div v-if="vehicleDisplay.imageUrl" class="mb-2">
                                    <b-img class="img-fluid" :src="vehicleDisplay.imageUrl[0]"/>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>Vehicle:</strong>
                                    <span>{{ vehicleDisplay.year }} {{ vehicleDisplay.make }} {{
                                            vehicleDisplay.model
                                        }}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>VIN:</strong>
                                    <b-link :href="`/inventory?endingVin=${vehicleDisplay.vin}`">
                                        {{ vehicleDisplay.vin }}
                                    </b-link>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>Days on Display:</strong>
                                    <span>{{ vehicleDisplay.deliveryDate | daysOnDisplay }}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>Dealer:</strong>
                                    <b-link :href="`/dealer/${vehicleDisplay.dealerName}`">
                                        {{ vehicleDisplay.dealerName }}
                                    </b-link>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>Located:</strong>
                                    <span>{{ vehicleDisplay.location }}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>Delivered Date:</strong>
                                    <span>{{ vehicleDisplay.deliveryDate | moment("MM/DD/YYYY") }}</span>
                                </div>
                                <div v-if="vehicleDisplay.removalDate"
                                     class="d-flex justify-content-between align-items-center">
                                    <strong>Removal Date:</strong>
                                    <span>{{ vehicleDisplay.removalDate | moment("MM/DD/YYYY") }}</span>
                                </div>
                                <div class="p-1">
                                    <b-button v-b-toggle="'edit-form' + key" size="sm" block>
                                        Edit Dates
                                    </b-button>
                                    <b-collapse :id="'edit-form' + key">
                                        <b-form>
                                            <edit-vehicle-display-form :vehicle-display="vehicleDisplay"/>
                                        </b-form>
                                    </b-collapse>
                                </div>
                            </div>
                        </b-card>
                    </b-collapse>
                    <b-collapse v-else :id="'collapse' + key" class="mt-2 mb-2">
                        <b-card body-class="py-2 px-1">
                            <div class="card-text">
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>Vehicle:</strong>
                                    <span>{{ vehicleDisplay.year }} {{ vehicleDisplay.make }} {{
                                            vehicleDisplay.model
                                        }}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>Days on Display:</strong>
                                    <span>{{ vehicleDisplay.deliveryDate | daysOnDisplay }}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>Located:</strong>
                                    <span>{{ vehicleDisplay.location }}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>Delivered Date:</strong>
                                    <span>{{ vehicleDisplay.deliveryDate | moment("MM/DD/YYYY") }}</span>
                                </div>
                                <div v-if="vehicleDisplay.removalDate"
                                     class="d-flex justify-content-between align-items-center">
                                    <strong>Removal Date:</strong>
                                    <span>{{ vehicleDisplay.removalDate | moment("MM/DD/YYYY") }}</span>
                                </div>
                                <div class="p-1">
                                    <b-button v-b-toggle="'edit-form' + key" size="sm" block>
                                        Edit Dates
                                    </b-button>
                                    <b-collapse :id="'edit-form' + key">
                                        <b-form>
                                            <edit-vehicle-display-form :vehicle-display="vehicleDisplay"/>
                                        </b-form>
                                    </b-collapse>
                                </div>
                            </div>
                        </b-card>
                    </b-collapse>
                </div>
            </div>

            <div v-else class="d-flex justify-content-center align-items-center alert alert-danger">
                <i aria-hidden="true" class="fas fa-exclamation-triangle"/>&nbsp; <span>Currently no vehicles are on display.</span>
            </div>
        </div>
        <div v-if="vehicleDisplayList.length > 0">
            <hr>
            <div class="mb-2 text-center">
                <span>Vehicle Display History</span> <strong>({{ vehicleDisplayList.length }})</strong>
            </div>
            <b-button v-b-toggle="'collapse-display-vehicle-history'" block variant="warning">
                View History
            </b-button>
            <b-collapse id="collapse-display-vehicle-history" class="mt-2 mb-2">
                <b-card body-class="py-2 px-1">
                    <b-table hover responsive class="mb-0"
                             :items="vehicleDisplayList" :fields="fields"
                    >
                        <template v-slot:cell(vin)="row">
                            <b-link :href="`/inventory?endingVin=${row.item.vin}`">
                                {{ row.item.vin }}
                            </b-link>
                        </template>
                        <template v-slot:cell(deliveryDate)="row">
                            {{ row.item.deliveryDate | moment('MM/DD/YYYY') }}
                        </template>
                        <template v-slot:cell(removalDate)="row">
                            {{ row.item.removalDate | moment('MM/DD/YYYY') }}
                        </template>
                    </b-table>
                </b-card>
            </b-collapse>
        </div>
    </i-box>
</template>
<script>
import IBox from 'Components/IBox';
import VehicleDisplayForm from 'Modules/walmart/components/Details/VehicleDisplayForm';
import EditVehicleDisplayForm from 'Modules/walmart/components/Details/EditVehicleDisplayForm';
import {call, sync} from 'vuex-pathify';
import moment from 'moment';

export default {
    components: {IBox, VehicleDisplayForm, EditVehicleDisplayForm},
    data() {
        return {
            fields: [
                {
                    key: 'vin',
                    label: 'VIN'
                },
                {
                    key: 'deliveryDate',
                    label: 'Delivery Date',
                    variant: 'success'
                },
                {
                    key: 'removalDate',
                    label: 'Removal Date',
                    variant: 'danger'
                }
            ]
        };
    },
    created() {
        this.getVehicleDisplayList();
    },
    methods: {
        getVehicleDisplayList: call('walmartDetails/getVehicleDisplayList')
    },
    computed: {
        vehicleDisplayList: sync('walmartDetails/vehicleDisplayList'),
        activeDisplayVehicles() {
            return _.filter(this.vehicleDisplayList, {removalDate: null});
        }
    },
    filters: {
        daysOnDisplay(deliveryDate) {
            const startDate = moment(deliveryDate);
            const current = moment().startOf('day');
            return current.diff(startDate, 'days');
        }
    }
};
</script>
