<template>
    <i-box :loading="isLoading" :collapsible="false">
        <div class="ibox-title">
            <h5>Search Results</h5>
            <div class="ibox-tools">
                <b-button v-b-modal.table-column-config size="sm">
                    <i aria-hidden="true" class="fas fa-cog"/>
                    <span class="list-action">Show / Hide Columns</span>
                </b-button>
                <table-column-selector
                    id="table-column-config"
                    v-model="displayFields"
                    :fields="fields"
                />

                <b-button v-b-modal.export-column-config size="sm">
                    <i aria-hidden="true" class="fas fa-file-export"/>
                    <span class="list-action">Export</span>
                </b-button>
                <export-column-selector
                    id="export-column-config"
                    v-model="exportFields"
                    :display-fields="displayFields"
                    :fields="fields"
                    @export="doExport"
                />
            </div>
        </div>
        <div class="ibox-content">
            <div class="result-count">
                <pagination
                    :show-pager="false"
                    :page-metadata="pageMetadata"
                    :page="page"
                    @page-changed="pageChanged"
                />
            </div>

            <b-table
                responsive
                striped
                hover
                bordered
                :fields="fieldsForDisplay()"
                :items="searchResults"
                :no-local-sorting="true"
                :sort-by.sync="sortBy"
                :sort-desc.sync="sortDesc"
                :no-sort-reset="true"
                @sort-changed="sortChanged"
            >
                <template v-slot:cell(id)="row">
                    <b-link :href="'/walmart/' + row.item.id">
                        {{ row.item.id }}
                    </b-link>
                </template>

                <template v-slot:cell(address)="row">
                    <div class="text-nowrap">
                        {{ row.item.address.street }}
                    </div>
                    <div class="text-nowrap">
                        {{
                            row.item.address.city
                        }}<span
                        v-if="
                                row.item.address.city &&
                                    (row.item.address.stateCode ||
                                        row.item.address.zipCode)
                            "
                    >,</span
                    >
                        {{ row.item.address.stateCode }}
                        {{ row.item.address.zipCode }}
                    </div>
                </template>

                <template v-slot:cell(status)="row">
                    <status-badge :status="row.item.status"/>
                </template>

                <template v-slot:cell(hasMarketingMaterials)="row">
                    <div
                        class="d-flex justify-content-center align-items-center w-100 h-100"
                    >
                        <span
                            v-if="row.item.hasMarketingMaterials"
                            class="label label-success"
                        >True</span
                        >
                        <span v-else class="label label-danger">False</span>
                    </div>
                </template>

                <template v-slot:cell(hasActiveDisplayVehicle)="row">
                    <div
                        class="d-flex justify-content-center align-items-center w-100 h-100"
                    >
                        <span
                            v-if="row.item.hasActiveDisplayVehicle"
                            class="label label-success"
                        >{{ row.item.hasActiveDisplayVehicle }}</span
                        >
                        <span v-else class="label label-danger">False</span>
                    </div>
                </template>

                <template v-slot:cell(accStatus)="row">
                    <div
                        class="d-flex justify-content-center align-items-center w-100 h-100"
                    >
                        <span
                            v-if="row.item.accStatus"
                            class="label label-success"
                        >True</span
                        >
                        <span v-else class="label label-danger">False</span>
                    </div>
                </template>

                <template v-slot:cell(hangtagStatus)="row">
                    <div
                        class="d-flex justify-content-center align-items-center w-100 h-100"
                    >
                        <span
                            v-if="row.item.hangtagStatus"
                            class="label label-success"
                        >Active</span
                        >
                        <span v-else class="label label-danger">Inactive</span>
                    </div>
                </template>

                <template v-slot:cell(approvedVehicleDisplayStatus)="row">
                    <div
                        class="d-flex justify-content-center align-items-center w-100 h-100"
                    >
                        <span
                            v-if="row.item.approvedVehicleDisplayStatus"
                            class="label label-success"
                        >True</span
                        >
                        <span v-else class="label label-danger">False</span>
                    </div>
                </template>

                <template v-slot:cell(actions)="row">
                    <b-button-group>
                        <b-button
                            variant="white"
                            size="xs"
                            :href="`/walmart/${row.item.id}`"
                        >
                            View
                        </b-button>
                    </b-button-group>
                </template>
            </b-table>

            <div class="pagination-wrapper">
                <pagination
                    :show-totals="false"
                    :page-metadata="pageMetadata"
                    :page="page"
                    @page-changed="pageChanged"
                />
            </div>
        </div>
    </i-box>
</template>

<script>
import {call, dispatch, get, sync} from "vuex-pathify";
import Pagination from "Components/Pagination";
import TableContentLoader from "Components/TableContentLoader";
import IBox from "Components/IBox";
import StatusBadge from "../StatusBadge";
import searchPageMixin from "@/mixins/searchPageMixin";
import TableColumnSelector from "Components/TableColumnSelector";
import ExportColumnSelector from "Components/ExportColumnSelector";

export default {
    components: {StatusBadge, IBox, TableContentLoader, Pagination, TableColumnSelector, ExportColumnSelector},

    mixins: [searchPageMixin],

    data() {
        return {
            fields: [
                {
                    key: "id",
                    label: "Store ID",
                    sortable: true
                },
                {
                    key: "name",
                    label: "Name",
                    sortable: false
                },
                {
                    key: "type",
                    label: "Type",
                    sortable: true
                },
                {
                    key: "address",
                    label: "Address",
                    sortable: false
                },
                {
                    key: "status",
                    label: "Status",
                    sortable: false
                },
                {
                    key: "hasMarketingMaterials",
                    label: "Marketing Materials",
                    sortable: false
                },
                {
                    key: "hasActiveDisplayVehicle",
                    label: "Active Display Vehicle",
                    sortable: false
                },
                {
                    key: "accStatus",
                    label: "Auto Care Center",
                    sortable: false
                },
                {
                    key: "hangtagStatus",
                    label: "Hangtag Status",
                    sortable: false
                },
                {
                    key: "approvedVehicleDisplayStatus",
                    label: "Approved Vehicle Display",
                    sortable: false
                },
                {
                    key: "actions",
                    label: "Actions",
                    sortable: false,
                    exportable: false
                }
            ]
        };
    },

    computed: {
        searchResults: get("walmartSearch/searchLoader@data"),
        pageMetadata: get("walmartSearch/pageMetadata"),
        isLoading: get("walmartSearch/<EMAIL>"),
        page: sync("walmartSearch/pageable@page"),
        sort: sync("walmartSearch/pageable@sort"),
        displayFields: sync("walmartSearch/displayFields"),
        exportFields: sync("walmartSearch/exportFields"),
    },

    watch: {
        page: () => {
            dispatch("walmartSearch/doPageLoad");
        },
        sort: () => {
            dispatch("walmartSearch/doSort");
        }
    },

    created() {
        dispatch("walmartSearch/doPageLoad");
    },

    methods: {
        goToDetails(walmart) {
            window.location = `/walmart/${walmart.id}`;
        },
        doExport: call("walmartSearch/doExport"),
    }
};
</script>

<style>
.result-count {
    padding: 10px 0;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
