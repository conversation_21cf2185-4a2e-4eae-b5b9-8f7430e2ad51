<template>
    <div>
        <b-row class="justify-content-center">
            <b-col lg="10">
                <i-box :title="title" class="pt-2" :collapsible="false">
                    <validation-observer ref="observer" v-slot="{ invalid }">
                        <b-form novalidate @submit.prevent="moveToPreview">
                            <b-row class="justify-content-center">
                                <b-col lg="6">
                                    <b-input-validate
                                        v-model="programName"
                                        label="Program Name *"
                                        name="Name"
                                        autocomplete="off"
                                        rules="required"
                                        :readonly="readonly"
                                    />
                                </b-col>
                                <b-col lg="6">
                                    <b-select-validate
                                        v-model="productId"
                                        label="Product *"
                                        name="Product"
                                        value-field="id"
                                        text-field="name"
                                        rules="required"
                                        :options="products"
                                        disabled
                                    />
                                </b-col>
                            </b-row>
                            <b-input-validate
                                v-model="supportEmail"
                                name="Support Email"
                                label="Support Email Address *"
                                autocomplete="off"
                                rules="required|email"
                                :readonly="readonly"
                            />
                            <b-input-validate
                                v-model="supportPhone"
                                v-mask="'(###) ###-####'"
                                :rules="{required: true,
                                         regex: /^((\(\d{3}\) ?)|(\d{3}-))?\d{3}-\d{4}/}"
                                name="Support phone number"
                                label="Support Phone No. *"
                                autocomplete="off"
                                type="tel"
                                :readonly="readonly"
                            />
                            <b-form-group label="Support Group ID">
                                <b-form-input
                                    v-model="supportGroupID"
                                    name="Support group ID"
                                    label="Support Group ID"
                                    autocomplete="off"
                                    :readonly="readonly"
                                />
                            </b-form-group>

                            <b-row v-if="previewScreen" class="justify-content-center mt-3">
                                <b-col lg="12" class="text-center">
                                    <div>
                                        <b-button
                                            size="lg"
                                            type="submit"
                                            variant="primary"
                                            class="mr-2"
                                            :disabled="invalid"
                                            :loading="loading"
                                            title="WARNING! - Any edits made, when submitted, will take immediate effect on the Client’s live program site."
                                            @click="onSubmit"
                                        >
                                            Submit
                                        </b-button>
                                        <b-button
                                            size="lg"
                                            class="mr-2"
                                            variant="outline-primary"
                                            title="Go back to previous screen to edit your changes."
                                            @click="backToEdit"
                                        >
                                            Go Back
                                        </b-button>
                                        <b-button
                                            size="lg"
                                            variant="danger"
                                            title="Cancel and return to the Program List page, edits will not be saved."
                                            @click="cancel"
                                        >
                                            Cancel
                                        </b-button>
                                    </div>
                                </b-col>
                            </b-row>
                            <b-row v-else class="justify-content-center mt-3">
                                <b-col lg="12" class="text-center">
                                    <div>
                                        <b-button
                                            size="lg"
                                            type="submit"
                                            variant="primary"
                                            class="mr-2"
                                            title="Save data and preview before submitting."
                                            :disabled="invalid"
                                            @click="moveToPreview"
                                        >
                                            Save
                                        </b-button>
                                        <b-button
                                            size="lg"
                                            variant="danger"
                                            title="Cancel and return to the Program List page."
                                            @click="cancel"
                                        >
                                            Cancel
                                        </b-button>
                                    </div>
                                </b-col>
                            </b-row>
                        </b-form>
                    </validation-observer>
                </i-box>
            </b-col>
        </b-row>
    </div>
</template>
<script>
import IBox from 'Components/IBox';
import { validationMixin } from 'vuelidate';
import BInputValidate from 'Components/FormValidation/BInputValidate';
import BSelectValidate from 'Components/FormValidation/BSelectValidate';
import { mask } from 'vue-the-mask';
import { dispatch, get, sync } from 'vuex-pathify';

export default {
    name: 'EditProgram',
    components: { BInputValidate, IBox, BSelectValidate },
    directives: { mask },
    mixins: [validationMixin],
      data () {
        return {
            addTitle: 'Edit Program',
            previewTitle: 'Preview - Edit Program',
            readonly: false,
            previewScreen: false
        };
    },
    watch: {
         loader () {
            if (this.loader.isError) {
                this.$toastr.e(this.loader.errorMessages);
            }
        }
    },
     mounted () {
        const id = location.pathname.split('/')[3];
         dispatch('programs/loadProductList')
        dispatch('programs/program', id);
    },
    computed: {
         products: get('programs/products@data'),
         program: get('programs/program@data'),
        loader: get('programs/program@loader'),
        title () {
            let title = this.addTitle;
            if (this.previewScreen) {
                title = this.previewTitle;
            }
            return title;
        },
        loading () {
            return this.loader.isLoading;
        },
        productId: {
            get () {
            const id = '';
            if (this.program && this.program.product && this.program.product.id) {
              return this.program.product.id;
            }
            return id;
            },
            set (newValue) {

            }
        },
         programName: {
            get () {
                const name = this.program.name;
                   return name;
            },
             set (newValue) {
                if (newValue != null) {
                    this.$store.commit('programs/updateName', newValue);
                }
            }
        },
        supportEmail: {
              get () {
                   return this.program.supportEmail;
            },
            set (newValue) {
                if (newValue != null) {
                    this.$store.commit('programs/updateSupportEmail', newValue);
                }
            }
        },
        supportPhone: {
           get () {
                   return this.program.supportPhone;
            },
              set (newValue) {
                if (newValue != null) {
                    this.$store.commit('programs/updateSupportPhone', newValue);
                }
            }
        },
         supportGroupID: {
           get () {
                   return this.program.supportGroupId;
            },
              set (newValue) {
                if (newValue != null) {
                    this.$store.commit('programs/supportGroupID', newValue);
                }
            }
        }

    },
    methods: {
        moveToPreview () {
            this.readonly = true;
            this.previewScreen = true;
        },
        cancel () {
            window.location = '/configs/programs';
        },
        backToEdit () {
            this.readonly = false;
            this.previewScreen = false;
        },
        onSubmit () {
            const phoneNumber = this.supportPhone.replace(/[^A-Z0-9]+/ig, '');
            const data = {
                id: this.program.id,
                name: this.programName.trim(),
                supportEmail: this.supportEmail.trim(),
                supportPhone: phoneNumber,
                supportGroupId: this.supportGroupID ? this.supportGroupID.trim() : ""
            };
            dispatch('programs/editProgram', data);
        }
    }
};
</script>

<style lang="scss" scoped>

</style>
