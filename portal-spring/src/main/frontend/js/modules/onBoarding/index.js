import Vue from 'vue';
import 'es6-promise/auto';
import * as VueGoogleMaps from 'vue2-google-maps';
import Vue2Filters from 'vue2-filters';
import Toastr from 'vue-toastr';
import 'vue-toastr/src/vue-toastr.scss';
import CarSaverPlugin from '@/lib/CarSaverPlugin';
import store from './store';
import VueRouter from 'vue-router';
import DealerCreate from './components/DealerCreation';
import DealerInfoUpdate from './components/DealerInfoUpdate';
import DealerBilling from './components/DealerBilling';
import DealerContract from './components/DealerContract';
import DealerHoursUpdate from './components/DealerHoursUpdate';
import FinanceIntegration from './components/FinanceIntegration';
import SalesMatching from './components/SalesMatching';
import InventoryConfiguration from './components/InventoryConfiguration';
import LeadConfiguration from './components/LeadConfiguration';
import MakesConfiguration from './components/MakesConfiguration';
import Preferences from './components/Preferences';
import VCalendar from 'v-calendar';

import '../../directives';
import '../../filters';
import {extend, setInteractionMode, ValidationObserver, ValidationProvider} from 'vee-validate';
import {email, integer, min, min_value, numeric, regex, required, required_if} from 'vee-validate/dist/rules';

Vue.use(require('vue-moment'));
Vue.use(VCalendar);
Vue.use(Vue2Filters);
Vue.use(VueGoogleMaps, {
    load: {
        key: 'AIzaSyAmeMzc4Bx3ogH6OT4oNZ_hy2ufznNTcwY',
        libraries: 'places' // This is required if you use the Autocomplete plugin
    }
});
Vue.use(Toastr);
Vue.use(CarSaverPlugin);
Vue.use(VueRouter);

Vue.component('ValidationProvider', ValidationProvider);
Vue.component('ValidationObserver', ValidationObserver);
// Adding a rules.
extend('required', required);
extend('required_if', required_if);
extend('email', email);
extend('integer', integer);
extend('min_value', min_value);
extend('min', min);
extend('max', {
    validate(value, {max}) {
        if (value && value.length) {
            return value.length <= max;
        }
        return true;
    },
    params: ['max'],
    message: 'The {_field_} field must not be greater than {max} characters'
});
extend('regex', regex);
extend('numeric', numeric);
extend('max_value', {
    validate(value, {max}) {
        return value <= max;
    },
    params: ['max'],
    message: 'The {_field_} field must not be greater than {max}'
});
setInteractionMode('eager');

const routes = [
    {
        path: '/on-boarding/dealer-creation',
        component: DealerCreate,
        props: true
    },
    {
        path: '/on-boarding/:dealerId/billing',
        name: 'billing',
        component: DealerBilling,
        props: true
    },
    {
        path: '/on-boarding/:dealerId/details',
        name: 'details',
        component: DealerInfoUpdate,
        props: true
    },
    {
        path: '/on-boarding/:dealerId/hours',
        name: 'hours',
        component: DealerHoursUpdate,
        props: true
    },
    {
        path: '/on-boarding/:dealerId/contract',
        component: DealerContract,
        props: true
    },
    {
        path: '/on-boarding/:dealerId/finance-integration',
        component: FinanceIntegration,
        props: true
    },
    {
        path: '/on-boarding/:dealerId/sales-matching',
        component: SalesMatching,
        props: true
    },
    {
        path: '/on-boarding/:dealerId/inventory-configuration',
        component: InventoryConfiguration,
        props: true
    },
    {
        path: '/on-boarding/:dealerId/lead-configuration',
        component: LeadConfiguration,
        props: true
    },
    {
        path: '/on-boarding/:dealerId/makes-configuration',
        component: MakesConfiguration,
        props: true
    },
    {
        path: '/on-boarding/:dealerId/dealer-preferences',
        component: Preferences,
        props: true
    }
];

const router = new VueRouter({
    mode: 'history',
    routes
});

new Vue({
    el: '#wrapper',
    store,
    router
});
