<template>
    <layout title="Dealer Preferences" :dealer-id="dealerId">
        <b-form @submit.prevent="onSubmit">
            <div class="container-fluid">
                <div class="row">
                    <loading-block v-if="isLoading"/>

                    <div v-else-if="isLocked" class="col-lg-8 border-right">
                        <b-alert show>
                            Settings are locked by the Dealer Group
                        </b-alert>
                    </div>

                    <div v-else class="col-lg-8 border-right">
                        <b-row>
                            <b-col cols="6">
                                <three-state-preference
                                    v-model="preferences.sendSmsForAppointments"
                                    label="SMS Alerts for Appointments"
                                    :default-value="
                                        defaultPreferences.sendSmsForAppointments
                                    "
                                    :default-override="
                                        isOverridden('sendSmsForAppointments')
                                    "
                                />
                            </b-col>
                            <b-col cols="6">
                                <three-state-preference
                                    v-model="preferences.sendSmsForConnections"
                                    label="SMS Alerts for Connections"
                                    :default-value="
                                        defaultPreferences.sendSmsForConnections
                                    "
                                    :default-override="
                                        isOverridden('sendSmsForConnections')
                                    "
                                />
                            </b-col>
                        </b-row>
                        <hr/>
                        <b-row>
                            <b-col cols="6">
                                <three-state-preference
                                    v-model="preferences.offeringOptionalVsc"
                                    label="Warranty - Offers VSC"
                                    :default-value="
                                        defaultPreferences.offeringOptionalVsc
                                    "
                                    :default-override="
                                        isOverridden('offeringOptionalVsc')
                                    "
                                />
                            </b-col>
                            <b-col cols="6">
                                <three-state-preference
                                    v-model="
                                        preferences.sendSmsAlertsAfterHours
                                    "
                                    label="SMS Alerts After Hours"
                                    :default-value="
                                        defaultPreferences.sendSmsAlertsAfterHours
                                    "
                                    :default-override="
                                        isOverridden('sendSmsAlertsAfterHours')
                                    "
                                />
                            </b-col>
                        </b-row>
                        <hr/>
                        <b-row>
                            <b-col cols="6">
                                <three-state-preference
                                    v-model="preferences.enableOutOfMarketLeads"
                                    label="Enable Out-Of-Market Leads"
                                    :default-value="
                                        defaultPreferences.enableOutOfMarketLeads
                                    "
                                    :default-override="
                                        isOverridden('enableOutOfMarketLeads')
                                    "
                                />
                            </b-col>
                            <b-col cols="6">
                                <three-state-preference
                                    v-model="preferences.enablePriceRequests"
                                    label="Enable Price Requests"
                                    :default-value="
                                        defaultPreferences.enablePriceRequests
                                    "
                                    :default-override="
                                        isOverridden('enablePriceRequests')
                                    "
                                />
                            </b-col>
                        </b-row>
                        <hr/>
                        <b-row>
                            <b-col cols="6">
                                <three-state-preference
                                    v-model="preferences.enableLeasePricing"
                                    label="Enable Lease Pricing"
                                    :default-value="
                                        defaultPreferences.enableLeasePricing
                                    "
                                    :default-override="
                                        isOverridden('enableLeasePricing')
                                    "
                                />
                            </b-col>
                            <b-col cols="6">
                                <three-state-preference
                                    v-model="preferences.uniqueProxyNumbers"
                                    label="Unique Proxy Number"
                                    :default-value="
                                        defaultPreferences.uniqueProxyNumbers
                                    "
                                    :default-override="
                                        isOverridden('uniqueProxyNumbers')
                                    "
                                />
                            </b-col>
                        </b-row>
                        <hr/>
                        <b-row>
                            <b-col cols="6">
                                <delivery-preference
                                    v-model="preferences.deliveryAvailability"
                                    label="Delivery Availability"
                                    :default-value="
                                        defaultPreferences.deliveryAvailability
                                    "
                                    :default-override="
                                        isOverridden('deliveryAvailability')
                                    "
                                />
                            </b-col>
                            <b-col cols="6">
                                <lead-transport-preference
                                    v-model="preferences.leadTransport"
                                    label="Lead Transport"
                                    :default-value="
                                        defaultPreferences.leadTransport
                                    "
                                    :default-override="
                                        isOverridden('leadTransport')
                                    "
                                />
                            </b-col>
                        </b-row>
                        <hr/>
                        <b-row>
                            <b-col cols="6">
                                <pricing-rules-preference
                                    v-model="preferences.pricingRules"
                                    label="Pricing Rules"
                                    :default-value="
                                        defaultPreferences.pricingRules
                                    "
                                    :default-override="
                                        isOverridden('pricingRules')
                                    "
                                />
                            </b-col>
                            <b-col cols="6">
                                <three-state-preference
                                    v-model="preferences.internetPriceEnabled"
                                    label="Allow Internet Price Offsets"
                                    :subtext="
                                        !this.hasInventory
                                            ? 'No inventory: Must have inventory to confirm internet price does not contain rebates before enabling'
                                            : null
                                    "
                                    :disabled="!this.hasInventory"
                                    :default-value="
                                        defaultPreferences.internetPriceEnabled
                                    "
                                    :default-override="
                                        isOverridden('internetPriceEnabled')
                                    "
                                    @show-warning-modal="showInternetPriceWarning"
                                />
                            </b-col>
                        </b-row>
                        <hr/>
                        <b-row>
                            <b-col cols="6">
                                <taxes-quote-type-preference
                                    v-model="preferences.taxesQuoteType"
                                    label="Taxes Quote Type"
                                    :default-value="defaultPreferences.taxesQuoteType"
                                    :default-override="
                                        isOverridden('taxesQuoteType')
                                    "
                                />
                            </b-col>
                        </b-row>
                    </div>
                    <div class="col-lg-4">
                        <div
                            class="d-flex flex-column justify-content-center align-items-center h-100"
                        >
                            <i
                                aria-hidden="true"
                                class="fas fa-cogs mb-4"
                                style="font-size: 100px;color: #e5e5e5 "
                            />
                            <div class="d-flex mb-3">
                                <b-button
                                    v-b-popover.hover.top="
                                        'Submits Dealer Preferences. You will be redirected to the dealer details page.'
                                    "
                                    :disabled="loading"
                                    class="mr-2"
                                    type="submit"
                                    variant="info"
                                >
                                    Save & Finish
                                </b-button>
                                <b-link
                                    v-b-popover.hover.top="
                                        'Cancels form and returns you to the dealer details page.'
                                    "
                                    :disabled="loading"
                                    class="btn btn-danger mr-2"
                                    :href="`/dealer/${dealerId}`"
                                >
                                    Cancel
                                </b-link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </b-form>

        <b-modal
            id="enable-internet-price-modal"
            size="md"
            header-bg-variant="danger"
            title="Warning!"
            no-close-on-backdrop
            no-close-on-esc
            hide-header-close
        >
            <h3>
                Enabling internet price on a dealer with rebates in their
                internet price will cause rebates to be added twice and result
                in a very low and inaccurate CarSaver price.
            </h3>

            <p>
                Please make sure you have verified that this dealer's internet
                price DOES NOT already contain ANY rebates.
            </p>

            <input
                v-model="agreeToEnable"
                type="checkbox"
                name="agree-to-enable"
            />

            <strong>
                By checking the box, you confirm that you have verified the
                internet price DOES NOT contain rebates
            </strong>

            <template slot="modal-footer">
                <b-button size="md" @click="noEnable()">
                    Don't Enable Internet Price
                </b-button>
                <b-button
                    size="md"
                    variant="success"
                    :disabled="!agreeToEnable"
                    @click="confirmEnable()"
                >
                    Yes, Allow Internet Price Offsets
                </b-button>
            </template>
        </b-modal>
    </layout>
</template>
<script>
import Layout from "Modules/onBoarding/components/Layout";
import LoadingBlock from "Modules/onBoarding/components/LoadingBlock";
import {call, get} from "vuex-pathify";
import api from "@/api";
import ThreeStatePreference from "Modules/onBoarding/components/Preferences/ThreeStatePreference";
import DeliveryPreference from "Modules/onBoarding/components/Preferences/DeliveryPreference";
import LeadTransportPreference from "Modules/onBoarding/components/Preferences/LeadTransportPreference";
import PricingRulesPreference from "Modules/onBoarding/components/Preferences/PricingRulesPreference";
import TaxesQuoteTypePreference from "Modules/onBoarding/components/Preferences/TaxesQuoteTypePreference";
import dealerFormMixin from "../../mixins/dealerFormMixin";
import {BIconExclamationCircle} from "bootstrap-vue";

export default {
    name: "Preferences",
    components: {
        PricingRulesPreference,
        LeadTransportPreference,
        DeliveryPreference,
        ThreeStatePreference,
        TaxesQuoteTypePreference,
        LoadingBlock,
        Layout,
        BIconExclamationCircle
    },
    mixins: [dealerFormMixin],
    props: {
        dealerId: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            agreeToEnable: false,
            defaultPreferences: {},
            preferences: {
                sendSmsForConnections: null,
                sendSmsForAppointments: null,
                sendSmsAlertsAfterHours: null,
                offeringOptionalVsc: null,
                leadTransport: null,
                enableLeasePricing: null,
                enableOutOfMarketLeads: null,
                enablePriceRequests: null,
                uniqueProxyNumbers: null,
                deliveryAvailability: null,
                pricingRules: null,
                internetPriceEnabled: null,
                taxesQuoteType: null,
            },
            loading: false,
            loadingDefaultPreferences: true,
            hasInventory: false
        };
    },
    computed: {
        dealer: get("onBoarding/dealer"),
        isLoading() {
            return this.loading || this.isLoadingData;
        },
        isLoadingData() {
            return _.isNil(this.dealer) || this.loadingDefaultPreferences;
        },
        isLocked() {
            return _.get(this.defaultPreferences, "lockSettings", false);
        }
    },
    created() {
        this.loadDealer(this.dealerId).then(dealer => {
            this.loadDealerPreferences(dealer);
        });

        api.get(`/on-boarding/dealer/${this.dealerId}/has-inventory`).then(
            response => {
                this.hasInventory = response.data;
            }
        );

        api.get(
            `/on-boarding/dealer/${this.dealerId}/default-preferences`
        ).then(response => {
            this.defaultPreferences = response.data;

            this.loadingDefaultPreferences = false;
        });
    },
    methods: {
        setDealerPreferences: call("onBoarding/setDealerPreferences"),
        loadDealer: call("onBoarding/loadDealer"),
        showInternetPriceWarning() {
            this.$bvModal.show("enable-internet-price-modal");
        },
        noEnable() {
            const isOverridden = this.isOverridden("internetPriceEnabled");
            this.preferences.internetPriceEnabled = isOverridden
                ? _.get(this.defaultPreferences, "internetPriceEnabled")
                : null;
            this.$bvModal.hide("enable-internet-price-modal");
            this.$toastr.i("Internet price offsets not enabled");
        },
        confirmEnable() {
            this.$bvModal.hide("enable-internet-price-modal");
        },
        onSubmit() {
            this.loading = true;

            if (this.isLocked) {
                document.location = "/dealer/" + this.dealer.id;
            } else {
                this.setDealerPreferences(this.preferences)
                    .then(dealer => {
                        document.location = "/dealer/" + dealer.id;
                    })
                    .catch(error => {
                        console.log("error =", error);
                    });
            }
        },
        isOverridden(pref) {
            const dealerPrefs = _.get(this.dealer, "preferences", null);
            if (_.isNil(dealerPrefs)) {
                return false;
            }
            const hasDealerPref = !_.isNil(dealerPrefs[pref]);
            const isNotEqualToDefault = !_.isEqualWith(
                this.defaultPreferences,
                dealerPrefs,
                pref
            );

            return hasDealerPref && isNotEqualToDefault;
        },
        loadDealerPreferences(dealer) {
            this.preferences.sendSmsForConnections = _.get(
                dealer,
                "preferences.sendSmsForConnections",
                null
            );
            this.preferences.sendSmsForAppointments = _.get(
                dealer,
                "preferences.sendSmsForAppointments",
                null
            );
            this.preferences.sendSmsAlertsAfterHours = _.get(
                dealer,
                "preferences.sendSmsAlertsAfterHours",
                null
            );
            this.preferences.offeringOptionalVsc = _.get(
                dealer,
                "preferences.offeringOptionalVsc",
                null
            );
            this.preferences.leadTransport = _.get(
                dealer,
                "preferences.leadTransport",
                null
            );
            this.preferences.enableLeasePricing = _.get(
                dealer,
                "preferences.enableLeasePricing",
                null
            );
            this.preferences.enableOutOfMarketLeads = _.get(
                dealer,
                "preferences.enableOutOfMarketLeads",
                null
            );
            this.preferences.enablePriceRequests = _.get(
                dealer,
                "preferences.enablePriceRequests",
                null
            );
            this.preferences.uniqueProxyNumbers = _.get(
                dealer,
                "preferences.uniqueProxyNumbers",
                null
            );
            this.preferences.deliveryAvailability = _.get(
                dealer,
                "preferences.deliveryAvailability",
                null
            );
            this.preferences.internetPriceEnabled = _.get(
                dealer,
                "preferences.internetPriceEnabled",
                null
            );
            this.preferences.pricingRules = _.get(
                dealer,
                "preferences.pricingRules",
                null
            );
            this.preferences.taxesQuoteType = _.get(
                dealer,
                "preferences.taxesQuoteType",
                null
            );
            this.preferences.lmsPreference = _.get(
                dealer,
                "preferences.lmsPreference",
                null
            );

        }
    }
};
</script>

<style lang="scss">
#enable-internet-price-modal {
    .modal-title {
        font-size: 20px;
    }
}
</style>
