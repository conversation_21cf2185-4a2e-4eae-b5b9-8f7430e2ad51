<template>
    <b-row>
        <b-col cols="2">
            {{ label }}
        </b-col>
        <b-col cols="3">
            <hours-of-operation-selector :value="value.openHours" @input="changeOpen"/>
        </b-col>
        <b-col cols="3">
            <hours-of-operation-selector :value="value.closeHours" @input="changeClose"/>
        </b-col>
    </b-row>
</template>

<script>
import HoursOfOperationSelector from './HoursOfOperationSelector';

export default {
    components: {
        HoursOfOperationSelector
    },
    props: {
        value: {
            type: Object,
            required: true
        },
        label: {
            type: String,
            required: true
        }
    },

    methods: {
        changeOpen(newValue) {
            this.$emit('input', {
                openHours: newValue,
                closeHours: this.value.closeHours
            });
        },
        changeClose(newValue) {
            this.$emit('input', {
                openHours: this.value.openHours,
                closeHours: newValue
            });
        }
    }
};
</script>
