<template>
    <b-form-group>
        <b-select-validate
            :value="value"
            rules="required"
            :options="hoursOptions"
            @input="change"
        />
    </b-form-group>
</template>

<script>
import moment from 'moment';
import BSelectValidate from 'Components/FormValidation/BSelectValidate';

export default {
    components: {BSelectValidate},
    props: {
        value: {
            type: String,
            required: true
        }
    },

    computed: {
        hoursOptions() {
            const times = [];
            times.push('CLOSED');

            const timeOfDay = moment().startOf('day');
            const minuteIncrement = 30;

            do {
                times.push(timeOfDay.format('h:mm A'));
                timeOfDay.add(minuteIncrement, 'minutes');
            }
            while (!(timeOfDay.hour() === 0 && timeOfDay.minute() === 0));

            return times;
        }
    },

    methods: {
        change(newValue) {
            this.$emit('input', newValue);
        }
    }
};
</script>
