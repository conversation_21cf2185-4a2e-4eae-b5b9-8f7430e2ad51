<template>
    <layout title="Dealer Creation" :dealer-id="form.dealerId">
        <validation-observer ref="observer" v-slot="{ passes }">
            <b-form @submit.prevent="passes(submit)">
                <div class="container-fluid my-2">
                    <div class="row">
                        <loading-block v-if="loading"/>
                        <div v-else class="col-lg-8 border-right">
                            <b-row>
                                <b-col cols="12">
                                    <b-form-group>
                                        <b-form-input
                                            ref="autocomplete"
                                            v-model.trim="form.name"
                                            autocomplete="off"
                                            label="Dealership Name"
                                            name="name"
                                            placeholder="Search Dealership Name"
                                        />
                                        <b-alert
                                            v-if="dealerExists"
                                            variant="danger"
                                            class="mt-1"
                                            show
                                        >
                                            This dealer already exists!
                                            <span v-for="d in existingDealers">
                                                <b-link
                                                    :href="`/dealer/${d.id}`"
                                                >{{ d.name }}
                                                </b-link>
                                            </span>
                                        </b-alert>
                                    </b-form-group>
                                </b-col>
                            </b-row>
                            <b-card
                                header="Only set these values if you know what you are doing! Otherwise they will be automatically set appropriately."
                                header-bg-variant="warning"
                                class="mb-2"
                            >
                                <b-row>
                                    <b-col cols="3">
                                        <b-form-group label="Dealer Id">
                                            <b-form-input
                                                v-model="form.dealerId"
                                                name="dealerId"
                                            />
                                        </b-form-group>
                                    </b-col>
                                    <b-col cols="3">
                                        <b-form-group label="Walmart Store Id">
                                            <b-form-input
                                                v-model="form.walmartStoreId"
                                                name="walmartStoreId"
                                            />
                                        </b-form-group>
                                    </b-col>
                                </b-row>
                            </b-card>

                            <b-row>
                                <b-col>
                                    <b-form-group>
                                        <b-input-validate
                                            v-model="form.address1"
                                            name="address1"
                                            label="Address"
                                            rules="required"
                                            placeholder="Enter Address"
                                        />
                                    </b-form-group>
                                </b-col>
                            </b-row>
                            <b-row>
                                <b-col cols="4">
                                    <b-form-group>
                                        <b-input-validate
                                            v-model="form.city"
                                            name="city"
                                            label="City"
                                            rules="required"
                                            placeholder="Enter City"
                                        />
                                    </b-form-group>
                                </b-col>
                                <b-col cols="4">
                                    <b-form-group label="State">
                                        <b-select-validate
                                            v-model="form.state"
                                            name="state"
                                            rules="required"
                                            :options="states"
                                        />
                                    </b-form-group>
                                </b-col>
                                <b-col cols="4">
                                    <b-form-group>
                                        <b-input-validate
                                            v-model="form.zipCode"
                                            name="zipCode"
                                            label="Zip Code"
                                            :rules="{
                                                required: true,
                                                regex: /^(\d{5})$/
                                            }"
                                            placeholder="Enter Zip Code"
                                        />
                                    </b-form-group>
                                </b-col>
                            </b-row>
                            <b-row>
                                <b-col cols="6">
                                    <b-form-group>
                                        <b-input-validate
                                            v-model="form.phoneNumber"
                                            v-mask="'(###) ###-####'"
                                            label="Phone Number"
                                            name="phoneNumber"
                                            type="tel"
                                            :rules="{
                                                required: true,
                                                regex: /^((\(\d{3}\) ?)|(\d{3}-))?\d{3}-\d{4}/
                                            }"
                                        />
                                    </b-form-group>
                                </b-col>
                                <b-col cols="6">
                                    <b-form-group>
                                        <b-select-validate
                                            v-model="form.timeZone"
                                            name="timeZone"
                                            label="Time Zone"
                                            rules="required"
                                            :options="timeZones"
                                        />
                                    </b-form-group>
                                </b-col>
                            </b-row>
                            <b-row>
                                <b-col cols="12">
                                    <b-form-group>
                                        <label>
                                            Dealership Website
                                            <i
                                                aria-hidden="true"
                                                class="fas fa-info-circle"
                                                v-b-popover.hover.top="
                                                    'This is the base url for the dealership website ie:\'http://www.carsaver.com\'. Do not include url parameters, trailing slashes etc.'
                                                "
                                            />
                                        </label>
                                        <b-input-validate
                                            v-model="form.websiteUrl"
                                            name="Dealership Website URL"
                                            :rules="{
                                                required: false,
                                                regex: validWebsiteUrlPattern
                                            }"
                                        />
                                    </b-form-group>
                                </b-col>
                            </b-row>
                            <b-row>
                                <b-col cols="6">
                                    <b-form-group>
                                        <label>
                                            New Inventory URL
                                            <i
                                                aria-hidden="true"
                                                class="fas fa-info-circle"
                                                v-b-popover.hover.top="
                                                    'This is used for e-commerce linking to Dealers New Inventory'
                                                "
                                            />
                                        </label>
                                        <b-input-validate
                                            v-model="form.newInventoryUrl"
                                            type="url"
                                            name="New Inventory Url"
                                            :rules="{
                                                regex: /^(http|https):\/\//
                                            }"
                                        />
                                    </b-form-group>
                                </b-col>
                                <b-col cols="6">
                                    <b-form-group>
                                        <label>
                                            Used Inventory URL
                                            <i
                                                aria-hidden="true"
                                                class="fas fa-info-circle"
                                                v-b-popover.hover.top="
                                                    'This is used for e-commerce linking to Dealers Used Inventory'
                                                "
                                            />
                                        </label>
                                        <b-input-validate
                                            v-model="form.usedInventoryUrl"
                                            type="url"
                                            name="Used Inventory Url"
                                            :rules="{
                                                regex: /^(http|https):\/\//
                                            }"
                                        />
                                    </b-form-group>
                                </b-col>
                            </b-row>
                            <b-row>
                                <b-col cols="12">
                                    <b-form-group
                                        label="Dealer Group (once this is set it cannot be edited!)"
                                    >
                                        <b-form-select
                                            v-model="form.dealerGroupId"
                                            autocomplete="false"
                                            name="dealerGroup"
                                            placeholder="Dealer Group"
                                            :disabled="
                                                isGroupSet
                                            "
                                        >
                                            <option :value="null" disabled>
                                                Select Dealer Group
                                            </option>
                                            <option
                                                v-for="group in dealerGroups"
                                                :value="group.id"
                                            >
                                                {{ group.name }}
                                            </option>
                                        </b-form-select>
                                    </b-form-group>
                                </b-col>
                            </b-row>
                            <b-row v-if="form.hoursOfOperations.length > 0">
                                <b-col>
                                    <b-row
                                        v-for="(day, index) in daysOfWeek"
                                        :key="index"
                                    >
                                        <b-col cols="2">
                                            {{ day }}
                                        </b-col>
                                        <b-col cols="3">
                                            <b-form-group>
                                                <b-select-validate
                                                    v-model="
                                                        form.hoursOfOperations[
                                                            index
                                                        ].openHours
                                                    "
                                                    name="hoursOfOperations"
                                                    rules="required"
                                                    :options="hoursOptions"
                                                />
                                            </b-form-group>
                                        </b-col>
                                        <b-col cols="3">
                                            <b-form-group>
                                                <b-select-validate
                                                    v-model="
                                                        form.hoursOfOperations[
                                                            index
                                                        ].closeHours
                                                    "
                                                    name="hoursOfOperations"
                                                    rules="required"
                                                    :options="hoursOptions"
                                                />
                                            </b-form-group>
                                        </b-col>
                                    </b-row>
                                </b-col>
                            </b-row>
                        </div>
                        <div class="col-lg-4">
                            <div
                                class="d-flex flex-column justify-content-center align-items-center h-100"
                            >
                                <i
                                    aria-hidden="true"
                                    class="fas fa-car mb-4"
                                    style="font-size: 140px;color: #e5e5e5 "
                                />
                                <div class="d-flex mb-3">
                                    <b-button
                                        v-b-popover.hover.top="
                                            'Creates Dealer and allows you to finish on-boarding them later! You will be redirected to the dealer details page.'
                                        "
                                        :disabled="loading || disabled"
                                        class="mr-2"
                                        type="submit"
                                        variant="primary"
                                    >
                                        Setup Billing
                                    </b-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </b-form>
        </validation-observer>
    </layout>
</template>
<script>
import _ from "lodash";
import api from "@/api";
import Layout from "Modules/onBoarding/components/Layout";
import LoadingBlock from "Modules/onBoarding/components/LoadingBlock";
import {call, get} from "vuex-pathify";
import BInputValidate from "Components/FormValidation/BInputValidate";
import BSelectValidate from "Components/FormValidation/BSelectValidate";
import {mask} from "vue-the-mask";
import moment from "moment";
import dealerFormMixin from "Modules/onBoarding/mixins/dealerFormMixin";

const urlPatternString =
    "(http(s)?):\\/\\/[(www\\.)a-zA-Z0-9]{2,256}\\.[a-z]{2,6}";

export default {
    name: "DealerCreate",
    components: {BSelectValidate, BInputValidate, LoadingBlock, Layout},
    directives: {mask},
    mixins: [dealerFormMixin],
    data() {
        return {
            loading: false,
            existingDealers: [],
            disabled: false,
            autocomplete: null,
            daysOfWeek: [
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday",
                "Saturday",
                "Sunday"
            ],
            timeZones: [],
            states: [],
            dealerGroups: [],
            form: {
                dealerId: null,
                name: "",
                address1: "",
                city: "",
                state: "",
                zipCode: "",
                phoneNumber: "",
                timeZone: "",
                googlePlaceId: null,
                lat: "",
                lng: "",
                hoursOfOperations: [],
                walmartStoreId: null,
                dealerGroupId: null,
                websiteUrl: "",
                newInventoryUrl: "",
                usedInventoryUrl: ""
            },
            validWebsiteUrlPattern: new RegExp(`^${urlPatternString}$`)
        };
    },
    watch: {
        googlePlaceId(newVal, oldVal) {
            if (_.isNil(_.get(this.dealerToEdit, "id", null))) {
                if (!_.isNil(this.googlePlaceId) && newVal !== oldVal) {
                    api.get("/on-boarding/google-place-id", {
                        googlePlaceId: this.googlePlaceId
                    })
                        .then(response => {
                            this.existingDealers = response.data;

                            if (this.dealerExists) {
                                this.disabled = true;
                                this.resetForm();
                            }
                        })
                        .catch(error => {
                            console.error(error);
                        });
                }
            }
        },
        dealerExists: function (newVal) {
            if (newVal === false) {
                this.disabled = false;
            }
        },
        isGroupSet(newVal, oldVal) {
            if (
                !_.isNil(this.dealerToEdit) &&
                !_.isNil(this.dealerToEdit.dealerGroupId)
            ) {
                return;
            }
            if (newVal === true && oldVal === false) {
                const message =
                    "Are you sure you want to set this dealer group? It cannot be changed once set.";
                const options = {
                    bodyBgVariant: "danger",
                    bodyTextVariant: "white"
                };
                this.$bvModal.msgBoxConfirm(message, options).then(value => {
                    if (value === false) {
                        this.form.dealerGroupId = null;
                    }
                });
            }
        }
    },

    mounted() {
        this.getDealerInfoOptions();

        this.autocomplete = new google.maps.places.Autocomplete(
            this.$refs.autocomplete.$el,
            {types: ["establishment"]}
        );
        this.autocomplete.setFields([
            "name",
            "formatted_phone_number",
            "opening_hours",
            "address_component",
            "geometry",
            "place_id",
            "website"
        ]);

        this.autocomplete.addListener("place_changed", () => {
            const websiteUrlExtractionPattern = new RegExp(urlPatternString);
            const place = this.autocomplete.getPlace();

            const lat = place.geometry.location.lat();
            const lng = place.geometry.location.lng();
            const name = place.name;
            const hoursOfOperation = place.opening_hours?.weekday_text;
            const nonFormattedPhoneNumber = place.formatted_phone_number?.replace(
                /\D+/g,
                ""
            );

            const websiteUrl = _.first(
                place.website.match(websiteUrlExtractionPattern)
            );

            const streetNumber = this.getGoogleMapsAddressField(
                place,
                "street_number"
            );
            const route = this.getGoogleMapsAddressField(place, "route");
            const city = this.getGoogleMapsAddressField(place, "locality");
            const state = this.getGoogleMapsAddressField(
                place,
                "administrative_area_level_1"
            );
            const zipCode = this.getGoogleMapsAddressField(
                place,
                "postal_code"
            );

            const formattedHours = this.formatHours(hoursOfOperation);

            this.form.googlePlaceId = place.place_id;
            this.form.lat = lat;
            this.form.lng = lng;
            this.form.address1 = `${streetNumber} ${route}`;
            this.form.city = city;
            this.form.state = state;
            this.form.zipCode = zipCode;
            this.form.phoneNumber = nonFormattedPhoneNumber;
            this.form.name = name;
            this.form.hoursOfOperations = formattedHours;
            this.form.websiteUrl = websiteUrl;
        });
    },
    computed: {
        dealerToEdit: get("onBoarding/dealer"),
        dealerExists() {
            return !_.isEmpty(this.existingDealers);
        },
        hoursOptions() {
            const times = [];
            times.push("CLOSED");

            const timeOfDay = moment().startOf("day");
            const minuteIncrement = 30;

            do {
                times.push(timeOfDay.format("h:mm A"));
                timeOfDay.add(minuteIncrement, "minutes");
            } while (!(timeOfDay.hour() === 0 && timeOfDay.minute() === 0));

            return times;
        },
        googlePlaceId() {
            return this.form.googlePlaceId;
        },
        isGroupSet() {
            return !_.isNil(this.form.dealerGroupId);
        }
    },
    methods: {
        fetchDealerData: call("onBoarding/fetchDealer"),
        createDealer: call("onBoarding/createDealer"),
        formatHours(hours) {
            let hoursRaw = hours;

            if (_.isEmpty(hours) || _.isNil(hours)) {
                hoursRaw = this.getDefaultHours();
            }

            const formattedHours = [];
            const regex2 = new RegExp(
                "\\w+: (\\d{1,2}:\\d{2} \\w{2}) . (\\d{1,2}:\\d{2} \\w{2})"
            );

            _.map(hoursRaw, h => {
                const day = {};
                const m = h.match(regex2);
                if (m === null) {
                    day.openHours = "CLOSED";
                    day.closeHours = "CLOSED";
                    formattedHours.push(day);
                    return;
                }
                day.openHours = m[1];
                day.closeHours = m[2];
                formattedHours.push(day);
            });

            return formattedHours;
        },
        getGoogleMapsAddressField(place, fieldName) {
            for (var i = 0; i < place.address_components.length; i++) {
                var addressType = place.address_components[i].types[0];
                if (addressType === fieldName) {
                    return place.address_components[i].short_name;
                }
            }

            return "";
        },
        submit() {
            this.loading = true;
            this.createDealer(this.form)
                .then(dealer => {
                    this.$router.push({
                        path: `/on-boarding/${dealer.id}/billing`
                    });
                })
                .catch(() => {
                    this.$toastr.e("Please check form errors!");
                });
        },
        resetForm() {
            this.form = {
                name: "",
                address1: "",
                city: "",
                state: "",
                zipCode: "",
                phoneNumber: "",
                dealerGroupId: null,
                timeZone: "",
                googlePlaceId: null,
                lat: "",
                lng: "",
                dealerId: null,
                hoursOfOperations: [],
                walmartStoreId: null
            };
        },
        getDealerInfoOptions() {
            api.get("/on-boarding/dealer-info-options")
                .then(response => {
                    this.timeZones = response.data.timeZones;
                    this.states = response.data.states;
                    this.dealerGroups = response.data.dealerGroups;
                })
                .catch(error => {
                    console.error(error);
                });
        },
        getDefaultHours() {
            return [
                "Monday: 9:00 AM – 9:00 PM",
                "Tuesday: 9:00 AM – 9:00 PM",
                "Wednesday: 9:00 AM – 9:00 PM",
                "Thursday: 9:00 AM – 9:00 PM",
                "Friday: 9:00 AM – 9:00 PM",
                "Saturday: 9:00 AM – 9:00 PM",
                "Sunday: 9:00 AM – 9:00 PM"
            ];
        }
    }
};
</script>
