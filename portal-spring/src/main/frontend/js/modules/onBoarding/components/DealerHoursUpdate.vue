<template>
    <layout title="Dealer Hours" :dealer-id="dealerId">
        <validation-observer ref="observer" v-slot="{ passes }">
            <b-form @submit.prevent="passes(submit)">
                <div class="container-fluid my-2">
                    <div class="row">
                        <loading-block v-if="loading"/>
                        <div v-else class="col-lg-8 border-right">
                            <div class="required-msg text-right">
                                * indicates a required field
                            </div>
                            <b-row>
                                <b-col cols="6">
                                    <b-form-group>
                                        <b-select-validate
                                            v-model="form.timeZone"
                                            name="timeZone"
                                            label="Time Zone *"
                                            rules="required"
                                            :options="timeZones"
                                        />
                                    </b-form-group>
                                </b-col>
                            </b-row>
                            <hours-of-operations
                                v-if="hoursOfOperations"
                                v-model="hoursOfOperations"
                            />
                        </div>
                        <div class="col-lg-4">
                            <div
                                class="d-flex flex-column justify-content-center align-items-center h-100"
                            >
                                <i
                                    class="fas fa-car mb-5"
                                    style="font-size: 140px;color: #e5e5e5 "
                                    aria-hidden="true"
                                />
                                <div class="d-flex mb-3">
                                    <b-button
                                        v-b-popover.hover.top="
                                            'Creates Dealer and allows you to finish on-boarding them later! You will be redirected to the dealer details page.'
                                        "
                                        :disabled="loading || disabled"
                                        type="submit"
                                        variant="primary"
                                    >
                                        Save & Finish
                                    </b-button>

                                    <b-button
                                        v-b-popover.hover.top="
                                            'Creates Dealer and continues to the Contract Details set up.'
                                        "
                                        :disabled="loading || disabled"
                                        class="mx-2"
                                        variant="info"
                                        @click="nextStep"
                                    >
                                        Save & Continue
                                    </b-button>
                                    <b-link
                                        v-b-popover.hover.top="
                                            'Cancel edits and return to the dealer details page.'
                                        "
                                        :disabled="loading"
                                        class="btn btn-danger mr-2 d-flex justify-content-center align-items-center"
                                        :href="`/dealer/${dealerId}`"
                                    >
                                        Cancel
                                    </b-link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </b-form>
        </validation-observer>
    </layout>
</template>
<script>
import _ from "lodash";
import api from "@/api";
import Layout from "Modules/onBoarding/components/Layout";
import LoadingBlock from "Modules/onBoarding/components/LoadingBlock";
import {call, get, sync} from "vuex-pathify";
import BInputValidate from "Components/FormValidation/BInputValidate";
import BSelectValidate from "Components/FormValidation/BSelectValidate";
import {mask} from "vue-the-mask";
import dealerFormMixin from "../mixins/dealerFormMixin";
import HoursOfOperations from "./HoursOfOperations";

export default {
    name: "DealerHoursUpdate",
    components: {
        HoursOfOperations,
        BSelectValidate,
        BInputValidate,
        LoadingBlock,
        Layout
    },
    directives: {mask},
    mixins: [dealerFormMixin],
    props: {
        dealerId: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            loading: false,
            disabled: false,
            form: {
                timeZone: ""
            },
            timeZones: [],
            defaultHours: [
                {openHours: "9:00 AM", closeHours: "9:00 PM"},
                {openHours: "9:00 AM", closeHours: "9:00 PM"},
                {openHours: "9:00 AM", closeHours: "9:00 PM"},
                {openHours: "9:00 AM", closeHours: "9:00 PM"},
                {openHours: "9:00 AM", closeHours: "9:00 PM"},
                {openHours: "9:00 AM", closeHours: "9:00 PM"},
                {openHours: "9:00 AM", closeHours: "9:00 PM"}
            ]
        };
    },
    created() {
        this.fetchDealerData(this.dealerId).then(() => {
            this.form = _.pick(this.dealerToEdit, Object.keys(this.form));

            if (_.isNil(this.dealerToEdit.hoursOfOperations)) {
                this.setDefaultHours(this.defaultHours);
            }
        });

        this.fetchTimeZones();
    },

    computed: {
        hoursOfOperations: sync("onBoarding/dealer@hoursOfOperations"),
        dealerToEdit: get("onBoarding/dealer")
    },
    methods: {
        fetchDealerData: call("onBoarding/fetchDealer"),
        updateDealer: call("onBoarding/updateDealer"),
        setDefaultHours: call("onBoarding/setDefaultHoursOfOperations"),

        submit() {
            this.loading = true;
            this.updateDealer({
                ...this.form,
                hoursOfOperations: this.hoursOfOperations
            })
                .then(dealer => {
                    if (_.isEqual(this.doContinue, true)) {
                        this.$router.push({
                            path: `/on-boarding/${this.dealerToEdit.id}/finance-integration`
                        });
                    } else {
                        document.location = "/dealer/" + dealer.id;
                    }
                })
                .catch(error => {
                    console.error(error);
                    this.$toastr.e("Please check form errors!");
                });
        },

        fetchTimeZones() {
            api.get("/on-boarding/time-zones")
                .then(response => {
                    this.timeZones = response.data;
                })
                .catch(error => {
                    console.error(error);
                });
        }
    }
};
</script>
