<template>
    <layout title="Inventory Configuration" :dealer-id="dealerId">
        <validation-observer ref="observer" v-slot="{ passes }">
            <b-form novalidate @submit.prevent="passes(submit)">
                <div class="container-fluid">
                    <div class="row">
                        <loading-block v-if="loading"/>

                        <div v-else class="col-lg-8 border-right">
                            <div class="required-msg text-right">
                                * indicates a required field
                            </div>
                            <b-row>
                                <b-col>
                                    <b-form-group class="mb-4" label="Free Delivery Enabled"
                                                  label-for="deliveryEnabled">
                                        <b-form-checkbox
                                            v-model="form.deliveryEnabled"
                                            name="check-button"
                                            switch
                                        >
                                            {{ form.deliveryEnabled | yesno }}
                                        </b-form-checkbox>
                                    </b-form-group>

                                    <b-input-validate v-if="form.deliveryEnabled"
                                                      v-model="form.deliveryDistance"
                                                      rules="integer|required_if:deliveryEnabled,true"
                                                      label="Delivery Distance (in miles)"
                                                      name="deliveryDistance"
                                    />
                                </b-col>
                            </b-row>
                            <hr>
                            <b-row>
                                <b-col cols="12">
                                    <b-select-validate
                                        v-model="form.inventorySource"
                                        rules="required"
                                        label="Inventory Source *"
                                        name="inventorySource"
                                        :options="inventorySourceOptions"
                                    />
                                </b-col>
                            </b-row>
                            <b-row>
                                <b-col cols="12">
                                    <b-form-group label="Vendor Inventory Id" label-for="vendorInventoryId">
                                        <b-form-input
                                            id="vendorInventoryId"
                                            v-model="form.vendorInventoryId"
                                            name="vendorInventoryId"
                                        />
                                    </b-form-group>
                                </b-col>
                            </b-row>
                            <b-row>
                                <b-col cols="12">
                                    <b-input-validate
                                        v-model="form.vastDealerId"
                                        rules="integer|required_if:inventorySource,VT"
                                        :label="isVastDealerIdRequired ? 'Vast Dealer Id *' : 'Vast Dealer Id'"
                                        name="vastDealerId"
                                    />
                                </b-col>
                            </b-row>
                            <b-row>
                                <b-col cols="12">
                                    <b-form-group label="Nissan Dealer Code" label-for="nnaDealerId">
                                        <b-form-input
                                            id="nnaDealerId"
                                            v-model="form.nnaDealerId"
                                            name="nnaDealerId"
                                        />
                                    </b-form-group>
                                </b-col>
                            </b-row>
                        </div>
                        <div class="col-lg-4">
                            <div class="d-flex flex-column justify-content-center align-items-center h-100">
                                <i aria-hidden="true" class="fas fa-cogs mb-4"
                                   style="font-size: 100px;color: #e5e5e5 "/>
                                <div class="d-flex mb-3">
                                    <b-button
                                        v-b-popover.hover.top="'Submits Inventory configurations and allows you to finish on-boarding them later! You will be redirected to the dealer details page.'"
                                        :disabled="loading"
                                        type="submit"
                                        variant="primary"
                                    >
                                        Save & Finish
                                    </b-button>

                                    <b-button
                                        v-b-popover.hover.top="'Submits Inventory configurations and continues to the Lead configuration set up!'"
                                        :disabled="loading"
                                        class="mx-2"
                                        variant="info"
                                        @click="passes(nextStep)"
                                    >
                                        Save & Continue
                                    </b-button>

                                    <b-link
                                        v-b-popover.hover.top="'Cancels form and returns you to the dealer details page.'"
                                        :disabled="loading"
                                        class="btn btn-danger mr-2 d-flex justify-content-center align-items-center"
                                        :href="`/dealer/${dealerId}`"
                                    >
                                        Cancel
                                    </b-link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </b-form>
        </validation-observer>
    </layout>
</template>
<script>
import Layout from 'Modules/onBoarding/components/Layout';
import api from '@/api';
import LoadingBlock from 'Modules/onBoarding/components/LoadingBlock';
import {call, get} from 'vuex-pathify';
import BSelectValidate from 'Components/FormValidation/BSelectValidate';
import BInputValidate from 'Components/FormValidation/BInputValidate';
import veeValidateUtils from '@/api/veeValidateUtils';
import dealerFormMixin from '../../mixins/dealerFormMixin';

export default {
    name: 'InventoryConfiguration',
    components: {BInputValidate, BSelectValidate, LoadingBlock, Layout},
    mixins: [dealerFormMixin],
    props: {
        dealerId: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            form: {
                inventorySource: null,
                vastDealerId: null,
                vendorInventoryId: null,
                deliveryEnabled: null,
                deliveryDistance: null,
                nnaDealerId: null,
            },
            inventorySourceOptions: [],
            loading: false
        };
    },
    computed: {
        dealer: get('onBoarding/dealer'),
        isVastDealerIdRequired() {
            return this.displayInventorySource(this.form.inventorySource) === 'Vast';
        }
    },
    created() {
        this.loadDealer(this.dealerId)
            .then((dealer) => {
                this.form.inventorySource = _.get(dealer, 'inventorySource');
                this.form.vastDealerId = _.get(dealer, 'vastDealerId');
                this.form.vendorInventoryId = _.get(dealer, 'vendorInventoryId');
                this.form.deliveryEnabled = _.get(dealer, 'deliveryEnabled');
                this.form.deliveryDistance = _.get(dealer, 'deliveryDistance');
                this.form.nnaDealerId = _.get(dealer, 'nnaDealerId');
            });

        api.get('/on-boarding/inventory-sources')
            .then(response => {
                this.inventorySourceOptions = response.data.map((val) => ({
                    value: val,
                    text: this.displayInventorySource(val)
                }));
            })
            .catch(error => {
                console.log('error =', error);
            });
    },
    methods: {
        setInventoryConfiguration: call('onBoarding/setInventoryConfiguration'),
        loadDealer: call('onBoarding/loadDealer'),
        submit() {
            this.loading = true;
            this.setInventoryConfiguration(this.form)
                .then((dealer) => {
                    if (_.isEqual(this.doContinue, true)) {
                        this.$router.push({path: `/on-boarding/${dealer.id}/lead-configuration`});
                    } else {
                        document.location = '/dealer/' + dealer.id;
                    }
                })
                .catch(error => {
                    this.$refs.observer.setErrors(veeValidateUtils.convertErrorToObserverErrors(error));
                });
        },
        displayInventorySource(source) {
            switch (source) {
                case 'AN':
                    return 'AutoNation';
                case 'HN':
                    return 'HomeNet';
                case 'VT':
                    return 'Vast';
                case 'CV':
                    return 'Carvana';
                default:
                    return 'Unknown';
            }
        }
    }
};
</script>
