<template>
    <layout v-if="$acl.hasAuthority('edit:contracts')" title="Stripe Subscription">
        <b-container fluid>
            <b-row align-h="center">
                <b-col v-if="stripeCustomerId" cols="6">
                    <loading-block v-if="loading"/>

                    <i-box v-if="contractId" class="pt-2" :collapsible="false">
                        <b-alert show>
                            Customer already has a contract configured.
                        </b-alert>
                        <div class="mt-2 float-right">
                            <b-button size="lg" @click="handleDone()">
                                Ok
                            </b-button>
                        </div>
                    </i-box>

                    <create-dealer-contract-form v-else :dealer-id="dealerId"
                                                 @cancel="handleDone" @done="handleDone"
                    />
                </b-col>
            </b-row>
        </b-container>
    </layout>
</template>
<script>
import Layout from 'Modules/onBoarding/components/Layout';
import dealerFormMixin from '../mixins/dealerFormMixin';
import LoadingBlock from 'Modules/onBoarding/components/LoadingBlock';
import {call, get} from 'vuex-pathify';
import CreateDealerContractForm from 'Modules/dealer/components/DealerContracts/CreateDealerContractForm';
import IBox from 'Components/IBox';

export default {
    name: 'DealerContract',
    components: {CreateDealerContractForm, LoadingBlock, Layout, IBox},
    mixins: [dealerFormMixin],
    props: {
        dealerId: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            loading: false
        };
    },
    computed: {
        stripeCustomerId: get('onBoarding/dealer@stripeCustomerId'),
        contractId: get('onBoarding/dealer@contractId')
    },
    created() {
        if (!this.$acl.hasAuthority('edit:contracts')) {
            document.location = '/dealer/' + this.dealerId;
        } else {
            this.loadDealer(this.dealerId);
        }
    },
    methods: {
        loadDealer: call('onBoarding/loadDealer'),
        handleDone() {
            document.location = '/dealer/' + this.dealerId;
        }
    }
};
</script>
