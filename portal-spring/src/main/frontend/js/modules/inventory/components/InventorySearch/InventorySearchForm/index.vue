<template>
    <b-form inline class="inventory-search-form fs-ignore-formabandon" @submit.prevent="doSubmit">
        <b-form-group>
            <b-input v-model="dealerName" placeholder="Dealer Name"/>
        </b-form-group>
        <b-form-group>
            <b-input v-model="vin" placeholder="VIN"/>
        </b-form-group>
        <b-button type="submit" size="sm" variant="primary">
            Search
        </b-button>
        <b-button id="clear-button" size="sm" variant="info"
                  @click="clearFilters"
        >
            Reset
        </b-button>
    </b-form>
</template>

<style scoped>
.form-group {
    margin-right: 5px;
}

.inventory-search-form {
    padding-left: 0;
}

#clear-button, #search-button {
    margin: 5px 0 5px 5px;
}

@media screen and (max-width: 765px) {
    .inventory-search-form {
        padding: 0 !important;
    }

    #clear-button, #search-button {
        margin: 5px 0 0 0;
        width: 100%;
    }
}
</style>

<script>
import {call, sync} from 'vuex-pathify';

export default {
    name: 'InventorySearchForm',

    computed: {
        name: sync('inventorySearch/filters@name'),
        dealerName: sync('inventorySearch/filters@dealerNameText'),
        vin: sync('inventorySearch/filters@vin')
    },

    methods: {
        doSubmit: call('inventorySearch/doSearch'),
        clearFilters: call('inventorySearch/clearFilters')
    }

};
</script>
