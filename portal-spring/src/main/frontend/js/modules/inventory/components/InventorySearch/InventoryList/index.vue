<template>
    <div v-if="isLoading">
        <table-content-loader/>
    </div>

    <div v-else>
        <div class="ibox">
            <div class="ibox-title">
                <h5>Search Results</h5>
                <div class="ibox-tools">
                    <b-button v-b-modal.table-column-config size="sm">
                        <i aria-hidden="true" class="fas fa-cog"/>
                        <span class="list-action">Show / Hide Columns</span>
                    </b-button>
                    <table-column-selector
                        id="table-column-config"
                        v-model="displayFields"
                        :fields="fields"
                    />

                    <b-button v-b-modal.export-column-config size="sm">
                        <i aria-hidden="true" class="fas fa-file-export"/>
                        <span class="list-action">Export</span>
                    </b-button>
                    <export-column-selector
                        id="export-column-config"
                        v-model="exportFields"
                        :display-fields="displayFields"
                        :fields="fields"
                        @export="doExport"
                    />
                </div>
            </div>
            <div class="ibox-content">
                <div class="result-count">
                    <pagination
                        :show-pager="false"
                        :page-metadata="pageMetadata"
                        :page="page"
                        @page-changed="pageChanged"
                    />
                </div>

                <b-table
                    responsive
                    striped
                    hover
                    bordered
                    :fields="fieldsForDisplay()"
                    :items="searchResults"
                    :sort-by.sync="sortBy"
                    :sort-desc.sync="sortDesc"
                    :no-local-sorting="true"
                    :no-sort-reset="true"
                    @sort-changed="sortChanged"
                >
                    <template v-slot:cell(id)="row">
                        <b-button
                            size="sm"
                            block
                            variant="outline-secondary"
                            @click="viewLog(row.item.id)"
                        >
                            <i aria-hidden="true" class="fa fa-list"/>
                            Audit Log
                        </b-button>
                    </template>
                    <template v-slot:cell(dealer)="row">
                        {{ row.item.dealer.name }}
                    </template>
                    <template v-slot:cell(price)="row">
                        {{ row.item.price | currency("$", 0) }}
                    </template>
                    <template v-slot:cell(vin)="row">
                        <a
                            v-if="row.item.active"
                            href="#"
                            @click="goToDetails(row.item)"
                        >{{ row.item.vin }}</a
                        >
                        <span v-else>{{ row.item.vin }}</span>
                    </template>
                </b-table>

                <!-- Audit Log modal -->
                <b-modal
                    id="audit-log-modal"
                    size="lg"
                    header-bg-variant="primary"
                    title="Audit Log"
                    ok-only
                >
                    <audit-log store="inventory"/>
                </b-modal>

                <div class="pagination-wrapper">
                    <pagination
                        :show-totals="false"
                        :page-metadata="pageMetadata"
                        :page="page"
                        @page-changed="pageChanged"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<style>
.result-count {
    padding: 10px 0;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>

<script>
import {call, dispatch, get, sync} from "vuex-pathify";
import Pagination from "Components/Pagination";
import TableContentLoader from "Components/TableContentLoader";
import TableColumnSelector from "Components/TableColumnSelector";
import searchPageMixin from "@/mixins/searchPageMixin";
import VehicleSaleAuditLog from "Modules/vehicleSale/components/VehicleSaleAuditLog";
import ExportColumnSelector from "Components/ExportColumnSelector";
import AuditLog from "Components/AuditLog";

export default {
    name: "InventoryList",
    components: {
        VehicleSaleAuditLog,
        TableContentLoader,
        ExportColumnSelector,
        TableColumnSelector,
        Pagination,
        AuditLog
    },
    mixins: [searchPageMixin],
    data() {
        return {
            fields: [
                {
                    key: "id",
                    label: "Audit Log",
                    exportable: false
                },
                {
                    key: "active"
                },
                {
                    key: "dealer.name",
                    label: "dealer"
                },
                {
                    key: "stockNumber",
                    label: "Stock #"
                },
                {
                    key: "vin"
                },
                {
                    key: "stockType",
                    sortable: true
                },
                {
                    key: "bodyStyle"
                },
                {
                    key: "year",
                    sortable: true
                },
                {
                    key: "make",
                    sortable: true
                },
                {
                    key: "model",
                    sortable: true
                },
                {
                    key: "trim",
                    sortable: true
                },
                {
                    key: "priceWithRebates",
                    label: "Price (w/ rebates)",
                    formatter: "currencyFormatter",
                    sortable: true
                }
            ]
        };
    },

    computed: {
        searchResults: get("inventorySearch/searchLoader@data"),
        pageMetadata: get("inventorySearch/pageMetadata"),
        isLoading: get("inventorySearch/<EMAIL>"),
        page: sync("inventorySearch/pageable@page"),
        sort: sync("inventorySearch/pageable@sort"),
        displayFields: sync("inventorySearch/displayFields"),
        exportFields: sync("inventorySearch/exportFields"),
        vehicleBaseUrl: get("inventorySearch/vehicleBaseUrl")
    },

    watch: {
        page() {
            this.doPageLoad();
        },
        sort() {
            this.doSort();
        }
    },

    mounted() {
        this.doPageLoad();
    },

    methods: {
        doExport: call("inventorySearch/doExport"),
        doPageLoad: call("inventorySearch/doPageLoad"),
        doSort: call("inventorySearch/doSort"),
        goToDetails(inventory) {
            window.open(
                `${this.vehicleBaseUrl}${inventory.stockType.toLowerCase()}/${
                    inventory.id
                }`
            );
        },
        currencyFormatter(value) {
            return this.$options.filters.currency(value, "$", 0);
        },
        viewLog(vehicleId) {
            dispatch('inventory/loadChangeSets', vehicleId)
                .then(() => this.$bvModal.show('audit-log-modal'));
        }
    }
};
</script>
