import Vuex from 'vuex';
import Vue from 'vue';
import pathify from '@/lib/pathify';
import loggedInUser from '@/store/loggedInUser';
import inventorySearch from './modules/inventorySearch';
import inventory from './modules/inventory';

const debug = process.env.NODE_ENV !== 'production';

Vue.use(Vuex);

export default new Vuex.Store({
    plugins: [pathify.plugin],
    modules: {
        loggedInUser,
        inventorySearch,
        inventory
    },
    strict: debug
});
