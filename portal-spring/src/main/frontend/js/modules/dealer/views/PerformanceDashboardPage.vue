<template>
    <dashboard :search-widget-enabled="false" :drill-down-enabled="false">
        <template v-slot:header>
            <b-row>
                <b-col>
                    <h1>Dealer KPI Search</h1>
                </b-col>
            </b-row>
            <b-row>
                <b-col>
                    <dealer-search/>
                </b-col>
            </b-row>
        </template>
    </dashboard>
</template>

<script>
import Dashboard from '../components/Dashboard';
import TableContentLoader from 'Components/TableContentLoader/index';
import DealerSearch from 'Modules/dealer/components/DealerSearch/index';

export default {
    name: 'DealerDetailsPage',
    components: {
        DealerSearch,
        Dashboard,
        TableContentLoader
    }
};
</script>

<style lang="scss">
td {
    overflow: auto;
}
</style>
