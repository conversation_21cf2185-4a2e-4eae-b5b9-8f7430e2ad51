<template>
    <div>
        <div v-if="isLoading">
            <h1>Loading Data...</h1>
            <table-content-loader/>
        </div>

        <div v-else>
            <dealer-banner/>

            <b-container fluid>
                <b-row class="my-4">
                    <b-col xl="3">

                        <manage-dealership/>

                        <contract-details v-presentation-hidden/>

                        <dealer-google-maps/>

                        <hours-of-operation/>
                    </b-col>

                    <b-col xl="5">
                        <dealership-details/>

                        <inventory-details/>

                        <preferences v-presentation-hidden/>
                    </b-col>

                    <b-col xl="4">
                        <pricing-metrics
                            v-if="renderPricingMetrics"
                            :dealer-id="dealerId"
                        />

                        <inventory-counts
                            v-if="inventorySource"
                            :dealer-id="dealerId"
                        />

                        <dealer-metrics :dealer-ids="[dealerId]"/>
                    </b-col>
                </b-row>

                <dealer-program-list :dealer-id="dealerId"/>

                <dealer-contracts v-presentation-hidden/>

                <dealer-fees :dealer-id="dealerId"/>

                <dealer-financier-list
                    v-if="showDealerFinancierList"
                    :dealer-id="dealerId"
                    :show-refresh="hasRouteOneDealerId"
                />

                <billing-details
                    v-presentation-hidden
                    :dealer-id="dealerId"
                    :stripe-customer-id="stripeCustomerId"
                />

                <notes-list id="notes" v-presentation-hidden :dealer-id="dealerId"/>

                <dealer-freshdesk v-presentation-hidden/>

                <dealer-users/>

                <dealer-leads :dealer-id="dealerId"/>

                <vehicle-sale v-presentation-hidden/>

                <dealer-events v-presentation-hidden/>
            </b-container>
        </div>


        <add-note-modal :dealer-id="dealerId"/>

        <dealer-add-contract-modal :dealer-id="dealerId"/>
    </div>
</template>

<script>
import ManageDealership from '../components/ManageDealership'
import ContractDetails from '../components/ContractDetails'
import DealerGoogleMaps from '../components/DealerGoogleMaps'
import HoursOfOperation from '../components/HoursOfOperation'
import DealershipDetails from '../components/DealershipDetails'
import Preferences from '../components/Preferences'
import PricingMetrics from '../components/PricingMetrics'
import DealerMetrics from '../components/DealerMetrics'
import DealerUsers from '../components/DealerUsers'
import DealerLeads from '../components/DealerLeads'
import DealerBanner from '../components/DealerBanner'
import InventoryCounts from '../components/InventoryCounts'
import DealerEvents from '../components/DealerEvents'
import VehicleSale from '../components/VehicleSales'
import DealerContracts from '../components/DealerContracts'
import DealerFreshdesk from '../components/DealerFreshdesk'
import NotesList from '../components/Notes/List'
import AddNoteModal from '../components/Notes/AddNoteModal'
import {call, get} from 'vuex-pathify'
import _ from 'lodash'
import BillingDetails from '../components/BillingDetails'
import InventoryDetails from '../components/InventoryDetails'
import DealerFinancierList from '../components/DealerFinancierList'
import DealerFees from '../components/DealerFees'
import TableContentLoader from 'Components/TableContentLoader/index'
import DealerProgramList from 'Modules/dealer/components/DealerPrograms/DealerProgramList'
import DealerAddContractModal from 'Modules/dealer/components/DealerContracts/DealerAddContractModal'

export default {
    name: 'DealerDetailsPage',
    components: {
        DealerAddContractModal,
        DealerProgramList,
        TableContentLoader,
        DealerFees,
        DealerFinancierList,
        InventoryDetails,
        BillingDetails,
        DealerFreshdesk,
        DealerContracts,
        VehicleSale,
        DealerEvents,
        InventoryCounts,
        DealerBanner,
        DealerLeads,
        DealerMetrics,
        PricingMetrics,
        Preferences,
        DealerUsers,
        DealershipDetails,
        HoursOfOperation,
        DealerGoogleMaps,
        ContractDetails,
        ManageDealership,
        NotesList,
        AddNoteModal
    },
    props: {
        dealerId: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            HN: 'HN',
            AN: 'AN'
        }
    },
    computed: {
        dealer: get('dealer/selectedDealer'),
        stripeCustomerId: get('dealer/selectedDealer@stripeCustomerId'),
        dealerInventorySource: get('dealer/selectedDealer@inventorySource'),
        atlasDealerTrackPhase2Enabled: get('loggedInUser/featureFlags@ATLAS_DEALER_TRACK_PHASE_2_ENABLED'),
        showDealerFinancierList () {
            return !this.atlasDealerTrackPhase2Enabled;
        },
        renderPricingMetrics() {
            return (
                this.dealerInventorySource === this.HN ||
                this.dealerInventorySource === this.AN
            )
        },
        inventorySource() {
            return !_.isNil(this.dealerInventorySource)
        },
        isLoading() {
            return _.isNil(this.dealer)
        },
        hasRouteOneDealerId() {
            return (
                !_.isNil(this.dealer.routeOneDealerId) &&
                this.dealer.routeOneDealerId !== ''
            )
        }
    },
    mounted() {
        this.fetchDealerData(this.dealerId)
    },
    methods: {
        fetchDealerData: call('dealer/fetchDealerData'),
    }
}
</script>

<style lang="scss">
td {
    overflow: auto;
}
</style>
