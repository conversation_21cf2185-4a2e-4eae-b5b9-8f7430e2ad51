<template>
    <b-modal v-model="isOpen" @show="onShow">
        <template #modal-title>
            Dealer Plugin Status
        </template>

        <p><strong>Last request from plugin:</strong></p>
        <b-spinner v-if="pluginStatus.loading" label="Loading"/>
        <ul v-else>
            <li v-for="lastAccess in lastAccessMap" :key="lastAccess.domain">
                {{ lastAccess.domain }}:
                {{ lastAccess.lastAccessTime | formatAccessTime }}
                <status-badge
                    :status="
                        isAcceptableLastAccessTime(lastAccess.lastAccessTime)
                    "
                />
            </li>
            <li v-if="lastAccessMap.length === 0">
                No requests found
                <status-badge
                    error-popover="There have been no requests from the plugin for a vin status from this dealer."
                    :status="false"
                />
            </li>
        </ul>

        <p><strong>Check URL for plugin issues:</strong></p>
        <b-form inline @submit.prevent="getPluginUrlStatus">
            <b-form-input
                v-model="url"
                class="flex-fill"
                required
                placeholder="URL to check"
            />
            <b-button type="submit" variant="primary">Check</b-button>
        </b-form>

        <b-spinner v-if="urlStatus.loading" label="Loading"/>
        <ul v-else-if="!urlStatus.loading && urlStatus.data" class="mt-3">
            <li>
                Response:
                <status-badge
                    error-popover="Unable to receive a response from the URL.  Check to make sure the URL is correct."
                    :status="urlStatus.data.responseSuccess"
                />
            </li>
            <li>
                Found javascript tag:
                <status-badge
                    error-popover="The javascript script tag is not found"
                    :status="urlStatus.data.foundJavascript"
                />
            </li>
            <li>
                Found widget tag:
                <status-badge
                    error-popover="The <cs-buy-widget> tag is not found. NOTE: Sometimes this is a false negative.  Dealer site providers sometimes put the widget onto their site dynamically, and this checker is not capable of detecting that.  A manual inspection of the page will need to be done to know for sure."
                    :status="urlStatus.data.foundWidget"
                />
            </li>
            <li>
                Found correct dealer ID:
                <status-badge
                    :error-popover="
                        `The dealer ID '${urlStatus.data.dealerIdUsed}' is not associated with this dealer.`
                    "
                    :status="urlStatus.data.correctDealerId"
                />
            </li>
            <li>
                Found VIN on widget:
                <status-badge
                    error-popover="No vin was found on the widget"
                    :status="urlStatus.data.foundVin"
                />
            </li>
            <li>
                Campaign ID being used:
                <blockquote class="bg-muted font-bold text-nowrap p-1">
                    {{ urlStatus.data.campaignIdUsed }}
                </blockquote>
            </li>
        </ul>
    </b-modal>
</template>

<script>
import StatusBadge from "./StatusBadge";
import api from "@/api";
import moment from "moment";

export default {
    components: {StatusBadge},
    props: {
        dealerId: {
            type: String,
            required: true
        }
    },
    filters: {
        formatAccessTime(time) {
            return moment(time).fromNow();
        }
    },
    data() {
        return {
            url: null,
            isOpen: false,
            pluginStatus: {
                loading: true,
                data: null
            },
            urlStatus: {
                loading: false,
                data: null
            }
        };
    },
    computed: {
        lastAccessMap() {
            return _.map(
                _.get(this.pluginStatus, "data.lastAccessMap"),
                (lastAccessTime, domain) => {
                    return {
                        lastAccessTime,
                        domain
                    };
                }
            );
        }
    },
    methods: {
        isAcceptableLastAccessTime(accessTime) {
            return moment().diff(moment(accessTime), "days") <= 1;
        },
        onShow() {
            this.getPluginStatus();
        },
        getPluginStatus() {
            this.pluginStatus.loading = true;
            api.get("/dealer-plugin", {
                dealerId: this.dealerId
            }).then(response => {
                this.pluginStatus.data = response.data;
                this.pluginStatus.loading = false;
            });
        },
        getPluginUrlStatus() {
            this.urlStatus.loading = true;
            api.get("/dealer-plugin", {
                dealerId: this.dealerId,
                url: encodeURI(this.url)
            }).then(response => {
                this.urlStatus.data = response.data;
                this.urlStatus.loading = false;
            });
        },
        open() {
            this.isOpen = true;
        }
    }
};
</script>
