<template>
    <i-box title="Inventory">
        <template slot="header-actions">
            <b-button
                v-if="$acl.hasAuthority('edit:dealer')"
                size="xs"
                variant="light"
                :href="`/on-boarding/${dealer.id}/inventory-configuration`"
            >
                <i aria-hidden="true" class="fas fa-pencil-alt"/>
            </b-button>
        </template>
        <div>
            <div>
                <div class="d-flex justify-content-between align-items-center border-bottom p-2">
                    <span class="font-weight-bold">Nissan Dealer Code</span>
                    <span>{{ dealer.nnaDealerId }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center border-bottom p-2">
                    <span class="font-weight-bold">Inventory Source</span>
                    <span>{{ dealer.inventorySource }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center border-bottom p-2">
                    <span class="font-weight-bold">Delivery Enabled</span>
                    <div>{{ dealer.deliveryEnabled }}</div>
                </div>
                <div v-if="dealer.deliveryEnabled"
                     class="d-flex justify-content-between align-items-center border-bottom p-2">
                    <span class="font-weight-bold">Delivery Distance</span>
                    <span>{{ dealer.deliveryDistance }}</span>
                </div>
                <div v-presentation-hidden>
                    <div class="d-flex justify-content-between align-items-center border-bottom p-2">
                        <span class="font-weight-bold">Vendor Inventory Id</span>
                        <span>{{ dealer.vendorInventoryId }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center border-bottom p-2">
                        <span class="font-weight-bold">Vendor DMS Id</span>
                        <span>{{ dealer.vendorDmsId }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center border-bottom p-2">
                        <span class="font-weight-bold">Vast Dealer Id</span>
                        <span>{{ dealer.vastDealerId }}</span>
                    </div>
                </div>
            </div>
        </div>
    </i-box>
</template>

<script>
import IBox from 'Components/IBox';
import {get} from 'vuex-pathify';

export default {
    components: {IBox},
    computed: {
        dealer: get('dealer/selectedDealer')
    }
};
</script>
