<template>
    <div>
        <b-form-select
            v-if="$acl.hasAuthority('ROLE_ADMIN')"
            :value="id"
            :options="options"
            text-field="name"
            value-field="id"
            @change="updateSuccessManager"
        >
            <template #first>
                <b-form-select-option :value="null">
                    -- None --
                </b-form-select-option>
            </template>
        </b-form-select>
        <div
            v-if="$acl.hasAuthority('ROLE_DEALER')"
            class="d-flex justify-content-between align-items-center"
        >
            <span v-if="id">
                {{ successManagerModel.name }}
            </span>
        </div>
    </div>
</template>

<script>
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";
import {dispatch, get} from "vuex-pathify";

export default {
    components: {Multiselect},
    props: {
        id: {
            type: String,
            required: false,
            default: null
        },
        subscriptionId: {
            type: String,
            required: true
        }
    },
    computed: {
        options: get("dealer/successManagers"),
        successManagerModel() {
            return _.filter(this.options, ["id", this.id]);
        }
    },
    methods: {
        updateSuccessManager(userId) {
            dispatch("dealer/updateSuccessManagerId", {
                userId,
                subscriptionId: this.subscriptionId
            });
        }
    }
};
</script>
