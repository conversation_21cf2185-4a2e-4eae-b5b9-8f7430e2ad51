<template>
    <div>
        <i-box title="Contracts">
            <template slot="header-actions">
                <dealer-add-contract :dealer-id="dealerId" @done="fetchContracts"/>
                <b-button
                    size="xs"
                    variant="light"
                    @click="fetchContracts"
                >
                    <i aria-hidden="true" class="fas fa-sync"/>
                </b-button>
            </template>

            <div v-if="dealerContracts <= 0">
                <div class="bg-muted p-2" style="border-radius: 3px;">
                    <i aria-hidden="true" class="fa fa-exclamation-triangle mr-1"/>
                    No <strong>Dealer Contracts</strong> have been set up yet.
                </div>
            </div>

            <div v-else class="table-responsive">
                <b-table stacked="md" striped hover
                         :fields="fields" :items="dealerContracts" :no-local-sorting="true"
                >
                    <template v-slot:cell(intacctInvoiceId)="row">
                        <span v-if="row.item.intacctInvoiceId">
                            {{ row.item.intacctInvoiceId }}
                        </span>
                        <span v-else-if="row.item.intacctSyncEnabled">
                            <push-to-intacct-button :dealer-id="dealerId" :contract-id="row.item.id"
                                                    @done="fetchContracts"/>
                        </span>
                    </template>
                    <template v-slot:cell(amount)="row">
                        <span v-if="row.item.subscription">
                            {{
                                row.item.subscription.plan.amount / 100 | currency
                            }} / {{ row.item.subscription.plan.interval }}
                        </span>
                        <span v-else-if="row.item.planInterval && row.item.planAmount">
                            {{ row.item.planAmount | currency }} /

                            <span v-if="row.item.planIntervalCount > 1">
                                every {{ row.item.planIntervalCount }} {{ row.item.planInterval + 's' }}
                            </span>
                            <span v-else>{{ row.item.planInterval + 'ly' }}</span>
                        </span>
                    </template>
                    <template v-slot:cell(actions)="row">
                        <modify-dealer-contract-button
                            :dealer-id="dealerId"
                            :contract-id="row.item.id"
                            :program-id="row.item.programId"
                            @done="fetchContracts"/>
                    </template>
                </b-table>
            </div>
        </i-box>
    </div>
</template>

<script>
import IBox from 'Components/IBox';
import {dispatch, get} from 'vuex-pathify';
import DealerAddContract from 'Modules/dealer/components/DealerContracts/DealerAddContract';
import PushToIntacctButton from 'Modules/dealer/components/DealerContracts/PushToIntacctButton';
import ModifyDealerContractButton from 'Modules/dealer/components/DealerContracts/ModifyDealerContractButton';

export default {
    name: 'DealerContracts',
    components: {ModifyDealerContractButton, PushToIntacctButton, DealerAddContract, IBox},
    data() {
        return {
            fields: [
                'id',
                {
                    key: 'intacctInvoiceId',
                    label: 'Intacct Invoice'
                },
                {
                    key: 'productName',
                    label: 'Product'
                },
                {
                    key: 'programName',
                    label: 'Program'
                },
                'amount',
                {
                    key: 'certificationAmount',
                    label: 'Certification'
                },
                {
                    key: 'payPerSaleAmount',
                    label: 'Pay Per Sale'
                },
                {
                    key: 'lifetimeWarrantyAmount',
                    label: 'Lifetime Warranty'
                },
                {
                    key: 'expressWarrantyAmount',
                    label: 'Express Warranty'
                },
                {
                    key: 'vscOptOutAmount',
                    label: 'VSC Opt Out'
                },
                '',
                'startDate',
                'endDate',
                'actions'
            ],
            selectedContractId: null,
            upcoming: {}
        };
    },
    computed: {
        dealerId: get('dealer/selectedDealer@id'),
        stripeCustomerId: get('dealer/selectedDealer@stripeCustomerId'),
        dealerContracts: get('dealer/dealerContracts')
    },
    mounted() {
        this.fetchContracts();
    },
    methods: {
        fetchContracts() {
            dispatch('dealer/fetchDealerContracts');
        }
    }
};
</script>
