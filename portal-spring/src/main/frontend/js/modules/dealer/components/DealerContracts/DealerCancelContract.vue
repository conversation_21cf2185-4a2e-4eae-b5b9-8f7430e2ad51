<template>
    <span>
        <b-button
            v-if="$acl.hasAuthority('edit:contracts') && contractId"
            v-b-modal.cancel-contract-modal
            size="xs"
            variant="light"
        >
            <i aria-hidden="true" class="fas fa-times"/>
        </b-button>

        <b-modal
            v-if="contractId"
            id="cancel-contract-modal"
            ref="cancel-contract-modal"
            size="xs"
            hide-header
            hide-footer
        >
            <dealer-contract-cancel-form :contract-id="contractId"
                                         :dealer-id="dealerId"
                                         @done="hideModal"
                                         @cancel="hideModal"
            />

        </b-modal>

    </span>
</template>

<script>
import DealerContractCancelForm from 'Modules/dealer/components/DealerContracts/DealerContractCancelForm';

export default {
    name: 'DealerCancelContract',
    components: {DealerContractCancelForm},
    props: {
        dealerId: {
            type: String,
            required: true
        },
        contractId: {
            type: Number,
            required: true
        }
    },
    data() {
        return {
            title: 'Cancel Contract'
        };
    },
    methods: {
        hideModal() {
            this.$refs['cancel-contract-modal'].hide();
            this.$emit('done');
        }
    }
};
</script>
