<template>
    <div class="row pt-3">
        <div class="col-lg-12">
            <i-box title="Recent Vehicle Sales">
                <template slot="header-actions">
                    <b-button
                        size="xs"
                        variant="light"
                        :href="'/sale/list?reset=true&dealerIds=' + dealerId"
                    >
                        View All
                    </b-button>
                </template>

                <div v-if="isLoading">
                    <table-content-loader/>
                </div>

                <div v-else>
                    <div v-if="noVehicleSales">
                        <div class="bg-muted p-2" style="border-radius: 3px;">
                            <i aria-hidden="true" class="fa fa-exclamation-triangle mr-1"/>
                            Currently no <strong>Vehicle Sales</strong> have
                            been established yet.
                        </div>
                    </div>

                    <div v-else class="table-responsive">
                        <b-table
                            striped
                            hover
                            bordered
                            :fields="fields"
                            :items="vehicleSales"
                        >
                            <template v-slot:cell(id)="row">
                                <a
                                    :href="`/sale/${row.item.id}`"
                                    class="btn btn-white btn-xs"
                                >
                                    View
                                </a>
                            </template>
                            <template v-slot:cell(status)="row">
                                <span :class="statusColor(row.item.status)">{{
                                        row.item.status
                                    }}</span>
                            </template>
                            <template v-slot:cell(vehicle)="row">
                                {{ row.item.vehicle | rejectNull }}
                            </template>
                            <template v-slot:cell(vin)="row">
                                {{ row.item.vin }}
                            </template>
                            <template v-slot:cell(reportedDate)="row">
                                {{ row.item.reportedDate | formatEpochDate }}
                            </template>
                            <template v-slot:cell(deliveryDate)="row">
                                {{ row.item.deliveryDate | formatEpochDate }}
                            </template>
                            <template v-slot:cell(invoice)="row">
                                <span class="label">{{ row.item.invoice.status }}
                                    {{
                                        row.item.invoice.amount
                                            | currency("$", 0)
                                    }}</span>
                            </template>
                            <template v-slot:cell(createdDate)="row">
                                {{ row.item.createdDate | formatEpochDate }}
                            </template>
                            <template v-slot:cell(lifetimeWarrantyStatus)="row">
                                <span
                                    v-b-tooltip.hover
                                    :class="
                                        warrantyColor(
                                            row.item.lifetimeWarrantyStatus
                                        )
                                    "
                                    :title="
                                        row.item.lifetimeWarrantyStatusReason
                                    "
                                >{{ row.item.lifetimeWarrantyStatus }}</span>
                            </template>
                        </b-table>
                    </div>
                </div>
            </i-box>
        </div>
    </div>
</template>

<script>
import {get} from 'vuex-pathify';
import _ from 'lodash';
import api from '@/api';
import TableContentLoader from 'Components/TableContentLoader';
import IBox from 'Components/IBox';

export default {
    name: 'VehicleSale',
    components: {IBox, TableContentLoader},
    filters: {
        rejectNull(value) {
            if (_.isNil(value) || value.trim().toLowerCase() === 'null') {
                return '';
            }

            return value;
        }
    },
    data() {
        return {
            vehicleSales: [],
            isLoading: true,
            fields: [
                {
                    key: 'id',
                    label: 'Details'
                },
                {
                    key: 'status'
                },
                {
                    key: 'walmartStoreId',
                    label: 'Store'
                },
                {
                    key: 'vehicle'
                },
                {
                    key: 'vin'
                },
                {
                    key: 'reportedDate',
                    label: 'Reported'
                },
                {
                    key: 'deliveryDate',
                    label: 'Delivered'
                },
                {
                    key: 'invoice'
                },
                {
                    key: 'lifetimeWarrantyStatus',
                    label: 'Warranty'
                },
                {
                    key: 'createdDate',
                    label: 'Created'
                }
            ]
        };
    },
    computed: {
        dealerId: get('dealer/selectedDealer@id'),
        noVehicleSales() {
            return _.isEmpty(this.vehicleSales);
        }
    },
    mounted() {
        api.get(`/sale/dealers/${this.dealerId}`).then(response => {
            this.vehicleSales = _.get(response.data, 'vehicleSales');
            this.isLoading = false;
        });
    },
    methods: {
        statusColor(status) {
            switch (status) {
                case 'CREDITED':
                    return 'label label-info';
                case 'DUPLICATE':
                    return 'label';
                case 'INCOMPLETE':
                    return 'label label-warning';
                case 'INVALID':
                    return 'label label-danger';
                case 'UNVERIFIED':
                    return 'label label-warning';
                case 'VERIFIED':
                    return 'label label-success';
                default:
                    return 'label';
            }
        },
        warrantyColor(warranty) {
            switch (warranty) {
                case 'Enrolled':
                    return 'label label-success';
                case 'Not Enrolled':
                    return 'label label-warning';
                case 'Customer Not Eligible':
                    return 'label label-danger';
                case 'Vehicle Not Eligible':
                    return 'label label-danger';
                case 'Unknown':
                    return 'label';
                default:
                    return 'label';
            }
        }
    }
};
</script>
