<template>
    <div>
        <div v-if="!isEditing">
            <div v-if="placeDetailsLoader.isComplete">
                <span><strong>Google Place</strong></span>
                <div v-if="placeId">
                    <span v-if="$acl.hasAuthority('edit:dealer')">
                        <strong>Id:</strong> <span>{{ placeId }}</span> <br>
                    </span>
                    <strong>Name:</strong> <span>{{ placeName }}</span> <br>
                    <star-rating v-model="rating" :increment="0.1" :read-only="true"
                                 :star-size="35"
                    />
                </div>
                <div v-if="$acl.hasAuthority('edit:dealer')">
                    <b-link @click="changePlace">
                        Change
                    </b-link>
                </div>
            </div>
        </div>
        <div v-else>
            <gmap-autocomplete placeholder="Dealer Name" style="width: 100%;" :options="autocompleteOptions"
                               @place_changed="getAddressData"
            />
            <br>
            <b-link @click="cancelEdit">
                Cancel
            </b-link>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import {dispatch, get, sync} from 'vuex-pathify';
import StarRating from 'vue-star-rating';

export default {
    name: 'GooglePlaces',
    components: {
        StarRating
    },
    data() {
        return {
            isEditing: false,
        };
    },
    computed: {
        placeName: get('dealer/<EMAIL>'),
        placeId: sync('dealer/<EMAIL>'),
        rating: sync('dealer/<EMAIL>'),
        placeDetailsLoader: get('dealer/placeDetails@loader'),
        selectedDealer: get('dealer/selectedDealer'),

        autocompleteOptions() {
            if (_.isNil(_.get(this.selectedDealer, 'lat')) || _.isNil(_.get(this.selectedDealer, 'lng'))) {
                return null;
            }

            const geolocation = {
                lat: _.get(this.selectedDealer, 'lat'),
                lng: _.get(this.selectedDealer, 'lng')
            };

            // 50 miles in meters
            const MILES_IN_METERS = 80467.2;

            const circle = new google.maps.Circle({center: geolocation, radius: MILES_IN_METERS});

            return {
                bounds: circle.getBounds()
            };
        }
    },
    mounted() {
        dispatch('dealer/fetchPlaceDetails');
    },
    methods: {
        getAddressData: function (placeResultData) {
            dispatch('dealer/updatePlaceId', placeResultData.place_id);
            this.isEditing = false;
        },
        changePlace: function () {
            this.isEditing = true;
        },
        cancelEdit: function () {
            this.isEditing = false;
        }
    }
};
</script>
