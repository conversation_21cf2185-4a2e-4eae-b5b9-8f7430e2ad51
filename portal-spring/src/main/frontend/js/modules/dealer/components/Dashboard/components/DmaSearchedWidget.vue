<template>
    <i-box class="stock-type-count" :title="title" :description="description"
           :alert-message="alertMessage" :collapsible="false" :loading="isLoading"
    >
        <div v-if="data.totalCount">
            <div ref="stockTypeChart"/>

            <div class="main-number text-center">
                {{ totalUnique | numeral('0,0') }} <span v-if="hasTotalUnique">Unique</span><span v-else>Total</span>
                <div v-if="hasTotalUnique" class="small">
                    {{ data.totalCount }} Total
                </div>
            </div>
        </div>
        <div v-else class="d-flex justify-content-center">
            <no-data :title="title"/>
        </div>
    </i-box>
</template>

<style lang="scss">
.stock-type-count {
    .c3-text {
        font-size: 1rem;
    }

    .main-number {
        font-size: 18px;
    }

    .ibox-title {
        padding-right: 15px !important;
    }

    .ibox-content {
        min-height: 234px;
    }
}
</style>

<script>
import _ from 'lodash';
import IBox from 'Components/IBox';
import {call, get} from 'vuex-pathify';
import api from '@/api';
import NoData from 'Modules/dealer/components/Dashboard/components/NoData';

export default {
    components: {NoData, IBox},

    props: {
        name: {
            type: String,
            required: true
        },
        title: {
            type: String,
            required: true
        },
        description: {
            type: String,
            required: false,
            default: null
        },
        alertMessage: {
            type: String,
            required: false,
            default: null
        }
    },

    data() {
        return {
            isLoading: true,
            data: {
                newCount: 0,
                usedCount: 0,
                totalCount: 0,
                unknownCount: 0
            }
        };
    },

    computed: {
        startDate: get('dashboard/startDate'),
        endDate: get('dashboard/endDate'),
        dateRangeStr: get('dashboard/dateRangeStr'),
        shortDealerId: get('dashboard/selectedDealer@dealerId'),

        hasTotalUnique() {
            return !_.isNil(this.data.totalUnique);
        },

        totalUnique() {
            if (this.hasTotalUnique) {
                return this.data.totalUnique;
            }

            if (!_.isNil(this.data.totalCount)) {
                return this.data.totalCount;
            }

            return this.total;
        },

        columns() {
            const header = ['x', 'New', 'Used'];
            const values = ['value', this.data.newCount, this.data.usedCount];

            if (_.get(this.data, 'unknownCount', 0) > 0) {
                header.push('Unknown');
                values.push(this.data.unknownCount);
            }

            return [
                header,
                values
            ];
        }
    },

    watch: {
        dateRangeStr() {
            this.loadData();
        },
        dealerIds() {
            this.loadData();
        }
    },

    created() {
        this.loadData();
    },

    methods: {
        incrementLoaderCount: call('dashboard/incrementLoaderCount'),
        decrementLoaderCount: call('dashboard/decrementLoaderCount'),

        updateChart() {
            c3.generate({
                bindto: this.$refs.stockTypeChart,
                size: {
                    height: 150
                },
                data: {
                    x: 'x',
                    columns: this.columns,
                    type: 'bar',
                    labels: true,
                    color: function (inColor, data) {
                        var colors = ['#1ab394', '#23c6c8', '#BABABA'];
                        if (data.index !== undefined) {
                            return colors[data.index];
                        }

                        return inColor;
                    }
                },
                axis: {
                    rotated: true,
                    x: {
                        type: 'category'
                    },
                    y: {
                        show: false
                    }
                },
                tooltip: {
                    grouped: true
                },
                legend: {
                    show: false
                }
            });
        },

        loadData() {
            if (_.isNil(this.startDate) || _.isNil(this.endDate)) {
                return;
            }

            this.incrementLoaderCount();
            this.isLoading = true;

            api.get(`/dealers/dashboard/${this.name}`,
                {
                    dealerIds: this.shortDealerId,
                    startDate: this.startDate,
                    endDate: this.endDate
                })
                .then(response => {
                    this.data = response.data;

                    this.isLoading = false;
                    this.decrementLoaderCount();
                })
                .then(() => {
                    this.updateChart();
                })
                .catch(error => {
                    console.log(error);

                    this.isLoading = false;
                    this.decrementLoaderCount();
                });
        }
    }
};
</script>
