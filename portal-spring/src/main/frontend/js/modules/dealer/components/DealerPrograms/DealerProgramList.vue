<template>
    <i-box title="Dealer Program Subscriptions">
        <template slot="header-actions">
            <dealer-program-enroll-button
                :dealer-id="dealerId"
                @done="fetchPrograms"
            />
            <b-button size="xs" variant="light" @click="fetchPrograms">
                <i aria-hidden="true" class="fas fa-sync"/>
            </b-button>
        </template>

        <div v-if="loader.isLoading">
            <table-content-loader/>
        </div>

        <div v-else>
            <b-row>
                <b-col>
                    <b-table
                        stacked="md"
                        striped
                        hover
                        :fields="fields"
                        :items="programs"
                        :no-local-sorting="true"
                    >
                        <template v-slot:cell(details)="data">
                            <dealer-program-subscription-details
                                :fields="data.item"
                            />
                        </template>
                        <template v-slot:cell(status)="data">
                            <dealer-status-chip :status="data.value"/>
                        </template>
                        <template #cell(successManager)="data">
                            <success-manager
                                :id="data.item.successManagerId"
                                :subscription-id="data.item.id"
                            />
                        </template>
                        <template #cell(accountManager)="data">
                            <account-manager
                                :id="data.item.accountManagerId"
                                :subscription-id="data.item.id"
                            />
                        </template>
                        <template v-slot:cell(config)="row">
                            <div v-if="row.item.config && row.item.config.gaTrackingId">
                                <b-button
                                    :id="`link-button-${row.item.id}`"
                                    size="xs"
                                    variant="light"
                                >
                                    <i
                                        class="fa fa-info-circle"
                                        aria-hidden="true"
                                    />
                                </b-button>
                                <b-popover
                                    :target="`link-button-${row.item.id}`"
                                    title="Program Config"
                                    placement="top"
                                    triggers="hover focus"
                                >
                                    <dl>
                                        <dt>GA Universal Id</dt>
                                        <dd>
                                            {{ row.item.config.gaTrackingId }}
                                        </dd>
                                    </dl>
                                </b-popover>
                            </div>
                        </template>
                        <template v-slot:cell(actions)="row">
                            <dealer-program-edit-button
                                :dealer-id="row.item.dealerId"
                                :subscription-id="row.item.id"
                                :product-id="row.item.program.product.id"
                                @done="fetchPrograms"
                            />
                        </template>
                    </b-table>
                </b-col>
            </b-row>
        </div>
    </i-box>
</template>

<script>
import IBox from "Components/IBox";
import TableContentLoader from "Components/TableContentLoader";
import loader from "@/api/loader";
import api from "@/api";
import DealerStatusChip from "Modules/dealer/components/DealerStatusChip";
import DealerProgramEnrollButton from "./DealerProgramEnrollButton";
import DealerProgramEditButton from "./DealerProgramEditButton";
import SuccessManager from "Modules/dealer/components/SuccessManager";
import AccountManager from "Modules/dealer/components/AccountManager";
import {call} from "vuex-pathify";
import DealerProgramSubscriptionDetails from "./DealerProgramSubscriptionDetails";

export default {
    components: {
        DealerProgramSubscriptionDetails,
        AccountManager,
        SuccessManager,
        DealerProgramEditButton,
        DealerProgramEnrollButton,
        DealerStatusChip,
        TableContentLoader,
        IBox
    },
    props: {
        dealerId: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            fields: [
                "details",
                "contractId",
                {
                    key: "program.product.name",
                    label: "Product"
                },
                {
                    key: "program.name",
                    label: "Program"
                },
                "status",
                "successManager",
                "accountManager",
                "enrollmentDate",
                "activationDate",
                "cancellationDate",
                "config",
                "actions"
            ],
            loader: loader.defaultState(),
            programs: []
        };
    },
    computed: {},
    mounted() {
        this.fetchPrograms();
        this.fetchSuccessManagers();
        this.fetchAccountManagers();
    },
    methods: {
        fetchSuccessManagers: call("dealer/fetchSuccessManagers"),
        fetchAccountManagers: call("dealer/fetchAccountManagers"),
        fetchPrograms() {
            this.loader = loader.started();
            api.get(`/dealer/${this.dealerId}/programs`)
                .then(response => {
                    this.loader = loader.successful();
                    this.programs = response.data;
                })
                .catch(error => {
                    console.error(error);
                    this.loader = loader.error(error);
                });
        }
    }
};
</script>
