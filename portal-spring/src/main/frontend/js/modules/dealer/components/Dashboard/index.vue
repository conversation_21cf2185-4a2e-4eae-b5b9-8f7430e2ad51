<template>
    <b-container fluid>
        <slot name="header"/>
        <b-card no-body>
            <b-tabs card v-model="selectedTabIndex">
                <b-tab title="CarSaver.com" lazy active>
                    <b-row>
                        <b-col>
                            <h4>Rolling 90 Days</h4>
                        </b-col>
                    </b-row>
                    <b-row>
                        <b-col>
                            Close Rate:
                            <percent-value
                                name="close-rate"
                                :start-date="rolling90Days.start"
                                :end-date="rolling90Days.end"
                                description="Percent of unique leads that resulted in a sale, from 120 days ago to 30 days ago. (rolling 90 days)"
                            />
                        </b-col>
                        <b-col>
                            Appointments:
                            <percent-value
                                name="appointment-close-rate"
                                :start-date="rolling90Days.start"
                                :end-date="rolling90Days.end"
                                description="Percent of sales that had an appointment, from 120 days ago to 30 days ago. (rolling 90 days)"
                            />
                        </b-col>
                        <b-col>
                            Connections:
                            <percent-value
                                name="connection-close-rate"
                                :start-date="rolling90Days.start"
                                :end-date="rolling90Days.end"
                                description="Percent of sales that had a connection, from 120 days ago to 30 days ago. (rolling 90 days)"
                            />
                        </b-col>
                        <b-col>
                            Price Requests:
                            <percent-value
                                name="price-request-close-rate"
                                :start-date="rolling90Days.start"
                                :end-date="rolling90Days.end"
                                description="Percent of sales that had a pricing request, from 120 days ago to 30 days ago. (rolling 90 days)"
                            />
                        </b-col>
                    </b-row>
                    <b-row>
                        <b-col>
                            <div class="search-page-row p-2 mb-3">
                                <b-form inline>
                                    <b-form-group>
                                        <date-range-picker2
                                            v-model="dateRange"
                                            :masks="{ input: 'MM/DD/YYYY' }"
                                            :max-date="dateToday"
                                            :input-read-only="
                                    datePickerProps.input.readonly
                                "
                                            :disabled="isLoading"
                                            :columns="$screens({ default: 1, lg: 2 })"
                                            placeholder="Date Range"
                                        />
                                    </b-form-group>
                                    <b-button-group class="mx-1">
                                        <b-button
                                            :pressed="isYearToDate"
                                            :disabled="isLoading"
                                            @click="enableYearToDate"
                                        >
                                            Year To Date
                                        </b-button>
                                        <b-button
                                            :pressed="isCurrentMonth"
                                            :disabled="isLoading"
                                            @click="enableCurrentMonth"
                                        >
                                            Current Month
                                        </b-button>
                                        <b-button
                                            :pressed="isPreviousMonth"
                                            :disabled="isLoading"
                                            @click="enablePreviousMonth"
                                        >
                                            Previous Month
                                        </b-button>
                                    </b-button-group>
                                </b-form>
                            </div>
                        </b-col>
                    </b-row>
                    <b-row>
                        <b-col v-if="searchWidgetEnabled" xl="3" lg="4" md="6">
                            <!--                <stock-type-count-widget name="searches" title="Searches" description="Number of searches in your Designated Market Area (DMA)" />-->
                            <dma-searched-widget
                                name="searches"
                                title="Searches"
                                description="Number of searches in your Designated Market Area (DMA)"
                            />
                        </b-col>

                        <b-col xl="3" lg="4" md="6">
                            <stock-type-count-widget
                                name="vehicle-views"
                                :alert-message="alertMessage"
                                title="Vehicle Views"
                                description="Number of your vehicles viewed by all users"
                            />
                        </b-col>

                        <b-col xl="3" lg="4" md="6">
                            <stock-type-count-widget
                                name="in-garages"
                                title="In Garages"
                                description="Number of your vehicles viewed by logged in users"
                            />
                        </b-col>

                        <b-col xl="3" lg="4" md="6">
                            <stock-type-count-widget
                                name="leads"
                                title="Leads"
                                description="Number of leads sent to your dealership"
                            />
                        </b-col>
                        <b-col lg="4" md="6">
                            <priced-widget
                                name="new-priced"
                                title="% New Priced"
                                description="Percent of new vehicles with valid pricing"
                            />
                        </b-col>
                        <b-col lg="4" md="6">
                            <priced-widget
                                name="used-priced"
                                title="% Used Priced"
                                description="Percent of used vehicles with valid pricing"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <stock-type-percent-widget
                                name="claimed"
                                title="Claimed"
                                description="Number of unique leads claimed"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <stock-type-percent-widget
                                :drill-down-enabled="drillDownEnabled"
                                name="contacted"
                                title="Contacted"
                                description="Number of unique leads contacted"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <stock-type-count-widget
                                :drill-down-enabled="drillDownEnabled"
                                name="sales"
                                title="Sales"
                                description="Number of verified sales"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <warranty-count-widget
                                name="warranty"
                                title="Warranty"
                                description="Number of verified and unverified warranty registrations."
                            />
                        </b-col>
                        <b-col v-if="smsEnabled" xl="3" lg="4" md="6">
                            <time-widget
                                name="time-to-claimed"
                                title="Time to Claimed"
                                description="Average time from when an SMS was sent to a program manager to when it was claimed. Calculated based off of leads that were actually claimed."
                            />
                        </b-col>
                        <b-col v-if="smsEnabled" xl="3" lg="4" md="6">
                            <time-widget
                                name="time-to-called"
                                title="Time to Called"
                                description="Average time from when the lead was sent to the dealer to when the customer was called. Calculated based off of leads that were actually contacted."
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <percent-widget
                                name="close-rate"
                                title="Close Rate"
                                description="Percent of unique leads that resulted in a sale"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <distance-pie-widget
                                name="distance"
                                title="Lead Distance"
                                description="Distances between dealer and user on unique leads."
                            />
                        </b-col>
                    </b-row>
                </b-tab>
                <b-tab title="Nissan" lazy>
                    <b-row>
                        <b-col>
                            <h4>Rolling 90 Days</h4>
                        </b-col>
                    </b-row>
                    <b-row>
                        <b-col>
                            Close Rate:
                            <percent-value
                                name="nissan-close-rate"
                                :start-date="rolling90Days.start"
                                :end-date="rolling90Days.end"
                                description="Percent of unique leads that resulted in a sale, from 120 days ago to 30 days ago. (rolling 90 days)"
                            />
                        </b-col>
                        <b-col>
                            Appointments:
                            <percent-value
                                name="nissan-appointment-close-rate"
                                :start-date="rolling90Days.start"
                                :end-date="rolling90Days.end"
                                description="Percent of sales that had an appointment, from 120 days ago to 30 days ago. (rolling 90 days)"
                            />
                        </b-col>
                        <b-col>
                            Connections:
                            <percent-value
                                name="nissan-connection-close-rate"
                                :start-date="rolling90Days.start"
                                :end-date="rolling90Days.end"
                                description="Percent of sales that had a connection, from 120 days ago to 30 days ago. (rolling 90 days)"
                            />
                        </b-col>
                        <b-col>
                            Price Requests:
                            <percent-value
                                name="nissan-price-request-close-rate"
                                :start-date="rolling90Days.start"
                                :end-date="rolling90Days.end"
                                description="Percent of sales that had a pricing request, from 120 days ago to 30 days ago. (rolling 90 days)"
                            />
                        </b-col>
                    </b-row>
                    <b-row>
                        <b-col>
                            <div class="search-page-row p-2 mb-3">
                                <b-form inline>
                                    <b-form-group>
                                        <date-range-picker2
                                            v-model="dateRange"
                                            :masks="{ input: 'MM/DD/YYYY' }"
                                            :max-date="dateToday"
                                            :input-read-only="datePickerProps.input.readonly"
                                            :disabled="isLoading"
                                            :columns="$screens({ default: 1, lg: 2 })"
                                            placeholder="Date Range"
                                        />
                                    </b-form-group>
                                    <b-button-group class="mx-1">
                                        <b-button
                                            :pressed="isYearToDate"
                                            :disabled="isLoading"
                                            @click="enableYearToDate"
                                        >
                                            Year To Date
                                        </b-button>
                                        <b-button
                                            :pressed="isCurrentMonth"
                                            :disabled="isLoading"
                                            @click="enableCurrentMonth"
                                        >
                                            Current Month
                                        </b-button>
                                        <b-button
                                            :pressed="isPreviousMonth"
                                            :disabled="isLoading"
                                            @click="enablePreviousMonth"
                                        >
                                            Previous Month
                                        </b-button>
                                    </b-button-group>
                                </b-form>
                            </div>
                        </b-col>
                    </b-row>
                    <b-row>
                        <b-col v-if="searchWidgetEnabled" xl="3" lg="4" md="6">
                            <dma-searched-widget
                                name="nissan-searches"
                                title="Searches"
                                description="Number of searches in your Designated Market Area (DMA)"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <stock-type-count-widget
                                name="nissan-vehicle-views"
                                :alert-message="alertMessage"
                                title="Vehicle Views"
                                description="Number of your vehicles viewed by all users"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <stock-type-count-widget
                                name="nissan-in-garages"
                                title="In Garages"
                                description="Number of your vehicles viewed by logged in users"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <stock-type-count-widget
                                name="leads"
                                title="Leads"
                                description="Number of leads sent to your dealership"
                            />
                        </b-col>
                        <b-col lg="4" md="6">
                            <priced-widget
                                name="nissan-new-priced"
                                title="% New Priced"
                                description="Percent of new vehicles with valid pricing"
                            />
                        </b-col>
                        <b-col lg="4" md="6">
                            <priced-widget
                                name="nissan-used-priced"
                                title="% Used Priced"
                                description="Percent of used vehicles with valid pricing"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <stock-type-percent-widget
                                name="nissan-claimed"
                                title="Claimed"
                                description="Number of unique leads claimed"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <stock-type-percent-widget
                                :drill-down-enabled="drillDownEnabled"
                                name="nissan-contacted"
                                title="Contacted"
                                description="Number of unique leads contacted"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <stock-type-count-widget
                                :drill-down-enabled="drillDownEnabled"
                                name="nissan-sales"
                                title="Sales"
                                description="Number of verified sales"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <warranty-count-widget
                                name="nissan-warranty"
                                title="Warranty"
                                description="Number of verified and unverified warranty registrations."
                            />
                        </b-col>
                        <b-col v-if="smsEnabled" xl="3" lg="4" md="6">
                            <time-widget
                                name="nissan-time-to-claimed"
                                title="Time to Claimed"
                                description="Average time from when an SMS was sent to a program manager to when it was claimed. Calculated based off of leads that were actually claimed."
                            />
                        </b-col>
                        <b-col v-if="smsEnabled" xl="3" lg="4" md="6">
                            <time-widget
                                name="nissan-time-to-called"
                                title="Time to Called"
                                description="Average time from when the lead was sent to the dealer to when the customer was called. Calculated based off of leads that were actually contacted."
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <percent-widget
                                name="nissan-close-rate"
                                title="Close Rate"
                                description="Percent of unique leads that resulted in a sale"
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <distance-pie-widget
                                name="nissan-distance"
                                title="Lead Distance"
                                description="Distances between dealer and user on unique leads."
                            />
                        </b-col>
                    </b-row>
                </b-tab>
                <b-tab title="Express" lazy>
                    <b-row>
                        <b-col>
                            <div class="search-page-row p-2 mb-3">
                                <b-form inline>
                                    <b-form-group>
                                        <date-range-picker2
                                            v-model="dateRange"
                                            :masks="{ input: 'MM/DD/YYYY' }"
                                            :max-date="dateToday"
                                            :input-read-only="datePickerProps.input.readonly"
                                            :disabled="isLoading"
                                            :columns="$screens({ default: 1, lg: 2 })"
                                            placeholder="Date Range"
                                        />
                                    </b-form-group>
                                    <b-button-group class="mx-1">
                                        <b-button
                                            :pressed="isYearToDate"
                                            :disabled="isLoading"
                                            @click="enableYearToDate"
                                        >
                                            Year To Date
                                        </b-button>
                                        <b-button
                                            :pressed="isCurrentMonth"
                                            :disabled="isLoading"
                                            @click="enableCurrentMonth"
                                        >
                                            Current Month
                                        </b-button>
                                        <b-button
                                            :pressed="isPreviousMonth"
                                            :disabled="isLoading"
                                            @click="enablePreviousMonth"
                                        >
                                            Previous Month
                                        </b-button>
                                    </b-button-group>
                                </b-form>
                            </div>
                        </b-col>
                    </b-row>
                    <b-row>
                        <b-col xl="3" lg="4" md="6">
                            <distance-pie-widget
                                name="express-distance"
                                title="Lead Distance"
                                description="Distances between dealer and user on Express leads."
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <warranty-count-widget
                                name="express-warranty"
                                title="Warranty"
                                description="Number of verified and unverified warranty registrations."
                            />
                        </b-col>
                        <b-col xl="3" lg="4" md="6">
                            <stock-type-count-widget
                                name="express-sales"
                                title="Sales"
                                description="Number of verified only Express Sales"
                            />
                        </b-col>
                    </b-row>
                </b-tab>
            </b-tabs>
        </b-card>
    </b-container>
</template>

<script>
import _ from "lodash";
import {dispatch, get, sync} from "vuex-pathify";
import StockTypeCountWidget from "./components/StockTypeCountWidget";
import moment from "moment";
import StockTypePercentWidget from "Modules/dealer/components/Dashboard/components/StockTypePercentWidget";
import WarrantyCountWidget from "Modules/dealer/components/Dashboard/components/WarrantyCountWidget";
import PricedWidget from "Modules/dealer/components/Dashboard/components/PricedWidget";
import MarketPricedWidget from "Modules/dealer/components/Dashboard/components/MarketPricedWidget";
import TimeWidget from "Modules/dealer/components/Dashboard/components/TimeWidget";
import PercentWidget from "Modules/dealer/components/Dashboard/components/PercentWidget";
import PercentValue from "Modules/dealer/components/Dashboard/components/PercentValue";
import DistancePieWidget from "Modules/dealer/components/Dashboard/components/DistancePieWidget";
import DmaSearchedWidget from "Modules/dealer/components/Dashboard/components/DmaSearchedWidget";
import DateRangePicker2 from "Components/DateRangePicker2/index";

export default {
    components: {
        DmaSearchedWidget,
        DistancePieWidget,
        PercentValue,
        PercentWidget,
        TimeWidget,
        MarketPricedWidget,
        PricedWidget,
        WarrantyCountWidget,
        StockTypePercentWidget,
        StockTypeCountWidget,
        DateRangePicker2
    },

    props: {
        searchWidgetEnabled: {
            type: Boolean,
            required: false,
            default: true
        },
        drillDownEnabled: {
            type: Boolean,
            required: false,
            default: true
        }
    },

    data() {
        return {
            dateToday: new Date(),
            datePickerProps: {
                popover: {
                    visibility: "click"
                },
                input: {
                    readonly: true
                }
            },
            dateRange: {
                start: null,
                end: new Date()
            }
        };
    },

    computed: {
        isLoading: get("dashboard/isLoading"),
        dealerName: get("dashboard/selectedDealer@name"),
        dmaName: get("dashboard/selectedDealer@dmaName"),
        dealerPreferences: get("dashboard/selectedDealer@preferences"),
        dealerIds: sync("dashboard/dealerIds"),
        dealerPageMetadata: get("dealerSearch/pageMetadata"),
        dealerSearchResults: get("dealerSearch/searchLoader@data"),
        dealerFetchSourceIncludes: sync("dealerSearch/fetchSource@includes"),
        startDate: sync("dashboard/startDate"),
        endDate: sync("dashboard/endDate"),
        dateRangeStr: sync("dashboard/dateRangeStr"),
        selectedTabIndex: sync("dashboard/selectedTabIndex"),
        currentDateStrings() {
            const today = this.dateToday;

            return {
                year: today.getFullYear().toString(),
                month: (today.getMonth() + 1).toString(),
                day: today.getDate().toString()
            };
        },
        yearToDate() {
            return {
                start: new Date("1/1/" + this.currentDateStrings.year),
                end: new Date()
            };
        },
        currentMonth() {
            const startDate = new Date();

            startDate.setDate(1);

            return {
                start: startDate,
                end: this.dateToday
            };
        },
        previousMonth() {
            const startDate = new Date();
            const endDate = new Date();

            startDate.setDate(0);
            startDate.setDate(1);

            endDate.setDate(0);

            return {
                start: startDate,
                end: endDate
            };
        },
        rolling90Days() {
            return {
                start: moment()
                    .subtract(120, "days")
                    .format("YYYY-MM-DD"),
                end: moment()
                    .subtract(30, "days")
                    .format("YYYY-MM-DD")
            };
        },

        smsEnabled() {
            if (
                _.get(this.dealerPreferences, "sendSmsForAppointments") ===
                false &&
                _.get(this.dealerPreferences, "sendSmsForConnections") === false
            ) {
                return false;
            }

            return true;
        },

        alertMessage() {
            if (moment(this.dateRange[0]).isBefore(moment("2019-06-06"))) {
                return "No data before 06/06/2019";
            }

            return null;
        },

        isYearToDate() {
            return _.isEqual(this.dateRange, this.yearToDate);
        },
        isCurrentMonth() {
            return _.isEqual(this.dateRange, this.currentMonth);
        },
        isPreviousMonth() {
            return _.isEqual(this.dateRange, this.previousMonth);
        }
    },

    watch: {
        dateRange(newDateRange) {
            const newStartDate = this.dateToString(
                _.get(newDateRange, "start", null)
            );
            const newEndDate = this.dateToString(
                _.get(newDateRange, "end", null)
            );

            if (
                newStartDate !== this.startDate ||
                newEndDate !== this.endDate
            ) {
                this.startDate = newStartDate;
                this.endDate = newEndDate;
                this.dateRangeStr = this.startDate + "-" + this.endDate;
            }
        },

        dealerSearchResults: function (newContent) {
            if (_.isArray(newContent)) {
                this.dealerIds = _.map(newContent, "id");
            } else {
                this.dealerIds = null;
            }
        }
    },

    created() {
        this.dateRange = this.yearToDate;
        dispatch("dealerSearch/queryModeScroll");
        dispatch("dealerSearch/pushHistoryEnabled", false);
        this.dealerFetchSourceIncludes = ["id"];
    },

    methods: {
        handleInput(val) {
            this.dateRange = val;
        },
        formattedSelectedDates(inputValue) {
            if (
                _.isNil(_.get(inputValue, "start")) ||
                _.isNil(_.get(inputValue, "end"))
            ) {
                return;
            }

            return (
                this.dateToString(inputValue.start) +
                " - " +
                this.dateToString(inputValue.end)
            );
        },
        dateToString(dateType) {
            if (_.isNil(dateType) || typeof dateType === "string") {
                return dateType;
            }

            return [
                dateType.getFullYear(),
                (dateType.getMonth() + 1).toString().padStart(2, "0"),
                dateType
                    .getDate()
                    .toString()
                    .padStart(2, "0")
            ].join("-");
        },
        enableYearToDate() {
            this.dateRange = this.yearToDate;
        },

        enableCurrentMonth() {
            this.dateRange = this.currentMonth;
        },

        enablePreviousMonth() {
            this.dateRange = this.previousMonth;
        }
    }
};
</script>

<style lang="scss">
.date-range-input {
    input {
        font-size: 13px;
        min-width: 175px;
    }
}
</style>
