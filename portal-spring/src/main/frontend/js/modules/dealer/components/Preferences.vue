<template>
    <i-box :title="title">
        <template slot="header-actions">
            <b-button
                v-if="$acl.hasAuthority('edit:dealer')"
                size="xs"
                variant="light"
                :href="editDealerUrl"
            >
                <i aria-hidden="true" class="fas fa-pencil-alt"/>
            </b-button>
        </template>
        <b-table id="dealer-preferences-table" stacked :fields="fields"
                 :items="items"
        />
    </i-box>
</template>

<script>
import IBox from 'Components/IBox';
import {get} from 'vuex-pathify';
import _ from 'lodash';

export default {
    name: 'Preferences',
    components: {IBox},
    data() {
        return {
            title: 'Preferences',
            fields: [
                'sendSmsForAppointments',
                'sendSmsForConnections',
                'sendSmsAlertsAfterHours',
                'offeringOptionalVsc',
                'leadTransport',
                'enableLeasePricing',
                'uniqueProxyNumbers',
                'enableOutOfMarketLeads',
                'enablePriceRequests',
                'deliveryAvailability',
                'pricingRules',
                'internetPriceEnabled',
                'taxesQuoteType'
            ]
        };
    },
    computed: {
        dealerId: get('dealer/selectedDealer@id'),
        effectivePreferences: get('dealer/selectedDealer@effectivePreferences'),
        editDealerUrl() {
            return `/on-boarding/${this.dealerId}/dealer-preferences`;
        },
        items() {
            return _.concat(this.effectivePreferences);
        }
    }
};
</script>
<style lang="scss">
#dealer-preferences-table {
    tr > :first-child {
        border-top-width: 1px !important;
    }

    td {
        overflow: auto;
    }
}
</style>
