<template>
    <i-box title="Contract Details">
        <template slot="header-actions">
            <dealer-cancel-contract v-if="contractId" :dealer-id="dealerId" :contract-id="contractId"
                                    @done="refreshDealer"
            />
            <dealer-add-contract v-if="!contractId" :dealer-id="dealerId" @done="refreshDealer"/>
        </template>

        <div v-if="!stripeCustomerId">
            <button v-if="$acl.hasAuthority('edit:contracts') && !stripeCustomerId" v-b-modal.add-stripe-customer-modal>
                Setup Billing Details
            </button>
            <b-modal
                id="add-stripe-customer-modal"
                ref="add-stripe-customer-modal"
                size="xs"
                hide-header
                hide-footer
            >
                <dealer-billing-customer-form :dealer="dealer" @done="handleOnStripeCustomerCreated"
                                              @cancel="$bvModal.hide('add-stripe-customer-modal')"
                />
            </b-modal>
        </div>

        <div v-if="activeContract">
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    Bill To Group
                </div>
                <div class="p-value">
                    {{ dealer.billToGroup }}
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    Contract Id
                </div>
                <div class="p-value">
                    {{ activeContract.id }}
                </div>
            </div>
            <div v-if="activeContract.subscription">
                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <div class="p-label font-weight-bold">
                        Subscription
                    </div>
                    <div class="p-value">
                        {{ activeContract.subscription.status }}
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <div class="p-label font-weight-bold">
                        Product
                    </div>
                    <div class="p-value">
                        {{ activeContract.subscription.plan.productObject.name }}
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <div class="p-label font-weight-bold">
                        Amount
                    </div>
                    <div class="p-value">
                        {{ activeContract.subscription.plan.amount / 100 | currency }} /
                        {{ activeContract.subscription.plan.interval }}
                    </div>
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    Pay Per Sale Amount
                </div>
                <div class="p-value">
                    {{ activeContract.payPerSaleAmount }}
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    Lifetime Warranty Amount
                </div>
                <div class="p-value">
                    {{ activeContract.lifetimeWarrantyAmount }}
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    Express Warranty Amount
                </div>
                <div class="p-value">
                    {{ activeContract.expressWarrantyAmount }}
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    VSC Opt Out Amount
                </div>
                <div class="p-value">
                    {{ activeContract.vscOptOutAmount }}
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    Start Date
                </div>
                <div class="p-value">
                    {{ activeContract.startDate }}
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    End Date
                </div>
                <div class="p-value">
                    {{ activeContract.endDate }}
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2">
                <div class="p-label font-weight-bold">
                    Walmart Store Id
                </div>
                <div class="p-value">
                    {{ dealer.walmartStoreId }}
                </div>
            </div>
        </div>

        <div v-else>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    Bill To Group
                </div>
                <div class="p-value">
                    {{ dealer.billToGroup }}
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    Structure
                </div>
                <div class="p-value">
                    {{ dealer.paymentStructureName }}
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    Subscription
                </div>
                <div class="p-value">
                    {{ dealer.subscriptionAmount | numeral('$0,0') }}
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    Pay Per Sale
                </div>
                <div class="p-value">
                    {{ dealer.payPerSaleAmount | numeral('$0,0') }}
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="p-label font-weight-bold">
                    Warranty
                </div>
                <div class="p-value">
                    {{ dealer.warrantyAmount | numeral('$0,0') }}
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center py-2">
                <div class="p-label font-weight-bold">
                    Optional VSC
                </div>
                <div class="p-value">
                    <span class="label">{{ dealer.effectivePreferences.offeringOptionalVsc }}</span>
                </div>
            </div>
        </div>
    </i-box>
</template>

<script>
import IBox from 'Components/IBox';
import {dispatch, get, sync} from 'vuex-pathify';
import DealerAddContract from 'Modules/dealer/components/DealerContracts/DealerAddContract';
import DealerCancelContract from 'Modules/dealer/components/DealerContracts/DealerCancelContract';
import DealerBillingCustomerForm from 'Modules/dealer/components/DealerContracts/DealerBillingCustomerForm';

export default {
    name: 'ContractDetails',
    components: {
        DealerBillingCustomerForm,
        DealerCancelContract,
        DealerAddContract,
        IBox
    },
    computed: {
        dealer: get('dealer/selectedDealer'),
        dealerId: get('dealer/selectedDealer@id'),
        stripeCustomerId: sync('dealer/selectedDealer@stripeCustomerId'),
        contractId: get('dealer/selectedDealer@contractId'),
        activeContract: get('dealer/activeContract'),
        dealerContracts: get('dealer/dealerContracts'),
        editDealerUrl() {
            return `/on-boarding/${this.dealer.id}/contract`;
        }
    },
    watch: {
        contractId() {
            dispatch('dealer/fetchActiveContract');
        }
    },
    mounted() {
        dispatch('dealer/fetchActiveContract');
    },
    methods: {
        handleOnStripeCustomerCreated(dealerObj) {
            if (!_.isNil(dealerObj)) {
                this.stripeCustomerId = dealerObj.stripeCustomerId;
            }

            this.$refs['add-stripe-customer-modal'].hide();
            dispatch('dealer/fetchActiveContract');
        },
        refreshDealer() {
            dispatch('dealer/fetchDealer');
        },
        refreshContract() {
            dispatch('dealer/fetchActiveContract');
        }
    }
};
</script>
