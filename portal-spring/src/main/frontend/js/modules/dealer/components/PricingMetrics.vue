<template>
    <div :class="{ 'pricing-metrics': warningPercentage }">
        <i-box :title="title" :collapsible="true">
            <div v-if="showInventoryPricing">
                <div v-if="warningPercentage">
                    <span>
                        Inventory should be priced at
                        <strong>{{ warningPercentageThreshold }}%</strong> or
                        better.
                    </span>
                </div>
                <div>
                    <span> Currently, inventory is priced at </span>
                    <strong> {{ totalPercentageNewUsedAndInTransitPriced }}%. </strong>
                </div>
                <pie-chart
                    v-if="!isLoading"
                    :colors="inventoryPricing.colors"
                    :columns="inventoryPricing.columns"
                    :new-metrics="newMetrics"
                    :used-metrics="usedMetrics"
                    :in-transit-metrics="inTransitMetrics"
                    :invalid-metrics="invalidMetrics"
                />
            </div>
            <div v-else>
                No vehicles in inventory
            </div>
        </i-box>
    </div>
</template>

<script>
import _ from "lodash";
import api from "../../../api";
import PieChart from "Modules/vehicleSale/components/VehicleSaleStats/PieChart";
import IBox from "Components/IBox";

export default {
    name: "PricingMetrics",
    components: {IBox, PieChart},
    props: {
        dealerId: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            title: "Inventory Pricing",
            description: "Minimum Pricing Warning",
            isLoading: true,
            pricingMetricsFromDb: null,
            newMetricsFromElastic: null,
            usedMetricsFromElastic: null,
            usedInTransitMetricsFromElastic: null,
            newInTransitMetricsFromElastic: null,
            newMetrics: {
                pricedCount: 0,
                total: 0
            },
            usedMetrics: {
                pricedCount: 0,
                total: 0
            },
            invalidMetrics: {
                invalidNewCount: 0,
                invalidUsedCount: 0
            },
            inTransitMetrics: {
                pricedCount: 0,
                total: 0,
            }
        };
    },
    computed: {
        totalNewUsedAndInTransit() {
            const newElastic = this.newMetricsFromElastic?.total ?? 0;
            const usedElastic = this.usedMetricsFromElastic?.total ?? 0;
            const newInTransitElastic = this.newIntransitMetricsFromElastic?.total ?? 0;
            const usedInTransitElastic = this.usedInTransitMetricsFromElastic?.total ?? 0;

            if (newElastic === 0 && usedElastic === 0 && newInTransitElastic === 0 && usedInTransitElastic === 0) {
                this.setMetricsDisplay("db");

                return this.pricingMetricsFromDb?.totalNewAndUsed ?? 0;
            }

            this.setMetricsDisplay("elastic");

            return newElastic + usedElastic + newInTransitElastic + usedInTransitElastic;
        },
        totalPriced() {
            const newElastic = this.newMetricsFromElastic?.pricedCount ?? 0;
            const usedElastic = this.usedMetricsFromElastic?.pricedCount ?? 0;
            const newInTransitElastic = this.newIntransitMetricsFromElastic?.pricedCount ?? 0;
            const usedInTransitElastic = this.usedInTransitMetricsFromElastic?.pricedCount ?? 0;

            if (newElastic === 0 && usedElastic === 0 && newInTransitElastic === 0 && usedInTransitElastic === 0) {
                return this.pricingMetricsFromDb?.totalNewAndUsedPriced ?? 0;
            }

            return newElastic + usedElastic + newInTransitElastic + usedInTransitElastic;
        },
        totalUnpriced() {
            return this.totalNewUsedAndInTransit - this.totalPriced;
        },
        warningPercentage() {
            return _.get(this.pricingMetricsFromDb, "warningPercentage");
        },
        warningPercentageThreshold() {
            return _.get(
                this.pricingMetricsFromDb,
                "warningPercentageThreshold"
            );
        },
        totalPercentageNewUsedAndInTransitPriced() {
            return _.round((this.totalPriced / this.totalNewUsedAndInTransit) * 100, 1);
        },
        inventoryPricing() {
            return {
                columns: [
                    ["Priced", this.totalPriced],
                    ["Not Priced", this.totalUnpriced]
                ],
                colors: {
                    Priced: "#1ab394",
                    "Not Priced": "#ed5565"
                }
            };
        },
        showInventoryPricing() {
            return !this.isLoading && this.totalNewUsedAndInTransit > 0
        }
    },
    mounted() {
        this.getPricingMetrics();
        this.getNewInTransitCounts();
        this.getUsedInTransitCounts();
        this.getNewCounts();
        this.getUsedCounts();
        this.getInvalidCounts();
    },
    methods: {
        // Dealers in onboarding may not have vehicles in elastic yet.
        // Falling back to db for metrics - nleone POR-2583 2.23.21
        setMetricsDisplay(source) {
            if (source === "db") {
                this.newMetrics.pricedCount =
                    this.pricingMetricsFromDb?.totalNewPriced ?? 0;
                this.newMetrics.total =
                    this.pricingMetricsFromDb?.totalNew ?? 0;
                this.usedMetrics.pricedCount =
                    this.pricingMetricsFromDb?.totalUsedPriced ?? 0;
                this.usedMetrics.total =
                    this.pricingMetricsFromDb?.totalUsed ?? 0;
            } else if (source === "elastic") {
                this.newMetrics.pricedCount =
                    this.newMetricsFromElastic?.pricedCount ?? 0;
                this.newMetrics.total = this.newMetricsFromElastic?.total ?? 0;
                this.usedMetrics.pricedCount =
                    this.usedMetricsFromElastic?.pricedCount ?? 0;
                this.usedMetrics.total =
                    this.usedMetricsFromElastic?.total ?? 0;
                this.inTransitMetrics.pricedCount =
                    (this.usedInTransitMetricsFromElastic?.pricedCount ?? 0) + (this.newIntransitMetricsFromElastic?.pricedCount ?? 0);
                this.inTransitMetrics.total =
                    (this.usedInTransitMetricsFromElastic?.total ?? 0) + (this.newIntransitMetricsFromElastic?.total ?? 0);
            }
        },
        getPricingMetrics() {
            return api
                .get(`/dealer/${this.dealerId}/pricing-metrics`)
                .then(response => {
                    this.pricingMetricsFromDb = response.data;
                })
                .catch(error => {
                    console.log(error);
                });
        },
        getNewCounts() {
            return api
                .post(`/dealers/dashboard/new-priced`, {
                    dealerIds: [this.dealerId]
                })
                .then(response => {
                    this.newMetricsFromElastic = response.data;
                    this.isLoading = false;
                })
                .catch(error => {
                    console.log(error);
                });
        },
        getNewInTransitCounts() {
            return api
                .post(`/dealers/dashboard/new-in-transit-priced`, {
                    dealerIds: [this.dealerId]
                })
                .then(response => {
                    this.newIntransitMetricsFromElastic = response.data;
                    this.isLoading = false;
                })
                .catch(error => {
                    console.log(error);
                });
        },
        getUsedCounts() {
            return api
                .post(`/dealers/dashboard/used-priced`, {
                    dealerIds: [this.dealerId]
                })
                .then(response => {
                    this.usedMetricsFromElastic = response.data;
                    this.isLoading = false;
                })
                .catch(error => {
                    console.log(error);
                });
        },
        getUsedInTransitCounts() {
            return api
                .post(`/dealers/dashboard/used-in-transit-priced`, {
                    dealerIds: [this.dealerId]
                })
                .then(response => {
                    this.usedInTransitMetricsFromElastic = response.data;
                    this.isLoading = false;
                })
                .catch(error => {
                    console.log(error);
                });
        },
        getInvalidCounts() {
            return api
                .get(`/stratus/dealer/${this.dealerId}/vehicle-pricing/metrics`)
                .then(response => {
                    this.invalidMetrics = response.data;
                })
                .catch(error => {
                    console.log(error);
                });
        }
    }
};
</script>
<style lang="scss">
.pricing-metrics .ibox-title {
    background-color: #ed5565;
    color: $white;
}
</style>
