<template>
    <div class="dealer-email-form">
        <validation-observer ref="observer" v-slot="{ passes }">
            <b-container>
                <b-form novalidate @submit.prevent="passes(submit)">
                    <i-box title="Lead Email Address" :collapsible="false">
                        <b-row class="justify-content-center">
                            <b-col>
                                <b-input-validate
                                    v-model="form.email"
                                    class="font-weight-bold"
                                    name="email"
                                    label="Email"
                                    rules="required|email"
                                />
                                <b-radio-group-validate
                                    v-model="form.contentType"
                                    class="font-weight-bold"
                                    label="Content Type"
                                    name="contentType"
                                    :options="options"
                                    rules="required"
                                />
                            </b-col>
                        </b-row>
                        <b-row class="justify-content-center">
                            <b-col>
                                <b-button variant="primary" @click="submit">
                                    Save
                                </b-button>
                                <b-button @click="$emit('cancel')">
                                    Cancel
                                </b-button>
                            </b-col>
                        </b-row>
                    </i-box>
                </b-form>
            </b-container>
        </validation-observer>
    </div>
</template>

<script>
import IBox from "Components/IBox";
import api from "@/api";
import _ from "lodash";
import BInputValidate from "Components/FormValidation/BInputValidate";
import BRadioGroupValidate from "Components/FormValidation/BRadioGroupValidate";
import veeValidateUtils from "@/api/veeValidateUtils";

export default {
    name: "DealerEmailForm",
    components: {
        BRadioGroupValidate,
        BInputValidate,
        IBox
    },
    props: {
        dealerId: {
            type: String,
            required: true
        },
        dealerEmailId: {
            type: String,
            required: false,
            default: null
        },
        email: {
            type: String,
            required: false,
            default: null
        },
        contentType: {
            type: String,
            required: false,
            default: null
        }
    },
    data() {
        return {
            form: {
                email: this.email,
                contentType: this.contentType
            },
            options: ["ADF", "TEXT"]
        };
    },
    methods: {
        submit() {
            const url = _.isNil(this.dealerEmailId)
                ? `/dealer/${this.dealerId}/emails`
                : `/dealer/${this.dealerId}/emails/${this.dealerEmailId}`;
            api.post(url, this.form)
                .then(() => {
                    this.$emit("success");
                })
                .catch(error => {
                    this.$refs.observer.setErrors(
                        veeValidateUtils.convertErrorToObserverErrors(error)
                    );
                });
        }
    }
};
</script>
