<template>
    <i-box :title="title">
        <template>
            <!--Refresh Inventory Button-->
            <div v-if="showHomeNetNotActive">
                <i
                    aria-hidden="true"
                    class="fa fa-exclamation-triangle"
                    data-toggle="tooltip"
                    data-placement="bottom"
                    title
                    data-original-title="This dealer has NOT yet received inventory from HomeNet's Data Conveyor. If recently activated, check back on this page a little later; otherwise, to activate, first ensure that this dealer is in the 'pricing' stage and <NAME_EMAIL> with Dealers Name, Dealer Id and address (AS SHOWN ON THIS PAGE)"
                />
                <span class="text-danger">HomeNet NOT Active</span>
            </div>
            <div v-if="showHomeNetRefreshButton" v-presentation-hidden>
                <b-button
                    v-if="$acl.hasAuthority('activate:dealer')"
                    size="sm"
                    variant="outline-secondary"
                    block
                    data-toggle="tooltip"
                    data-placement="top"
                    title
                    data-original-title="Clicking 'Refresh Inventory' will trigger HomeNet to resend all in stock vehicles for this dealer as well as resend the last 7 days of deletes"
                    class="mb-1"
                    @click="homeNetRefreshUrl"
                >
                    <i aria-hidden="true" class="fas fa-sync-alt"/> Refresh
                    Inventory
                </b-button>
            </div>
            <div v-if="showHomeNetRefreshButton" v-presentation-hidden>
                <b-button
                    v-if="$acl.hasAuthority('activate:dealer')"
                    size="sm"
                    variant="outline-secondary"
                    block
                    data-toggle="tooltip"
                    data-placement="top"
                    title
                    data-original-title="Clicking 'Reset Inventory' will remove all inventory from the system and trigger HomeNet to resend all in stock vehicles for this dealer as well as resend the last 7 days of deletes. This can take up to 5 minutes to complete."
                    class="mb-1"
                    @click="homeNetResetUrl"
                >
                    <i aria-hidden="true" class="fas fa-exclamation-triangle"/> Reset Inventory
                </b-button>
            </div>
            <!--/END Refresh Inventory Button-->

            <publish-inventory v-presentation-hidden/>

            <!--Checkin Portal Button-->
            <b-button
                v-if="$acl.hasAuthority('read:check-in')"
                size="sm"
                block
                variant="outline-secondary"
                :href="checkInPortalUrl"
                class="mb-1"
            >
                <i aria-hidden="true" class="fa fa-check-circle"/> Checkin
                Portal
            </b-button>
            <!--/END Checkin Portal Button-->

            <!--Test Lead Button-->
            <b-button
                v-if="$acl.hasAuthority('edit:dealer')"
                v-b-tooltip.hover
                size="sm"
                block
                variant="outline-secondary"
                class="mb-1 mt-0"
                title="Create test appointment and start lead flow"
                @click="sendTestLead"
            >
                <i aria-hidden="true" class="fas fa-sms"/> Test Lead Flow
            </b-button>
            <!--/END Test Lead Button-->

            <!--Dealer Status Button-->
            <dealer-status/>

            <!--Audit Log Button-->
            <dealer-audit-log v-presentation-hidden/>

            <div v-presentation-hidden>
                <!-- Warranty eRating Button -->
                <div v-if="isEnrolledInWarranty && !showWarrantyUnenroll">
                    <b-button
                        siz="sm"
                        block
                        variant="outline-secondary"
                        :href="eRatingUrl"
                        class="mb-1"
                    >
                        <i aria-hidden="true" class="fa fa-certificate"/>
                        Warranty eRating
                    </b-button>
                </div>
                <!--/END Warranty eRating Button -->

                <!--Warranty Enrollment Button-->
                <div v-else>
                    <b-button
                        v-if="$acl.hasAuthority('edit:warranty')"
                        :href="enrollInWarrantyUrl"
                        size="sm"
                        block
                        variant="outline-secondary"
                        :disabled="
                            !canEnrollInWarranty && !showWarrantyUnenroll
                        "
                        class="mb-1"
                        @click="fireToastr"
                    >
                        {{ warrantyText }}
                    </b-button>
                </div>
                <!--/END Warranty Enrollment Button-->
            </div>
            <!--Manage Pricing Button-->
            <div v-if="isPricingPortalSupported">
                <b-button
                    v-if="
                        $acl.hasAuthority([
                            'read:pricing-new',
                            'read:pricing-used'
                        ])
                    "
                    size="sm"
                    block
                    variant="outline-secondary"
                    :target="managePricingTarget"
                    :href="managePricingUrl"
                    class="mb-1"
                >
                    <i aria-hidden="true" class="fas fa-dollar-sign"/> Manage
                    Pricing
                </b-button>
            </div>
            <!--/END Manage Pricing Button-->

            <b-button
                v-if="$acl.hasAuthority('read:reports')"
                size="sm"
                block
                variant="outline-secondary"
                :to="'/dealer/' + dealer.id + '/dashboard'"
                class="mb-1"
            >
                <i aria-hidden="true" class="fas fa-tachometer-alt"/> Dashboard
            </b-button>

            <div v-if="isAltasSupported">
                <b-button
                    v-if="$acl.hasAuthority('read:dealer')"
                    size="sm"
                    block
                    variant="outline-secondary"
                    class="mb-1"
                    target="_blank"
                    :href="atlasDealerDetailsUrl"
                >
                    <i aria-hidden="true" class="fas fa-globe"/> Atlas
                </b-button>
            </div>
            <b-button
                v-if="$acl.hasAuthority('read:dealer')"
                size="sm"
                block
                variant="outline-secondary"
                class="mb-1"
                @click="openPluginStatusModal"
            >
                <i aria-hidden="true" class="fas fa-plug"/> Dealer Plugin
                Status
            </b-button>

            <dealer-plugin-status-modal
                ref="pluginStatusModal"
                :dealer-id="dealer.id"
            />
        </template>
    </i-box>
</template>

<script>
import IBox from "Components/IBox";
import {call, dispatch, get} from "vuex-pathify";
import PublishInventory from "Modules/dealer/components/PublishInventory";
import DealerStatus from "Modules/dealer/components/DealerStatus";
import DealerAuditLog from "Modules/dealer/components/DealerAuditLog";
import DealerPluginStatusModal from "./DealerPluginStatusModal";
import _ from "lodash";

export default {
    name: "ManageDealership",
    components: {
        DealerAuditLog,
        DealerStatus,
        PublishInventory,
        IBox,
        DealerPluginStatusModal
    },
    data() {
        return {
            title: "Manage Dealership"
        };
    },
    computed: {
        dealer: get("dealer/selectedDealer"),
        homeNetMostRecentImportJob: get("dealer/homeNetMostRecentImportJob"),
        isHomeNetDealer() {
            return this.dealer.inventorySource === "HN";
        },
        isPricingPortalSupported() {
            return this.dealer.pricingPortalSupported;
        },
        showHomeNetNotActive() {
            return (
                this.isHomeNetDealer && _.isNil(this.homeNetMostRecentImportJob)
            );
        },
        showHomeNetRefreshButton() {
            return (
                this.isHomeNetDealer &&
                !_.isNil(this.homeNetMostRecentImportJob)
            );
        },
        isEnrolledInWarranty() {
            return _.get(this.dealer, "enrolledInWarrantyProgram");
        },
        canEnrollInWarranty() {
            if (!this.isEnrolledInWarranty) {
                if (
                    this.dealer.certified === false &&
                    (this.dealer.state === "MA" || this.dealer.state === "CA")
                ) {
                    return false;
                }

                return true;
            }

            return false;
        },
        checkInPortalUrl() {
            return `/stratus/dealer/${this.dealer.id}/checkin`;
        },
        enrollInWarrantyUrl() {
            return `/dealer/${this.dealer.id}/enroll-warranty`;
        },
        eRatingUrl() {
            return `/stratus/dealer/${this.dealer.id}/warranty`;
        },
        managePricingUrl() {
            return this.dealer.useAtlasForManagePricing === true
                ? `${this.dealer.atlasUrl}/dealer/${this.dealer.id}/pricing/new`
                : `/stratus/dealer/${this.dealer.id}/vehicle-pricing`;
        },
        managePricingTarget() {
            return this.dealer.useAtlasForManagePricing === true
                ? "_blank"
                : "_self";
        },
        isAltasSupported() {
            return true;
        },
        atlasDealerDetailsUrl() {
            return `${this.dealer.atlasUrl}/dealer/details?dealerIds=${this.dealer.id}`;
        },
        showWarrantyUnenroll() {
            return (
                _.get(this.dealer, "dealerStatus") === "CANCELLED" &&
                this.isEnrolledInWarranty
            );
        },
        warrantyText() {
            if (this.showWarrantyUnenroll) {
                return "Disenroll Warranty";
            } else {
                return "Enroll Warranty";
            }
        }
    },
    mounted() {
        this.fetchHomeNetMostRecentImportJob();
    },
    methods: {
        fetchHomeNetMostRecentImportJob: call(
            "dealer/fetchHomeNetMostRecentImportJob"
        ),
        openPluginStatusModal() {
            this.$refs.pluginStatusModal.open();
        },
        homeNetResetUrl() {
            dispatch("dealer/homeNetReset").then(response => {
                if (response.data.success === true) {
                    this.$toastr.s("Dealer inventory reset initiated");
                } else {
                    this.$toastr.e(response.data.message);
                }
            });
        },
        homeNetRefreshUrl() {
            dispatch("dealer/homeNetRefresh").then(response => {
                if (response.data.success === true) {
                    this.$toastr.s("Dealer inventory refresh initiated");
                } else {
                    this.$toastr.e(response.data.message);
                }
            });
        },
        sendTestLead() {
            dispatch("dealer/sendTestAppointment").then(response => {
                if (response.data.type === "SUCCESS") {
                    this.$toastr.s(response.data.message);
                } else {
                    this.$toastr.e(response.data.message);
                }
            });
        },
        fireToastr() {
            if (this.showWarrantyUnenroll) {
                return this.$toastr.i(
                    "Request sent to unenroll dealer in warranty"
                );
            } else {
                return this.$toastr.i(
                    "Request sent to enroll dealer in warranty"
                );
            }
        }
    }
};
</script>
