<template>
    <div class="ibox">
        <div
            class="ibox-title d-flex justify-content-between align-items-center pr-3"
        >
            <h5>Notes</h5>
            <div class="text-white">
                <a
                    v-if="$acl.hasAuthority('create:notes')"
                    class="btn btn-primary btn-circle modal-action"
                    @click="openAddNoteModal"
                >
                    <i aria-hidden="true" class="fa fa-plus"/>
                </a>
            </div>
        </div>
        <div class="ibox-content">
            <div v-if="notes.length <= 0" id="no-notes-message">
                No Notes found for this dealer.
            </div>

            <div v-else id="notes-feed-activity" class="feed-activity-list">
                <div
                    v-for="note in notes"
                    :key="note.id"
                    class="d-flex justify-content-between feed-element"
                >
                    <span
                        v-if="note.createdByUser.gravitarEmailHash"
                        class="mr-2"
                    >
                        <img
                            alt="user"
                            :src="
                                'https://www.gravatar.com/avatar/' +
                                    note.createdByUser.gravitarEmailHash +
                                    ',' +
                                    's=48'
                            "
                            class="img-circle notes-circle"
                        />
                    </span>

                    <div class="media-body d-flex flex-column">
                        <div class="d-flex flex-column">
                            <div class="d-flex justify-content-between">
                                <div v-if="note.createdByUser">
                                    <strong>{{
                                            note.createdByUser.name
                                        }}</strong
                                    ><small v-if="note.createdByUser.jobTitle">
                                    ({{
                                        note.createdByUser.jobTitle
                                    }})</small
                                >
                                    <span>posted a note</span>

                                    <a
                                        v-if="
                                            note.createdByUser.id ===
                                                loggedInUserId
                                        "
                                        @click="openEditNoteModal(note.id)"
                                    >
                                        <i
                                            aria-hidden="true"
                                            class="fas fa-pencil-alt"
                                        />
                                    </a>
                                </div>

                                <div v-if="note.modified">
                                    <span class="muted text-warning"
                                    >edited</span
                                    >
                                    <small>
                                        {{
                                            note.lastModifiedDate
                                                | durationFromCreatedDate
                                        }}
                                    </small>
                                </div>

                                <div v-else>
                                    <small>
                                        {{
                                            note.createdDate
                                                | durationFromCreatedDate
                                        }}
                                    </small>
                                </div>
                            </div>

                            <small class="text-muted">{{
                                    note.createdDate
                                        | formatDateTime("MM/DD/YYYY h:mm a z")
                                }}</small>
                        </div>

                        <div class="note-well">
                            {{ note.content }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="notes.length > 0" class="ibox-footer">
            <div
                class="pagination-wrapper d-flex justify-content-center w-100 mt-2"
            >
                <b-pagination
                    v-if="notesData"
                    v-model="notesPageNumber"
                    :total-rows="notesData.totalElements"
                    :per-page="notesData.size"
                />
            </div>
        </div>
    </div>
</template>
<script>
import {call, dispatch, get} from "vuex-pathify";
import _ from "lodash";
import moment from "moment";

export default {
    name: "NotesList",
    filters: {
        durationFromCreatedDate: function (date) {
            if (!date) return "";

            return moment(date).fromNow();
        }
    },
    props: {
        dealerId: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            notesPageNumber: 1
        };
    },
    computed: {
        loggedInUserId: get("loggedInUser/id"),
        hasAuthority: get("loggedInUser/hasAuthority"),
        notesData: get("notes/notes"),
        page: get("notes/notes@number"),
        notes() {
            return _.get(this.notesData, "content", []);
        }
    },
    watch: {
        notesPageNumber(newPageNumberNumber) {
            const pageNumber = this.page + 1;
            if (newPageNumberNumber !== pageNumber) {
                this.fetchDealerNotes({
                    dealerId: this.dealerId,
                    page: newPageNumberNumber - 1
                });
            }
        },
        page(newVal) {
            const pageNumber = newVal + 1;
            if (pageNumber !== this.notesPageNumber) {
                this.notesPageNumber = pageNumber;
            }
        }
    },
    methods: {
        openAddNoteModal: call("notes/openAddNoteModal"),
        openEditNoteModal(noteId) {
            dispatch("notes/openEditNoteModal", noteId);
        },
        fetchDealerNotes: call("notes/fetchDealerNotes")
    },
    created() {
        this.fetchDealerNotes({dealerId: this.dealerId});
    }
};
</script>
<style lang="scss">
.note-well {
    border: 1px solid #e7eaec;
    box-shadow: none;
    margin-top: 10px;
    margin-bottom: 5px;
    padding: 10px 20px;
    font-size: 11px;
    line-height: 16px;
    min-height: 20px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.img-circle {
    width: 38px;
    height: 38px;
}
</style>
