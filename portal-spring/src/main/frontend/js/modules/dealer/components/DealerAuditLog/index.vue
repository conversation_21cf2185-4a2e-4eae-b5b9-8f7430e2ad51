<template>
    <div class="mb-1">
        <b-button
            size="sm"
            block
            variant="outline-secondary"
            @click="viewLog()"
        >
            <i aria-hidden="true" class="fa fa-list"/>
            Audit Log
        </b-button>

        <b-modal
            id="audit-log-modal"
            size="lg"
            header-bg-variant="primary"
            title="Audit Log"
            ok-only
        >
            <audit-log store="dealer"/>
        </b-modal>
    </div>
</template>

<script>
import {dispatch} from 'vuex-pathify';
import AuditLog from 'Components/AuditLog/index';

export default {
    name: 'DealerAuditLog',
    components: {AuditLog},
    methods: {
        viewLog() {
            dispatch('dealer/loadChangeSets')
                .then(() => this.$bvModal.show('audit-log-modal'));
        }
    }
};
</script>
<style lang="scss">
#audit-log-modal {
    .modal-title {
        font-size: 20px;
    }
}
</style>
