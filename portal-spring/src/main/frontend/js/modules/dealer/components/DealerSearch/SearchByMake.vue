<template>
    <checkbox-facet
        ref="makesComponent"
        facet-label="Makes"
        store="dealerSearch"
        facet-name="makes"
        filter-name="makes"
    >
        <template slot="actions">
            <a @click="populateLevelOneMakes">Select Level 1 Makes</a>
        </template>
    </checkbox-facet>
</template>

<script>
import CheckboxFacet from "Components/Facets/CheckboxFacet";

export default {
    name: "SearchByMake",

    components: {CheckboxFacet},

    data() {
        return {
            levelOneMakes: [
                "Chevrolet",
                "Ford",
                "Honda",
                "Hyundai",
                "Jeep",
                "Kia",
                "Nissan",
                "Toyota"
            ],
            levelOnes: []
        };
    },

    methods: {
        populateLevelOneMakes() {
            const availableMakesById = _.map(
                this.$refs.makesComponent.facets,
                "id"
            );
            this.$refs.makesComponent.filters = _.intersection(
                this.levelOneMakes,
                availableMakesById
            );
        }
    }
};
</script>
