<template>
    <i-box class="market-priced-widget" :title="title" :description="description"
           :alert-message="alertMessage" :collapsible="false" :loading="isLoading"
    >
        <div ref="marketPricedWidget"/>

        <div class="text-center">
            {{ data.overMarketCount | numeral('0,0') }} over market
        </div>
    </i-box>
</template>

<style lang="scss">
.market-priced-widget {
    .percent-number {
        font-size: 18px;
    }

    .ibox-title {
        padding-right: 15px !important;
    }
}
</style>

<script>
import _ from 'lodash';
import IBox from 'Components/IBox';
import {call, get} from 'vuex-pathify';
import api from '@/api';

export default {
    components: {IBox},

    props: {
        name: {
            type: String,
            required: true
        },
        title: {
            type: String,
            required: true
        },
        colorConfig: {
            type: Object,
            required: false
        },
        description: {
            type: String,
            required: false,
            default: null
        },
        alertMessage: {
            type: String,
            required: false,
            default: null
        }
    },

    data() {
        return {
            isLoading: true,
            data: {
                overMarketCount: 0,
                total: 0
            },
            defaultColorConfig: {
                green: 90,
                yellow: 75
            }
        };
    },

    computed: {
        dealerIds: get('dashboard/dealerIds'),

        colorConfigInUse() {
            return _.merge(this.defaultColorConfig, this.colorConfig);
        },

        columns() {
            const pricedCount = (this.data.total - this.data.overMarketCount);
            if (_.isNaN(pricedCount)) {
                return [
                    [this.title, 0]
                ];
            }

            return [
                [this.title, pricedCount]
            ];
        },

        max() {
            const total = _.get(this.data, 'total', 100);
            if (_.isNaN(total) || total <= 0) {
                return 100;
            }

            return total;
        }
    },

    watch: {
        dealerIds() {
            this.loadData();
        }
    },

    created() {
        this.loadData();
    },

    methods: {
        incrementLoaderCount: call('dashboard/incrementLoaderCount'),
        decrementLoaderCount: call('dashboard/decrementLoaderCount'),

        updateChart() {
            c3.generate({
                bindto: this.$refs.marketPricedWidget,
                data: {
                    columns: this.columns,
                    type: 'gauge'
                },
                gauge: {
                    max: this.max
                },
                color: {
                    pattern: ['#dc3545', '#F6C600', '#60B044'],
                    threshold: {
                        values: [((this.colorConfigInUse.yellow / 100) * this.data.total), ((this.colorConfigInUse.green / 100) * this.data.total), 100]
                    }
                }
            });
        },

        loadData() {
            this.incrementLoaderCount();
            this.isLoading = true;

            api.post(`/dealers/dashboard/${this.name}`,
                {
                    dealerIds: this.dealerIds
                })
                .then(response => {
                    this.data = response.data;

                    this.isLoading = false;
                    this.decrementLoaderCount();
                })
                .then(() => {
                    this.updateChart();
                })
                .catch(error => {
                    console.log(error);

                    this.isLoading = false;
                    this.decrementLoaderCount();
                });
        }
    }
};
</script>
