<template>
    <div>
        <b-row class="justify-content-center">
            <b-col lg="10">
                <i-box :title="title" class="pt-2" :collapsible="false">
                    <validation-observer ref="observer" v-slot="{ passes }">
                        <b-form novalidate @submit.prevent="passes(onSubmit)">
                            <b-form-group label="End Date">
                                <v-date-picker v-model="form.endDate" color="red" :popover="datePickerProps.popover"
                                               :input-props="datePickerProps.input"
                                />
                            </b-form-group>

                            <b-form-group>
                                <b-form-checkbox v-model="form.cancelSubscription" switch>
                                    Cancel Subscription
                                </b-form-checkbox>
                            </b-form-group>

                            <b-alert variant="danger" :show="isError">
                                Error cancelling contract.
                            </b-alert>

                            <div class="mt-2 float-right">
                                <b-button size="lg" type="submit" variant="danger"
                                          class="mr-2" :disabled="submitting" @submit="onSubmit()"
                                >
                                    Cancel Contract
                                </b-button>
                                <b-button size="lg" variant="primary" :disabled="submitting"
                                          @click="onCancel()"
                                >
                                    Don't Cancel
                                </b-button>
                            </div>
                        </b-form>
                    </validation-observer>
                </i-box>
            </b-col>
        </b-row>
    </div>
</template>

<script>
import api from '@/api';
import IBox from 'Components/IBox';
import moment from 'moment';
import veeValidateUtils from '@/api/veeValidateUtils';

export default {
    name: 'DealerContractCancelForm',
    components: {IBox},
    props: {
        dealerId: {
            type: String,
            required: true
        },
        contractId: {
            type: Number,
            required: true
        }
    },
    data() {
        return {
            datePickerProps: {
                popover: {
                    visibility: 'click'
                },
                input: {
                    readonly: true
                }
            },
            title: 'Cancel Contract',
            form: {
                endDate: new Date(),
                cancelSubscription: false
            },
            submitting: false,
            isError: false
        };
    },
    methods: {
        onSubmit() {
            this.submitting = true;
            this.isError = false;
            const strEndDate = !_.isNil(this.form.endDate) ? moment(this.form.endDate).format('YYYY-MM-DD') : null;
            const formData = {
                ...this.form,
                endDate: strEndDate,
                dealerId: this.dealerId
            };

            api.post(`/dealer/${this.dealerId}/contracts/${this.contractId}/cancel`, formData)
                .then(() => {
                    this.$emit('done');
                })
                .catch(error => {
                    this.$refs.observer.setErrors(veeValidateUtils.convertErrorToObserverErrors(error));
                    this.isError = true;
                })
                .finally(() => {
                    this.submitting = false;
                });
        },
        onCancel() {
            this.$emit('cancel');
        }
    }
};
</script>
