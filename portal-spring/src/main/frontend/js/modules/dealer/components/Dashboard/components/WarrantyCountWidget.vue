<template>
    <i-box class="warranty-count" :title="title" :description="description"
           :alert-message="alertMessage" :collapsible="false" :loading="isLoading"
    >
        <div v-if="totalCount > 0">
            <div ref="warrantyCountWidget"/>
        </div>
        <div v-else>
            <no-data :title="title"/>
        </div>
    </i-box>
</template>

<style lang="scss">
.warranty-count {
    .ibox-title {
        padding-right: 15px !important;
    }

    .ibox-content {
        min-height: 234px;
    }
}
</style>

<script>
import _ from 'lodash';
import IBox from 'Components/IBox';
import {call, get} from 'vuex-pathify';
import api from '@/api';
import NoData from 'Modules/dealer/components/Dashboard/components/NoData';

export default {
    components: {NoData, IBox},

    props: {
        name: {
            type: String,
            required: true
        },
        title: {
            type: String,
            required: true
        },
        description: {
            type: String,
            required: false,
            default: null
        },
        alertMessage: {
            type: String,
            required: false,
            default: null
        }
    },

    data() {
        return {
            isLoading: true,
            data: {
                vscCount: 0,
                warrantyCount: 0
            }
        };
    },

    computed: {
        startDate: get('dashboard/startDate'),
        endDate: get('dashboard/endDate'),
        dateRangeStr: get('dashboard/dateRangeStr'),
        dealerIds: get('dashboard/dealerIds'),

        totalCount() {
            return this.data.vscCount + this.data.warrantyCount;
        },

        columns() {
            return [
                ['VSC', this.data.vscCount],
                ['Lifetime Warranty', this.data.warrantyCount]
            ];
        }
    },

    watch: {
        dateRangeStr() {
            this.loadData();
        },
        dealerIds() {
            this.loadData();
        }
    },

    created() {
        this.loadData();
    },

    methods: {
        incrementLoaderCount: call('dashboard/incrementLoaderCount'),
        decrementLoaderCount: call('dashboard/decrementLoaderCount'),

        updateChart() {
            c3.generate({
                bindto: this.$refs.warrantyCountWidget,
                size: {
                    height: 200
                },
                data: {
                    columns: this.columns,
                    type: 'bar',
                    colors: {
                        VSC: '#1ab394',
                        'Lifetime Warranty': '#23c6c8'
                    }
                },
                tooltip: {
                    format: {
                        title: function (x, index) {
                            return 'Warranty';
                        }
                    }
                },
                axis: {
                    x: {
                        show: false
                    },
                    y: {
                        show: false
                    }
                }
            });
        },

        loadData() {
            if (_.isNil(this.startDate) || _.isNil(this.endDate)) {
                return;
            }

            this.incrementLoaderCount();
            this.isLoading = true;

            api.post(`/dealers/dashboard/${this.name}`,
                {
                    dealerIds: this.dealerIds,
                    startDate: this.startDate,
                    endDate: this.endDate
                })
                .then(response => {
                    this.data = response.data;

                    this.isLoading = false;
                    this.decrementLoaderCount();
                })
                .then(() => {
                    this.updateChart();
                })
                .catch(error => {
                    console.log(error);

                    this.isLoading = false;
                    this.decrementLoaderCount();
                });
        }
    }
};
</script>
