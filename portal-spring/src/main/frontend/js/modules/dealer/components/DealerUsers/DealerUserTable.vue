<template>
    <div :id="`${userRole}-table`">
        <i-box :title="`${title}s`">
            <template slot="header-actions">
                <b-button
                    v-if="$acl.hasAuthority('edit:dealer') && !label"
                    v-b-modal="`user-role-${userRole}-modal`"
                    size="xs"
                    variant="light"
                >
                    <i aria-hidden="true" class="fa fa-plus"/>
                </b-button>
                <b-button
                    v-else-if="$acl.hasAuthority('edit:dealer') && label"
                    size="xs"
                    variant="light"
                    @click="openAtlas"
                >
                    {{ label }}
                </b-button>
            </template>

            <div class="dealer-user-table-container">
                <div v-if="items <= 0">
                    <div class="bg-muted p-2" style="border-radius: 3px;">
                        <i
                            aria-hidden="true"
                            class="fa fa-exclamation-triangle mr-1"
                        />
                        No <strong>{{ userRole }}</strong> have been set up yet
                        for this dealer! Please use the plus icon in the right
                        corner to add one.
                    </div>
                </div>

                <div v-else>
                    <b-table
                        stacked="md"
                        striped
                        hover
                        bordered
                        :fields="fields"
                        :items="items"
                        :no-local-sorting="true"
                    >
                        <template v-slot:cell(actions)="data">
                            <div class="text-nowrap">
                                <b-button
                                    v-if="$acl.hasAuthority('edit:dealer')"
                                    size="xs"
                                    variant="primary"
                                    @click="editUser(data.item.id)"
                                >
                                    <i
                                        aria-hidden="true"
                                        class="fas fa-pencil-alt"
                                    />
                                </b-button>
                                <b-button
                                    v-if="$acl.hasAuthority('edit:dealer')"
                                    size="xs"
                                    variant="danger"
                                    @click="removeUser(data.item)"
                                >
                                    <i
                                        aria-hidden="true"
                                        class="fas fa-trash-alt"
                                    />
                                </b-button>
                            </div>
                        </template>
                        <template v-slot:cell(smsVerified)="data">
                            <small
                                v-if="
                                    data.item.phoneNumberInfo &&
                                        data.item.phoneNumberInfo.carrier
                                            .type === 'landline'
                                "
                                class="text-danger"
                            >
                                Landline number, unable to send SMS.
                            </small>
                            <div v-else>
                                <div v-if="data.item.smsVerified">
                                    <i
                                        aria-hidden="true"
                                        class="fas fa-check-circle"
                                    />
                                    <b-button
                                        class="text-nowrap"
                                        size="xs"
                                        variant="primary"
                                        @click="sendTestSms(data.item)"
                                    >
                                        Send Test
                                    </b-button>
                                </div>
                                <div v-else>
                                    <i
                                        aria-hidden="true"
                                        class="fas fa-comment-slash"
                                    />
                                    <b-link
                                        class="text-nowrap"
                                        @click="sendVerificationText(data.item)"
                                    >
                                        Send Verification SMS
                                    </b-link>
                                </div>
                            </div>
                        </template>
                        <template v-slot:cell(firstName)="data">
                            <b-link
                                v-if="adminOrDealer(data.item.type)"
                                :href="`/user/business-users/${data.item.id}`"
                            >
                                {{
                                    data.item.firstName +
                                    " " +
                                    data.item.lastName
                                }}
                            </b-link>
                            <b-link
                                v-if="customer(data.item.type)"
                                :href="`/user/customers/${data.item.id}`"
                            >
                                {{
                                    data.item.firstName +
                                    " " +
                                    data.item.lastName
                                }}
                            </b-link>
                            <span
                                v-else-if="$acl.hasAuthority('ROLE_DEALER')"
                            >{{
                                    data.item.firstName +
                                    " " +
                                    data.item.lastName
                                }}</span
                            >
                            <div
                                v-if="data.item.contactType"
                                class="contact-label mt-1"
                                :class="
                                    contactLabelColor(data.item.contactType)
                                "
                            >
                                <i
                                    v-if="data.item.contactType === 'Primary'"
                                    class="fa fa-star pr-1"
                                    aria-hidden="true"
                                />{{ data.item.contactType }} Contact
                            </div>
                        </template>
                        <template v-slot:cell(phoneNumber)="data">
                            <b-link :href="`tel:${data.item.phoneNumber}`">
                                {{ data.item.phoneNumber | phone }}
                            </b-link>
                        </template>
                        <template v-slot:cell(email)="data">
                            <b-link :href="`mailto:${data.item.email}`">
                                {{ data.item.email }}
                            </b-link>
                        </template>
                        <template v-slot:cell(stockType)="data">
                            <span class="label">
                                {{ normalizeStockType(data.item.stockType) }}
                            </span>
                        </template>
                        <template v-slot:cell(internalSms)="data">
                            <span :class="labelCss(data.item.internalSms)">
                                {{ data.item.internalSms }}
                            </span>
                        </template>
                        <template v-slot:cell(spanishSpeaking)="data">
                            <span
                                :class="labelCss(data.item.spanishSpeaking)"
                            >
                                {{ displayBoolean(data.item.spanishSpeaking) }}
                            </span>
                        </template>
                        <template v-slot:cell(crmId)="data">
                            <span>{{ data.item.crmId }}</span>
                        </template>
                        <template v-slot:cell(permissions)="data">
                            <ul
                                v-for="permission in data.item.permissions"
                                class="mb-0"
                            >
                                <li>{{ permission.description }}</li>
                            </ul>
                            <b-button
                                v-if="$acl.hasAuthority('edit:dealer')"
                                size="xs"
                                class="float-right"
                                variant="primary"
                                @click="showPermissionsModal(data.item)"
                            >
                                Edit
                            </b-button>
                        </template>
                    </b-table>
                </div>
            </div>
        </i-box>

        <b-modal
            :id="`user-role-${userRole}-modal`"
            :title="title"
            hide-footer
            :size="dealerUserId ? 'xl' : 'md'"
            @hidden="clearUserRoleModal()"
        >
            <div v-if="loading" class="loader text-center">
                <b-spinner/>
            </div>

            <div v-else-if="dealerUserId">
                <user-form
                    :dealer-user-id="dealerUserId"
                    :is-create="false"
                    :dealer-id="dealerId"
                    @done="hideUserRoleModal"
                    @cancel="hideUserRoleModal"
                />
            </div>

            <div v-else>
                <b-form inline @submit.prevent="findUser">
                    <p>
                        * Search for an existing user and If the user doesn't
                        exist you will be prompted to add them.
                    </p>
                    <b-input-group class="w-100">
                        <b-input
                            v-model="userSearchEmail"
                            name="email"
                            placeholder="Enter email: <EMAIL>"
                        />
                        <b-input-group-append>
                            <b-button type="submit" variant="primary">
                                <i aria-hidden="true" class="fas fa-search"/>
                            </b-button>
                        </b-input-group-append>
                    </b-input-group>
                </b-form>
            </div>
        </b-modal>

        <b-modal
            :id="`dealer-${userRole}-permissions-modal`"
            title="Dealer Permissions"
            hide-footer
            @hidden="isEditPermissions = false"
        >
            <dealer-permissions-form
                :dealer-user="dealerUser"
                :dealer-id="dealerId"
                :is-edit-permissions="isEditPermissions"
                @completed="hideAndRefresh"
                @cancel="hidePermissionsModal"
            />
        </b-modal>

        <b-modal
            :id="`add-user-${userRole}-modal`"
            title="Add User"
            hide-footer
            size="xl"
        >
            <user-form
                :user-search-email="userSearchEmail"
                :is-create="true"
                :dealer-id="dealerId"
                @done="showPermissionsModal"
                @cancel="hideUserRoleModal"
            />
        </b-modal>
    </div>
</template>

<script>
import IBox from "Components/IBox";
import api from "@/api";
import get from "lodash/get";
import isNil from "lodash/isNil";
import pull from "lodash/pull";
import UserForm from "Modules/user/components/UserForm";
import DealerPermissionsForm from "Modules/dealer/components/DealerPermissionsForm";

export default {
    name: "DealerUserTable",
    components: {DealerPermissionsForm, UserForm, IBox},
    props: {
        title: {
            type: String,
            required: true
        },
        items: {
            type: Array,
            required: true
        },
        userRole: {
            type: String,
            required: true
        },
        dealerId: {
            type: String,
            required: true
        },
        fields: {
            type: Array,
            required: true
        },
        label: {
            type: String,
            required: false
        },
        atlasUrl: {
            type: String,
            required: false
        }
    },
    data() {
        return {
            userSearchEmail: "",
            loading: false,
            dealerUserId: null,
            dealerUser: null,
            isEditPermissions: false,
            normStockTypes: {
                NEW: "New",
                USED: "Used",
                UNKNOWN: "New & Used"
            }
        };
    },
    mounted() {
        if (this.isStratus) {
            pull(this.fields, "actions");
        }
    },
    methods: {
        contactLabelColor(contactType) {
            switch (contactType) {
                case "Primary":
                    return "label label-success";
                case "Secondary":
                    return "label label-warning";
                default:
                    return "label";
            }
        },
        clearUserRoleModal() {
            this.userSearchEmail = "";
            this.dealerUserId = null;
        },
        normalizeStockType(stockType) {
            const newAndUsed = "unknown"
            const type = isNil(stockType) || this.notAString(stockType) ? newAndUsed : stockType;
            let normType = this.normStockTypes[type];

            return normType;
        },
        notAString(str) {
            return typeof str !== 'string';
        },
        showPermissionsModal(user) {
            this.dealerUser = user;
            this.isEditPermissions = true;
            this.$bvModal.hide(`add-user-${this.userRole}-modal`);
            this.$bvModal.show(`dealer-${this.userRole}-permissions-modal`);
        },
        showAddUserModal() {
            this.$bvModal.show(`add-user-${this.userRole}-modal`);
        },
        hideUserRoleModal() {
            this.$bvModal.hide(`user-role-${this.userRole}-modal`);
            this.$emit("refresh");
        },
        hidePermissionsModal() {
            this.$bvModal.hide(`dealer-${this.userRole}-permissions-modal`);
        },
        hideAndRefresh() {
            this.$emit("refresh");
            this.hidePermissionsModal();
        },
        findUser() {
            this.loading = true;
            api.get(`/dealer/${this.dealerId}/users`, {
                q: this.userSearchEmail
            })
                .then(response => {
                    this.loading = false;
                    this.dealerUser = response.data;
                    this.$bvModal.show(
                        `dealer-${this.userRole}-permissions-modal`
                    );
                })
                .catch(() => {
                    this.showAddUserModal();
                    this.loading = false;
                })
                .finally(() => {
                    this.$bvModal.hide(`user-role-${this.userRole}-modal`);
                });
        },
        labelCss(item) {
            return item === false || isNil(item)
                ? "label label-danger"
                : "label label-success";
        },
        displayBoolean(item) {
            return item === false || isNil(item) ? "false" : "true";
        },
        sendVerificationText(user) {
            api.post(`/business-users/${user.id}/send-verification-text`)
                .then(() => {
                    this.$bvToast.toast(
                        `Verification SMS sent to ${user.firstName} ${user.lastName}`
                    );
                })
                .catch(error => {
                    const errorMsg = get(
                        error,
                        "response.data.message",
                        "Unknown Error"
                    );
                    this.$bvToast.toast(`Error: ${errorMsg}`, {
                        title: "Failure sending Verification SMS",
                        noAutoHide: true,
                        variant: "danger"
                    });
                });
        },
        sendTestSms(user) {
            this.$emit("test-sms", user);
        },
        removeUser(user) {
            this.$emit("remove-user", user);
        },
        editUser(userId) {
            this.dealerUserId = userId;
            this.$bvModal.show(`user-role-${this.userRole}-modal`);
        },
        openAtlas() {
            if (!isNil(this.atlasUrl)) {
                window.open(`${this.atlasUrl}/dealer/details?dealerIds=${this.dealerId}`);
            }
        },
        adminOrDealer(userType) {
            userType = userType.toLowerCase();
            return (
                this.$acl.hasAuthority("ROLE_ADMIN") &&
                (userType === "admin" || userType === "dealer")
            );
        },
        customer(userType) {
            userType = userType.toLowerCase();
            return userType === "user";
        }
    }
};
</script>

<style lang="scss" scoped>
.dealer-user-table-container {
    overflow: auto;

    .contact-label {
        max-width: 112px;
    }
}
</style>
