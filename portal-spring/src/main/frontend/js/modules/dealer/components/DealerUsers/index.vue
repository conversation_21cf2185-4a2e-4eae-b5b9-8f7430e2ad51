<template>
    <div>
        <dealer-emails/>

        <dealer-user-table
            :title="pmTitle"
            :items="programManagers"
            :user-role="pmRole"
            :dealer-id="dealerId"
            :fields="managerFields"
            @refresh="fetchAllUsers"
            @remove-user="user => fireModal(user, pmRole)"
            @test-sms="doSendTestSms"
        />

        <dealer-user-table
            :title="fmTitle"
            :items="followUpManagers"
            :user-role="fmRole"
            :dealer-id="dealerId"
            :fields="managerFields"
            @refresh="fetchAllUsers"
            @remove-user="user => fireModal(user, fmRole)"
            @test-sms="doSendTestSms"
        />

        <dealer-user-table
            :title="smTitle"
            :items="salesManagers"
            :user-role="smRole"
            :dealer-id="dealerId"
            :fields="managerFields"
            @refresh="fetchAllUsers"
            @remove-user="user => fireModal(user, smRole)"
            @test-sms="doSendTestSms"
        />

        <dealer-user-table
            :title="basicTitle"
            :items="basicUsers"
            :user-role="basicUser"
            :dealer-id="dealerId"
            :fields="basicUserFields"
            :label="atlasLabel"
            :atlas-url="atlasUrl"
            @refresh="fetchAllUsers"
            @remove-user="user => fireModal(user, basicUser)"
        />

        <!-- Remove User Modal -->
        <b-modal
            id="remove-user-modal"
            ref="remove-user-modal"
            size="md"
            header-bg-variant="danger"
            title="Remove User"
        >
            <p>
                Are you sure you want to remove
                <strong>{{ this.selectedUser.name }}</strong>
            </p>

            <template slot="modal-footer">
                <b-button size="md" variant="secondary" @click="noRemove()">
                    No, Don't Remove
                </b-button>
                <b-button size="md" variant="primary" @click="confirmRemoval()">
                    Yes, Remove
                </b-button>
            </template>
        </b-modal>
        <!--/END Remove User Modal -->

        <!-- Last PM Modal -->
        <b-modal
            id="last-pm-modal"
            ref="last-pm-modal"
            size="md"
            header-bg-variant="danger"
            title="Warning - Last Program Manager!"
            ok-only
        >
            <p>
                A <strong>LIVE</strong> dealer must have at least one Program
                Manager.
            </p>
            <p>
                Please add another Program Manager before removing
                <strong>{{ this.selectedUser.name }}</strong
                >. Or flip the dealer status to <strong>ONBOARDING</strong> in
                order to remove the last Program Manager.
            </p>
        </b-modal>
        <!--/End Last PM Modal -->
    </div>
</template>

<script>
import {call, dispatch, get} from "vuex-pathify";
import DealerUserTable from "./DealerUserTable";
import DealerEmails from "../DealerEmails";
import _ from "lodash";

export default {
    name: "DealerUsers",
    components: {DealerUserTable, DealerEmails},
    data() {
        return {
            selectedUser: {
                id: null,
                name: null,
                type: null
            },
            pmTitle: "Program Manager",
            fmTitle: "Follow Up Manager",
            smTitle: "Sales Manager",
            basicTitle: "User",
            pmRole: "program-manager",
            fmRole: "follow-up-manager",
            smRole: "sales-manager",
            basicUser: "basic-user",
            liveDealer: "LIVE",
            atlasLabel: "Manage in Atlas",
            managerFields: [
                "actions",
                {
                    key: "smsVerified",
                    label: "SMS Verified"
                },
                {
                    key: "firstName",
                    label: "Name"
                },
                {
                    key: "phoneNumber",
                    label: "Phone"
                },
                "email",
                "jobTitle",
                "stockType",
                {
                    key: "internalSms",
                    label: "SMS Enabled"
                },
                "spanishSpeaking",
                "crmId"
            ],
            basicUserFields: [
                "actions",
                {
                    key: "firstName",
                    label: "Name"
                },
                {
                    key: "phoneNumber",
                    label: "Phone"
                },
                "email",
                "jobTitle",
                "permissions",
                {
                    key: "dealerUserAtlasData.createdDate",
                    label: "Created Date"
                },
                {
                    key: "dealerUserAtlasData.latestLoginDate",
                    label: "Last Login Date"
                },
                {
                    key: "dealerUserAtlasData.totalLoginsByDealerUser",
                    label: "Total Logins"
                }
            ]
        };
    },
    computed: {
        dealerId: get("dealer/selectedDealer@id"),
        dealerStatus: get("dealer/selectedDealer@dealerStatus"),
        dealerName: get("dealer/selectedDealer@name"),
        followUpManagers: get("dealer/followUpManagers"),
        programManagers: get("dealer/programManagers"),
        salesManagers: get("dealer/salesManagers"),
        basicUsers: get("dealer/basicUsers"),
        atlasUrl: get("dealer/selectedDealer@atlasUrl"),
        isLastPmForLiveDealer() {
            return this.isPmTable && this.isLiveDealer && this.isLastPm;
        },
        isLiveDealer() {
            return this.dealerStatus === this.liveDealer;
        },
        isLastPm() {
            return this.programManagers.length === 1;
        },
        isPmTable() {
            return this.selectedUser.type === this.pmRole;
        },
        isBasicUserTable() {
            return this.selectedUser.type === this.basicUser;
        },
        isBasicUserAlsoLastPm() {
            if (this.isBasicUserTable && this.isLastPm) {
                const basicUserId = this.selectedUser.id;
                const lastPmId = this.programManagers[0].id;

                return basicUserId === lastPmId;
            }
        }
    },
    methods: {
        fetchProgramManagers: call("dealer/fetchProgramManagers"),
        fetchFollowUpManagers: call("dealer/fetchFollowUpManagers"),
        fetchSalesManagers: call("dealer/fetchSalesManagers"),
        fetchBasicUsers: call("dealer/fetchBasicUsers"),
        sendTestSms: call("dealer/sendTestSms"),
        fetchAllUsers() {
            this.fetchProgramManagers();
            this.fetchFollowUpManagers();
            this.fetchSalesManagers();
            this.fetchBasicUsers();
        },
        fireModal(user, type) {
            this.selectedUser = {
                id: user.id,
                name: user.firstName + " " + user.lastName,
                type: type
            };

            if (this.isLastPmForLiveDealer || this.isBasicUserAlsoLastPm) {
                this.$refs["last-pm-modal"].show();
                return;
            }

            this.$refs["remove-user-modal"].show();
        },
        confirmRemoval() {
            const userType = this.selectedUser.type;
            const userId = this.selectedUser.id;

            if (userType === this.pmRole) {
                dispatch("dealer/removeProgramManagerAssociation", userId)
                    .then(() => {
                        this.fetchProgramManagers();
                        this.$toastr.s(
                            `${this.selectedUser.name} has been removed`
                        );
                    })
                    .catch(error => {
                        this.$toastr.e(
                            `Error removing ${this.selectedUser.name}`
                        );
                        console.error(error);
                    });
            } else if (userType === this.fmRole) {
                dispatch("dealer/removeFollowUpManagerAssociation", userId)
                    .then(() => {
                        this.fetchFollowUpManagers();
                        this.$toastr.s(
                            `${this.selectedUser.name} has been removed`
                        );
                    })
                    .catch(error => {
                        this.$toastr.e(
                            `Error removing ${this.selectedUser.name}`
                        );
                        console.error(error);
                    });
            } else if (userType === this.smRole) {
                dispatch("dealer/removeSalesManagerAssociation", userId)
                    .then(() => {
                        this.fetchSalesManagers();
                        this.$toastr.s(
                            `${this.selectedUser.name} has been removed`
                        );
                    })
                    .catch(error => {
                        this.$toastr.e(
                            `Error removing ${this.selectedUser.name}`
                        );
                        console.error(error);
                    });
            } else if (userType === this.basicUser) {
                dispatch("dealer/removeBasicUserAssociation", userId)
                    .then(() => {
                        this.fetchProgramManagers();
                        this.fetchFollowUpManagers();
                        this.fetchBasicUsers();
                        this.$toastr.s(
                            `${this.selectedUser.name} has been removed`
                        );
                    })
                    .catch(error => {
                        this.$toastr.e(
                            `Error removing ${this.selectedUser.name}`
                        );
                        console.error(error);
                    });
            }

            this.$refs["remove-user-modal"].hide();
        },
        noRemove() {
            this.$toastr.i(`${this.selectedUser.name} not removed`);
            this.$refs["remove-user-modal"].hide();
        },
        doSendTestSms(user) {
            this.sendTestSms(user.id)
                .then(response => {
                    const status = _.get(response.data, "type", "");
                    this.$bvToast.toast(
                        `SMS sent to ${user.firstName} ${user.lastName}`,
                        {
                            title: `${_.capitalize(status)}!`,
                            variant: "success"
                        }
                    );
                })
                .catch(error => {
                    this.$bvToast.toast(`Error sending Test SMS\n : ${error}`, {
                        variant: "danger"
                    });
                });
        }
    },
    mounted() {
        this.fetchAllUsers();
    }
};
</script>
<style lang="scss">
#remove-user-modal,
#last-pm-modal {
    .modal-title {
        font-size: 20px;
    }
}
</style>
