<template>
    <div role="group" class="dropdown" aria-label="Button group with nested dropdown">
        <dealer-reindexer-wrapper :tooltip="tooltip">
            <b-button
                size="sm"
                block
                variant="outline-secondary"
                aria-expanded="false"
                class="mb-1"
            >
                <i aria-hidden="true" class="fa fa-upload" :class="{'fa-spin': isLoading}"/>
                Publish Inventory
            </b-button>
        </dealer-reindexer-wrapper>
    </div>
</template>

<script>
import {get} from 'vuex-pathify';
import DealerReindexerWrapper from 'Components/DealerReindexerWrapper';

export default {
    name: 'PublishInventory',

    components: {DealerReindexerWrapper},

    computed: {
        isLoading: get('dealer/dealerReindex@loader/isLoading')
    },

    data() {
        return {
            tooltip: 'Use to republish dealer\'s inventory to the main website WWW.CARSAVER.COM'
        };
    }

};
</script>
