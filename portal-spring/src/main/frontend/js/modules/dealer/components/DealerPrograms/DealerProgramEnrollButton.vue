<template>
    <span>
        <b-button
            v-if="$acl.hasAuthority('edit:dealer')"
            v-b-modal.program-enroll-modal
            size="xs"
            variant="light"
        >
            <i aria-hidden="true" class="fa fa-plus"/>
        </b-button>

        <b-modal
            id="program-enroll-modal"
            ref="program-enroll-modal"
            size="xs"
            hide-header
            hide-footer
            no-close-on-backdrop
        >
            <dealer-program-subscription-form :dealer-id="dealerId"
                                              @cancel="hideModal"
                                              @done="hideModal"
            />

        </b-modal>
    </span>
</template>

<script>
import DealerProgramSubscriptionForm from './DealerProgramSubscriptionForm';

export default {
    name: 'DealerProgramEnrollButton',
    components: {DealerProgramSubscriptionForm},
    props: {
        dealerId: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            title: 'Program Enrollment'
        };
    },
    methods: {
        hideModal() {
            this.$refs['program-enroll-modal'].hide();
            this.$emit('done');
        }
    }
};
</script>
