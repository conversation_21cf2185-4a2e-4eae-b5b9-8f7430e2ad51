<template>
    <b-form
        inline
        class="dealer-search-form fs-ignore-formabandon"
        @submit.prevent="doSubmit"
    >
        <b-form-group>
            <b-input
                v-model="query"
                placeholder="Name / City / DealerId"
                data-cs="dealer-name"
            />
        </b-form-group>
        <b-form-group>
            <b-input
                v-model="zipCode"
                placeholder="Zip Code"
                data-cs="zipcode"
            />
        </b-form-group>
        <b-form-group>
            <b-input
                v-model="distance"
                placeholder="Distance (miles)"
                type="number"
                min="0"
                data-cs="distance"
                autocomplete="off"
                @keypress="isNumber($event)"
            />
        </b-form-group>
        <b-form-group>
            <b-button-toolbar>
                <date-range-picker2
                    v-model="lastContactDate"
                    :masks="{ input: 'YYYY-MM-DD' }"
                    :max-date="new Date()"
                    placeholder="Last Contact Date"
                />
                <b-button-group size="sm">
                    <b-dropdown
                        v-model="lastContactDatePeriod"
                        right
                        :text="lastContactDatePeriod"
                        variant="white"
                        class="last-contact-dropdown"
                    >
                        <b-dropdown-item
                            :active="true"
                            @click="handleLastContact(7)"
                        >7-14 days ago
                        </b-dropdown-item
                        >
                        <b-dropdown-item @click="handleLastContact(14)"
                        >14-28 days ago
                        </b-dropdown-item
                        >
                        <b-dropdown-item @click="handleLastContact(28)"
                        >over 28 days ago
                        </b-dropdown-item
                        >
                    </b-dropdown>
                </b-button-group>
            </b-button-toolbar>
        </b-form-group>
        <b-button id="search-button" type="submit" variant="primary" size="sm">
            Search
        </b-button>
        <b-button
            id="clear-button"
            variant="info"
            size="sm"
            @click="clearFilters"
        >
            Reset
        </b-button>
    </b-form>
</template>

<script>
import {call, sync} from "vuex-pathify";
import DateRangePicker2 from "Components/DateRangePicker2/index";
import moment from "moment";
import timezone from "moment-timezone";

export default {
    name: "DealerSearchForm",

    components: {DateRangePicker2},

    data() {
        return {
            lastContactDatePeriod: "Last Contact Periods"
        };
    },

    computed: {
        query: sync("dealerSearch/filters@query"),
        zipCode: sync("dealerSearch/filters@zipCode"),
        distance: sync("dealerSearch/filters@distance"),
        lastContactDate: sync("dealerSearch/filters@lastContactDate")
    },

    watch: {
        lastContactDate() {
            if (this.lastContactDate === null) {
                this.lastContactDatePeriod = "Last Contact Periods";
            }
        }
    },

    methods: {
        doSubmit: call("dealerSearch/doSearch"),
        clearFilters: call("dealerSearch/clearFilters"),
        handleLastContact(days) {
            if (this.lastContactDate === undefined) {
                this.lastContactDate = {
                    start: null,
                    end: null,
                    timezone: timezone.tz.guess()
                };
            }
            switch (days) {
                case 7:
                    this.lastContactDatePeriod = "7-14 days ago";
                    this.lastContactDate = {
                        start: moment()
                            .subtract(14, "days")
                            .format("YYYY-MM-DD"),
                        end: moment()
                            .subtract(7, "days")
                            .format("YYYY-MM-DD"),
                        timeZone: timezone.tz.guess()
                    };
                    break;
                case 14:
                    this.lastContactDate = {
                        start: moment()
                            .subtract(28, "days")
                            .format("YYYY-MM-DD"),
                        end: moment()
                            .subtract(14, "days")
                            .format("YYYY-MM-DD"),
                        timeZone: timezone.tz.guess()
                    };
                    break;
                case 28:
                    this.lastContactDate = {
                        start: moment()
                            .subtract(1, "years")
                            .format("YYYY-MM-DD"),
                        end: moment()
                            .subtract(28, "days")
                            .format("YYYY-MM-DD"),
                        timeZone: timezone.tz.guess()
                    };
                    break;
                default:
                    return;
            }
        },
        isNumber: function (evt) {
            let charCode = (evt.which) ? evt.which : evt.keyCode;

            if ((charCode > 31 && (charCode < 48 || charCode > 57)) && charCode !== 46) {
                evt.preventDefault();

            } else {
                return true;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.dealer-search-form {
    padding-left: 0;
}

#clear-button,
#search-button {
    margin: 5px 0 5px 5px;
}

@media screen and (max-width: 765px) {
    .dealer-search-form {
        padding: 0 !important;
    }
    #clear-button,
    #search-button {
        margin: 5px 0 0 0;
        width: 100%;
    }
}

.last-contact-dropdown {
    background-color: #fff;
    font-size: 14px;
}
</style>
