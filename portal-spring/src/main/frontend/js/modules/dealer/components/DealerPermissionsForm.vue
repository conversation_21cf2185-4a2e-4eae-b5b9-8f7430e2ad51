<template>
    <div>
        <b-card header="Dealer User Info">
            <b-alert
                v-if="dealerUser.associated && !isEditPermissions"
                show
                variant="danger"
            >
                <strong
                >{{ dealerUser.firstName }} {{ dealerUser.lastName }} |
                    {{ dealerUser.email }}</strong
                >
                is already associated to this dealer. You can make updates to
                the roles and permissions if needed.
            </b-alert>
            <b-alert
                v-else-if="dealerUser.associated && isEditPermissions"
                show
                variant="warning"
            >
                Editing permissions and roles for
                <strong
                >{{ dealerUser.firstName }}
                    {{ dealerUser.lastName }}</strong
                >
            </b-alert>
            <b-alert v-else show variant="success">
                Associate User: {{ dealerUser.name }} | {{ dealerUser.email }}
            </b-alert>
            <b-form novalidate @submit.prevent="associateUser()">
                <div v-for="option in managerStockTypeOptions" :key="option.label">
                    <b-form-group :label="option.labels.active">
                        <b-form-checkbox v-model="form[option.id]" switch/>
                    </b-form-group>
                    <b-form-group
                        v-if="option.active"
                        :label="option.labels.stockSelect"
                    >
                        <b-form-select
                            v-model="form[option.stockTypeKey]"
                            :name="option.name"
                        >
                            <option v-for="stockType in stockTypes" :value="stockType.value" :key="stockType.value">
                                {{ stockType.text }}
                            </option>
                        </b-form-select>
                        <small>
                            (This will limit the leads to this stock type for this user)
                        </small>
                    </b-form-group>
                </div>
                <b-form-group label="Contact Type">
                    <b-form-select v-model="form.contactType">
                        <option :value="null">
                            None
                        </option>
                        <option
                            v-for="t in availableContactTypes"
                            :key="t.value"
                        >
                            {{ t.text }}
                        </option>
                    </b-form-select>
                </b-form-group>
                <b-form-group>
                    <b-form-checkbox-group
                        v-model="form.permissions"
                        name="permissions"
                        stacked
                    >
                        <b-form-checkbox
                            v-for="p in availablePermissions"
                            :key="p.id"
                            :value="p.id"
                        >
                            {{ p.description }}
                        </b-form-checkbox>
                    </b-form-checkbox-group>
                </b-form-group>
                <b-row class="justify-content-center mt-3">
                    <b-col lg="12" class="text-center">
                        <div>
                            <b-button
                                size="md"
                                type="submit"
                                variant="primary"
                                class="mr-2"
                                :disabled="disabled"
                                @submit="associateUser()"
                            >
                                {{ disabled ? "Submitting..." : "Save" }}
                            </b-button>
                            <b-button
                                size="md"
                                variant="danger"
                                @click="onCancel()"
                            >
                                Cancel
                            </b-button>
                        </div>
                    </b-col>
                </b-row>
            </b-form>
        </b-card>
    </div>
</template>

<script>
import api from "@/api";
import get from "lodash/get";
import map from "lodash/map";
import isEmpty from "lodash/isEmpty";
import isNil from "lodash/isNil";

export default {
    name: "DealerPermissionsForm",
    props: {
        dealerUser: {
            type: Object,
            required: true
        },
        dealerId: {
            type: String,
            required: true
        },
        isEditPermissions: {
            type: Boolean,
            required: true
        }
    },
    data() {
        return {
            availablePermissions: [],
            availableContactTypes: [],
            disabled: false,
            form: {
                programManager: false,
                followupManager: false,
                salesManager: false,
                pmStockType: null,
                fmStockType: null,
                smStockType: null,
                permissions: [],
                contactType: null
            },
            stockTypes: {
                newAndUsed: {
                    value: "Unknown",
                    text: "NEW & USED"
                },
                new: {
                    value: "New",
                    text: "NEW"
                },
                used: {
                    value: "Used",
                    text: "USED"
                }
            }
        };
    },
    computed: {
        managerStockTypeOptions() {
            return [
                {
                    id: "programManager",
                    labels: {
                        stockSelect: "Program Manager Stock Type",
                        active: "Program Manager"
                    },
                    name: "programManagerStockType",
                    active: this.form.programManager,
                    stockTypes: this.stockTypes,
                    stockTypeKey: "pmStockType"
                },
                {
                    id: "followupManager",
                    labels: {
                        stockSelect: "Followup Manager Stock Type",
                        active: "Followup Manager"
                    },
                    name: "followupManagerStockType",
                    active: this.form.followupManager,
                    stockTypes: this.stockTypes,
                    stockTypeKey: "fmStockType"
                },
                {
                    id: "salesManager",
                    labels: {
                        stockSelect: "Sales Manager Stock Type",
                        active: "Sales Manager"
                    },
                    name: "salesManagerStockType",
                    active: this.form.salesManager,
                    stockTypes: this.stockTypes,
                    stockTypeKey: "smStockType"
                },
            ]
        }
    },
    mounted() {
        this.fetchPermissions();

        this.form = {
            ...this.form,
            ...this.dealerUser
        };

        if (!isEmpty(this.dealerUser.permissions)) {
            this.form.permissions = map(this.dealerUser.permissions, "id");
        }
    },
    methods: {
        handleResponseData(data) {
            if (isNil(data)) {
                return;
            }

            this.availablePermissions = data.permissions;
            this.availableContactTypes = map(data.contactTypes, t => {
                return {
                    text: t.valueOf(),
                    value: t.valueOf()
                };
            });
        },
        fetchPermissions() {
            api.get("/business-users/dealer/permissions")
                .then(response => {
                    this.handleResponseData(get(response, "data", null));
                })
                .catch(error => {
                    console.error(error);
                });
        },
        associateUser() {
            api.post(
                `/dealer/${this.dealerId}/users/${this.dealerUser.id}/associate`,
                this.form
            )
                .then(response => {
                    this.$emit("completed");
                })
                .catch(error => {
                    console.error(error);
                    this.$toastr.e("Error associating user!");
                });
        },
        onCancel() {
            this.$emit("cancel");
        }
    }
};
</script>
