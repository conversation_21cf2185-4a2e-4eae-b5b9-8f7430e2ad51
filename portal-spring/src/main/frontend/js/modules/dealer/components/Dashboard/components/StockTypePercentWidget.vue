<template>
    <i-box class="stock-type-percent" :title="title" :description="description"
           :alert-message="alertMessage" :collapsible="false" :loading="isLoading"
    >
        <div ref="stockTypeChart"/>

        <div class="d-flex justify-content-center">
            <span class="pr-3">New: {{ data.newCount }}</span>
            <span>Used: {{ data.usedCount }}</span>
        </div>

        <div class="percent-number text-center"
             :class="{'red': isRed(percentAsInt), 'yellow': isYellow(percentAsInt), 'green': isGreen(percentAsInt)}">
            {{ data.percent | numeral('0%') }} Overall
        </div>
    </i-box>
</template>

<style lang="scss">
.stock-type-percent {
    .percent-number {
        font-size: 18px;

        &.red {
            color: $red;
        }

        &.yellow {
            color: $yellow;
        }

        &.green {
            color: $green;
        }
    }

    .ibox-title {
        padding-right: 15px !important;
    }

    .ibox-content {
        min-height: 234px;
    }
}
</style>

<script>
import _ from 'lodash';
import IBox from 'Components/IBox';
import {call, get} from 'vuex-pathify';
import api from '@/api';
import moment from 'moment';

export default {
    components: {IBox},

    props: {
        name: {
            type: String,
            required: true
        },
        title: {
            type: String,
            required: true
        },
        colorConfig: {
            type: Object,
            required: false
        },
        drillDownEnabled: {
            type: Boolean,
            required: false,
            default: true
        },
        description: {
            type: String,
            required: false,
            default: null
        },
        alertMessage: {
            type: String,
            required: false,
            default: null
        }
    },

    data() {
        return {
            isLoading: true,
            data: {
                newCount: 0,
                usedCount: 0,
                percent: 0,
                totalNew: 0,
                totalUsed: 0
            },
            defaultColorConfig: {
                green: 90,
                yellow: 75
            }
        };
    },

    computed: {
        startDate: get('dashboard/startDate'),
        endDate: get('dashboard/endDate'),
        dateRangeStr: get('dashboard/dateRangeStr'),
        dealerIds: get('dashboard/dealerIds'),

        colorConfigInUse() {
            return _.merge(this.defaultColorConfig, this.colorConfig);
        },

        percentAsInt() {
            return (this.data.percent * 100);
        },

        newPercentAsInt() {
            if (this.data.totalNew === 0) return 0;
            return (this.data.newCount / this.data.totalNew) * 100;
        },

        usedPercentAsInt() {
            if (this.data.totalUsed === 0) return 0;
            return (this.data.usedCount / this.data.totalUsed) * 100;
        },

        columns() {
            const headers = ['x', 'New', 'Used'];
            const values = ['value', this.newPercentAsInt, this.usedPercentAsInt];

            return [headers, values];
        },

        newColor() {
            if (this.isRed(this.newPercentAsInt)) {
                return '#dc3545';
            } else if (this.isYellow(this.newPercentAsInt)) {
                return '#ffb237';
            }

            return '#60B044';
        },

        usedColor() {
            if (this.isRed(this.usedPercentAsInt)) {
                return '#dc3545';
            } else if (this.isYellow(this.usedPercentAsInt)) {
                return '#ffb237';
            }

            return '#60B044';
        }
    },

    watch: {
        dateRangeStr() {
            this.loadData();
        },
        dealerIds() {
            this.loadData();
        }
    },

    created() {
        this.loadData();
    },

    methods: {
        incrementLoaderCount: call('dashboard/incrementLoaderCount'),
        decrementLoaderCount: call('dashboard/decrementLoaderCount'),

        isRed(value) {
            return value < this.colorConfigInUse.yellow &&
                value < this.colorConfigInUse.green;
        },

        isYellow(value) {
            return value < this.colorConfigInUse.green &&
                value >= this.colorConfigInUse.yellow;
        },

        isGreen(value) {
            return value >= this.colorConfigInUse.green;
        },

        goToLeadsList(stockType, status) {
            const timeZone = moment.tz.guess();
            return document.location = `/lead?statuses=${status}&dealerIds=${this.dealerId}&stockTypes=${stockType}&createdDate.start=${this.startDate}&createdDate.end=${this.endDate}&createdDate.timeZone=${timeZone}&reset=1&sort=createdDate,desc&page=1`;
        },

        updateChart() {
            let clickAction;
            if (this.drillDownEnabled === true) {
                clickAction = (data) => {
                    if (data.index === 0) {
                        if (this.name === 'contacted') {
                            this.goToLeadsList('NEW', 'CONTACTED');
                        } else if (this.name === 'claimed') {
                            this.goToLeadsList('NEW', 'CLAIMED');
                        }
                    } else if (data.index === 1) {
                        if (this.name === 'contacted') {
                            this.goToLeadsList('USED', 'CONTACTED');
                        } else if (this.name === 'claimed') {
                            this.goToLeadsList('USED', 'CLAIMED');
                        }
                    }
                };
            }

            c3.generate({
                bindto: this.$refs.stockTypeChart,
                size: {
                    height: 150
                },
                data: {
                    x: 'x',
                    columns: this.columns,
                    type: 'bar',
                    color: (color, data) => {
                        const component = this;
                        if (data.index === 0) {
                            return component.newColor;
                        } else if (data.index === 1) {
                            return component.usedColor;
                        }

                        return color;
                    },
                    onclick: clickAction,
                    labels: {
                        format: function (value) {
                            return Math.round(value) + '%';
                        }
                    }
                },
                tooltip: {
                    show: false
                },
                axis: {
                    rotated: true,
                    x: {
                        type: 'category'
                    },
                    y: {
                        show: false,
                        max: 100
                    }
                },
                legend: {
                    show: false
                }
            });
        },

        loadData() {
            if (_.isNil(this.startDate) || _.isNil(this.endDate)) {
                return;
            }

            this.incrementLoaderCount();
            this.isLoading = true;

            api.post(`/dealers/dashboard/${this.name}`,
                {
                    dealerIds: this.dealerIds,
                    startDate: this.startDate,
                    endDate: this.endDate
                })
                .then(response => {
                    this.data = response.data;

                    this.isLoading = false;
                    this.decrementLoaderCount();
                })
                .then(() => {
                    this.updateChart();
                })
                .catch(error => {
                    console.log(error);

                    this.isLoading = false;
                    this.decrementLoaderCount();
                });
        }
    }
};
</script>
