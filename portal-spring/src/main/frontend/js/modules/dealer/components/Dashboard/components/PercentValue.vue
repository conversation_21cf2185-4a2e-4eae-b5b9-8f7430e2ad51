<template>
    <div class="percent-value" :class="{'red': isRed, 'yellow': isYellow, 'green': isGreen}">
        {{ data.percent | numeral('0%') }}
        <b-link v-if="description" v-b-tooltip.hover :title="description">
            <i aria-hidden="true" class="fas fa-info-circle"/>
        </b-link>
    </div>
</template>

<style lang="scss" scoped>
.percent-value {
    font-size: 16px;

    &.red {
        color: $red;
    }

    &.yellow {
        color: $yellow;
    }

    &.green {
        color: $green;
    }
}
</style>

<script>
import _ from 'lodash';
import {call, get} from 'vuex-pathify';
import api from '@/api';

export default {
    props: {
        name: {
            type: String,
            required: true
        },
        colorConfig: {
            type: Object,
            required: false
        },
        linkEnabled: {
            type: Boolean,
            required: false,
            default: true
        },
        description: {
            type: String,
            required: false,
            default: null
        },
        startDate: {
            type: String,
            required: true
        },
        endDate: {
            type: String,
            required: true
        }
    },

    data() {
        return {
            isLoading: true,
            data: {
                percent: 0
            },
            defaultColorConfig: {
                green: 90,
                yellow: 75
            }
        };
    },

    computed: {
        dealerIds: get('dashboard/dealerIds'),

        colorConfigInUse() {
            return _.merge(this.defaultColorConfig, this.colorConfig);
        },

        percentAsInt() {
            return (this.percent * 100);
        },

        isRed() {
            return this.percentAsInt < this.colorConfigInUse.yellow &&
                this.percentAsInt < this.colorConfigInUse.green;
        },

        isYellow() {
            return this.percentAsInt < this.colorConfigInUse.green &&
                this.percentAsInt >= this.colorConfigInUse.yellow;
        },

        isGreen() {
            return this.percentAsInt >= this.colorConfigInUse.green;
        }
    },

    watch: {
        dealerIds() {
            this.loadData();
        }
    },

    created() {
        this.loadData();
    },

    methods: {
        incrementLoaderCount: call('dashboard/incrementLoaderCount'),
        decrementLoaderCount: call('dashboard/decrementLoaderCount'),

        loadData() {
            if (_.isNil(this.startDate) || _.isNil(this.endDate)) {
                return;
            }

            this.incrementLoaderCount();
            this.isLoading = true;

            api.post(`/dealers/dashboard/${this.name}`,
                {
                    dealerIds: this.dealerIds,
                    startDate: this.startDate,
                    endDate: this.endDate
                })
                .then(response => {
                    this.data = response.data;

                    this.isLoading = false;
                    this.decrementLoaderCount();
                })
                .catch(error => {
                    console.log(error);

                    this.isLoading = false;
                    this.decrementLoaderCount();
                });
        }
    }
};
</script>
<style lang="scss" scoped>
.tooltip {
    z-index: 2002 !important;
}
</style>
