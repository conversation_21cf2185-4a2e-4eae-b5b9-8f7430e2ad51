<template>
    <fieldset>
        <legend>Email</legend>
        <div class="row">
            <div class="col-lg-8">
                <b-row>
                    <b-col cols="6">
                        <b-form-group label="Email">
                            <b-form-input
                                v-model="$v.email.$model"
                                name="email"
                                :state="
                                    $v.email.$dirty ? !$v.email.$error : null
                                "
                                :disabled="verifyEmail"
                                aria-describedby="email-live-feedback"
                                placeholder="Enter email"
                            />
                            <b-form-invalid-feedback id="email-live-feedback">
                                This is a required field and must be a valid
                                email address.
                            </b-form-invalid-feedback>
                            <b-alert
                                :show="!verifyEmail"
                                variant="primary"
                                class="mt-3"
                            >
                                If customer exist and has a dealer link it will
                                skip the "Personal Info" step.
                            </b-alert>
                        </b-form-group>
                    </b-col>
                    <b-col v-if="verifyEmail" cols="6">
                        <b-form-group label="Email Code">
                            <b-form-input
                                v-model="$v.emailCode.$model"
                                name="emailCode"
                                :state="
                                    $v.emailCode.$dirty
                                        ? !$v.emailCode.$error
                                        : null
                                "
                                aria-describedby="emailCode-live-feedback"
                                placeholder="Enter code from email"
                            />
                            <b-form-invalid-feedback
                                id="emailCode-live-feedback"
                            >
                                Invalid email verification code.
                            </b-form-invalid-feedback>
                        </b-form-group>
                    </b-col>
                </b-row>
                <b-row v-if="verifyEmail">
                    <b-col cols="6">
                        <p>
                            Email account already exists but customer has no
                            dealer link. Please have the customer check their
                            email and enter the code from their email.
                        </p>
                        <p>
                            <small
                            >Please have the customer check their junk
                                folder if the email cannot be found.</small
                            >
                        </p>
                        <p>
                            <b-button
                                size="small"
                                variant="info"
                                @click="resendValidationCode"
                            >
                                Resend Verification Email
                            </b-button>
                        </p>
                    </b-col>
                </b-row>
            </div>
            <div class="col-lg-4">
                <div class="text-center">
                    <div style="margin-top: 20px">
                        <i
                            aria-hidden="true"
                            class="fas fa-at"
                            style="font-size: 180px;color: #e5e5e5 "
                        />
                    </div>
                </div>
            </div>
        </div>
    </fieldset>
</template>

<script>
import {validationMixin} from "vuelidate";
import {helpers, required} from "vuelidate/lib/validators";
import _ from "lodash";
import api from "@/api";

// using regex from Vuelidate source code: https://github.com/vuelidate/vuelidate/blob/master/src/validators/email.js
// updated the regex to allow capital letters after the @ sign
const validEmail = helpers.regex(
    "validEmail",
    /^(?:[A-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[A-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9]{2,}(?:[a-zA-Z0-9-]*[a-zA-Z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-zA-Z0-9-]*[a-zA-Z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])$/
);

export default {
    mixins: [validationMixin],

    props: {
        verifyEmail: {
            type: Boolean,
            required: true
        }
    },

    data() {
        return {
            email: "",
            emailCode: ""
        };
    },

    validations: {
        email: {
            required,
            validEmail
        },
        emailCode: {
            required,
            isValid(emailCode) {
                if (_.isNil(emailCode) || emailCode === "") {
                    return true;
                }

                if (emailCode.length < 4) {
                    return false;
                }

                return api
                    .get("/users/validate-email-code", {
                        email: this.email,
                        code: this.emailCode
                    })
                    .then(response => {
                        return true;
                    })
                    .catch(response => {
                        return false;
                    });
            }
        },
        step1: ["email"],
        step2: ["emailCode"]
    },

    methods: {
        resendValidationCode() {
            api.post("/users/validate-email-code", {email: this.email}).then(
                () => {
                    this.$toastr.s("Verification email has been resent.");
                }
            );
        },
        validate() {
            if (this.verifyEmail) {
                this.$v.step2.$touch();
                const isValid = !this.$v.step2.$invalid;
                this.$emit("on-validate", this.$data, isValid);
                return isValid;
            }

            this.$v.step1.$touch();
            const isValid = !this.$v.step1.$invalid;
            this.$emit("on-validate", this.$data, isValid);
            return isValid;
        }
    }
};
</script>
