<template>
    <search-page :store="store" :search-index="searchIndex">
        <dealer-search-form slot="searchForm"/>
        <dealer-facets slot="facets"/>
    </search-page>
</template>

<script>
import DealerSearchForm from "./DealerSearchForm";
import DealerFacets from "./DealerFacets";
import SearchPage from "Components/SearchPage";

export default {
    name: "DealerSearch",
    components: {SearchPage, DealerFacets, DealerSearchForm},

    data() {
        return {
            store: "dealerSearch",
            searchIndex: "dealers"
        };
    }
};
</script>
