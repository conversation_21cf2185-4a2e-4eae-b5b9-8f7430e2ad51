import Vue from 'vue';
import 'es6-promise/auto';
import CarSaverPlugin from '@/lib/CarSaverPlugin';
import Vue2Filters from 'vue2-filters';
import Toastr from 'vue-toastr';
import UniqueId from 'vue-unique-id';
import {extend, ValidationObserver, ValidationProvider} from 'vee-validate';
import {length, max, required} from 'vee-validate/dist/rules';

import store from './store';
import SearchPage from '../../components/SearchPage';
import DealerGroupSearch from './components/DealerGroupSearch';
import DealerGroupList from './components/DealerGroupSearch/DealerGroupList';
import DealerList from '../dealer/components/DealerSearch/DealerList';
import DealerGroupForm from './components/DealerGroupForm';
import DealerGroupDetails from './components/DealerGroupDetails';
import DealerGroupSharedSubset from './components/DealerGroupSharedSubset/DealerGroupSharedSubset';
import VPopover from 'v-tooltip'
import '../../directives';
import '../../filters';

Vue.use(CarSaverPlugin);
Vue.use(require('vue-moment'));
Vue.use(Vue2Filters);
Vue.use(Toastr);
Vue.use(UniqueId);

Vue.use(VPopover);

Vue.component('ValidationProvider', ValidationProvider);
Vue.component('ValidationObserver', ValidationObserver);
// Adding a rules.
extend('required', required);
extend('length', length);
extend('max', max);

new Vue({
    el: '#wrapper',
    store,
    components: {
        SearchPage,
        DealerGroupSearch,
        DealerGroupList,
        DealerList,
        DealerGroupForm,
        DealerGroupDetails,
        DealerGroupSharedSubset
    }
});
