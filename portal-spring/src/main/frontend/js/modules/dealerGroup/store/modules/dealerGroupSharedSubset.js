import {make} from 'vuex-pathify';
import {cloneDeep, findIndex} from 'lodash';
import Vue from "vue";

const DEALER_SUBSET = 'dealerSubset';

const state = {
    availableDealers: [],
    subsetData: [],
};

const mutations = {
    ...make.mutations(state),

    initializeSubsetData(state, value) {
        console.log('Store: initializeSubsetData(): ' + value);
        state.subsetData = cloneDeep(value.initialData);
    },

// TODO: rename method names further to be more clear: addSubsetData sb addSubset, addDealer sb addDealerToSubset
    newSubset(state, value) {
        console.log('Store: newSubset(): ' + value.newSubset);
        state.subsetData.push(value.newSubset);
    },

    removeSubsetData(state, value) {
        const dealerGroupIndex = findIndex(state.subsetData, function (o) {
            return o.dealerSubsetName === value.groupName
        });
        console.log('Store: removeSubsetData(): "' + value.groupName + '" subsetData: ' + state.subsetData[dealerGroupIndex][DEALER_SUBSET]);

        Vue.delete(state.subsetData, dealerGroupIndex);
    },

    addDealer(state, value) {
        console.log('Store: addDealer(): ');
        const dealerIndex = findIndex(state.subsetData[value.groupName], function (o) {
            return o.id === value.dealerData['id']
        });
        const dealerGroupIndex = findIndex(state.subsetData, function (o) {
            return o.dealerSubsetName === value.groupName
        });
        const doesDealerExist = dealerIndex !== -1;

        if (!doesDealerExist) {
            state.subsetData[dealerGroupIndex][DEALER_SUBSET].push(value.dealerData);
        } else {
            console.trace('Dealer already exists: ', value);
        }
    },

    removeDealer(state, value) {
        const dealerGroupIndex = findIndex(state.subsetData, function (o) {
            return o.dealerSubsetName === value.groupName
        });
        console.log('Store: removeDealer(): ', state.subsetData[dealerGroupIndex][value.groupName]);
        const dealers = state.subsetData[dealerGroupIndex][DEALER_SUBSET].slice();

        const newDealersList = dealers.filter(dealer => dealer.id !== value.dealerId);
        Vue.set(state.subsetData[dealerGroupIndex], DEALER_SUBSET, newDealersList);
    },
};

const getters = {
    ...make.getters(state)
};

export default {
    namespaced: true,
    state,
    getters,
    mutations
};
