<template>
    <search-page :store="store" :search-index="searchIndex" :exportable="false">
        <dealer-group-search-form slot="searchForm"/>
        <dealer-group-facets slot="facets"/>
    </search-page>
</template>

<script>
import SearchPage from "../../../../components/SearchPage";
import DealerGroupSearchForm from "./DealerGroupSearchForm";
import DealerGroupFacets from "./DealerGroupFacets";

export default {
    name: "DealerGroupSearch",
    components: {DealerGroupFacets, DealerGroupSearchForm, SearchPage},

    data() {
        return {
            store: "dealerGroupSearch",
            searchIndex: "dealer-group"
        };
    }
};
</script>
