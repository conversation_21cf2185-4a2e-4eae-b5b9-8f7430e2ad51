<template>
    <div v-if="isLoading">
        <table-content-loader/>
    </div>

    <div v-else>
        <div class="ibox">
            <div class="ibox-content">
                <div class="result-count">
                    <pagination
                        :show-pager="false"
                        :page-metadata="pageMetadata"
                        :page="page"
                        @page-changed="pageChanged"
                    />
                </div>

                <b-table
                    responsive
                    striped
                    hover
                    bordered
                    :fields="fields"
                    :items="searchResults"
                    :no-local-sorting="true"
                    :sort-by.sync="sortBy"
                    :sort-desc.sync="sortDesc"
                    :no-sort-reset="true"
                    @sort-changed="sortChanged"
                >
                    <template v-slot:cell(name)="row">
                        <b-link :href="`/dealerGroup/${row.item.id}`">
                            {{ row.item.name }}
                        </b-link>
                    </template>
                    <template v-slot:cell(address)="row">
                        <div class="text-nowrap">
                            {{ row.item.address.street }}
                        </div>
                        <div class="text-nowrap">
                            {{
                                row.item.address.city
                            }}<span
                            v-if="
                                    row.item.address.city &&
                                        (row.item.address.stateCode ||
                                            row.item.address.zipCode)
                                "
                        >,</span
                        >
                            {{ row.item.address.stateCode }}
                            {{ row.item.address.zipCode }}
                        </div>
                    </template>
                    <template v-slot:cell(actions)="row">
                        <b-button-group>
                            <b-button
                                variant="white"
                                size="xs"
                                :href="`/dealerGroup/${row.item.id}`"
                            >
                                View
                            </b-button>
                            <b-button
                                v-if="$acl.hasAuthority('edit:dealerGroup')"
                                variant="white"
                                size="xs"
                                :href="`/dealerGroup/${row.item.id}?form`"
                            >
                                Edit
                            </b-button>
                        </b-button-group>
                    </template>
                </b-table>

                <div class="pagination-wrapper">
                    <pagination
                        :show-totals="false"
                        :page-metadata="pageMetadata"
                        :page="page"
                        @page-changed="pageChanged"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<style>
.result-count {
    padding: 10px 0;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>

<script>
import {dispatch, get, sync} from "vuex-pathify";
import Pagination from "Components/Pagination";
import TableContentLoader from "Components/TableContentLoader";
import searchPageMixin from "@/mixins/searchPageMixin";

export default {
    name: "DealerGroupList",
    components: {TableContentLoader, Pagination},
    mixins: [searchPageMixin],
    data() {
        return {
            fields: [
                {
                    key: "name",
                    sortable: true
                },
                "address",
                {
                    key: "actions",
                    label: "Actions",
                    sortable: false
                }
            ]
        };
    },

    computed: {
        searchResults: get("dealerGroupSearch/searchLoader@data"),
        pageMetadata: get("dealerGroupSearch/pageMetadata"),
        isLoading: get("dealerGroupSearch/<EMAIL>"),
        page: sync("dealerGroupSearch/pageable@page"),
        sort: sync("dealerGroupSearch/pageable@sort")
    },

    watch: {
        page: () => {
            dispatch("dealerGroupSearch/doPageLoad");
        },
        sort: () => {
            dispatch("dealerGroupSearch/doSort");
        }
    },

    mounted() {
        dispatch("dealerGroupSearch/doPageLoad");
    },

    methods: {
        goToDetails(dealerGroup) {
            window.location = `/dealerGroup/${dealerGroup.id}`;
        }
    }
};
</script>
