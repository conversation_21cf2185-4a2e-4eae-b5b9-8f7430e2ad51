import _ from 'lodash';
import {make} from 'vuex-pathify';
import api from '@/api';

const state = {
    warrantyContract: _.get(window, '_CS_SELECTED_WARRANTY_CONTRACT', null),
    warrantyVehicle: _.get(window, '_CS_SELECTED_WARRANTY_VEHICLE', null),
    dealer: null,
    user: null,
    createdBy: null
};

const mutations = {
    ...make.mutations(state)
};

const actions = {
    getWarrantyInfo({commit, state}) {
        api.get(`/warranty-contracts/dealer/${state.warrantyContract.dealerId}`)
            .then(response => {
                commit('SET_DEALER', response.data);
            })
            .catch(error => {
                console.log(error);
            });

        api.get(`/warranty-contracts/user/${state.warrantyContract.userId}`)
            .then(response => {
                commit('SET_USER', response.data);
            })
            .catch(error => {
                console.log(error);
            });

        api.get(`/warranty-contracts/user/${state.warrantyContract.createdBy}`)
            .then(response => {
                commit('SET_CREATED_BY', response.data);
            })
            .catch(error => {
                console.log(error);
            });
    }
};

export default {
    namespaced: true,
    state,
    actions,
    mutations
};
