<template>
    <div v-if="isLoading">
        <table-content-loader/>
    </div>

    <div v-else>
        <div class="ibox">
            <div class="ibox-content">
                <div class="result-count">
                    <pagination
                        :show-pager="false"
                        :page-metadata="pageMetadata"
                        :page="page"
                        @page-changed="pageChanged"
                    />
                </div>

                <b-table
                    responsive
                    striped
                    hover
                    bordered
                    :fields="fields"
                    :items="searchResults"
                    :no-local-sorting="true"
                    :sort-by.sync="sortBy"
                    :sort-desc.sync="sortDesc"
                    @sort-changed="sortChanged"
                >
                    <template v-slot:cell(customer)="row">
                        <b-link
                            v-if="row.item.user.id"
                            :href="'/user/' + row.item.user.id"
                        >
                            {{ row.item.user.firstName }}
                            {{ row.item.user.lastName }}
                        </b-link>
                        <b-button
                            v-else
                            size="xs"
                            variant="primary"
                            :href="'/warranty/' + row.item.id + '/user?form'"
                        >
                            Add User
                        </b-button>
                    </template>

                    <template v-slot:cell(dealer)="row">
                        <b-link :href="'/dealer/' + row.item.dealer.id">
                            {{ row.item.dealer.name }}
                        </b-link>
                    </template>

                    <template v-slot:cell(registrationId)="row">
                        <b-link :href="'/warranty/' + row.item.id">
                            {{ row.item.registrationId }}
                        </b-link>
                    </template>

                    <template v-slot:cell(planType)="row">
                        <b-badge>{{ row.item.planType }}</b-badge>
                    </template>

                    <template v-slot:cell(upgrade)="row">
                        <b-button
                            v-if="row.item.paymentRequired && !row.item.paid"
                            size="xs"
                            variant="primary"
                            @click="emailInvoiceForm(row.item)"
                        >
                            Email Invoice
                        </b-button>
                        <b-button
                            v-else-if="
                                row.item.planType === 'LIFETIME' &&
                                    row.item.user.id != null &&
                                    row.item.registrationStatus !== 'Voided'
                            "
                            size="xs"
                            variant="primary"
                            :href="
                                '/stratus/dealer/' +
                                    row.item.dealer.id +
                                    '/warranty/' +
                                    row.item.id +
                                    '/upgrade'
                            "
                        >
                            Upgrade
                        </b-button>
                    </template>

                    <template v-slot:cell(invoiceSent)="row">
                        <b-badge v-if="row.item.invoiceSent" variant="success">
                            True
                        </b-badge>
                        <b-badge v-else variant="danger">
                            False
                        </b-badge>
                    </template>

                    <template v-slot:cell(payment)="row">
                        <b-badge
                            v-if="row.item.paymentRequired && row.item.paid"
                            variant="success"
                        >
                            Paid
                        </b-badge>
                        <b-badge
                            v-else-if="
                                row.item.paymentRequired && !row.item.paid
                            "
                            variant="warning"
                        >
                            Unpaid
                        </b-badge>
                        <b-badge v-else>
                            N/A
                        </b-badge>
                    </template>

                    <template v-slot:cell(registrationStatus)="row">
                        <b-badge>{{ row.item.registrationStatus }}</b-badge>
                    </template>

                    <template v-slot:cell(createdBy)="row">
                        {{ row.item.createdBy.firstName }}
                        {{ row.item.createdBy.lastName }}
                    </template>

                    <template v-slot:cell(createdDate)="row">
                        {{
                            row.item.createdDate
                                | formatDateTime("MM/DD/YYYY h:mm a z")
                        }}
                    </template>
                </b-table>

                <div class="pagination-wrapper">
                    <pagination
                        :show-totals="false"
                        :page-metadata="pageMetadata"
                        :page="page"
                        @page-changed="pageChanged"
                    />
                </div>
            </div>
        </div>

        <b-modal v-model="showEmailInvoiceForm" size="sm" title="Send Invoice">
            <b-form class="send-invoice-form">
                <b-form-group>
                    <label
                    >* Enter the email you wish to send the invoice
                        to.</label
                    >
                    <b-form-input v-model="invoiceEmail"/>
                </b-form-group>
            </b-form>

            <div slot="modal-footer">
                <b-button
                    v-if="selectedWarranty && selectedWarranty.paid"
                    variant="primary"
                    size="sm"
                    disabled
                >
                    Paid
                </b-button>
                <b-button
                    v-else
                    variant="primary"
                    size="sm"
                    class="premium-invoice"
                    @click="emailInvoice"
                >
                    Email Invoice
                </b-button>

                <b-button
                    size="sm"
                    variant="danger"
                    @click="showEmailInvoiceForm = false"
                >
                    Close
                </b-button>
            </div>
        </b-modal>
    </div>
</template>

<script>
import api from "@/api";
import {dispatch, get, sync} from "vuex-pathify";
import Pagination from "Components/Pagination";
import TableContentLoader from "Components/TableContentLoader";
import searchPageMixin from "@/mixins/searchPageMixin";

export default {
    components: {TableContentLoader, Pagination},
    mixins: [searchPageMixin],
    data() {
        return {
            showEmailInvoiceForm: false,
            selectedWarranty: null,
            invoiceEmail: "",
            fields: [
                {
                    key: "customer",
                    label: "Customer"
                },
                {
                    key: "dealer",
                    label: "Dealer"
                },
                {
                    key: "registrationId",
                    label: "Registration ID"
                },
                {
                    key: "registrationBy",
                    label: "Registration Source"
                },
                {
                    key: "planType",
                    label: "Plan Type"
                },
                {
                    key: "upgrade",
                    label: "Upgrade/Email Invoice"
                },
                {
                    key: "invoiceSent",
                    label: "Invoice Sent"
                },
                {
                    key: "payment",
                    label: "Payment"
                },
                {
                    key: "registrationStatus",
                    label: "Registration Status"
                },
                {
                    key: "createdSource",
                    label: "Source"
                },
                {
                    key: "createdBy",
                    label: "Created By"
                },
                {
                    key: "createdDate",
                    label: "Created Date"
                }
            ]
        };
    },

    computed: {
        searchResults: get("warrantySearch/searchLoader@data"),
        pageMetadata: get("warrantySearch/pageMetadata"),
        isLoading: get("warrantySearch/<EMAIL>"),
        page: sync("warrantySearch/pageable@page"),
        sort: sync("warrantySearch/pageable@sort")
    },

    watch: {
        page: () => {
            dispatch("warrantySearch/doPageLoad");
        },
        sort: () => {
            dispatch("warrantySearch/doSort");
        }
    },

    mounted() {
        dispatch("warrantySearch/doPageLoad");
    },

    methods: {
        emailInvoiceForm(warranty) {
            this.selectedWarranty = warranty;
            this.invoiceEmail = _.get(warranty, "user.email");
            this.showEmailInvoiceForm = true;
        },
        emailInvoice() {
            this.showEmailInvoiceForm = false;
            api.post(
                `/warranty-contracts/${this.selectedWarranty.id}/email-invoice`,
                {
                    email: this.invoiceEmail
                }
            ).then(response => {
                this.$toastr.s(
                    `Warranty invoice emailed to ${this.invoiceEmail}`
                );
            });
        },
        goToDetails(warranty) {
            window.location = `/warranty/${warranty.id}`;
        },
        convertRequestStatus(status) {
            switch (status) {
                case "COMPLETE":
                    return "Complete";
                case "LENDERS_FOUND":
                    return "Lenders Found";
                case "RESUBMIT_POSTED":
                    return "Resubmit Posted";
                case "FORM_FILLED":
                    return "Form Filled";
                default:
                    return "Unknown";
            }
        },
        convertResponseStatus(status) {
            switch (status) {
                case "A":
                    return "Approved";
                case "D":
                    return "Declined";
                default:
                    return "Unknown";
            }
        },
        displayVehicle(vehicle) {
            if (_.isNil(vehicle)) {
                return null;
            }
            return `${vehicle.year} ${vehicle.make} ${vehicle.model}`;
        },
        hasResponses(responses) {
            return !_.isEmpty(responses);
        }
    }
};
</script>

<style>
.result-count {
    padding: 10px 0;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
