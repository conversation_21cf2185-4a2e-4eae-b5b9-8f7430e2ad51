<template>
    <b-modal :id="id" title="Show/Hide Columns" ok-only>
        <b-form-group>
            <b-form-checkbox
                v-for="field in fields"
                :key="fieldKey(field)"
                v-model="selected"
                :value="fieldKey(field)"
            >
                {{ fieldName(field) }}
            </b-form-checkbox>
        </b-form-group>
    </b-modal>
</template>
<script>

export default {
    name: 'TableColumnSelector',
    props: {
        id: {
            type: String,
            default: 'table-column-selector'
        },
        fields: {
            type: Array,
            required: true
        },
        value: {
            type: Array,
            default: []
        }
    },

    data() {
        return {
            selected: []
        };
    },

    watch: {
        selected() {
            this.$emit('input', this.selected);
        }
    },

    mounted() {
        this.selected = this.value;
    },

    methods: {
        fieldKey(field) {
            if (_.isObject(field)) {
                return _.get(field, 'key');
            }

            return field;
        },
        fieldName(field) {
            if (_.isObject(field)) {
                const label = _.get(field, 'label', _.startCase(_.get(field, 'key')));
                if (label !== '') {
                    return label;
                } else {
                    return _.startCase(_.get(field, 'key'));
                }
            }

            return _.startCase(field);
        }
    }
};
</script>
