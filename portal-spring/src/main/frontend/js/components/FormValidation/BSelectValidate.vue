<template>
    <validation-provider
        ref="validator"
        v-slot="{ valid, errors }"
        :vid="vid"
        :name="$attrs.name"
        :rules="rules"
    >
        <b-form-group v-bind="$attrs">
            <b-form-select
                :value="value"
                v-bind="$attrs"
                :state="errors[0] ? false : (valid ? true : null)"
                @input="onInput"
            >
                <template v-if="!hidePlaceholder" v-slot:first>
                    <option :value="null" disabled>
                        Select {{ $attrs.name }}
                    </option>
                </template>
                <slot name="options"/>
            </b-form-select>
            <b-form-invalid-feedback id="inputLiveFeedback">
                {{ errors[0] }}
            </b-form-invalid-feedback>
        </b-form-group>
    </validation-provider>
</template>

<script>
import {ValidationProvider} from 'vee-validate';

export default {
    name: 'BSelectValidate',
    components: {
        ValidationProvider
    },
    props: {
        vid: {
            type: String
        },
        rules: {
            type: [Object, String],
            default: ''
        },
        value: {
            type: [String, Number, Boolean],
            required: false,
            default: null
        },
        hidePlaceholder: {
            type: Boolean,
            required: false,
            default: false
        }
    },
    methods: {
        onInput(value) {
            this.$emit('input', value);
        }
    }
};
</script>
