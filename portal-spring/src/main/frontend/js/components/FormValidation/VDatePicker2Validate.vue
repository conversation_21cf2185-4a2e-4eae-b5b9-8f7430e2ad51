<template>
    <validation-provider
        v-slot="{ valid, errors }"
        :vid="vid"
        :name="$attrs.name"
        :rules="rules"
    >
        <b-form-group v-bind="$attrs">
            <date-picker2
                v-model="innerValue"
                v-bind="$attrs"
                :errors="errors"
                :valid="valid"
            />
        </b-form-group>
    </validation-provider>
</template>

<script>
import {ValidationProvider} from "vee-validate";
import DatePicker2 from "Components/DatePicker2";

export default {
    name: "VDatePicker2Validate",
    components: {
        DatePicker2,
        ValidationProvider
    },
    props: {
        vid: {
            type: String
        },
        rules: {
            type: [Object, String],
            default: ""
        },
        // must be included in props
        value: {
            type: null
        }
    },
    data: () => ({
        innerValue: ""
    }),
    watch: {
        // Handles internal model changes.
        innerValue(newVal) {
            this.$emit("input", newVal);
        },
        // Handles external model changes.
        value(newVal) {
            this.innerValue = newVal;
        }
    },
    created() {
        if (!_.isNil(this.value)) {
            this.innerValue = this.value;
        }
    }
};
</script>
