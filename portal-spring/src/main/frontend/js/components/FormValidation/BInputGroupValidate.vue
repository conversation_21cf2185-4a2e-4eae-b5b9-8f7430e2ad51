<template>
    <validation-provider v-slot="{ valid, errors }" :vid="vid" :name="$attrs.name"
                         :rules="rules"
    >
        <b-input-group v-bind="$attrs">
            <slot name="prepend"/>
            <b-form-input
                v-model="innerValue"
                :size="inputSize"
                v-bind="$attrs"
                :state="errors[0] ? false : (valid ? true : null)"
            />
            <slot name="append"/>
            <b-form-invalid-feedback id="inputLiveFeedback">
                {{ errors[0] }}
            </b-form-invalid-feedback>
        </b-input-group>
    </validation-provider>
</template>

<script>
import {ValidationProvider} from 'vee-validate';

export default {
    name: 'BInputGroupValidate',
    components: {
        ValidationProvider
    },
    props: {
        vid: {
            type: String
        },
        rules: {
            type: [Object, String],
            default: ''
        },
        // must be included in props
        value: {
            type: null
        },
        inputSize: {
            type: String,
            required: false
        }
    },
    data: () => ({
        innerValue: ''
    }),
    watch: {
        // Handles internal model changes.
        innerValue(newVal) {
            this.$emit('input', newVal);
        },
        // Handles external model changes.
        value(newVal) {
            this.innerValue = newVal;
        }
    },
    created() {
        if (this.value) {
            this.innerValue = this.value;
        }
    }
};
</script>
