<template>
    <div class="row pt-3">
        <div class="col-lg-12">
            <i-box title="Invoice Items">
                <div v-if="isLoading">
                    <table-content-loader/>
                </div>

                <div v-else>
                    <div v-if="invoiceItems.length == 0">
                        <div class="bg-muted p-2" style="border-radius: 3px;">
                            <i aria-hidden="true" class="fa fa-exclamation-triangle mr-1"/>
                            Currently no <strong>Invoice Items</strong> have been created.
                        </div>
                    </div>

                    <div v-else class="table-responsive">
                        <b-table responsive striped hover
                                 bordered :fields="fields" :items="invoiceItems"
                                 :no-local-sorting="true"
                        >
                            <template v-slot:cell(referenceItem.user)="data">
                                <b-link v-if="data.item.referenceItem.user"
                                        :href="`/user/${data.item.referenceItem.user.id}`">
                                    {{ data.item.referenceItem.user.firstName }}
                                    {{ data.item.referenceItem.user.lastName }}
                                </b-link>
                            </template>
                            <template v-slot:cell(dealer.name)="data">
                                <b-link v-if="data.item.dealer" :href="`/dealer/${data.item.dealer.id}`">
                                    {{ data.item.dealer.name }}
                                </b-link>
                            </template>
                            <template v-slot:cell(charge)="data">
                                <span>{{ convertCharge(data.item.charge) }}</span>
                            </template>
                            <template v-slot:cell(createdDate)="data">
                                <span>{{ data.item.createdDate | formatDateTime('MM/DD/YYYY h:mm a z') }}</span>
                            </template>
                            <template v-slot:cell(referenceItem.description)="data">
                                <b-link v-if="data.item.referenceItem.id"
                                        @click="goToProductPage(data.item.referenceItem)">
                                    {{ data.item.referenceItem.description }}
                                </b-link>
                                <span v-else>{{ data.item.referenceItem.description }}</span>
                            </template>
                        </b-table>
                    </div>
                </div>
            </i-box>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import api from '@/api';
import TableContentLoader from 'Components/TableContentLoader';
import IBox from 'Components/IBox';

export default {
    components: {IBox, TableContentLoader},
    props: {
        referenceItemId: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            invoiceItems: [],
            isLoading: true,
            fields: [
                {
                    key: 'status',
                    sortable: true
                },
                {
                    key: 'referenceItem.user',
                    label: 'Customer Name'
                },
                {
                    key: 'dealer.name',
                    label: 'Dealer',
                    sortable: true
                },
                {
                    key: 'referenceItem.description',
                    label: 'Description',
                    sortable: false
                },
                {
                    key: 'charge',
                    sortable: true
                },
                {
                    key: 'amount',
                    sortable: true
                },
                {
                    key: 'createdDate',
                    sortable: true
                }
            ]
        };
    },
    mounted() {
        api.post('/invoice-items/search', {
            referenceItemId: this.referenceItemId
        })
            .then(response => {
                this.invoiceItems = _.get(response.data, 'content');
                this.isLoading = false;
            });
    },
    methods: {
        convertCharge(charge) {
            switch (charge) {
                case 'PPS':
                    return 'Pay Per Sale';
                case 'LTW':
                    return 'Lifetime Warranty';
                case 'GIFT_CARD':
                    return 'Gift Card';
                case 'OPT_OUT':
                    return 'VSC Opt Out';
                case 'VSC':
                    return 'VSC';
                case 'VSC_CREDIT':
                    return 'VSC Credit';
                default:
                    return charge;
            }
        }
    }
};
</script>
