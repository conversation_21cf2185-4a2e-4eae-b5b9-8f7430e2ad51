<template>
    <b-modal :id="id" title="Include Columns in Export" ok-title="Export"
             @shown="syncExportFields" @ok="okClicked" :ok-disabled="noFieldSelected"
    >
        <b-link @click="selectAll">
            Select All
        </b-link>
        <b-form-group>
            <b-form-checkbox
                v-for="field in exportableFields"
                :key="fieldKey(field)"
                v-model="selected"
                :value="fieldKey(field)"
            >
                {{ fieldName(field) }}
            </b-form-checkbox>
        </b-form-group>
    </b-modal>
</template>

<script>
import _ from 'lodash';

export default {
    props: {
        id: {
            type: String,
            default: 'export-column-selector'
        },
        fields: {
            type: Array,
            required: true
        },
        displayFields: {
            type: Array,
            required: false,
            default: []
        },
        value: {
            type: Array,
            default: () => []
        }
    },

    data() {
        return {
            selected: []
        };
    },

    computed: {
        exportableFields() {
            return _.filter(this.fields, (field) => {
                return _.get(field, 'exportable', true);
            });
        },
        noFieldSelected() {
            return this.selected.length === 0
        }
    },

    watch: {
        selected() {
            this.$emit('input', this.selected);
        }
    },

    methods: {
        selectAll() {
            const allSelected = _.map(this.exportableFields, field => this.fieldKey(field));
            this.selected = allSelected;
        },
        okClicked() {
            this.$emit('export');
        },
        syncExportFields() {
            const fields = this.fields;
            this.selected = _.filter(this.displayFields, (field) => {
                const fieldDefinition = _.find(fields, ['key', field]);

                if (_.isNil(fieldDefinition)) {
                    return fields.indexOf(field) !== -1;
                }

                return _.get(fieldDefinition, 'exportable', true);
            });

            let orderedSelected = [];
            fields.map(f => {
                const isObject = typeof f === 'object';
                const fieldValue = isObject ? f.key : f;
                if (this.selected.indexOf(fieldValue) !== -1) {
                    orderedSelected.push(fieldValue);
                }
            })
            this.selected = orderedSelected;
        },
        fieldKey(field) {
            if (_.isObject(field)) {
                return _.get(field, 'key');
            }

            return field;
        },
        fieldName(field) {
            if (_.isObject(field)) {
                const label = _.get(field, 'label', _.startCase(_.get(field, 'key')));
                if (label !== '') {
                    return label;
                } else {
                    return _.startCase(_.get(field, 'key'));
                }
            }

            return _.startCase(field);
        }
    }
};
</script>
