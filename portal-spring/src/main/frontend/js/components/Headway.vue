<template>
    <div v-if="$acl.hasAuthority('ROLE_ADMIN')">
        <span class="headway"/>
    </div>
</template>

<script>
export default {
    name: 'Headway',

    mounted() {
        const config = {
            selector: '.headway', // CSS selector where to inject the badge
            account: 'yBvmpy'
        };

        // As an instance method inside a component
        this.$loadScript('https://cdn.headwayapp.co/widget.js')
            .then(() => {
                Headway.init(config);
            });
    }
};
</script>
