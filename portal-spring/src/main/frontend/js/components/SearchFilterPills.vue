<template>
    <div class="pill-box">
        <slot/>

        <div class="pill-wrapper">
            <div
                v-for="(value, key) in filters"
                v-if="hasFilters(key)"
                v-b-tooltip.hover
                class="pill"
                :title="getValue(key, value, false)"
            >
                <i
                    aria-hidden="true"
                    class="fa fa-times-circle"
                    @click.stop="clearFilter(key)"
                />
                <i
                    v-if="searchMethod(key) === 'NEGATIVE'"
                    aria-hidden="true"
                    class="fas fa-minus-circle"
                />
                <span>
                    {{ getLabel(key) }}:
                    <span>{{ getValue(key, value, true) }}</span>
                </span>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.pill-box {
    border: 1px solid #dddddd;
    min-height: 50px;
    background: white;
    padding: 0 5px;
    display: flex;
    align-items: center;
}

.pill-wrapper {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    padding: 5px;
}

.pill {
    display: flex;
    align-items: center;
    background: #efefef;
    border: 1px solid #d4d4d4;
    border-radius: 4px;
    font-size: 10px;
    padding: 3px 20px;
    margin-left: 5px;
    height: 30px;
    position: relative;
}

.pill .fa-times-circle {
    position: absolute;
    top: 1px;
    right: 3px;
    cursor: pointer;
}
</style>

<script>
import _ from "lodash";

export default {
    name: "SearchFilterPills",
    props: {
        store: {
            type: String,
            required: true
        },
        filterField: {
            type: String,
            default: "filters",
            required: false
        },
        facetField: {
            type: String,
            default: "facets",
            required: false
        },
        pillsField: {
            type: String,
            default: "pills",
            required: false
        }
    },

    computed: {
        pills() {
            const filterProp = this.store + "." + this.pillsField;
            const f = _.get(this.$store.state, filterProp);
            return _.isNil(f) ? [] : f;
        },
        facets() {
            const filterProp = this.store + "." + this.facetField;
            const f = _.get(this.$store.state, filterProp);
            return _.isNil(f) ? [] : f;
        },
        filters() {
            const filterProp = this.store + "." + this.filterField;
            const f = _.get(this.$store.state, filterProp);
            return _.isNil(f) ? [] : f;
        }
    },

    methods: {
        searchMethod(field) {
            const filterProp = this.store + ".searchMethods." + field;
            const f = _.get(this.$store.state, filterProp);
            return _.isNil(f) ? "POSITIVE" : f;
        },
        hasFilters(key) {
            // if the key isn't excluded we show the pills for it
            const isEnabled = _.get(this.pills, key + ".enabled", true);
            const filter = _.get(this.filters, key);

            if (key.toLowerCase() === "topdmas") {
                if (_.isNil(filter)) {
                    return false;
                }

                if( _.get(this.filters, key + ".start") == null) {
                    return false;
                }

                return !(
                    _.get(this.filters, key + ".start") === null &&
                    _.get(this.filters, key + ".end") === null
                );
            }

            if (isEnabled) {
                return !_.isNil(filter);
            }

            return false;
        },
        getLabel(key) {
            return _.get(this.pills[key], "label", _.startCase(key));
        },
        getValue(key, value, shorten) {
            // we will attempt to see if the associated filter maps to a facet and get the display value of that
            // This is useful when using userId or other IDs and we want to display a proper value
            const facetName = _.get(this.pills[key], "facet", key);
            const type = _.get(this.pills[key], "type");

            const prefix =
                this.searchMethod(key) === "NEGATIVE" ? "Removing: " : "";

            if (shorten && _.isArray(value) && value.length > 2) {
                return `(${value.length} terms)`;
            }

            if (!_.isNil(this.facets[facetName]) || type === "range") {
                if (_.isArray(value)) {
                    const formattedValues = [];
                    _.forEach(value, v => {
                        const facetData = _.get(this.facets[facetName], "data");
                        const facet = _.find(facetData, function (o) {
                            return _.toString(o.id) === _.toString(v);
                        });
                        formattedValues.push(_.get(facet, "name", v));
                    });

                    return prefix + _.join(formattedValues, ", ");
                } else if (type === "range" && value) {
                    const start = _.get(value, "start", null);
                    const end = _.get(value, "end", null);

                    if (_.isNil(start) && _.isNil(end)) {
                        return "no values";
                    } else if (_.isNil(start) && !_.isNil(end)) {
                        return `up to ${end}`;
                    } else if (!_.isNil(start) && _.isNil(end)) {
                        return `from ${start}`;
                    } else {
                        return `between ${start} and ${end}`;
                    }
                } else {
                    const facetData = _.get(this.facets[facetName], "data");
                    const facet = _.find(facetData, function (o) {
                        return o.id === value;
                    });
                    return prefix + _.get(facet, "name", value);
                }
            }

            if (_.isString(value)) {
                return prefix + value;
            } else if (_.isArray(value)) {
                return prefix + _.join(value, ", ");
            } else if (_.has(value, "start") && _.has(value, "end")) {
                return prefix + `${value.start} - ${value.end}`;
            }

            return value;
        },
        clearFilter(filterName) {
            this.$store.dispatch(this.store + "/clearFilter", filterName);
        }
    }
};
</script>
