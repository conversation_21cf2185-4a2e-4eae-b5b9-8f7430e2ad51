<template>
    <div class="ibox">
        <div v-if="title" class="ibox-title">
            <h5>
                {{ title }}
                <b-link
                    v-if="description"
                    v-b-tooltip.hover
                    :title="description"
                >
                    <i aria-hidden="true" class="fas fa-info-circle"/>
                </b-link>
                <b-link
                    v-if="alertMessage"
                    v-b-tooltip.hover
                    :title="alertMessage"
                >
                    <i aria-hidden="true" class="fas fa-exclamation-triangle"/>
                </b-link>
            </h5>
            <div class="ibox-tools">
                <slot name="header-actions"/>
                <a
                    v-if="collapsible"
                    class="collapse-link"
                    @click="visible = !visible"
                >
                    <i aria-hidden="true" class="fa fa-chevron-up"/>
                </a>
            </div>
        </div>
        <b-collapse v-model="visible">
            <div
                class="ibox-content clearfix"
                :class="{ 'sk-loading': loading }"
            >
                <div class="sk-spinner sk-spinner-wave">
                    <div class="sk-rect1"/>
                    <div class="sk-rect2"/>
                    <div class="sk-rect3"/>
                    <div class="sk-rect4"/>
                    <div class="sk-rect5"/>
                </div>
                <slot/>
            </div>
        </b-collapse>
    </div>
</template>

<style lang="scss">
.fa-exclamation-triangle {
    color: $red;
}

.ibox-tools a.btn-light {
    color: #212529;
}
</style>

<script>
export default {
    props: {
        title: {
            type: String
        },
        description: {
            type: String,
            required: false,
            default: null
        },
        alertMessage: {
            type: String,
            required: false,
            default: null
        },
        loading: {
            type: Boolean,
            required: false,
            default: false
        },
        collapsible: {
            type: Boolean,
            required: false,
            default: true
        }
    },

    data() {
        return {
            visible: true
        };
    }
};
</script>
