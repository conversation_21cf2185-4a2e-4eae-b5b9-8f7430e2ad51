<template>
    <div class="facet-base">
        <!-- $idRef & $id are from vue-unique-id npm package-->
        <a
            class="facet-base-label"
            data-toggle="collapse"
            :href="$idRef(facetName)"
            @click="showCollapse = !showCollapse"
        >
            <div>
                <i
                    aria-hidden="true"
                    :class="
                        showCollapse
                            ? 'fa fa-chevron-down'
                            : 'fa fa-chevron-right'
                    "
                />
            </div>

            <span class="mr-auto">{{ facetLabel }}</span>

            <a
                v-if="isFiltered"
                class="small clear"
                @click.prevent.stop="clearCurrentFilter"
            >
                clear
            </a>
        </a>
        <div v-if="hasActionsSlot" class="facet-actions">
            <slot name="actions"/>
        </div>
        <div :id="$id(facetName)" class="collapse">
            <div v-if="facetLoading" class="clearfix sk-loading">
                <div class="sk-spinner sk-spinner-wave">
                    <div class="sk-rect1"/>
                    <div class="sk-rect2"/>
                    <div class="sk-rect3"/>
                    <div class="sk-rect4"/>
                    <div class="sk-rect5"/>
                </div>
            </div>
            <div v-if="numberOfFacets > 10" class="inner-addon left-addon">
                <i aria-hidden="true" class="fas fa-search"/>
                <b-form-input
                    v-model="searchText"
                    size="sm"
                    placeholder="Filter values"
                />
            </div>
            <ul v-if="hasFacets && !facetLoading" class="facet-list">
                <li v-for="facet in facetsToShow">
                    <slot :facet="facet"/>
                </li>
            </ul>
            <span v-else-if="!facetLoading && !hasFacets" class="no-facets">
                No Facets Available
            </span>
        </div>
    </div>
</template>

<style lang="scss">
.inner-addon {
    position: relative;
}

/* style icon */
.inner-addon .fas {
    position: absolute;
    padding: 10px;
    pointer-events: none;
}

/* align icon */
.left-addon .fas {
    left: 0px;
}

/* add padding  */
.left-addon input {
    padding-left: 30px;
}

.facet-base {
    border-radius: 0;
    border-bottom: 1px solid #e9e8ea;
}

.facet-base .facet-base-label {
    background-color: white;
    display: flex;
    cursor: pointer;
    font-size: 13px;
    font-family: "Noto Sans", "Lucida Grande", "Lucida Sans Unicode", sans-serif;
    text-transform: capitalize;
    padding: 4px 12px;
    margin: 4px 0;
    font-weight: 700;
}

.facet-base .facet-base-label:hover {
    background-color: rgb(235, 245, 250);
}

.facet-base .facet-base-label .facet-disabled {
    color: #cccccc;
}

.facet-base {
    .facet-base-label {
        div {
            margin-right: 8px;
        }
    }
}

.facet-base ul {
    list-style: none;
    margin: 5px 10px 10px 10px;
    padding: 0;
}

.facet-base ul:first-child {
    border-top: none;
}

.facet-base .x-undo:before {
    content: "⤺ ";
    font-size: 12px;
}

.facet-base .x-selected {
    font-weight: bold;
}

.facet-actions {
    padding-left: 15px;
}

.facet-list {
    overflow: scroll;
    max-height: 235px;
}

.clear {
    color: $primary-blue !important;
}

.facet-list > li > div {
    white-space: nowrap;
}

.no-facets {
    padding-left: 20px;
}

.custom-control-label,
.form-check-label {
    width: 100%;
}

.facet {
    font-size: 12px;
    margin-bottom: 0;
    color: #333;

    &:hover {
        background-color: #ebf5fa;
    }
}

.facet-count {
    color: #888;
    font-size: 0.9em;
}
</style>

<script>
import _ from "lodash";
import {FACET_TYPE} from "@/api/searchUtils";

export default {
    name: "FacetBase",

    props: {
        facetLabel: {
            type: String,
            default: "",
            required: false
        },
        store: {
            type: String,
            required: true
        },
        facetField: {
            type: String,
            default: "facets",
            required: false
        },
        facetType: {
            type: String,
            default: FACET_TYPE.MULTIPLE,
            required: false
        },
        facetName: {
            type: String,
            required: true
        },
        filterName: {
            type: String,
            required: true
        },
        showCount: {
            type: Boolean,
            required: false,
            default: true
        }
    },

    data() {
        return {
            showCollapse: false,
            searchText: ""
        };
    },

    computed: {
        isPositiveSearchMethod() {
            return this.searchMethod !== "NEGATIVE";
        },
        isNegativeSearchMethod() {
            return this.searchMethod === "NEGATIVE";
        },
        facetsToShow() {
            let allFacets = this.facets || [];
            
            try {
                if (Array.isArray(this.facets) && this.facets.length > 0 && Array.isArray(this.facets[0])) {
                    allFacets = _.flatten(this.facets);
                }
            } catch (error) {
                console.error('Error flattening facets:', error);
                return [];
            }

            if (_.isNil(this.searchText) || this.searchText === "") {
                return allFacets;
            }

            return _.filter(allFacets, facet => {
                return (
                    _.toLower(facet.id).indexOf(
                        this.searchText.toLowerCase()
                    ) !== -1 ||
                    _.toLower(facet.name).indexOf(
                        this.searchText.toLowerCase()
                    ) !== -1
                );
            });
        },
        numberOfFacets() {
            return _.size(this.facets);
        },
        facets() {
            const getterName = `${this.store}/getFacetsByName`;
            return this.$store.getters[getterName](this.facetName);
        },
        filters() {
            const getterName = `${this.store}/getFiltersByName`;
            const f = this.$store.getters[getterName](this.filterName);
            const type = this.facetType;

            if (_.isNil(f)) {
                if (type === FACET_TYPE.MULTIPLE.toString()) {
                    return [];
                }
                return null;
            }

            if (type === FACET_TYPE.MULTIPLE.toString() && !_.isArray(f)) {
                return [f];
            }

            return f;
        },
        searchMethod() {
            const filterProp = this.store + ".searchMethods." + this.filterName;
            const f = _.get(this.$store.state, filterProp);
            return _.isNil(f) ? "POSITIVE" : f;
        },
        facetLoading() {
            const facetProp =
                this.store + "." + this.facetField + "." + this.facetName;
            const facetDataProp = facetProp + ".loader.isLoading";
            return _.get(this.$store.state, facetDataProp, false);
        },
        isFiltered() {
            return !_.isEmpty(this.filters);
        },
        hasFacets() {

            return !_.isEmpty(this.facets);
        },
        hasActionsSlot() {
            return !!this.$slots.actions;
        }
    },

    watch: {
        showCollapse: function (newVal) {
            if (newVal === true) {
                this.loadFacetInfo(this.facetName);
            }
        }
    },

    methods: {
        addPositiveFilter(filter) {
            return this.$store.dispatch(
                `${this.store}/addPositiveFilter`,
                filter
            );
        },
        addNegativeFilter(filter) {
            return this.$store.dispatch(
                `${this.store}/addNegativeFilter`,
                filter
            );
        },
        removeFilter(filter) {
            return this.$store.dispatch(`${this.store}/removeFilter`, filter);
        },
        clearFilter(filter) {
            return this.$store.dispatch(`${this.store}/clearFilter`, filter);
        },
        loadFacetInfo(facetInfo) {
            return this.$store.dispatch(
                `${this.store}/loadFacetInfo`,
                facetInfo
            );
        },
        facetId(facet) {
            return `${this.filterName}_${facet.id}`;
        },
        clearCurrentFilter() {
            this.clearFilter(this.filterName);
        }
    }
};
</script>
