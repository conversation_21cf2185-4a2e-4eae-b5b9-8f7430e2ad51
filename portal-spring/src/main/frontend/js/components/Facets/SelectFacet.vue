<template>
    <div :class="computedCssClass">
        <div class="md-form" style="padding-top: 5px">
            <label v-if="!minimal">{{ facetLabel }}</label>
            <multiselect-wrapper
                :multiple="multiple"
                :value="filters"
                track-by="id"
                :options="facets"
                :custom-label="facetOptionLabel"
                select-label=""
                deselect-label=""
                placeholder=""
                @input="selected"
            />
            <input v-model="filters" type="hidden" :name="filterName"/>
        </div>
    </div>
</template>

<script>
import MultiselectWrapper from "../MultiselectWrapper";
import _ from "lodash";

export default {
    name: "SelectFacet",

    components: {MultiselectWrapper},

    props: {
        minimal: {
            type: Boolean,
            default: false,
            required: false
        },
        facetLabel: {
            type: String,
            default: "",
            required: false
        },
        multiple: {
            type: Boolean,
            default: true
        },
        store: {
            type: String,
            required: true
        },
        cssClass: {
            type: String,
            default: "col-md-3 col-lg-3",
            required: false
        },
        facetField: {
            type: String,
            default: "facets",
            required: false
        },
        filterField: {
            type: String,
            default: "filters",
            required: false
        },
        facetName: {
            type: String,
            required: true
        },
        filterName: {
            type: String,
            required: true
        }
    },

    computed: {
        facets() {
            const facetProp =
                this.store + "." + this.facetField + "." + this.facetName;
            return _.get(this.$store.state, facetProp);
        },
        filters() {
            const getterName = `${this.store}/getFiltersByName`;
            return this.$store.getters[getterName](this.filterName);
        },
        computedCssClass() {
            return this.minimal ? "" : this.cssClass;
        }
    },

    methods: {
        selected(values) {
            this.addPositiveFilter(values);
        },
        facetOptionLabel(facet) {
            return `${facet.name} (${facet.count})`;
        }
    }
};
</script>
