<template>
    <div>
        <fade-transition :duration="200" origin="center top" mode="out-in">
            <router-view/>
        </fade-transition>
    </div>
</template>
<script>
import {FadeTransition} from 'vue2-transitions';

export default {
    components: {
        FadeTransition
    }
};
</script>
<style lang="scss">
#lcs {
    font-size: px2rem(14);
    line-height: px2remUnits(15, 14);
    font-family: Helvetica, sans-serif;
}
</style>
