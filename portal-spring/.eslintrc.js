// https://eslint.org/docs/user-guide/configuring

module.exports = {
    root: true,
    parserOptions: {
        parser: 'babel-eslint'
    },
    globals: {
        '$': false,
    },
    env: {
        browser: true
    },
    extends: [
        // https://github.com/vuejs/eslint-plugin-vue#priority-a-essential-error-prevention
        'plugin:vue/recommended',
        // https://github.com/standard/standard/blob/master/docs/RULES-en.md
        'standard',
        'plugin:cypress/recommended'
    ],
    // required to lint *.vue files
    plugins: [
        'vue',
        'cypress'
    ],
    // add your custom rules here
    rules: {
        // allow async-await
        'generator-star-spacing': 'off',
        // allow debugger during development
        'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
        'indent': ['error', 4],
        'vue/html-indent': ['error', 4],
        // 'vue/script-indent': ['error', 4],
        "vue/max-attributes-per-line": [2, {
            "singleline": 3,
            "multiline": {
                "max": 3,
                "allowFirstLine": true
            }
        }],
        "vue/component-name-in-template-casing": ["error",
            "kebab-case"
        ],
        'semi': ['error', 'always']
    },

    overrides: [
        {
            files: [
                "*.vue",
                '**/__tests__/*.{j,t}s?(x)',
                '**/tests/unit/**/*.spec.{j,t}s?(x)',
                "**/*.spec.js",
                "**/*.spec.jsx"
            ],
            "rules": {
                'indent': "off"
            },
            env: {
                jest: true
            }
        }
    ]
};
