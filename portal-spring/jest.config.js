const config = {
    verbose: true,
    moduleNameMapper: {
        "^Components/(.*)$": "<rootDir>/src/main/frontend/js/components/$1",
        "^Modules/(.*)$": "<rootDir>/src/main/frontend/js/modules/$1",
        "^@/(.*)$": "<rootDir>/src/main/frontend/js/$1",
        //When you import image files, <PERSON><PERSON> tries to interpret the binary codes of the images as .js, hence runs into errors.
        // The only way out is to mock a default response anytime jest sees an image import!
        "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/tests/__mocks__/fileMock.js",
    },
    transform: {
        // process `*.js` files with `babel-jest`
        ".*\\.(js)$": "babel-jest",
        ".*\\.(vue)$": "vue-jest",
    },
    moduleFileExtensions: [
        "js",
        "json",
        // tell Jest to handle `*.vue` files
        "vue"
    ],
    testEnvironment: "jsdom",
    transformIgnorePatterns: [
        '<rootDir>/node_modules/',
    ],
    testPathIgnorePatterns: ["<rootDir>/cypress/"],
    collectCoverage: true,
    collectCoverageFrom: [
        "src/main/frontend/js/**/*.{js,vue}",
        "!**/node_modules/**"
    ],
    coverageReporters: [
        "text-summary",
        "lcov"
    ],
    testResultsProcessor: "jest-sonar-reporter",
};

module.exports = config
