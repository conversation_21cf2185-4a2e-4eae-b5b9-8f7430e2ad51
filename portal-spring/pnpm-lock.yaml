lockfileVersion: '6.0'

dependencies:
  '@carsaver/body-style-icon':
    specifier: ^1.0.2
    version: 1.0.9
  '@popperjs/core':
    specifier: ^2.4.0
    version: 2.11.6
  '@vue/cli-service':
    specifier: ^5.0.4
    version: 5.0.8(@babel/core@7.20.2)(babel-core@7.0.0-bridge.0)(lodash@4.17.21)(sass-loader@9.0.3)(vue-template-compiler@2.7.14)(vue@2.7.14)(webpack-cli@4.10.0)
  arrive:
    specifier: ^2.4.1
    version: 2.4.1
  axios:
    specifier: ^0.21.1
    version: 0.21.4
  bootstrap:
    specifier: ^4.6.0
    version: 4.6.2(jquery@3.6.1)(popper.js@1.16.1)
  bootstrap-vue:
    specifier: ^2.21.2
    version: 2.23.1(jquery@3.6.1)(vue@2.7.14)
  dot-object:
    specifier: ^2.1.4
    version: 2.1.4
  es6-promise:
    specifier: ^4.2.8
    version: 4.2.8
  eslint-plugin-jest:
    specifier: ^26.1.3
    version: 26.9.0(eslint@6.8.0)(jest@24.9.0)(typescript@4.8.4)
  jest-sonar-reporter:
    specifier: ^2.0.0
    version: 2.0.0
  jquery:
    specifier: '>=1.9.1 <4.0.0'
    version: 3.6.1
  lodash:
    specifier: ^4.17.21
    version: 4.17.21
  moment:
    specifier: ^2.29.1
    version: 2.29.4
  moment-timezone:
    specifier: ^0.5.32
    version: 0.5.38
  path-to-regexp:
    specifier: ^6.2.0
    version: 6.2.1
  popper.js:
    specifier: ^1.16.1
    version: 1.16.1
  store:
    specifier: ^2.0.12
    version: 2.0.12
  typescript:
    specifier: ^4.7.3
    version: 4.8.4
  url-parse:
    specifier: ^1.4.7
    version: 1.5.10
  v-calendar:
    specifier: ^2.2.4
    version: 2.4.1(@popperjs/core@2.11.6)(vue@2.7.14)
  v-tooltip:
    specifier: ^2.1.3
    version: 2.1.3(vue@2.7.14)
  vee-validate:
    specifier: ^3.4.5
    version: 3.4.14(vue@2.7.14)
  vue:
    specifier: ^2.6.12
    version: 2.7.14
  vue-content-loader:
    specifier: ^0.2.3
    version: 0.2.3
  vue-currency-filter:
    specifier: ^5.2.0
    version: 5.2.0
  vue-form-wizard:
    specifier: ^0.8.4
    version: 0.8.4
  vue-json-pretty:
    specifier: ^1.9.3
    version: 1.9.3
  vue-lodash-filters:
    specifier: ^1.3.2
    version: 1.3.2
  vue-mention:
    specifier: ^1.0.0
    version: 1.1.0(v-tooltip@2.1.3)(vue@2.7.14)
  vue-moment:
    specifier: ^4.1.0
    version: 4.1.0(vue@2.7.14)
  vue-multiselect:
    specifier: ^2.1.6
    version: 2.1.6
  vue-router:
    specifier: ^3.4.9
    version: 3.6.5(vue@2.7.14)
  vue-star-rating:
    specifier: ^1.7.0
    version: 1.7.0
  vue-suglify:
    specifier: ^1.0.14
    version: 1.0.14
  vue-the-mask:
    specifier: ^0.11.1
    version: 0.11.1
  vue-toastr:
    specifier: ^2.1.2
    version: 2.1.2
  vue-tree-list:
    specifier: ^1.5.0
    version: 1.5.0
  vue-unique-id:
    specifier: ^3.1.5
    version: 3.2.1
  vue2-filters:
    specifier: ^0.11.1
    version: 0.11.1
  vue2-google-maps:
    specifier: ^0.10.7
    version: 0.10.7
  vue2-transitions:
    specifier: ^0.3.0
    version: 0.3.0
  vuelidate:
    specifier: ^0.7.6
    version: 0.7.7
  vuex:
    specifier: ^3.6.0
    version: 3.6.2(vue@2.7.14)
  vuex-pathify:
    specifier: ^1.4.5
    version: 1.5.1(vue@2.7.14)(vuex@3.6.2)
  vuex-persistedstate:
    specifier: ^3.2.0
    version: 3.2.1(vue@2.7.14)(vuex@3.6.2)

devDependencies:
  '@babel/core':
    specifier: ^7.12.10
    version: 7.20.2
  '@babel/eslint-parser':
    specifier: ^7.17.0
    version: 7.19.1(@babel/core@7.20.2)(eslint@6.8.0)
  '@babel/plugin-proposal-class-properties':
    specifier: ^7.12.1
    version: 7.18.6(@babel/core@7.20.2)
  '@babel/plugin-proposal-decorators':
    specifier: ^7.12.12
    version: 7.20.2(@babel/core@7.20.2)
  '@babel/plugin-proposal-export-namespace-from':
    specifier: ^7.12.1
    version: 7.18.9(@babel/core@7.20.2)
  '@babel/plugin-proposal-function-sent':
    specifier: ^7.12.1
    version: 7.18.6(@babel/core@7.20.2)
  '@babel/plugin-proposal-json-strings':
    specifier: ^7.12.1
    version: 7.18.6(@babel/core@7.20.2)
  '@babel/plugin-proposal-numeric-separator':
    specifier: ^7.12.7
    version: 7.18.6(@babel/core@7.20.2)
  '@babel/plugin-proposal-object-rest-spread':
    specifier: ^7.12.1
    version: 7.20.2(@babel/core@7.20.2)
  '@babel/plugin-proposal-throw-expressions':
    specifier: ^7.12.1
    version: 7.18.6(@babel/core@7.20.2)
  '@babel/plugin-syntax-dynamic-import':
    specifier: ^7.8.3
    version: 7.8.3(@babel/core@7.20.2)
  '@babel/plugin-syntax-import-meta':
    specifier: ^7.10.4
    version: 7.10.4(@babel/core@7.20.2)
  '@babel/plugin-transform-runtime':
    specifier: ^7.12.10
    version: 7.19.6(@babel/core@7.20.2)
  '@babel/preset-env':
    specifier: ^7.12.11
    version: 7.20.2(@babel/core@7.20.2)
  '@babel/runtime-corejs2':
    specifier: ^7.12.5
    version: 7.20.1
  '@vue/cli-plugin-unit-jest':
    specifier: ^4.5.17
    version: 4.5.19(@vue/cli-service@5.0.8)(vue-template-compiler@2.7.14)(vue@2.7.14)
  '@vue/test-utils':
    specifier: ^1.3.0
    version: 1.3.3(vue-template-compiler@2.7.14)(vue@2.7.14)
  autoprefixer:
    specifier: ^9.8.6
    version: 9.8.8
  babel-eslint:
    specifier: ^10.1.0
    version: 10.1.0(eslint@6.8.0)
  babel-loader:
    specifier: ^8.2.2
    version: 8.3.0(@babel/core@7.20.2)(webpack@4.46.0)
  brotli-webpack-plugin:
    specifier: ^1.1.0
    version: 1.1.0
  compression-webpack-plugin:
    specifier: ^4.0.1
    version: 4.0.1(webpack@4.46.0)
  css-loader:
    specifier: ^3.6.0
    version: 3.6.0(webpack@4.46.0)
  cssnano:
    specifier: ^4.1.10
    version: 4.1.11
  cypress:
    specifier: ^6.3.0
    version: 6.9.1
  eslint:
    specifier: ^6.8.0
    version: 6.8.0
  eslint-plugin-cypress:
    specifier: ^2.11.2
    version: 2.12.1(eslint@6.8.0)
  eslint-plugin-vue:
    specifier: ^6.2.2
    version: 6.2.2(eslint@6.8.0)
  file-loader:
    specifier: ^6.2.0
    version: 6.2.0(webpack@4.46.0)
  mini-css-extract-plugin:
    specifier: ^0.9.0
    version: 0.9.0(webpack@4.46.0)
  numeral:
    specifier: ^2.0.6
    version: 2.0.6
  postcss:
    specifier: ^7.0.35
    version: 7.0.39
  postcss-clean:
    specifier: ^1.2.0
    version: 1.2.2
  postcss-import:
    specifier: ^12.0.1
    version: 12.0.1
  postcss-loader:
    specifier: ^4.2.0
    version: 4.3.0(postcss@7.0.39)(webpack@4.46.0)
  sass:
    specifier: ^1.32.5
    version: 1.56.1
  sass-loader:
    specifier: ^9.0.3
    version: 9.0.3(sass@1.56.1)(webpack@4.46.0)
  sass-resources-loader:
    specifier: ^2.1.1
    version: 2.2.5
  standard:
    specifier: ^14.3.4
    version: 14.3.4
  style-loader:
    specifier: ^1.3.0
    version: 1.3.0(webpack@4.46.0)
  vue-loader:
    specifier: ^15.9.6
    version: 15.10.0(babel-core@7.0.0-bridge.0)(css-loader@3.6.0)(lodash@4.17.21)(vue-template-compiler@2.7.14)(webpack@4.46.0)
  vue-plugin-load-script:
    specifier: ^1.3.2
    version: 1.3.6
  vue-template-compiler:
    specifier: ^2.6.12
    version: 2.7.14
  webpack:
    specifier: ^4.46.0
    version: 4.46.0(webpack-cli@4.10.0)
  webpack-bundle-analyzer:
    specifier: ^4.5.0
    version: 4.7.0
  webpack-cli:
    specifier: ^4.5.0
    version: 4.10.0(webpack-bundle-analyzer@4.7.0)(webpack@4.46.0)

packages:

  /@achrinza/node-ipc@9.2.2:
    resolution: {integrity: sha512-b90U39dx0cU6emsOvy5hxU4ApNXnE3+Tuo8XQZfiKTGelDwpMwBVgBP7QX6dGTcJgu/miyJuNJ/2naFBliNWEw==}
    engines: {node: 8 || 10 || 12 || 14 || 16 || 17}
    dependencies:
      '@node-ipc/js-queue': 2.0.3
      event-pubsub: 4.3.0
      js-message: 1.0.7
    dev: true

  /@achrinza/node-ipc@9.2.6:
    resolution: {integrity: sha512-ULSIYPy4ZPM301dfCxRz0l2GJjOwIo/PqmWonIu1bLml7UmnVQmH+juJcoyXp6E8gIRRNAjGYftJnNQlfy4vPg==}
    engines: {node: 8 || 9 || 10 || 11 || 12 || 13 || 14 || 15 || 16 || 17 || 18 || 19}
    dependencies:
      '@node-ipc/js-queue': 2.0.3
      event-pubsub: 4.3.0
      js-message: 1.0.7

  /@ampproject/remapping@2.2.0:
    resolution: {integrity: sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.1.1
      '@jridgewell/trace-mapping': 0.3.17

  /@babel/code-frame@7.18.6:
    resolution: {integrity: sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.18.6

  /@babel/compat-data@7.20.1:
    resolution: {integrity: sha512-EWZ4mE2diW3QALKvDMiXnbZpRvlj+nayZ112nK93SnhqOtpdsbVD4W+2tEoT3YNBAG9RBR0ISY758ZkOgsn6pQ==}
    engines: {node: '>=6.9.0'}

  /@babel/core@7.20.2:
    resolution: {integrity: sha512-w7DbG8DtMrJcFOi4VrLm+8QM4az8Mo+PuLBKLp2zrYRCow8W/f9xiXm5sN53C8HksCyDQwCKha9JiDoIyPjT2g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.2.0
      '@babel/code-frame': 7.18.6
      '@babel/generator': 7.20.4
      '@babel/helper-compilation-targets': 7.20.0(@babel/core@7.20.2)
      '@babel/helper-module-transforms': 7.20.2
      '@babel/helpers': 7.20.1
      '@babel/parser': 7.20.3
      '@babel/template': 7.18.10
      '@babel/traverse': 7.20.1
      '@babel/types': 7.20.2
      convert-source-map: 1.9.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.1
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color

  /@babel/eslint-parser@7.19.1(@babel/core@7.20.2)(eslint@6.8.0):
    resolution: {integrity: sha512-AqNf2QWt1rtu2/1rLswy6CDP7H9Oh3mMhk177Y67Rg8d7RD9WfOLLv8CGn6tisFvS2htm86yIe1yLF6I1UDaGQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || >=14.0.0}
    peerDependencies:
      '@babel/core': '>=7.11.0'
      eslint: ^7.5.0 || ^8.0.0
    dependencies:
      '@babel/core': 7.20.2
      '@nicolo-ribaudo/eslint-scope-5-internals': 5.1.1-v1
      eslint: 6.8.0
      eslint-visitor-keys: 2.1.0
      semver: 6.3.0
    dev: true

  /@babel/generator@7.20.4:
    resolution: {integrity: sha512-luCf7yk/cm7yab6CAW1aiFnmEfBJplb/JojV56MYEK7ziWfGmFlTfmL9Ehwfy4gFhbjBfWO1wj7/TuSbVNEEtA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
      '@jridgewell/gen-mapping': 0.3.2
      jsesc: 2.5.2

  /@babel/helper-annotate-as-pure@7.18.6:
    resolution: {integrity: sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-builder-binary-assignment-operator-visitor@7.18.9:
    resolution: {integrity: sha512-yFQ0YCHoIqarl8BCRwBL8ulYUaZpz3bNsA7oFepAzee+8/+ImtADXNOmO5vJvsPff3qi+hvpkY/NYBTrBQgdNw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-explode-assignable-expression': 7.18.6
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-compilation-targets@7.20.0(@babel/core@7.20.2):
    resolution: {integrity: sha512-0jp//vDGp9e8hZzBc6N/KwA5ZK3Wsm/pfm4CrY7vzegkVxc65SgSn6wYOnwHe9Js9HRQ1YTCKLGPzDtaS3RoLQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/compat-data': 7.20.1
      '@babel/core': 7.20.2
      '@babel/helper-validator-option': 7.18.6
      browserslist: 4.21.4
      semver: 6.3.0

  /@babel/helper-create-class-features-plugin@7.20.2(@babel/core@7.20.2):
    resolution: {integrity: sha512-k22GoYRAHPYr9I+Gvy2ZQlAe5mGy8BqWst2wRt8cwIufWTxrsVshhIBvYNqC80N0GSFWTsqRVexOtfzlgOEDvA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-member-expression-to-functions': 7.18.9
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-replace-supers': 7.19.1
      '@babel/helper-split-export-declaration': 7.18.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-create-regexp-features-plugin@7.19.0(@babel/core@7.20.2):
    resolution: {integrity: sha512-htnV+mHX32DF81amCDrwIDr8nrp1PTm+3wfBN9/v8QJOLEioOCOG7qNyq0nHeFiWbT3Eb7gsPwEmV64UCQ1jzw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-annotate-as-pure': 7.18.6
      regexpu-core: 5.2.1
    dev: true

  /@babel/helper-define-polyfill-provider@0.3.3(@babel/core@7.20.2):
    resolution: {integrity: sha512-z5aQKU4IzbqCC1XH0nAqfsFLMVSo22SBKUc0BxGrLkolTdPTructy0ToNnlO2zA4j9Q/7pjMZf0DSY+DSTYzww==}
    peerDependencies:
      '@babel/core': ^7.4.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-compilation-targets': 7.20.0(@babel/core@7.20.2)
      '@babel/helper-plugin-utils': 7.20.2
      debug: 4.3.4
      lodash.debounce: 4.0.8
      resolve: 1.22.1
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-environment-visitor@7.18.9:
    resolution: {integrity: sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-explode-assignable-expression@7.18.6:
    resolution: {integrity: sha512-eyAYAsQmB80jNfg4baAtLeWAQHfHFiR483rzFK+BhETlGZaQC9bsfrugfXDCbRHLQbIA7U5NxhhOxN7p/dWIcg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-function-name@7.19.0:
    resolution: {integrity: sha512-WAwHBINyrpqywkUH0nTnNgI5ina5TFn85HKS0pbPDfxFfhyR/aNQEn4hGi1P1JyT//I0t4OgXUlofzWILRvS5w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.18.10
      '@babel/types': 7.20.2

  /@babel/helper-hoist-variables@7.18.6:
    resolution: {integrity: sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2

  /@babel/helper-member-expression-to-functions@7.18.9:
    resolution: {integrity: sha512-RxifAh2ZoVU67PyKIO4AMi1wTenGfMR/O/ae0CCRqwgBAt5v7xjdtRw7UoSbsreKrQn5t7r89eruK/9JjYHuDg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-module-imports@7.18.6:
    resolution: {integrity: sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2

  /@babel/helper-module-transforms@7.20.2:
    resolution: {integrity: sha512-zvBKyJXRbmK07XhMuujYoJ48B5yvvmM6+wcpv6Ivj4Yg6qO7NOZOSnvZN9CRl1zz1Z4cKf8YejmCMh8clOoOeA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-simple-access': 7.20.2
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/helper-validator-identifier': 7.19.1
      '@babel/template': 7.18.10
      '@babel/traverse': 7.20.1
      '@babel/types': 7.20.2
    transitivePeerDependencies:
      - supports-color

  /@babel/helper-optimise-call-expression@7.18.6:
    resolution: {integrity: sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-plugin-utils@7.20.2:
    resolution: {integrity: sha512-8RvlJG2mj4huQ4pZ+rU9lqKi9ZKiRmuvGuM2HlWmkmgOhbs6zEAw6IEiJ5cQqGbDzGZOhwuOQNtZMi/ENLjZoQ==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-remap-async-to-generator@7.18.9(@babel/core@7.20.2):
    resolution: {integrity: sha512-dI7q50YKd8BAv3VEfgg7PS7yD3Rtbi2J1XMXaalXO0W0164hYLnh8zpjRS0mte9MfVp/tltvr/cfdXPvJr1opA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-wrap-function': 7.19.0
      '@babel/types': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-replace-supers@7.19.1:
    resolution: {integrity: sha512-T7ahH7wV0Hfs46SFh5Jz3s0B6+o8g3c+7TMxu7xKfmHikg7EAZ3I2Qk9LFhjxXq8sL7UkP5JflezNwoZa8WvWw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-member-expression-to-functions': 7.18.9
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/traverse': 7.20.1
      '@babel/types': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-simple-access@7.20.2:
    resolution: {integrity: sha512-+0woI/WPq59IrqDYbVGfshjT5Dmk/nnbdpcF8SnMhhXObpTq2KNBdLFRFrkVdbDOyUmHBCxzm5FHV1rACIkIbA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2

  /@babel/helper-skip-transparent-expression-wrappers@7.20.0:
    resolution: {integrity: sha512-5y1JYeNKfvnT8sZcK9DVRtpTbGiomYIHviSP3OQWmDPU3DeH4a1ZlT/N2lyQ5P8egjcRaT/Y9aNqUxK0WsnIIg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-split-export-declaration@7.18.6:
    resolution: {integrity: sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2

  /@babel/helper-string-parser@7.19.4:
    resolution: {integrity: sha512-nHtDoQcuqFmwYNYPz3Rah5ph2p8PFeFCsZk9A/48dPc/rGocJ5J3hAAZ7pb76VWX3fZKu+uEr/FhH5jLx7umrw==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-identifier@7.19.1:
    resolution: {integrity: sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-option@7.18.6:
    resolution: {integrity: sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw==}
    engines: {node: '>=6.9.0'}

  /@babel/helper-wrap-function@7.19.0:
    resolution: {integrity: sha512-txX8aN8CZyYGTwcLhlk87KRqncAzhh5TpQamZUa0/u3an36NtDpUP6bQgBCBcLeBs09R/OwQu3OjK0k/HwfNDg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-function-name': 7.19.0
      '@babel/template': 7.18.10
      '@babel/traverse': 7.20.1
      '@babel/types': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helpers@7.20.1:
    resolution: {integrity: sha512-J77mUVaDTUJFZ5BpP6mMn6OIl3rEWymk2ZxDBQJUG3P+PbmyMcF3bYWvz0ma69Af1oobDqT/iAsvzhB58xhQUg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.18.10
      '@babel/traverse': 7.20.1
      '@babel/types': 7.20.2
    transitivePeerDependencies:
      - supports-color

  /@babel/highlight@7.18.6:
    resolution: {integrity: sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.19.1
      chalk: 2.4.2
      js-tokens: 4.0.0

  /@babel/parser@7.20.3:
    resolution: {integrity: sha512-OP/s5a94frIPXwjzEcv5S/tpQfc6XhxYUnmWpgdqMWGgYCuErA3SzozaRAMQgSZWKeTJxht9aWAkUY+0UzvOFg==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.20.2

  /@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-Dgxsyg54Fx1d4Nge8UnvTrED63vrwOdPmyvPzlNN/boaliRP54pm3pGzZD1SJUwrBA+Cs/xdG8kXX6Mn/RfISQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.18.9(@babel/core@7.20.2):
    resolution: {integrity: sha512-AHrP9jadvH7qlOj6PINbgSuphjQUAK7AOT7DPjBo9EHoLhQTnnK5u45e1Hd4DbSQEO9nqPWtQ89r+XEOWFScKg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0
      '@babel/plugin-proposal-optional-chaining': 7.18.9(@babel/core@7.20.2)
    dev: true

  /@babel/plugin-proposal-async-generator-functions@7.20.1(@babel/core@7.20.2):
    resolution: {integrity: sha512-Gh5rchzSwE4kC+o/6T8waD0WHEQIsDmjltY8WnWRXHUdH8axZhuH86Ov9M72YhJfDrZseQwuuWaaIT/TmePp3g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-remap-async-to-generator': 7.18.9(@babel/core@7.20.2)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.20.2)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-class-properties@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-create-class-features-plugin': 7.20.2(@babel/core@7.20.2)
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-class-static-block@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-+I3oIiNxrCpup3Gi8n5IGMwj0gOCAjcJUSQEcotNnCCPMEnixawOQ+KeJPlgfjzx+FKQ1QSyZOWe7wmoJp7vhw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-create-class-features-plugin': 7.20.2(@babel/core@7.20.2)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.20.2)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-decorators@7.20.2(@babel/core@7.20.2):
    resolution: {integrity: sha512-nkBH96IBmgKnbHQ5gXFrcmez+Z9S2EIDKDQGp005ROqBigc88Tky4rzCnlP/lnlj245dCEQl4/YyV0V1kYh5dw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-create-class-features-plugin': 7.20.2(@babel/core@7.20.2)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-replace-supers': 7.19.1
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/plugin-syntax-decorators': 7.19.0(@babel/core@7.20.2)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-dynamic-import@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-1auuwmK+Rz13SJj36R+jqFPMJWyKEDd7lLSdOj4oJK0UTgGueSAtkrCvz9ewmgyU/P941Rv2fQwZJN8s6QruXw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.20.2)
    dev: true

  /@babel/plugin-proposal-export-namespace-from@7.18.9(@babel/core@7.20.2):
    resolution: {integrity: sha512-k1NtHyOMvlDDFeb9G5PhUXuGj8m/wiwojgQVEhJ/fsVsMCpLyOP4h0uGEjYJKrRI+EVPlb5Jk+Gt9P97lOGwtA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.20.2)
    dev: true

  /@babel/plugin-proposal-function-sent@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-UdaOKPOLPt0O+Xu26tnw6oAZMLXhk+yMrXOzn6kAzTHBnWHJsoN1hlrgxFAQ+FRLS0ql1oYIQ2phvoFzmN3GMw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-wrap-function': 7.19.0
      '@babel/plugin-syntax-function-sent': 7.18.6(@babel/core@7.20.2)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-json-strings@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-lr1peyn9kOdbYc0xr0OdHTZ5FMqS6Di+H0Fz2I/JwMzGmzJETNeOFq2pBySw6X/KFL5EWDjlJuMsUGRFb8fQgQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.20.2)
    dev: true

  /@babel/plugin-proposal-logical-assignment-operators@7.18.9(@babel/core@7.20.2):
    resolution: {integrity: sha512-128YbMpjCrP35IOExw2Fq+x55LMP42DzhOhX2aNNIdI9avSWl2PI0yuBWarr3RYpZBSPtabfadkH2yeRiMD61Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.20.2)
    dev: true

  /@babel/plugin-proposal-nullish-coalescing-operator@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.20.2)
    dev: true

  /@babel/plugin-proposal-numeric-separator@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-ozlZFogPqoLm8WBr5Z8UckIoE4YQ5KESVcNudyXOR8uqIkliTEgJ3RoketfG6pmzLdeZF0H/wjE9/cCEitBl7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.20.2)
    dev: true

  /@babel/plugin-proposal-object-rest-spread@7.20.2(@babel/core@7.20.2):
    resolution: {integrity: sha512-Ks6uej9WFK+fvIMesSqbAto5dD8Dz4VuuFvGJFKgIGSkJuRGcrwGECPA1fDgQK3/DbExBJpEkTeYeB8geIFCSQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.20.1
      '@babel/core': 7.20.2
      '@babel/helper-compilation-targets': 7.20.0(@babel/core@7.20.2)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.20.2)
      '@babel/plugin-transform-parameters': 7.20.3(@babel/core@7.20.2)
    dev: true

  /@babel/plugin-proposal-optional-catch-binding@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-Q40HEhs9DJQyaZfUjjn6vE8Cv4GmMHCYuMGIWUnlxH6400VGxOuwWsPt4FxXxJkC/5eOzgn0z21M9gMT4MOhbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.20.2)
    dev: true

  /@babel/plugin-proposal-optional-chaining@7.18.9(@babel/core@7.20.2):
    resolution: {integrity: sha512-v5nwt4IqBXihxGsW2QmCWMDS3B3bzGIk/EQVZz2ei7f3NJl8NzAJVvUmpDW5q1CRNY+Beb/k58UAH1Km1N411w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.20.2)
    dev: true

  /@babel/plugin-proposal-private-methods@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-nutsvktDItsNn4rpGItSNV2sz1XwS+nfU0Rg8aCx3W3NOKVzdMjJRu0O5OkgDp3ZGICSTbgRpxZoWsxoKRvbeA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-create-class-features-plugin': 7.20.2(@babel/core@7.20.2)
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-private-property-in-object@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-9Rysx7FOctvT5ouj5JODjAFAkgGoudQuLPamZb0v1TGLpapdNaftzifU8NTWQm0IRjqoYypdrSmyWgkocDQ8Dw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-create-class-features-plugin': 7.20.2(@babel/core@7.20.2)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.20.2)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-throw-expressions@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-WHOrJyhGoGrdtW480L79cF7Iq/gZDZ/z6OqK7mVyFR5I37dTpog/wNgb6hmaM3HYZtULEJl++7VaMWkNZsOcHg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-throw-expressions': 7.18.6(@babel/core@7.20.2)
    dev: true

  /@babel/plugin-proposal-unicode-property-regex@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w==}
    engines: {node: '>=4'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-create-regexp-features-plugin': 7.19.0(@babel/core@7.20.2)
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.20.2):
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.20.2):
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.20.2):
    resolution: {integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-decorators@7.19.0(@babel/core@7.20.2):
    resolution: {integrity: sha512-xaBZUEDntt4faL1yN8oIFlhfXeQAWJW7CLKYsHTUqriCUbj8xOra8bfxxKGi/UwExPFBuPdH4XfHc9rGQhrVkQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.20.2):
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.20.2):
    resolution: {integrity: sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-function-sent@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-f3OJHIlFIkg+cP1Hfo2SInLhsg0pz2Ikmgo7jMdIIKC+3jVXQlHB0bgSapOWxeWI0SU28qIWmfn5ZKu1yPJHkg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-import-assertions@7.20.0(@babel/core@7.20.2):
    resolution: {integrity: sha512-IUh1vakzNoWalR8ch/areW7qFopR2AEw03JlG7BbrDqmQ4X3q9uuipQwSGrUn7oGiemKjtSLDhNtQHzMHr1JdQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.20.2):
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.20.2):
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.20.2):
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.20.2):
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.20.2):
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.20.2):
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2

  /@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.20.2):
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.20.2):
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.20.2):
    resolution: {integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-throw-expressions@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-rp1CqEZXGv1z1YZ3qYffBH3rhnOxrTwQG8fh2yqulTurwv9zu3Gthfd+niZBLSOi1rY6146TgF+JmVeDXaX4TQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.20.2):
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-arrow-functions@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-9S9X9RUefzrsHZmKMbDXxweEH+YlE8JJEuat9FdvW9Qh1cw7W64jELCtWNkPBPX5En45uy28KGvA/AySqUh8CQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-async-to-generator@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-ARE5wZLKnTgPW7/1ftQmSi1CmkqqHo2DNmtztFhvgtOWSDfq0Cq9/9L+KnZNYSNrydBekhW3rwShduf59RoXag==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-remap-async-to-generator': 7.18.9(@babel/core@7.20.2)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-block-scoped-functions@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-ExUcOqpPWnliRcPqves5HJcJOvHvIIWfuS4sroBUenPuMdmW+SMHDakmtS7qOo13sVppmUijqeTv7qqGsvURpQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-block-scoping@7.20.2(@babel/core@7.20.2):
    resolution: {integrity: sha512-y5V15+04ry69OV2wULmwhEA6jwSWXO1TwAtIwiPXcvHcoOQUqpyMVd2bDsQJMW8AurjulIyUV8kDqtjSwHy1uQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-classes@7.20.2(@babel/core@7.20.2):
    resolution: {integrity: sha512-9rbPp0lCVVoagvtEyQKSo5L8oo0nQS/iif+lwlAz29MccX2642vWDlSZK+2T2buxbopotId2ld7zZAzRfz9j1g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-compilation-targets': 7.20.0(@babel/core@7.20.2)
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-replace-supers': 7.19.1
      '@babel/helper-split-export-declaration': 7.18.6
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-computed-properties@7.18.9(@babel/core@7.20.2):
    resolution: {integrity: sha512-+i0ZU1bCDymKakLxn5srGHrsAPRELC2WIbzwjLhHW9SIE1cPYkLCL0NlnXMZaM1vhfgA2+M7hySk42VBvrkBRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-destructuring@7.20.2(@babel/core@7.20.2):
    resolution: {integrity: sha512-mENM+ZHrvEgxLTBXUiQ621rRXZes3KWUv6NdQlrnr1TkWVw+hUjQBZuP2X32qKlrlG2BzgR95gkuCRSkJl8vIw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-dotall-regex@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-6S3jpun1eEbAxq7TdjLotAsl4WpQI9DxfkycRcKrjhQYzU87qpXdknpBg/e+TdcMehqGnLFi7tnFUBR02Vq6wg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-create-regexp-features-plugin': 7.19.0(@babel/core@7.20.2)
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-duplicate-keys@7.18.9(@babel/core@7.20.2):
    resolution: {integrity: sha512-d2bmXCtZXYc59/0SanQKbiWINadaJXqtvIQIzd4+hNwkWBgyCd5F/2t1kXoUdvPMrxzPvhK6EMQRROxsue+mfw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-exponentiation-operator@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-wzEtc0+2c88FVR34aQmiz56dxEkxr2g8DQb/KfaFa1JYXOFVsbhvAonFN6PwVWj++fKmku8NP80plJ5Et4wqHw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.18.9
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-for-of@7.18.8(@babel/core@7.20.2):
    resolution: {integrity: sha512-yEfTRnjuskWYo0k1mHUqrVWaZwrdq8AYbfrpqULOJOaucGSp4mNMVps+YtA8byoevxS/urwU75vyhQIxcCgiBQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-function-name@7.18.9(@babel/core@7.20.2):
    resolution: {integrity: sha512-WvIBoRPaJQ5yVHzcnJFor7oS5Ls0PYixlTYE63lCj2RtdQEl15M68FXQlxnG6wdraJIXRdR7KI+hQ7q/9QjrCQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-compilation-targets': 7.20.0(@babel/core@7.20.2)
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-literals@7.18.9(@babel/core@7.20.2):
    resolution: {integrity: sha512-IFQDSRoTPnrAIrI5zoZv73IFeZu2dhu6irxQjY9rNjTT53VmKg9fenjvoiOWOkJ6mm4jKVPtdMzBY98Fp4Z4cg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-member-expression-literals@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-qSF1ihLGO3q+/g48k85tUjD033C29TNTVB2paCwZPVmOsjn9pClvYYrM2VeJpBY2bcNkuny0YUyTNRyRxJ54KA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-modules-amd@7.19.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-uG3od2mXvAtIFQIh0xrpLH6r5fpSQN04gIVovl+ODLdUMANokxQLZnPBHcjmv3GxRjnqwLuHvppjjcelqUFZvg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-module-transforms': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-modules-commonjs@7.19.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-8PIa1ym4XRTKuSsOUXqDG0YaOlEuTVvHMe5JCfgBMOtHvJKw/4NGovEGN33viISshG/rZNVrACiBmPQLvWN8xQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-module-transforms': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-simple-access': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-modules-systemjs@7.19.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-fqGLBepcc3kErfR9R3DnVpURmckXP7gj7bAlrTQyBxrigFqszZCkFkcoxzCp2v32XmwXLvbw+8Yq9/b+QqksjQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-module-transforms': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-validator-identifier': 7.19.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-modules-umd@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-dcegErExVeXcRqNtkRU/z8WlBLnvD4MRnHgNs3MytRO1Mn1sHRyhbcpYbVMGclAqOjdW+9cfkdZno9dFdfKLfQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-module-transforms': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-named-capturing-groups-regex@7.19.1(@babel/core@7.20.2):
    resolution: {integrity: sha512-oWk9l9WItWBQYS4FgXD4Uyy5kq898lvkXpXQxoJEY1RnvPk4R/Dvu2ebXU9q8lP+rlMwUQTFf2Ok6d78ODa0kw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-create-regexp-features-plugin': 7.19.0(@babel/core@7.20.2)
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-new-target@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-DjwFA/9Iu3Z+vrAn+8pBUGcjhxKguSMlsFqeCKbhb9BAV756v0krzVK04CRDi/4aqmk8BsHb4a/gFcaA5joXRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-object-super@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-uvGz6zk+pZoS1aTZrOvrbj6Pp/kK2mp45t2B+bTDre2UgsZZ8EZLSJtUg7m/no0zOJUWgFONpB7Zv9W2tSaFlA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-replace-supers': 7.19.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-parameters@7.20.3(@babel/core@7.20.2):
    resolution: {integrity: sha512-oZg/Fpx0YDrj13KsLyO8I/CX3Zdw7z0O9qOd95SqcoIzuqy/WTGWvePeHAnZCN54SfdyjHcb1S30gc8zlzlHcA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-property-literals@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-cYcs6qlgafTud3PAzrrRNbQtfpQ8+y/+M5tKmksS9+M1ckbH6kzY8MrexEM9mcA6JDsukE19iIRvAyYl463sMg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-regenerator@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-poqRI2+qiSdeldcz4wTSTXBRryoq3Gc70ye7m7UD5Ww0nE29IXqMl6r7Nd15WBgRd74vloEMlShtH6CKxVzfmQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      regenerator-transform: 0.15.0
    dev: true

  /@babel/plugin-transform-reserved-words@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-oX/4MyMoypzHjFrT1CdivfKZ+XvIPMFXwwxHp/r0Ddy2Vuomt4HDFGmft1TAY2yiTKiNSsh3kjBAzcM8kSdsjA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-runtime@7.19.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-PRH37lz4JU156lYFW1p8OxE5i7d6Sl/zV58ooyr+q1J1lnQPyg5tIiXlIwNVhJaY4W3TmOtdc8jqdXQcB1v5Yw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-plugin-utils': 7.20.2
      babel-plugin-polyfill-corejs2: 0.3.3(@babel/core@7.20.2)
      babel-plugin-polyfill-corejs3: 0.6.0(@babel/core@7.20.2)
      babel-plugin-polyfill-regenerator: 0.4.1(@babel/core@7.20.2)
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-shorthand-properties@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-eCLXXJqv8okzg86ywZJbRn19YJHU4XUa55oz2wbHhaQVn/MM+XhukiT7SYqp/7o00dg52Rj51Ny+Ecw4oyoygw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-spread@7.19.0(@babel/core@7.20.2):
    resolution: {integrity: sha512-RsuMk7j6n+r752EtzyScnWkQyuJdli6LdO5Klv8Yx0OfPVTcQkIUfS8clx5e9yHXzlnhOZF3CbQ8C2uP5j074w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0
    dev: true

  /@babel/plugin-transform-sticky-regex@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-kfiDrDQ+PBsQDO85yj1icueWMfGfJFKN1KCkndygtu/C9+XUfydLC8Iv5UYJqRwy4zk8EcplRxEOeLyjq1gm6Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-template-literals@7.18.9(@babel/core@7.20.2):
    resolution: {integrity: sha512-S8cOWfT82gTezpYOiVaGHrCbhlHgKhQt8XH5ES46P2XWmX92yisoZywf5km75wv5sYcXDUCLMmMxOLCtthDgMA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-typeof-symbol@7.18.9(@babel/core@7.20.2):
    resolution: {integrity: sha512-SRfwTtF11G2aemAZWivL7PD+C9z52v9EvMqH9BuYbabyPuKUvSWks3oCg6041pT925L4zVFqaVBeECwsmlguEw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-unicode-escapes@7.18.10(@babel/core@7.20.2):
    resolution: {integrity: sha512-kKAdAI+YzPgGY/ftStBFXTI1LZFju38rYThnfMykS+IXy8BVx+res7s2fxf1l8I35DV2T97ezo6+SGrXz6B3iQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-unicode-regex@7.18.6(@babel/core@7.20.2):
    resolution: {integrity: sha512-gE7A6Lt7YLnNOL3Pb9BNeZvi+d8l7tcRrG4+pwJjK9hD2xX4mEvjlQW60G9EEmfXVYRPv9VRQcyegIVHCql/AA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-create-regexp-features-plugin': 7.19.0(@babel/core@7.20.2)
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/preset-env@7.20.2(@babel/core@7.20.2):
    resolution: {integrity: sha512-1G0efQEWR1EHkKvKHqbG+IN/QdgwfByUpM5V5QroDzGV2t3S/WXNQd693cHiHTlCFMpr9B6FkPFXDA2lQcKoDg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.20.1
      '@babel/core': 7.20.2
      '@babel/helper-compilation-targets': 7.20.0(@babel/core@7.20.2)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-validator-option': 7.18.6
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.18.9(@babel/core@7.20.2)
      '@babel/plugin-proposal-async-generator-functions': 7.20.1(@babel/core@7.20.2)
      '@babel/plugin-proposal-class-properties': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-proposal-class-static-block': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-proposal-dynamic-import': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-proposal-export-namespace-from': 7.18.9(@babel/core@7.20.2)
      '@babel/plugin-proposal-json-strings': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-proposal-logical-assignment-operators': 7.18.9(@babel/core@7.20.2)
      '@babel/plugin-proposal-nullish-coalescing-operator': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-proposal-numeric-separator': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-proposal-object-rest-spread': 7.20.2(@babel/core@7.20.2)
      '@babel/plugin-proposal-optional-catch-binding': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-proposal-optional-chaining': 7.18.9(@babel/core@7.20.2)
      '@babel/plugin-proposal-private-methods': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-proposal-private-property-in-object': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.20.2)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.20.2)
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.20.2)
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.20.2)
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.20.2)
      '@babel/plugin-syntax-import-assertions': 7.20.0(@babel/core@7.20.2)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.20.2)
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.20.2)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.20.2)
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.20.2)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.20.2)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.20.2)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.20.2)
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.20.2)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.20.2)
      '@babel/plugin-transform-arrow-functions': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-async-to-generator': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-block-scoped-functions': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-block-scoping': 7.20.2(@babel/core@7.20.2)
      '@babel/plugin-transform-classes': 7.20.2(@babel/core@7.20.2)
      '@babel/plugin-transform-computed-properties': 7.18.9(@babel/core@7.20.2)
      '@babel/plugin-transform-destructuring': 7.20.2(@babel/core@7.20.2)
      '@babel/plugin-transform-dotall-regex': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-duplicate-keys': 7.18.9(@babel/core@7.20.2)
      '@babel/plugin-transform-exponentiation-operator': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-for-of': 7.18.8(@babel/core@7.20.2)
      '@babel/plugin-transform-function-name': 7.18.9(@babel/core@7.20.2)
      '@babel/plugin-transform-literals': 7.18.9(@babel/core@7.20.2)
      '@babel/plugin-transform-member-expression-literals': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-modules-amd': 7.19.6(@babel/core@7.20.2)
      '@babel/plugin-transform-modules-commonjs': 7.19.6(@babel/core@7.20.2)
      '@babel/plugin-transform-modules-systemjs': 7.19.6(@babel/core@7.20.2)
      '@babel/plugin-transform-modules-umd': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.19.1(@babel/core@7.20.2)
      '@babel/plugin-transform-new-target': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-object-super': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-parameters': 7.20.3(@babel/core@7.20.2)
      '@babel/plugin-transform-property-literals': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-regenerator': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-reserved-words': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-shorthand-properties': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-spread': 7.19.0(@babel/core@7.20.2)
      '@babel/plugin-transform-sticky-regex': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-template-literals': 7.18.9(@babel/core@7.20.2)
      '@babel/plugin-transform-typeof-symbol': 7.18.9(@babel/core@7.20.2)
      '@babel/plugin-transform-unicode-escapes': 7.18.10(@babel/core@7.20.2)
      '@babel/plugin-transform-unicode-regex': 7.18.6(@babel/core@7.20.2)
      '@babel/preset-modules': 0.1.5(@babel/core@7.20.2)
      '@babel/types': 7.20.2
      babel-plugin-polyfill-corejs2: 0.3.3(@babel/core@7.20.2)
      babel-plugin-polyfill-corejs3: 0.6.0(@babel/core@7.20.2)
      babel-plugin-polyfill-regenerator: 0.4.1(@babel/core@7.20.2)
      core-js-compat: 3.26.0
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/preset-modules@0.1.5(@babel/core@7.20.2):
    resolution: {integrity: sha512-A57th6YRG7oR3cq/yt/Y84MvGgE0eJG2F1JLhKuyG+jFxEgrd/HAMJatiFtmOiZurz+0DkrvbheCLaV5f2JfjA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6(@babel/core@7.20.2)
      '@babel/plugin-transform-dotall-regex': 7.18.6(@babel/core@7.20.2)
      '@babel/types': 7.20.2
      esutils: 2.0.3
    dev: true

  /@babel/runtime-corejs2@7.20.1:
    resolution: {integrity: sha512-9hxzcvnXxG38gZ3ycsZ91+PNbpWwPVXmTsKaaS6mvJyXs49XziitKsj2SHCpTVwcedRc3hmYrzjGSs+9aAQe7Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.13.10
    dev: true

  /@babel/runtime@7.20.1:
    resolution: {integrity: sha512-mrzLkl6U9YLF8qpqI7TB82PESyEGjm/0Ly91jG575eVxMMlb8fYfOXFZIJ8XfLrJZQbm7dlKry2bJmXBUEkdFg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.13.10

  /@babel/template@7.18.10:
    resolution: {integrity: sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/parser': 7.20.3
      '@babel/types': 7.20.2

  /@babel/traverse@7.20.1:
    resolution: {integrity: sha512-d3tN8fkVJwFLkHkBN479SOsw4DMZnz8cdbL/gvuDuzy3TS6Nfw80HuQqhw1pITbIruHyh7d1fMA47kWzmcUEGA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/generator': 7.20.4
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/parser': 7.20.3
      '@babel/types': 7.20.2
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  /@babel/types@7.20.2:
    resolution: {integrity: sha512-FnnvsNWgZCr232sqtXggapvlkk/tuwR/qhGzcmxI0GXLCjmPYQPzio2FbdlWuY6y1sHFfQKk+rRbUZ9VStQMog==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.19.4
      '@babel/helper-validator-identifier': 7.19.1
      to-fast-properties: 2.0.0

  /@carsaver/body-style-icon@1.0.9:
    resolution: {integrity: sha512-vsVdmXVWKfo4onkY8To5SofXk9hF6e3YtQv5PvbWwlxq0FoSpaepXGGN6M7Vi/C+n7iwn97fTRlT8uQQFGO3Kw==, tarball: https://carsaver.jfrog.io/carsaver/api/npm/npm-local/@carsaver/body-style-icon/-/@carsaver/body-style-icon-1.0.9.tgz}
    dev: false

  /@cnakazawa/watch@1.0.4:
    resolution: {integrity: sha512-v9kIhKwjeZThiWrLmj0y17CWoyddASLj9O2yvbZkbvw/N3rWOYy9zkV66ursAoVr0mV15bL8g0c4QZUE6cdDoQ==}
    engines: {node: '>=0.1.95'}
    hasBin: true
    dependencies:
      exec-sh: 0.3.6
      minimist: 1.2.7

  /@colors/colors@1.5.0:
    resolution: {integrity: sha512-ooWCrlZP11i8GImSjTHYHLkvFDP48nS4+204nGb1RiX/WXYHmJA2III9/e2DWVabCESdW7hBAEzHRqUn9OUVvQ==}
    engines: {node: '>=0.1.90'}
    requiresBuild: true
    dev: true
    optional: true

  /@cypress/listr-verbose-renderer@0.4.1:
    resolution: {integrity: sha512-EDiBsVPWC27DDLEJCo+*******************************************************************==}
    engines: {node: '>=4'}
    dependencies:
      chalk: 1.1.3
      cli-cursor: 1.0.2
      date-fns: 1.30.1
      figures: 1.7.0
    dev: true

  /@cypress/request@2.88.10:
    resolution: {integrity: sha512-Zp7F+R93N0yZyG34GutyTNr+okam7s/Fzc1+i3kcqOP8vk6OuajuE9qZJ6Rs+10/1JFtXFYMdyarnU1rZuJesg==}
    engines: {node: '>= 6'}
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.11.0
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      http-signature: 1.3.6
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 8.3.2
    dev: true

  /@cypress/xvfb@1.2.4(supports-color@7.2.0):
    resolution: {integrity: sha512-skbBzPggOVYCbnGgV+0dmBdW/s77ZkAOXIC1knS8NagwDjBrNC1LuXtQJeiN6l+m7lzmHtaoUw/ctJKdqkG57Q==}
    dependencies:
      debug: 3.2.7(supports-color@7.2.0)
      lodash.once: 4.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@discoveryjs/json-ext@0.5.7:
    resolution: {integrity: sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==}
    engines: {node: '>=10.0.0'}

  /@gar/promisify@1.1.3:
    resolution: {integrity: sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==}
    dev: true

  /@hapi/address@2.1.4:
    resolution: {integrity: sha512-QD1PhQk+s31P1ixsX0H0Suoupp3VMXzIVMSwobR3F3MSUO2YCV0B7xqLcUw/Bh8yuvd3LhpyqLQWTNcRmp6IdQ==}
    deprecated: Moved to 'npm install @sideway/address'
    dev: true

  /@hapi/bourne@1.3.2:
    resolution: {integrity: sha512-1dVNHT76Uu5N3eJNTYcvxee+jzX4Z9lfciqRRHCU27ihbUcYi+iSc2iml5Ke1LXe1SyJCLA0+14Jh4tXJgOppA==}
    deprecated: This version has been deprecated and is no longer supported or maintained
    dev: true

  /@hapi/hoek@8.5.1:
    resolution: {integrity: sha512-yN7kbciD87WzLGc5539Tn0sApjyiGHAJgKvG9W8C7O+6c7qmoQMfVs0W4bX17eqz6C78QJqqFrtgdK5EWf6Qow==}
    deprecated: This version has been deprecated and is no longer supported or maintained
    dev: true

  /@hapi/hoek@9.3.0:
    resolution: {integrity: sha512-/c6rf4UJlmHlC9b5BaNvzAcFv7HZ2QHaV0D4/HNlBdvFnvQq8RI4kYdhyPCl7Xj+oWvTWQ8ujhqS53LIgAe6KQ==}

  /@hapi/joi@15.1.1:
    resolution: {integrity: sha512-entf8ZMOK8sc+8YfeOlM8pCfg3b5+WZIKBfUaaJT8UsjAAPjartzxIYm3TIbjvA4u+u++KbcXD38k682nVHDAQ==}
    deprecated: Switch to 'npm install joi'
    dependencies:
      '@hapi/address': 2.1.4
      '@hapi/bourne': 1.3.2
      '@hapi/hoek': 8.5.1
      '@hapi/topo': 3.1.6
    dev: true

  /@hapi/topo@3.1.6:
    resolution: {integrity: sha512-tAag0jEcjwH+P2quUfipd7liWCNX2F8NvYjQp2wtInsZxnMlypdw0FtAOLxtvvkO+GSRRbmNi8m/5y42PQJYCQ==}
    deprecated: This version has been deprecated and is no longer supported or maintained
    dependencies:
      '@hapi/hoek': 8.5.1
    dev: true

  /@hapi/topo@5.1.0:
    resolution: {integrity: sha512-foQZKJig7Ob0BMAYBfcJk8d77QtOe7Wo4ox7ff1lQYoNNAb6jwcY1ncdoy2e9wQZzvNy7ODZCYJkK8kzmcAnAg==}
    dependencies:
      '@hapi/hoek': 9.3.0

  /@jest/console@24.9.0:
    resolution: {integrity: sha512-Zuj6b8TnKXi3q4ymac8EQfc3ea/uhLeCGThFqXeC8H9/raaH8ARPUTdId+XyGd03Z4In0/VjD2OYFcBF09fNLQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/source-map': 24.9.0
      chalk: 2.4.2
      slash: 2.0.0

  /@jest/core@24.9.0:
    resolution: {integrity: sha512-Fogg3s4wlAr1VX7q+rhV9RVnUv5tD7VuWfYy1+whMiWUrvl7U3QJSJyWcDio9Lq2prqYsZaeTv2Rz24pWGkJ2A==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/console': 24.9.0
      '@jest/reporters': 24.9.0
      '@jest/test-result': 24.9.0
      '@jest/transform': 24.9.0
      '@jest/types': 24.9.0
      ansi-escapes: 3.2.0
      chalk: 2.4.2
      exit: 0.1.2
      graceful-fs: 4.2.10
      jest-changed-files: 24.9.0
      jest-config: 24.9.0
      jest-haste-map: 24.9.0
      jest-message-util: 24.9.0
      jest-regex-util: 24.9.0
      jest-resolve: 24.9.0
      jest-resolve-dependencies: 24.9.0
      jest-runner: 24.9.0
      jest-runtime: 24.9.0
      jest-snapshot: 24.9.0
      jest-util: 24.9.0
      jest-validate: 24.9.0
      jest-watcher: 24.9.0
      micromatch: 3.1.10
      p-each-series: 1.0.0
      realpath-native: 1.1.0
      rimraf: 2.7.1
      slash: 2.0.0
      strip-ansi: 5.2.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  /@jest/environment@24.9.0:
    resolution: {integrity: sha512-5A1QluTPhvdIPFYnO3sZC3smkNeXPVELz7ikPbhUj0bQjB07EoE9qtLrem14ZUYWdVayYbsjVwIiL4WBIMV4aQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/fake-timers': 24.9.0
      '@jest/transform': 24.9.0
      '@jest/types': 24.9.0
      jest-mock: 24.9.0
    transitivePeerDependencies:
      - supports-color

  /@jest/fake-timers@24.9.0:
    resolution: {integrity: sha512-eWQcNa2YSwzXWIMC5KufBh3oWRIijrQFROsIqt6v/NS9Io/gknw1jsAC9c+ih/RQX4A3O7SeWAhQeN0goKhT9A==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      jest-message-util: 24.9.0
      jest-mock: 24.9.0
    transitivePeerDependencies:
      - supports-color

  /@jest/reporters@24.9.0:
    resolution: {integrity: sha512-mu4X0yjaHrffOsWmVLzitKmmmWSQ3GGuefgNscUSWNiUNcEOSEQk9k3pERKEQVBb0Cnn88+UESIsZEMH3o88Gw==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/environment': 24.9.0
      '@jest/test-result': 24.9.0
      '@jest/transform': 24.9.0
      '@jest/types': 24.9.0
      chalk: 2.4.2
      exit: 0.1.2
      glob: 7.2.3
      istanbul-lib-coverage: 2.0.5
      istanbul-lib-instrument: 3.3.0
      istanbul-lib-report: 2.0.8
      istanbul-lib-source-maps: 3.0.6
      istanbul-reports: 2.2.7
      jest-haste-map: 24.9.0
      jest-resolve: 24.9.0
      jest-runtime: 24.9.0
      jest-util: 24.9.0
      jest-worker: 24.9.0
      node-notifier: 5.4.5
      slash: 2.0.0
      source-map: 0.6.1
      string-length: 2.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  /@jest/source-map@24.9.0:
    resolution: {integrity: sha512-/Xw7xGlsZb4MJzNDgB7PW5crou5JqWiBQaz6xyPd3ArOg2nfn/PunV8+olXbbEZzNl591o5rWKE9BRDaFAuIBg==}
    engines: {node: '>= 6'}
    dependencies:
      callsites: 3.1.0
      graceful-fs: 4.2.10
      source-map: 0.6.1

  /@jest/test-result@24.9.0:
    resolution: {integrity: sha512-XEFrHbBonBJ8dGp2JmF8kP/nQI/ImPpygKHwQ/SY+es59Z3L5PI4Qb9TQQMAEeYsThG1xF0k6tmG0tIKATNiiA==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/console': 24.9.0
      '@jest/types': 24.9.0
      '@types/istanbul-lib-coverage': 2.0.4

  /@jest/test-sequencer@24.9.0:
    resolution: {integrity: sha512-6qqsU4o0kW1dvA95qfNog8v8gkRN9ph6Lz7r96IvZpHdNipP2cBcb07J1Z45mz/VIS01OHJ3pY8T5fUY38tg4A==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/test-result': 24.9.0
      jest-haste-map: 24.9.0
      jest-runner: 24.9.0
      jest-runtime: 24.9.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  /@jest/transform@24.9.0:
    resolution: {integrity: sha512-TcQUmyNRxV94S0QpMOnZl0++6RMiqpbH/ZMccFB/amku6Uwvyb1cjYX7xkp5nGNkbX4QPH/FcB6q1HBTHynLmQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@babel/core': 7.20.2
      '@jest/types': 24.9.0
      babel-plugin-istanbul: 5.2.0
      chalk: 2.4.2
      convert-source-map: 1.9.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: 4.2.10
      jest-haste-map: 24.9.0
      jest-regex-util: 24.9.0
      jest-util: 24.9.0
      micromatch: 3.1.10
      pirates: 4.0.5
      realpath-native: 1.1.0
      slash: 2.0.0
      source-map: 0.6.1
      write-file-atomic: 2.4.1
    transitivePeerDependencies:
      - supports-color

  /@jest/types@24.9.0:
    resolution: {integrity: sha512-XKK7ze1apu5JWQ5eZjHITP66AX+QsLlbaJRBGYr8pNzwcAE2JVkwnf0yqjHTsDRcjR0mujy/NmZMXw5kl+kGBw==}
    engines: {node: '>= 6'}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.4
      '@types/istanbul-reports': 1.1.2
      '@types/yargs': 13.0.12

  /@jridgewell/gen-mapping@0.1.1:
    resolution: {integrity: sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.14

  /@jridgewell/gen-mapping@0.3.2:
    resolution: {integrity: sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.14
      '@jridgewell/trace-mapping': 0.3.17

  /@jridgewell/resolve-uri@3.1.0:
    resolution: {integrity: sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/set-array@1.1.2:
    resolution: {integrity: sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/source-map@0.3.2:
    resolution: {integrity: sha512-m7O9o2uR8k2ObDysZYzdfhb08VuEml5oWGiosa1VdaPZ/A6QyPkAJuwN0Q1lhULOf6B7MtQmHENS743hWtCrgw==}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.2
      '@jridgewell/trace-mapping': 0.3.17

  /@jridgewell/sourcemap-codec@1.4.14:
    resolution: {integrity: sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==}

  /@jridgewell/trace-mapping@0.3.17:
    resolution: {integrity: sha512-MCNzAp77qzKca9+W/+I0+sEpaUnZoeasnghNeVc41VZCEKaCH73Vq3BZZ/SzWIgrqE4H4ceI+p+b6C0mHf9T4g==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.0
      '@jridgewell/sourcemap-codec': 1.4.14

  /@leichtgewicht/ip-codec@2.0.4:
    resolution: {integrity: sha512-Hcv+nVC0kZnQ3tD9GVu5xSMR4VVYOteQIr/hwFPVEvPdlXqgGEuRjiheChHgdM+JyqdgNcmzZOX/tnl0JOiI7A==}

  /@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1:
    resolution: {integrity: sha512-54/JRvkLIzzDWshCWfuhadfrfZVPiElY8Fcgmg1HroEly/EDSszzhBAsarCux+D/kOslTRquNzuyGSmUSTTHGg==}
    dependencies:
      eslint-scope: 5.1.1
    dev: true

  /@node-ipc/js-queue@2.0.3:
    resolution: {integrity: sha512-fL1wpr8hhD5gT2dA1qifeVaoDFlQR5es8tFuKqjHX+kdOtdNHnxkVZbtIrR2rxnMFvehkjaZRNV2H/gPXlb0hw==}
    engines: {node: '>=1.0.0'}
    dependencies:
      easy-stack: 1.0.1

  /@nodelib/fs.scandir@2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  /@nodelib/fs.stat@2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  /@nodelib/fs.walk@1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.13.0

  /@npmcli/fs@1.1.1:
    resolution: {integrity: sha512-8KG5RD0GVP4ydEzRn/I4BNDuxDtqVbOdm8675T49OIG/NGhaK0pjPX7ZcDlvKYbA+ulvVK3ztfcF4uBdOxuJbQ==}
    dependencies:
      '@gar/promisify': 1.1.3
      semver: 7.3.8
    dev: true

  /@npmcli/move-file@1.1.2:
    resolution: {integrity: sha512-1SUf/Cg2GzGDyaf15aR9St9TWlb+XvbZXWpDx8YKs7MLzMH/BCeopv+y9vzrzgkfykCGuWOlSu3mZhj2+FQcrg==}
    engines: {node: '>=10'}
    deprecated: This functionality has been moved to @npmcli/fs
    dependencies:
      mkdirp: 1.0.4
      rimraf: 3.0.2
    dev: true

  /@nuxt/opencollective@0.3.3:
    resolution: {integrity: sha512-6IKCd+gP0HliixqZT/p8nW3tucD6Sv/u/eR2A9X4rxT/6hXlMzA4GZQzq4d2qnBAwSwGpmKyzkyTjNjrhaA25A==}
    engines: {node: '>=8.0.0', npm: '>=5.0.0'}
    hasBin: true
    dependencies:
      chalk: 4.1.2
      consola: 2.15.3
      node-fetch: 2.6.7
    transitivePeerDependencies:
      - encoding
    dev: false

  /@polka/url@1.0.0-next.21:
    resolution: {integrity: sha512-a5Sab1C4/icpTZVzZc5Ghpz88yQtGOyNqYXcZgOssB2uuAr+wF/MvN6bgtW32q7HHrvBki+BsZ0OuNv6EV3K9g==}

  /@popperjs/core@2.11.6:
    resolution: {integrity: sha512-50/17A98tWUfQ176raKiOGXuYpLyyVMkxxG6oylzL3BPOlA6ADGdK7EYunSa4I064xerltq9TGXs8HmOk5E+vw==}
    dev: false

  /@samverschueren/stream-to-observable@0.3.1(rxjs@6.6.7):
    resolution: {integrity: sha512-c/qwwcHyafOQuVQJj0IlBjf5yYgBI7YPJ77k4fOJYesb41jio65eaJODRUmfYKhTOFBrIZ66kgvGPlNbjuoRdQ==}
    engines: {node: '>=6'}
    peerDependencies:
      rxjs: '*'
      zen-observable: '*'
    peerDependenciesMeta:
      rxjs:
        optional: true
      zen-observable:
        optional: true
    dependencies:
      any-observable: 0.3.0(rxjs@6.6.7)
      rxjs: 6.6.7
    transitivePeerDependencies:
      - zenObservable
    dev: true

  /@sideway/address@4.1.4:
    resolution: {integrity: sha512-7vwq+rOHVWjyXxVlR76Agnvhy8I9rpzjosTESvmhNeXOXdZZB15Fl+TI9x1SiHZH5Jv2wTGduSxFDIaq0m3DUw==}
    dependencies:
      '@hapi/hoek': 9.3.0

  /@sideway/formula@3.0.0:
    resolution: {integrity: sha512-vHe7wZ4NOXVfkoRb8T5otiENVlT7a3IAiw7H5M2+GO+9CDgcVUUsX1zalAztCmwyOr2RUTGJdgB+ZvSVqmdHmg==}

  /@sideway/pinpoint@2.0.0:
    resolution: {integrity: sha512-RNiOoTPkptFtSVzQevY/yWtZwf/RxyVnPy/OcA9HBM3MlGDnBEYL5B41H0MTn0Uec8Hi+2qUtTfG2WWZBmMejQ==}

  /@soda/friendly-errors-webpack-plugin@1.8.1(webpack@5.75.0):
    resolution: {integrity: sha512-h2ooWqP8XuFqTXT+NyAFbrArzfQA7R6HTezADrvD9Re8fxMLTPPniLdqVTdDaO0eIoLaAwKT+d6w+5GeTk7Vbg==}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      chalk: 3.0.0
      error-stack-parser: 2.1.4
      string-width: 4.2.3
      strip-ansi: 6.0.1
      webpack: 5.75.0(webpack-cli@4.10.0)

  /@soda/get-current-script@1.0.2:
    resolution: {integrity: sha512-T7VNNlYVM1SgQ+VsMYhnDkcGmWhQdL0bDyGm5TlQ3GBXnJscEClUUOKduWTmm2zCnvNLC1hc3JpuXjs/nFOc5w==}

  /@trysound/sax@0.2.0:
    resolution: {integrity: sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==}
    engines: {node: '>=10.13.0'}

  /@types/babel__core@7.1.20:
    resolution: {integrity: sha512-PVb6Bg2QuscZ30FvOU7z4guG6c926D9YRvOxEaelzndpMsvP+YM74Q/dAFASpg2l6+XLalxSGxcq/lrgYWZtyQ==}
    dependencies:
      '@babel/parser': 7.20.3
      '@babel/types': 7.20.2
      '@types/babel__generator': 7.6.4
      '@types/babel__template': 7.4.1
      '@types/babel__traverse': 7.18.2

  /@types/babel__generator@7.6.4:
    resolution: {integrity: sha512-tFkciB9j2K755yrTALxD44McOrk+gfpIpvC3sxHjRawj6PfnQxrse4Clq5y/Rq+G3mrBurMax/lG8Qn2t9mSsg==}
    dependencies:
      '@babel/types': 7.20.2

  /@types/babel__template@7.4.1:
    resolution: {integrity: sha512-azBFKemX6kMg5Io+/rdGT0dkGreboUVR0Cdm3fz9QJWpaQGJRQXl7C+6hOTCZcMll7KFyEQpgbYI2lHdsS4U7g==}
    dependencies:
      '@babel/parser': 7.20.3
      '@babel/types': 7.20.2

  /@types/babel__traverse@7.18.2:
    resolution: {integrity: sha512-FcFaxOr2V5KZCviw1TnutEMVUVsGt4D2hP1TAfXZAMKuHYW3xQhe3jTxNPWutgCJ3/X1c5yX8ZoGVEItxKbwBg==}
    dependencies:
      '@babel/types': 7.20.2

  /@types/body-parser@1.19.2:
    resolution: {integrity: sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==}
    dependencies:
      '@types/connect': 3.4.35
      '@types/node': 18.11.9

  /@types/bonjour@3.5.10:
    resolution: {integrity: sha512-p7ienRMiS41Nu2/igbJxxLDWrSZ0WxM8UQgCeO9KhoVF7cOVFkrKsiDr1EsJIla8vV3oEEjGcz11jc5yimhzZw==}
    dependencies:
      '@types/node': 18.11.9

  /@types/connect-history-api-fallback@1.3.5:
    resolution: {integrity: sha512-h8QJa8xSb1WD4fpKBDcATDNGXghFj6/3GRWG6dhmRcu0RX1Ubasur2Uvx5aeEwlf0MwblEC2bMzzMQntxnw/Cw==}
    dependencies:
      '@types/express-serve-static-core': 4.17.31
      '@types/node': 18.11.9

  /@types/connect@3.4.35:
    resolution: {integrity: sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ==}
    dependencies:
      '@types/node': 18.11.9

  /@types/eslint-scope@3.7.4:
    resolution: {integrity: sha512-9K4zoImiZc3HlIp6AVUDE4CWYx22a+lhSZMYNpbjW04+YF0KWj4pJXnEMjdnFTiQibFFmElcsasJXDbdI/EPhA==}
    dependencies:
      '@types/eslint': 8.4.10
      '@types/estree': 0.0.51

  /@types/eslint@8.4.10:
    resolution: {integrity: sha512-Sl/HOqN8NKPmhWo2VBEPm0nvHnu2LL3v9vKo8MEq0EtbJ4eVzGPl41VNPvn5E1i5poMk4/XD8UriLHpJvEP/Nw==}
    dependencies:
      '@types/estree': 0.0.51
      '@types/json-schema': 7.0.11

  /@types/estree@0.0.51:
    resolution: {integrity: sha512-CuPgU6f3eT/XgKKPqKd/gLZV1Xmvf1a2R5POBOGQa6uv82xpls89HU5zKeVoyR8XzHd1RGNOlQlvUe3CFkjWNQ==}

  /@types/express-serve-static-core@4.17.31:
    resolution: {integrity: sha512-DxMhY+NAsTwMMFHBTtJFNp5qiHKJ7TeqOo23zVEM9alT1Ml27Q3xcTH0xwxn7Q0BbMcVEJOs/7aQtUWupUQN3Q==}
    dependencies:
      '@types/node': 18.11.9
      '@types/qs': 6.9.7
      '@types/range-parser': 1.2.4

  /@types/express@4.17.14:
    resolution: {integrity: sha512-TEbt+vaPFQ+xpxFLFssxUDXj5cWCxZJjIcB7Yg0k0GMHGtgtQgpvx/MUQUeAkNbA9AAGrwkAsoeItdTgS7FMyg==}
    dependencies:
      '@types/body-parser': 1.19.2
      '@types/express-serve-static-core': 4.17.31
      '@types/qs': 6.9.7
      '@types/serve-static': 1.15.0

  /@types/html-minifier-terser@6.1.0:
    resolution: {integrity: sha512-oh/6byDPnL1zeNXFrDXFLyZjkr1MsBG667IM792caf1L2UPOOMf65NFzjUH/ltyfwjAGfs1rsX1eftK0jC/KIg==}

  /@types/http-proxy@1.17.9:
    resolution: {integrity: sha512-QsbSjA/fSk7xB+UXlCT3wHBy5ai9wOcNDWwZAtud+jXhwOM3l+EYZh8Lng4+/6n8uar0J7xILzqftJdJ/Wdfkw==}
    dependencies:
      '@types/node': 18.11.9

  /@types/istanbul-lib-coverage@2.0.4:
    resolution: {integrity: sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g==}

  /@types/istanbul-lib-report@3.0.0:
    resolution: {integrity: sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg==}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.4

  /@types/istanbul-reports@1.1.2:
    resolution: {integrity: sha512-P/W9yOX/3oPZSpaYOCQzGqgCQRXn0FFO/V8bWrCQs+wLmvVVxk6CRBXALEvNs9OHIatlnlFokfhuDo2ug01ciw==}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.4
      '@types/istanbul-lib-report': 3.0.0

  /@types/jest@24.9.1:
    resolution: {integrity: sha512-Fb38HkXSVA4L8fGKEZ6le5bB8r6MRWlOCZbVuWZcmOMSCd2wCYOwN1ibj8daIoV9naq7aaOZjrLCoCMptKU/4Q==}
    dependencies:
      jest-diff: 24.9.0
    dev: true

  /@types/json-schema@7.0.11:
    resolution: {integrity: sha512-wOuvG1SN4Us4rez+tylwwwCV1psiNVOkJeM3AUWUNWg/jDQY2+HE/444y5gc+jBmRqASOm2Oeh5c1axHobwRKQ==}

  /@types/mime@3.0.1:
    resolution: {integrity: sha512-Y4XFY5VJAuw0FgAqPNd6NNoV44jbq9Bz2L7Rh/J6jLTiHBSBJa9fxqQIvkIld4GsoDOcCbvzOUAbLPsSKKg+uA==}

  /@types/minimist@1.2.2:
    resolution: {integrity: sha512-jhuKLIRrhvCPLqwPcx6INqmKeiA5EWrsCOPhrlFSrbrmU4ZMPjj5Ul/oLCMDO98XRUIwVm78xICz4EPCektzeQ==}

  /@types/node@12.12.50:
    resolution: {integrity: sha512-5ImO01Fb8YsEOYpV+aeyGYztcYcjGsBvN4D7G5r1ef2cuQOpymjWNQi5V0rKHE6PC2ru3HkoUr/Br2/8GUA84w==}
    dev: true

  /@types/node@18.11.9:
    resolution: {integrity: sha512-CRpX21/kGdzjOpFsZSkcrXMGIBWMGNIHXXBVFSH+ggkftxg+XYP20TESbh+zFvFj3EQOl5byk0HTRn1IL6hbqg==}

  /@types/normalize-package-data@2.4.1:
    resolution: {integrity: sha512-Gj7cI7z+98M282Tqmp2K5EIsoouUEzbBJhQQzDE3jSIRk6r9gsz0oUokqIUR4u1R3dMHo0pDHM7sNOHyhulypw==}

  /@types/parse-json@4.0.0:
    resolution: {integrity: sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA==}

  /@types/q@1.5.5:
    resolution: {integrity: sha512-L28j2FcJfSZOnL1WBjDYp2vUHCeIFlyYI/53EwD/rKUBQ7MtUUfbQWiyKJGpcnv4/WgrhWsFKrcPstcAt/J0tQ==}
    dev: true

  /@types/qs@6.9.7:
    resolution: {integrity: sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw==}

  /@types/range-parser@1.2.4:
    resolution: {integrity: sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw==}

  /@types/retry@0.12.0:
    resolution: {integrity: sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA==}

  /@types/semver@7.3.13:
    resolution: {integrity: sha512-21cFJr9z3g5dW8B0CVI9g2O9beqaThGQ6ZFBqHfwhzLDKUxaqTIy3vnfah/UPkfOiF2pLq+tGz+W8RyCskuslw==}
    dev: false

  /@types/serve-index@1.9.1:
    resolution: {integrity: sha512-d/Hs3nWDxNL2xAczmOVZNj92YZCS6RGxfBPjKzuu/XirCgXdpKEb88dYNbrYGint6IVWLNP+yonwVAuRC0T2Dg==}
    dependencies:
      '@types/express': 4.17.14

  /@types/serve-static@1.15.0:
    resolution: {integrity: sha512-z5xyF6uh8CbjAu9760KDKsH2FcDxZ2tFCsA4HIMWE6IkiYMXfVoa+4f9KX+FN0ZLsaMw1WNG2ETLA6N+/YA+cg==}
    dependencies:
      '@types/mime': 3.0.1
      '@types/node': 18.11.9

  /@types/sinonjs__fake-timers@6.0.4:
    resolution: {integrity: sha512-IFQTJARgMUBF+xVd2b+hIgXWrZEjND3vJtRCvIelcFB5SIXfjV4bOHbHJ0eXKh+0COrBRc8MqteKAz/j88rE0A==}
    dev: true

  /@types/sizzle@2.3.3:
    resolution: {integrity: sha512-JYM8x9EGF163bEyhdJBpR2QX1R5naCJHC8ucJylJ3w9/CVBaskdQ8WqBf8MmQrd1kRvp/a4TS8HJ+bxzR7ZJYQ==}
    dev: true

  /@types/sockjs@0.3.33:
    resolution: {integrity: sha512-f0KEEe05NvUnat+boPTZ0dgaLZ4SfSouXUgv5noUiefG2ajgKjmETo9ZJyuqsl7dfl2aHlLJUiki6B4ZYldiiw==}
    dependencies:
      '@types/node': 18.11.9

  /@types/stack-utils@1.0.1:
    resolution: {integrity: sha512-l42BggppR6zLmpfU6fq9HEa2oGPEI8yrSPL3GITjfRInppYFahObbIQOQK3UGxEnyQpltZLaPe75046NOZQikw==}

  /@types/strip-bom@3.0.0:
    resolution: {integrity: sha512-xevGOReSYGM7g/kUBZzPqCrR/KYAo+F0yiPc85WFTJa0MSLtyFTVTU6cJu/aV4mid7IffDIWqo69THF2o4JiEQ==}
    dev: true

  /@types/strip-json-comments@0.0.30:
    resolution: {integrity: sha512-7NQmHra/JILCd1QqpSzl8+mJRc8ZHz3uDm8YV1Ks9IhK0epEiTw8aIErbvH9PI+6XbqhyIQy3462nEsn7UVzjQ==}
    dev: true

  /@types/ws@8.5.3:
    resolution: {integrity: sha512-6YOoWjruKj1uLf3INHH7D3qTXwFfEsg1kf3c0uDdSBJwfa/llkwIjrAGV7j7mVgGNbzTQ3HiHKKDXl6bJPD97w==}
    dependencies:
      '@types/node': 18.11.9

  /@types/yargs-parser@21.0.0:
    resolution: {integrity: sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA==}

  /@types/yargs@13.0.12:
    resolution: {integrity: sha512-qCxJE1qgz2y0hA4pIxjBR+PelCH0U5CK1XJXFwCNqfmliatKp47UCXXE9Dyk1OXBDLvsCF57TqQEJaeLfDYEOQ==}
    dependencies:
      '@types/yargs-parser': 21.0.0

  /@typescript-eslint/scope-manager@5.42.1:
    resolution: {integrity: sha512-QAZY/CBP1Emx4rzxurgqj3rUinfsh/6mvuKbLNMfJMMKYLRBfweus8brgXF8f64ABkIZ3zdj2/rYYtF8eiuksQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      '@typescript-eslint/types': 5.42.1
      '@typescript-eslint/visitor-keys': 5.42.1
    dev: false

  /@typescript-eslint/types@5.42.1:
    resolution: {integrity: sha512-Qrco9dsFF5lhalz+lLFtxs3ui1/YfC6NdXu+RAGBa8uSfn01cjO7ssCsjIsUs484vny9Xm699FSKwpkCcqwWwA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: false

  /@typescript-eslint/typescript-estree@5.42.1(typescript@4.8.4):
    resolution: {integrity: sha512-qElc0bDOuO0B8wDhhW4mYVgi/LZL+igPwXtV87n69/kYC/7NG3MES0jHxJNCr4EP7kY1XVsRy8C/u3DYeTKQmw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/types': 5.42.1
      '@typescript-eslint/visitor-keys': 5.42.1
      debug: 4.3.4
      globby: 11.1.0
      is-glob: 4.0.3
      semver: 7.3.8
      tsutils: 3.21.0(typescript@4.8.4)
      typescript: 4.8.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@typescript-eslint/utils@5.42.1(eslint@6.8.0)(typescript@4.8.4):
    resolution: {integrity: sha512-Gxvf12xSp3iYZd/fLqiQRD4uKZjDNR01bQ+j8zvhPjpsZ4HmvEFL/tC4amGNyxN9Rq+iqvpHLhlqx6KTxz9ZyQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      '@types/json-schema': 7.0.11
      '@types/semver': 7.3.13
      '@typescript-eslint/scope-manager': 5.42.1
      '@typescript-eslint/types': 5.42.1
      '@typescript-eslint/typescript-estree': 5.42.1(typescript@4.8.4)
      eslint: 6.8.0
      eslint-scope: 5.1.1
      eslint-utils: 3.0.0(eslint@6.8.0)
      semver: 7.3.8
    transitivePeerDependencies:
      - supports-color
      - typescript
    dev: false

  /@typescript-eslint/visitor-keys@5.42.1:
    resolution: {integrity: sha512-LOQtSF4z+hejmpUvitPlc4hA7ERGoj2BVkesOcG91HCn8edLGUXbTrErmutmPbl8Bo9HjAvOO/zBKQHExXNA2A==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      '@typescript-eslint/types': 5.42.1
      eslint-visitor-keys: 3.3.0
    dev: false

  /@vue/cli-overlay@5.0.8:
    resolution: {integrity: sha512-KmtievE/B4kcXp6SuM2gzsnSd8WebkQpg3XaB6GmFh1BJGRqa1UiW9up7L/Q67uOdTigHxr5Ar2lZms4RcDjwQ==}

  /@vue/cli-plugin-router@5.0.8(@vue/cli-service@5.0.8):
    resolution: {integrity: sha512-Gmv4dsGdAsWPqVijz3Ux2OS2HkMrWi1ENj2cYL75nUeL+Xj5HEstSqdtfZ0b1q9NCce+BFB6QnHfTBXc/fCvMg==}
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0 || ^5.0.0-0
    dependencies:
      '@vue/cli-service': 5.0.8(@babel/core@7.20.2)(babel-core@7.0.0-bridge.0)(lodash@4.17.21)(sass-loader@9.0.3)(vue-template-compiler@2.7.14)(vue@2.7.14)(webpack-cli@4.10.0)
      '@vue/cli-shared-utils': 5.0.8
    transitivePeerDependencies:
      - encoding

  /@vue/cli-plugin-unit-jest@4.5.19(@vue/cli-service@5.0.8)(vue-template-compiler@2.7.14)(vue@2.7.14):
    resolution: {integrity: sha512-yX61mpeU7DnjOv+Lxtjmr3pzESqBLIXeTK4MJpa/UdzrhnylHP4r6mCYETNLEYtxp8WZUXPjZFIzrKn5poZPJg==}
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/plugin-transform-modules-commonjs': 7.19.6(@babel/core@7.20.2)
      '@types/jest': 24.9.1
      '@vue/cli-service': 5.0.8(@babel/core@7.20.2)(babel-core@7.0.0-bridge.0)(lodash@4.17.21)(sass-loader@9.0.3)(vue-template-compiler@2.7.14)(vue@2.7.14)(webpack-cli@4.10.0)
      '@vue/cli-shared-utils': 4.5.19
      babel-core: 7.0.0-bridge.0(@babel/core@7.20.2)
      babel-jest: 24.9.0(@babel/core@7.20.2)
      babel-plugin-transform-es2015-modules-commonjs: 6.26.2
      deepmerge: 4.2.2
      jest: 24.9.0
      jest-environment-jsdom-fifteen: 1.0.2
      jest-serializer-vue: 2.0.2
      jest-transform-stub: 2.0.0
      jest-watch-typeahead: 0.4.2
      ts-jest: 24.3.0(jest@24.9.0)
      vue-jest: 3.0.7(babel-core@7.0.0-bridge.0)(vue-template-compiler@2.7.14)(vue@2.7.14)
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
      - vue
      - vue-template-compiler
    dev: true

  /@vue/cli-plugin-vuex@5.0.8(@vue/cli-service@5.0.8):
    resolution: {integrity: sha512-HSYWPqrunRE5ZZs8kVwiY6oWcn95qf/OQabwLfprhdpFWAGtLStShjsGED2aDpSSeGAskQETrtR/5h7VqgIlBA==}
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0 || ^5.0.0-0
    dependencies:
      '@vue/cli-service': 5.0.8(@babel/core@7.20.2)(babel-core@7.0.0-bridge.0)(lodash@4.17.21)(sass-loader@9.0.3)(vue-template-compiler@2.7.14)(vue@2.7.14)(webpack-cli@4.10.0)

  /@vue/cli-service@5.0.8(@babel/core@7.20.2)(babel-core@7.0.0-bridge.0)(lodash@4.17.21)(sass-loader@9.0.3)(vue-template-compiler@2.7.14)(vue@2.7.14)(webpack-cli@4.10.0):
    resolution: {integrity: sha512-nV7tYQLe7YsTtzFrfOMIHc5N2hp5lHG2rpYr0aNja9rNljdgcPZLyQRb2YRivTHqTv7lI962UXFURcpStHgyFw==}
    engines: {node: ^12.0.0 || >= 14.0.0}
    hasBin: true
    peerDependencies:
      cache-loader: '*'
      less-loader: '*'
      pug-plain-loader: '*'
      raw-loader: '*'
      sass-loader: '*'
      stylus-loader: '*'
      vue-template-compiler: ^2.0.0
      webpack-sources: '*'
    peerDependenciesMeta:
      cache-loader:
        optional: true
      less-loader:
        optional: true
      pug-plain-loader:
        optional: true
      raw-loader:
        optional: true
      sass-loader:
        optional: true
      stylus-loader:
        optional: true
      vue-template-compiler:
        optional: true
      webpack-sources:
        optional: true
    dependencies:
      '@babel/helper-compilation-targets': 7.20.0(@babel/core@7.20.2)
      '@soda/friendly-errors-webpack-plugin': 1.8.1(webpack@5.75.0)
      '@soda/get-current-script': 1.0.2
      '@types/minimist': 1.2.2
      '@vue/cli-overlay': 5.0.8
      '@vue/cli-plugin-router': 5.0.8(@vue/cli-service@5.0.8)
      '@vue/cli-plugin-vuex': 5.0.8(@vue/cli-service@5.0.8)
      '@vue/cli-shared-utils': 5.0.8
      '@vue/component-compiler-utils': 3.3.0(babel-core@7.0.0-bridge.0)(lodash@4.17.21)
      '@vue/vue-loader-v15': /vue-loader@15.10.0(babel-core@7.0.0-bridge.0)(css-loader@6.7.1)(lodash@4.17.21)(vue-template-compiler@2.7.14)(webpack@5.75.0)
      '@vue/web-component-wrapper': 1.3.0
      acorn: 8.8.1
      acorn-walk: 8.2.0
      address: 1.2.1
      autoprefixer: 10.4.13(postcss@8.4.19)
      browserslist: 4.21.4
      case-sensitive-paths-webpack-plugin: 2.4.0
      cli-highlight: 2.1.11
      clipboardy: 2.3.0
      cliui: 7.0.4
      copy-webpack-plugin: 9.1.0(webpack@5.75.0)
      css-loader: 6.7.1(webpack@5.75.0)
      css-minimizer-webpack-plugin: 3.4.1(webpack@5.75.0)
      cssnano: 5.1.14(postcss@8.4.19)
      debug: 4.3.4
      default-gateway: 6.0.3
      dotenv: 10.0.0
      dotenv-expand: 5.1.0
      fs-extra: 9.1.0
      globby: 11.1.0
      hash-sum: 2.0.0
      html-webpack-plugin: 5.5.0(webpack@5.75.0)
      is-file-esm: 1.0.0
      launch-editor-middleware: 2.6.0
      lodash.defaultsdeep: 4.6.1
      lodash.mapvalues: 4.6.0
      mini-css-extract-plugin: 2.6.1(webpack@5.75.0)
      minimist: 1.2.7
      module-alias: 2.2.2
      portfinder: 1.0.32
      postcss: 8.4.19
      postcss-loader: 6.2.1(postcss@8.4.19)(webpack@5.75.0)
      progress-webpack-plugin: 1.0.16(webpack@5.75.0)
      sass-loader: 9.0.3(sass@1.56.1)(webpack@4.46.0)
      ssri: 8.0.1
      terser-webpack-plugin: 5.3.6(webpack@5.75.0)
      thread-loader: 3.0.4(webpack@5.75.0)
      vue-loader: 17.0.1(vue@2.7.14)(webpack@5.75.0)
      vue-style-loader: 4.1.3
      vue-template-compiler: 2.7.14
      webpack: 5.75.0(webpack-cli@4.10.0)
      webpack-bundle-analyzer: 4.7.0
      webpack-chain: 6.5.1
      webpack-dev-server: 4.11.1(debug@4.3.4)(webpack-cli@4.10.0)(webpack@5.75.0)
      webpack-merge: 5.8.0
      webpack-virtual-modules: 0.4.6
      whatwg-fetch: 3.6.2
    transitivePeerDependencies:
      - '@babel/core'
      - '@parcel/css'
      - '@swc/core'
      - '@vue/compiler-sfc'
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - bufferutil
      - clean-css
      - coffee-script
      - csso
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - encoding
      - esbuild
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - supports-color
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - uglify-js
      - underscore
      - utf-8-validate
      - vash
      - velocityjs
      - vue
      - walrus
      - webpack-cli
      - whiskers

  /@vue/cli-shared-utils@4.5.19:
    resolution: {integrity: sha512-JYpdsrC/d9elerKxbEUtmSSU6QRM60rirVubOewECHkBHj+tLNznWq/EhCjswywtePyLaMUK25eTqnTSZlEE+g==}
    dependencies:
      '@achrinza/node-ipc': 9.2.2
      '@hapi/joi': 15.1.1
      chalk: 2.4.2
      execa: 1.0.0
      launch-editor: 2.6.0
      lru-cache: 5.1.1
      open: 6.4.0
      ora: 3.4.0
      read-pkg: 5.2.0
      request: 2.88.2
      semver: 6.3.0
      strip-ansi: 6.0.1
    dev: true

  /@vue/cli-shared-utils@5.0.8:
    resolution: {integrity: sha512-uK2YB7bBVuQhjOJF+O52P9yFMXeJVj7ozqJkwYE9PlMHL1LMHjtCYm4cSdOebuPzyP+/9p0BimM/OqxsevIopQ==}
    dependencies:
      '@achrinza/node-ipc': 9.2.6
      chalk: 4.1.2
      execa: 1.0.0
      joi: 17.7.0
      launch-editor: 2.6.0
      lru-cache: 6.0.0
      node-fetch: 2.6.7
      open: 8.4.0
      ora: 5.4.1
      read-pkg: 5.2.0
      semver: 7.3.8
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - encoding

  /@vue/compiler-sfc@2.7.14:
    resolution: {integrity: sha512-aNmNHyLPsw+sVvlQFQ2/8sjNuLtK54TC6cuKnVzAY93ks4ZBrvwQSnkkIh7bsbNhum5hJBS00wSDipQ937f5DA==}
    dependencies:
      '@babel/parser': 7.20.3
      postcss: 8.4.19
      source-map: 0.6.1

  /@vue/component-compiler-utils@3.3.0(babel-core@7.0.0-bridge.0)(lodash@4.17.21):
    resolution: {integrity: sha512-97sfH2mYNU+2PzGrmK2haqffDpVASuib9/w2/noxiFi31Z54hW+q3izKQXXQZSNhtiUpAI36uSuYepeBe4wpHQ==}
    dependencies:
      consolidate: 0.15.1(babel-core@7.0.0-bridge.0)(lodash@4.17.21)
      hash-sum: 1.0.2
      lru-cache: 4.1.5
      merge-source-map: 1.1.0
      postcss: 7.0.39
      postcss-selector-parser: 6.0.10
      source-map: 0.6.1
      vue-template-es2015-compiler: 1.9.1
    optionalDependencies:
      prettier: 2.7.1
    transitivePeerDependencies:
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - underscore
      - vash
      - velocityjs
      - walrus
      - whiskers

  /@vue/test-utils@1.3.3(vue-template-compiler@2.7.14)(vue@2.7.14):
    resolution: {integrity: sha512-DmZkKrH5/MSkrU0hhHhv5+aOXcEJSaOhutKMOh2viuiLiMaFeOLPiTEvtegLunO3rXBagzHO681qW1sNMaB1sQ==}
    peerDependencies:
      vue: 2.x
      vue-template-compiler: ^2.x
    dependencies:
      dom-event-types: 1.1.0
      lodash: 4.17.21
      pretty: 2.0.0
      vue: 2.7.14
      vue-template-compiler: 2.7.14
    dev: true

  /@vue/web-component-wrapper@1.3.0:
    resolution: {integrity: sha512-Iu8Tbg3f+emIIMmI2ycSI8QcEuAUgPTgHwesDU1eKMLE4YC/c/sFbGc70QgMq31ijRftV0R7vCm9co6rldCeOA==}

  /@webassemblyjs/ast@1.11.1:
    resolution: {integrity: sha512-ukBh14qFLjxTQNTXocdyksN5QdM28S1CxHt2rdskFyL+xFV7VremuBLVbmCePj+URalXBENx/9Lm7lnhihtCSw==}
    dependencies:
      '@webassemblyjs/helper-numbers': 1.11.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.1

  /@webassemblyjs/ast@1.9.0:
    resolution: {integrity: sha512-C6wW5L+b7ogSDVqymbkkvuW9kruN//YisMED04xzeBBqjHa2FYnmvOlS6Xj68xWQRgWvI9cIglsjFowH/RJyEA==}
    dependencies:
      '@webassemblyjs/helper-module-context': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/wast-parser': 1.9.0

  /@webassemblyjs/floating-point-hex-parser@1.11.1:
    resolution: {integrity: sha512-iGRfyc5Bq+NnNuX8b5hwBrRjzf0ocrJPI6GWFodBFzmFnyvrQ83SHKhmilCU/8Jv67i4GJZBMhEzltxzcNagtQ==}

  /@webassemblyjs/floating-point-hex-parser@1.9.0:
    resolution: {integrity: sha512-TG5qcFsS8QB4g4MhrxK5TqfdNe7Ey/7YL/xN+36rRjl/BlGE/NcBvJcqsRgCP6Z92mRE+7N50pRIi8SmKUbcQA==}

  /@webassemblyjs/helper-api-error@1.11.1:
    resolution: {integrity: sha512-RlhS8CBCXfRUR/cwo2ho9bkheSXG0+NwooXcc3PAILALf2QLdFyj7KGsKRbVc95hZnhnERon4kW/D3SZpp6Tcg==}

  /@webassemblyjs/helper-api-error@1.9.0:
    resolution: {integrity: sha512-NcMLjoFMXpsASZFxJ5h2HZRcEhDkvnNFOAKneP5RbKRzaWJN36NC4jqQHKwStIhGXu5mUWlUUk7ygdtrO8lbmw==}

  /@webassemblyjs/helper-buffer@1.11.1:
    resolution: {integrity: sha512-gwikF65aDNeeXa8JxXa2BAk+REjSyhrNC9ZwdT0f8jc4dQQeDQ7G4m0f2QCLPJiMTTO6wfDmRmj/pW0PsUvIcA==}

  /@webassemblyjs/helper-buffer@1.9.0:
    resolution: {integrity: sha512-qZol43oqhq6yBPx7YM3m9Bv7WMV9Eevj6kMi6InKOuZxhw+q9hOkvq5e/PpKSiLfyetpaBnogSbNCfBwyB00CA==}

  /@webassemblyjs/helper-code-frame@1.9.0:
    resolution: {integrity: sha512-ERCYdJBkD9Vu4vtjUYe8LZruWuNIToYq/ME22igL+2vj2dQ2OOujIZr3MEFvfEaqKoVqpsFKAGsRdBSBjrIvZA==}
    dependencies:
      '@webassemblyjs/wast-printer': 1.9.0

  /@webassemblyjs/helper-fsm@1.9.0:
    resolution: {integrity: sha512-OPRowhGbshCb5PxJ8LocpdX9Kl0uB4XsAjl6jH/dWKlk/mzsANvhwbiULsaiqT5GZGT9qinTICdj6PLuM5gslw==}

  /@webassemblyjs/helper-module-context@1.9.0:
    resolution: {integrity: sha512-MJCW8iGC08tMk2enck1aPW+BE5Cw8/7ph/VGZxwyvGbJwjktKkDK7vy7gAmMDx88D7mhDTCNKAW5tED+gZ0W8g==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0

  /@webassemblyjs/helper-numbers@1.11.1:
    resolution: {integrity: sha512-vDkbxiB8zfnPdNK9Rajcey5C0w+QJugEglN0of+kmO8l7lDb77AnlKYQF7aarZuCrv+l0UvqL+68gSDr3k9LPQ==}
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.11.1
      '@webassemblyjs/helper-api-error': 1.11.1
      '@xtuc/long': 4.2.2

  /@webassemblyjs/helper-wasm-bytecode@1.11.1:
    resolution: {integrity: sha512-PvpoOGiJwXeTrSf/qfudJhwlvDQxFgelbMqtq52WWiXC6Xgg1IREdngmPN3bs4RoO83PnL/nFrxucXj1+BX62Q==}

  /@webassemblyjs/helper-wasm-bytecode@1.9.0:
    resolution: {integrity: sha512-R7FStIzyNcd7xKxCZH5lE0Bqy+hGTwS3LJjuv1ZVxd9O7eHCedSdrId/hMOd20I+v8wDXEn+bjfKDLzTepoaUw==}

  /@webassemblyjs/helper-wasm-section@1.11.1:
    resolution: {integrity: sha512-10P9No29rYX1j7F3EVPX3JvGPQPae+AomuSTPiF9eBQeChHI6iqjMIwR9JmOJXwpnn/oVGDk7I5IlskuMwU/pg==}
    dependencies:
      '@webassemblyjs/ast': 1.11.1
      '@webassemblyjs/helper-buffer': 1.11.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.1
      '@webassemblyjs/wasm-gen': 1.11.1

  /@webassemblyjs/helper-wasm-section@1.9.0:
    resolution: {integrity: sha512-XnMB8l3ek4tvrKUUku+IVaXNHz2YsJyOOmz+MMkZvh8h1uSJpSen6vYnw3IoQ7WwEuAhL8Efjms1ZWjqh2agvw==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0

  /@webassemblyjs/ieee754@1.11.1:
    resolution: {integrity: sha512-hJ87QIPtAMKbFq6CGTkZYJivEwZDbQUgYd3qKSadTNOhVY7p+gfP6Sr0lLRVTaG1JjFj+r3YchoqRYxNH3M0GQ==}
    dependencies:
      '@xtuc/ieee754': 1.2.0

  /@webassemblyjs/ieee754@1.9.0:
    resolution: {integrity: sha512-dcX8JuYU/gvymzIHc9DgxTzUUTLexWwt8uCTWP3otys596io0L5aW02Gb1RjYpx2+0Jus1h4ZFqjla7umFniTg==}
    dependencies:
      '@xtuc/ieee754': 1.2.0

  /@webassemblyjs/leb128@1.11.1:
    resolution: {integrity: sha512-BJ2P0hNZ0u+Th1YZXJpzW6miwqQUGcIHT1G/sf72gLVD9DZ5AdYTqPNbHZh6K1M5VmKvFXwGSWZADz+qBWxeRw==}
    dependencies:
      '@xtuc/long': 4.2.2

  /@webassemblyjs/leb128@1.9.0:
    resolution: {integrity: sha512-ENVzM5VwV1ojs9jam6vPys97B/S65YQtv/aanqnU7D8aSoHFX8GyhGg0CMfyKNIHBuAVjy3tlzd5QMMINa7wpw==}
    dependencies:
      '@xtuc/long': 4.2.2

  /@webassemblyjs/utf8@1.11.1:
    resolution: {integrity: sha512-9kqcxAEdMhiwQkHpkNiorZzqpGrodQQ2IGrHHxCy+Ozng0ofyMA0lTqiLkVs1uzTRejX+/O0EOT7KxqVPuXosQ==}

  /@webassemblyjs/utf8@1.9.0:
    resolution: {integrity: sha512-GZbQlWtopBTP0u7cHrEx+73yZKrQoBMpwkGEIqlacljhXCkVM1kMQge/Mf+csMJAjEdSwhOyLAS0AoR3AG5P8w==}

  /@webassemblyjs/wasm-edit@1.11.1:
    resolution: {integrity: sha512-g+RsupUC1aTHfR8CDgnsVRVZFJqdkFHpsHMfJuWQzWU3tvnLC07UqHICfP+4XyL2tnr1amvl1Sdp06TnYCmVkA==}
    dependencies:
      '@webassemblyjs/ast': 1.11.1
      '@webassemblyjs/helper-buffer': 1.11.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.1
      '@webassemblyjs/helper-wasm-section': 1.11.1
      '@webassemblyjs/wasm-gen': 1.11.1
      '@webassemblyjs/wasm-opt': 1.11.1
      '@webassemblyjs/wasm-parser': 1.11.1
      '@webassemblyjs/wast-printer': 1.11.1

  /@webassemblyjs/wasm-edit@1.9.0:
    resolution: {integrity: sha512-FgHzBm80uwz5M8WKnMTn6j/sVbqilPdQXTWraSjBwFXSYGirpkSWE2R9Qvz9tNiTKQvoKILpCuTjBKzOIm0nxw==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/helper-wasm-section': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
      '@webassemblyjs/wasm-opt': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
      '@webassemblyjs/wast-printer': 1.9.0

  /@webassemblyjs/wasm-gen@1.11.1:
    resolution: {integrity: sha512-F7QqKXwwNlMmsulj6+O7r4mmtAlCWfO/0HdgOxSklZfQcDu0TpLiD1mRt/zF25Bk59FIjEuGAIyn5ei4yMfLhA==}
    dependencies:
      '@webassemblyjs/ast': 1.11.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.1
      '@webassemblyjs/ieee754': 1.11.1
      '@webassemblyjs/leb128': 1.11.1
      '@webassemblyjs/utf8': 1.11.1

  /@webassemblyjs/wasm-gen@1.9.0:
    resolution: {integrity: sha512-cPE3o44YzOOHvlsb4+E9qSqjc9Qf9Na1OO/BHFy4OI91XDE14MjFN4lTMezzaIWdPqHnsTodGGNP+iRSYfGkjA==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/ieee754': 1.9.0
      '@webassemblyjs/leb128': 1.9.0
      '@webassemblyjs/utf8': 1.9.0

  /@webassemblyjs/wasm-opt@1.11.1:
    resolution: {integrity: sha512-VqnkNqnZlU5EB64pp1l7hdm3hmQw7Vgqa0KF/KCNO9sIpI6Fk6brDEiX+iCOYrvMuBWDws0NkTOxYEb85XQHHw==}
    dependencies:
      '@webassemblyjs/ast': 1.11.1
      '@webassemblyjs/helper-buffer': 1.11.1
      '@webassemblyjs/wasm-gen': 1.11.1
      '@webassemblyjs/wasm-parser': 1.11.1

  /@webassemblyjs/wasm-opt@1.9.0:
    resolution: {integrity: sha512-Qkjgm6Anhm+OMbIL0iokO7meajkzQD71ioelnfPEj6r4eOFuqm4YC3VBPqXjFyyNwowzbMD+hizmprP/Fwkl2A==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0

  /@webassemblyjs/wasm-parser@1.11.1:
    resolution: {integrity: sha512-rrBujw+dJu32gYB7/Lup6UhdkPx9S9SnobZzRVL7VcBH9Bt9bCBLEuX/YXOOtBsOZ4NQrRykKhffRWHvigQvOA==}
    dependencies:
      '@webassemblyjs/ast': 1.11.1
      '@webassemblyjs/helper-api-error': 1.11.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.1
      '@webassemblyjs/ieee754': 1.11.1
      '@webassemblyjs/leb128': 1.11.1
      '@webassemblyjs/utf8': 1.11.1

  /@webassemblyjs/wasm-parser@1.9.0:
    resolution: {integrity: sha512-9+wkMowR2AmdSWQzsPEjFU7njh8HTO5MqO8vjwEHuM+AMHioNqSBONRdr0NQQ3dVQrzp0s8lTcYqzUdb7YgELA==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-api-error': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/ieee754': 1.9.0
      '@webassemblyjs/leb128': 1.9.0
      '@webassemblyjs/utf8': 1.9.0

  /@webassemblyjs/wast-parser@1.9.0:
    resolution: {integrity: sha512-qsqSAP3QQ3LyZjNC/0jBJ/ToSxfYJ8kYyuiGvtn/8MK89VrNEfwj7BPQzJVHi0jGTRK2dGdJ5PRqhtjzoww+bw==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/floating-point-hex-parser': 1.9.0
      '@webassemblyjs/helper-api-error': 1.9.0
      '@webassemblyjs/helper-code-frame': 1.9.0
      '@webassemblyjs/helper-fsm': 1.9.0
      '@xtuc/long': 4.2.2

  /@webassemblyjs/wast-printer@1.11.1:
    resolution: {integrity: sha512-IQboUWM4eKzWW+N/jij2sRatKMh99QEelo3Eb2q0qXkvPRISAj8Qxtmw5itwqK+TTkBuUIE45AxYPToqPtL5gg==}
    dependencies:
      '@webassemblyjs/ast': 1.11.1
      '@xtuc/long': 4.2.2

  /@webassemblyjs/wast-printer@1.9.0:
    resolution: {integrity: sha512-2J0nE95rHXHyQ24cWjMKJ1tqB/ds8z/cyeOZxJhcb+rW+SQASVjuznUSmdz5GpVJTzU8JkhYut0D3siFDD6wsA==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/wast-parser': 1.9.0
      '@xtuc/long': 4.2.2

  /@webpack-cli/configtest@1.2.0(webpack-cli@4.10.0)(webpack@4.46.0):
    resolution: {integrity: sha512-4FB8Tj6xyVkyqjj1OaTqCjXYULB9FMkqQ8yGrZjRDrYh0nOE+7Lhs45WioWQQMV+ceFlE368Ukhe6xdvJM9Egg==}
    peerDependencies:
      webpack: 4.x.x || 5.x.x
      webpack-cli: 4.x.x
    dependencies:
      webpack: 4.46.0(webpack-cli@4.10.0)
      webpack-cli: 4.10.0(webpack-bundle-analyzer@4.7.0)(webpack@4.46.0)

  /@webpack-cli/info@1.5.0(webpack-cli@4.10.0):
    resolution: {integrity: sha512-e8tSXZpw2hPl2uMJY6fsMswaok5FdlGNRTktvFk2sD8RjH0hE2+XistawJx1vmKteh4NmGmNUrp+Tb2w+udPcQ==}
    peerDependencies:
      webpack-cli: 4.x.x
    dependencies:
      envinfo: 7.8.1
      webpack-cli: 4.10.0(webpack-bundle-analyzer@4.7.0)(webpack@4.46.0)

  /@webpack-cli/serve@1.7.0(webpack-cli@4.10.0):
    resolution: {integrity: sha512-oxnCNGj88fL+xzV+dacXs44HcDwf1ovs3AuEzvP7mqXw7fQntqIhQ1BRmynh4qEKQSSSRSWVyXRjmTbZIX9V2Q==}
    peerDependencies:
      webpack-cli: 4.x.x
      webpack-dev-server: '*'
    peerDependenciesMeta:
      webpack-dev-server:
        optional: true
    dependencies:
      webpack-cli: 4.10.0(webpack-bundle-analyzer@4.7.0)(webpack@4.46.0)

  /@xtuc/ieee754@1.2.0:
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}

  /@xtuc/long@4.2.2:
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}

  /abab@2.0.6:
    resolution: {integrity: sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==}

  /abbrev@1.1.1:
    resolution: {integrity: sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==}
    dev: true

  /accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  /acorn-globals@4.3.4:
    resolution: {integrity: sha512-clfQEh21R+D0leSbUdWf3OcfqyaCSAQ8Ryq00bofSekfr9W8u1jyYZo6ir0xu9Gtcf7BjcHJpnbZH7JOCpP60A==}
    dependencies:
      acorn: 6.4.2
      acorn-walk: 6.2.0

  /acorn-import-assertions@1.8.0(acorn@8.8.1):
    resolution: {integrity: sha512-m7VZ3jwz4eK6A4Vtt8Ew1/mNbP24u0FhdyfA7fSvnJR6LMdfOYnmuIrrJAgrYfYJ10F/otaHTtrtrtmHdMNzEw==}
    peerDependencies:
      acorn: ^8
    dependencies:
      acorn: 8.8.1

  /acorn-jsx@5.3.2(acorn@7.4.1):
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 7.4.1

  /acorn-walk@6.2.0:
    resolution: {integrity: sha512-7evsyfH1cLOCdAzZAd43Cic04yKydNx0cF+7tiA19p1XnLLPU4dpCQOqpjqwokFe//vS0QqfqqjCS2JkiIs0cA==}
    engines: {node: '>=0.4.0'}

  /acorn-walk@8.2.0:
    resolution: {integrity: sha512-k+iyHEuPgSw6SbuDpGQM+06HQUa04DZ3o+F6CSzXMvvI5KMvnaEqXe+YVe555R9nn6GPt404fos4wcgpw12SDA==}
    engines: {node: '>=0.4.0'}

  /acorn@5.7.4:
    resolution: {integrity: sha512-1D++VG7BhrtvQpNbBzovKNc1FLGGEE/oGe7b9xJm/RFHMBeUaUGpluV9RLjZa47YFdPcDAenEYuq9pQPcMdLJg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  /acorn@6.4.2:
    resolution: {integrity: sha512-XtGIhXwF8YM8bJhGxG5kXgjkEuNGLTkoYqVE+KMR+aspr4KGYmKYg7yUe3KghyQ9yheNwLnjmzh/7+gfDBmHCQ==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  /acorn@7.4.1:
    resolution: {integrity: sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  /acorn@8.8.1:
    resolution: {integrity: sha512-7zFpHzhnqYKrkYdUjF1HI1bzd0VygEGX8lFk4k5zVMqHEoES+P+7TKI+EvLO9WVMJ8eekdO0aDEK044xTXwPPA==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  /address@1.2.1:
    resolution: {integrity: sha512-B+6bi5D34+fDYENiH5qOlA0cV2rAGKuWZ9LeyUUehbXy8e0VS9e498yO0Jeeh+iM+6KbfudHTFjXw2MmJD4QRA==}
    engines: {node: '>= 10.0.0'}

  /aggregate-error@3.1.0:
    resolution: {integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==}
    engines: {node: '>=8'}
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0
    dev: true

  /ajv-errors@1.0.1(ajv@6.12.6):
    resolution: {integrity: sha512-DCRfO/4nQ+89p/RK43i8Ezd41EqdGIU4ld7nGF8OQ14oc/we5rEntLCUa7+jrn3nn83BosfwZA0wb4pon2o8iQ==}
    peerDependencies:
      ajv: '>=5.0.0'
    dependencies:
      ajv: 6.12.6

  /ajv-formats@2.1.1(ajv@8.11.0):
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: 8.11.0

  /ajv-keywords@3.5.2(ajv@6.12.6):
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==}
    peerDependencies:
      ajv: ^6.9.1
    dependencies:
      ajv: 6.12.6

  /ajv-keywords@5.1.0(ajv@8.11.0):
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==}
    peerDependencies:
      ajv: ^8.8.2
    dependencies:
      ajv: 8.11.0
      fast-deep-equal: 3.1.3

  /ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  /ajv@8.11.0:
    resolution: {integrity: sha512-wGgprdCvMalC0BztXvitD2hC04YffAvtsUn93JbGXYLAtCUO4xd17mCCZQxUOItiBwZvJScWo8NIvQMQ71rdpg==}
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1

  /alphanum-sort@1.0.2:
    resolution: {integrity: sha512-0FcBfdcmaumGPQ0qPn7Q5qTgz/ooXgIyp1rf8ik5bGX8mpE2YHjC0P/eyQvxu1GURYQgq9ozf2mteQ5ZD9YiyQ==}
    dev: true

  /ansi-escapes@3.2.0:
    resolution: {integrity: sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ==}
    engines: {node: '>=4'}

  /ansi-escapes@4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.21.3

  /ansi-html-community@0.0.8:
    resolution: {integrity: sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw==}
    engines: {'0': node >= 0.8.0}
    hasBin: true

  /ansi-regex@2.1.1:
    resolution: {integrity: sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-regex@3.0.1:
    resolution: {integrity: sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==}
    engines: {node: '>=4'}

  /ansi-regex@4.1.1:
    resolution: {integrity: sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==}
    engines: {node: '>=6'}

  /ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  /ansi-styles@2.2.1:
    resolution: {integrity: sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3

  /ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1

  /any-observable@0.3.0(rxjs@6.6.7):
    resolution: {integrity: sha512-/FQM1EDkTsf63Ub2C6O7GuYFDsSXUwsaZDurV0np41ocwq0jthUAYCmhBX9f+KwlaCgIuWyr/4WlUQUBfKfZog==}
    engines: {node: '>=6'}
    peerDependencies:
      rxjs: '*'
      zenObservable: '*'
    peerDependenciesMeta:
      rxjs:
        optional: true
      zenObservable:
        optional: true
    dependencies:
      rxjs: 6.6.7
    dev: true

  /any-promise@1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}

  /anymatch@2.0.0:
    resolution: {integrity: sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==}
    dependencies:
      micromatch: 3.1.10
      normalize-path: 2.1.1
    transitivePeerDependencies:
      - supports-color

  /anymatch@3.1.2:
    resolution: {integrity: sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  /aproba@1.2.0:
    resolution: {integrity: sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==}

  /arch@2.2.0:
    resolution: {integrity: sha512-Of/R0wqp83cgHozfIYLbBMnej79U/SVGOOyuB3VVFv1NRM/PSFMK12x9KVtiYzJqmnU5WR2qp0Z5rHb7sWGnFQ==}

  /are-we-there-yet@1.1.7:
    resolution: {integrity: sha512-nxwy40TuMiUGqMyRHgCSWZ9FM4VAoRP4xUYSTv5ImRog+h9yISPbVH7H8fASCIzYn9wlEv4zvFL7uKDMCFQm3g==}
    dependencies:
      delegates: 1.0.0
      readable-stream: 2.3.7
    dev: true
    optional: true

  /argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}
    dependencies:
      sprintf-js: 1.0.3

  /arr-diff@4.0.0:
    resolution: {integrity: sha512-YVIQ82gZPGBebQV/a8dar4AitzCQs0jjXwMPZllpXMaGjXPYVUawSxQrRsjhjupyVxEvbHgUmIhKVlND+j02kA==}
    engines: {node: '>=0.10.0'}

  /arr-flatten@1.1.0:
    resolution: {integrity: sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==}
    engines: {node: '>=0.10.0'}

  /arr-union@3.1.0:
    resolution: {integrity: sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q==}
    engines: {node: '>=0.10.0'}

  /array-equal@1.0.0:
    resolution: {integrity: sha512-H3LU5RLiSsGXPhN+Nipar0iR0IofH+8r89G2y1tBKxQ/agagKyAjhkAFDRBfodP2caPrNKHpAWNIM/c9yeL7uA==}

  /array-flatten@1.1.1:
    resolution: {integrity: sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==}

  /array-flatten@2.1.2:
    resolution: {integrity: sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ==}

  /array-includes@3.1.6:
    resolution: {integrity: sha512-sgTbLvL6cNnw24FnbaDyjmvddQ2ML8arZsgaJhoABMoplz/4QRhtrYS+alr1BUM1Bwp6dhx8vVCBSLG+StwOFw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
      get-intrinsic: 1.1.3
      is-string: 1.0.7
    dev: true

  /array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}

  /array-unique@0.3.2:
    resolution: {integrity: sha512-SleRWjh9JUud2wH1hPs9rZBZ33H6T9HOiL0uwGnGx9FpE6wKGyfWugmbkEOIs6qWrZhg0LWeLziLrEwQJhs5mQ==}
    engines: {node: '>=0.10.0'}

  /array.prototype.reduce@1.0.5:
    resolution: {integrity: sha512-kDdugMl7id9COE8R7MHF5jWk7Dqt/fs4Pv+JXoICnYwqpjjjbUurz6w5fT5IG6brLdJhv6/VoHB0H7oyIBXd+Q==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
      es-array-method-boxes-properly: 1.0.0
      is-string: 1.0.7

  /arrive@2.4.1:
    resolution: {integrity: sha512-gnpcKdKN4HJAmzpZIxDBJsw/z59sBvh9i4mJyHJCk+enrKnEZ/l1GspM1MGPxMxnRcdt+v/OIWEY5mNQSGisPQ==}
    dev: false

  /asn1.js@5.4.1:
    resolution: {integrity: sha512-+I//4cYPccV8LdmBLiX8CYvf9Sp3vQsrqu2QNXRcrbiWvcx/UdlFiqUJJzxRQxgsZmvhXhn4cSKeSmoFjVdupA==}
    dependencies:
      bn.js: 4.12.0
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      safer-buffer: 2.1.2

  /asn1@0.2.6:
    resolution: {integrity: sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==}
    dependencies:
      safer-buffer: 2.1.2

  /assert-plus@1.0.0:
    resolution: {integrity: sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==}
    engines: {node: '>=0.8'}

  /assert@1.5.0:
    resolution: {integrity: sha512-EDsgawzwoun2CZkCgtxJbv392v4nbk9XDD06zI+kQYoBM/3RBWLlEyJARDOmhAAosBjWACEkKL6S+lIZtcAubA==}
    dependencies:
      object-assign: 4.1.1
      util: 0.10.3

  /assign-symbols@1.0.0:
    resolution: {integrity: sha512-Q+JC7Whu8HhmTdBph/Tq59IoRtoy6KAm5zzPv00WdujX82lbAL8K7WVjne7vdCsAmbF4AYaDOPyO3k0kl8qIrw==}
    engines: {node: '>=0.10.0'}

  /astral-regex@1.0.0:
    resolution: {integrity: sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg==}
    engines: {node: '>=4'}

  /async-each@1.0.3:
    resolution: {integrity: sha512-z/WhQ5FPySLdvREByI2vZiTWwCnF0moMJ1hK9YQwDTHKh6I7/uSckMetoRGb5UBZPC1z0jlw+n/XCgjeH7y1AQ==}
    optional: true

  /async-limiter@1.0.1:
    resolution: {integrity: sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ==}

  /async@2.6.4:
    resolution: {integrity: sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==}
    dependencies:
      lodash: 4.17.21

  /async@3.2.4:
    resolution: {integrity: sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ==}
    dev: true

  /asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  /at-least-node@1.0.0:
    resolution: {integrity: sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==}
    engines: {node: '>= 4.0.0'}

  /atob@2.1.2:
    resolution: {integrity: sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==}
    engines: {node: '>= 4.5.0'}
    hasBin: true

  /autoprefixer@10.4.13(postcss@8.4.19):
    resolution: {integrity: sha512-49vKpMqcZYsJjwotvt4+h/BCjJVnhGwcLpDt5xkcaOG3eLrG/HUYLagrihYsQ+qrIBgIzX1Rw7a6L8I/ZA1Atg==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      browserslist: 4.21.4
      caniuse-lite: 1.0.30001431
      fraction.js: 4.2.0
      normalize-range: 0.1.2
      picocolors: 1.0.0
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /autoprefixer@9.8.8:
    resolution: {integrity: sha512-eM9d/swFopRt5gdJ7jrpCwgvEMIayITpojhkkSMRsFHYuH5bkSQ4p/9qTEHtmNudUZh22Tehu7I6CxAW0IXTKA==}
    hasBin: true
    dependencies:
      browserslist: 4.21.4
      caniuse-lite: 1.0.30001431
      normalize-range: 0.1.2
      num2fraction: 1.2.2
      picocolors: 0.2.1
      postcss: 7.0.39
      postcss-value-parser: 4.2.0
    dev: true

  /aws-sign2@0.7.0:
    resolution: {integrity: sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==}

  /aws4@1.11.0:
    resolution: {integrity: sha512-xh1Rl34h6Fi1DC2WWKfxUTVqRsNnr6LsKz2+hfwDxQJWmrx8+c7ylaqBMcHfl1U1r2dsifOvKX3LQuLNZ+XSvA==}

  /axios@0.21.4:
    resolution: {integrity: sha512-ut5vewkiu8jjGBdqpM44XxjuCjq9LAKeHVmoVfHVzy8eHgxxq8SbAVQNovDA8mVi05kP0Ea/n/UzcSHcTJQfNg==}
    dependencies:
      follow-redirects: 1.15.2(debug@4.3.4)
    transitivePeerDependencies:
      - debug
    dev: false

  /babel-code-frame@6.26.0:
    resolution: {integrity: sha512-XqYMR2dfdGMW+hd0IUZ2PwK+fGeFkOxZJ0wY+JaQAHzt1Zx8LcvpiZD2NiGkEG8qx0CfkAOr5xt76d1e8vG90g==}
    dependencies:
      chalk: 1.1.3
      esutils: 2.0.3
      js-tokens: 3.0.2
    dev: true

  /babel-core@7.0.0-bridge.0(@babel/core@7.20.2):
    resolution: {integrity: sha512-poPX9mZH/5CSanm50Q+1toVci6pv5KSRv/5TWCwtzQS5XEwn40BcCrgIeMFWP9CKKIniKXNxoIOnOq4VVlGXhg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2

  /babel-eslint@10.1.0(eslint@6.8.0):
    resolution: {integrity: sha512-ifWaTHQ0ce+448CYop8AdrQiBsGrnC+bMgfyKFdi6EsPLTAWG+QfyDeM6OH+FmWnKvEq5NnBMLvlBUPKQZoDSg==}
    engines: {node: '>=6'}
    deprecated: babel-eslint is now @babel/eslint-parser. This package will no longer receive updates.
    peerDependencies:
      eslint: '>= 4.12.1'
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/parser': 7.20.3
      '@babel/traverse': 7.20.1
      '@babel/types': 7.20.2
      eslint: 6.8.0
      eslint-visitor-keys: 1.3.0
      resolve: 1.22.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-helper-vue-jsx-merge-props@2.0.3:
    resolution: {integrity: sha512-gsLiKK7Qrb7zYJNgiXKpXblxbV5ffSwR0f5whkPAaBAR4fhi6bwRZxX9wBlIc5M/v8CCkXUbXZL4N/nSE97cqg==}
    dev: false

  /babel-jest@24.9.0(@babel/core@7.20.2):
    resolution: {integrity: sha512-ntuddfyiN+EhMw58PTNL1ph4C9rECiQXjI4nMMBKBaNjXvqLdkXpPRcMSr4iyBrJg/+wz9brFUD6RhOAT6r4Iw==}
    engines: {node: '>= 6'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.20.2
      '@jest/transform': 24.9.0
      '@jest/types': 24.9.0
      '@types/babel__core': 7.1.20
      babel-plugin-istanbul: 5.2.0
      babel-preset-jest: 24.9.0(@babel/core@7.20.2)
      chalk: 2.4.2
      slash: 2.0.0
    transitivePeerDependencies:
      - supports-color

  /babel-loader@8.3.0(@babel/core@7.20.2)(webpack@4.46.0):
    resolution: {integrity: sha512-H8SvsMF+m9t15HNLMipppzkC+Y2Yq+v3SonZyU70RBL/h1gxPkH08Ot8pEE9Z4Kd+czyWJClmFS8qzIP9OZ04Q==}
    engines: {node: '>= 8.9'}
    peerDependencies:
      '@babel/core': ^7.0.0
      webpack: '>=2'
    dependencies:
      '@babel/core': 7.20.2
      find-cache-dir: 3.3.2
      loader-utils: 2.0.4
      make-dir: 3.1.0
      schema-utils: 2.7.1
      webpack: 4.46.0(webpack-cli@4.10.0)
    dev: true

  /babel-messages@6.23.0:
    resolution: {integrity: sha512-Bl3ZiA+LjqaMtNYopA9TYE9HP1tQ+E5dLxE0XrAzcIJeK2UqF0/EaqXwBn9esd4UmTfEab+P+UYQ1GnioFIb/w==}
    dependencies:
      babel-runtime: 6.26.0
    dev: true

  /babel-plugin-istanbul@5.2.0:
    resolution: {integrity: sha512-5LphC0USA8t4i1zCtjbbNb6jJj/9+X6P37Qfirc/70EQ34xKlMW+a1RHGwxGI+SwWpNwZ27HqvzAobeqaXwiZw==}
    engines: {node: '>=6'}
    dependencies:
      '@babel/helper-plugin-utils': 7.20.2
      find-up: 3.0.0
      istanbul-lib-instrument: 3.3.0
      test-exclude: 5.2.3
    transitivePeerDependencies:
      - supports-color

  /babel-plugin-jest-hoist@24.9.0:
    resolution: {integrity: sha512-2EMA2P8Vp7lG0RAzr4HXqtYwacfMErOuv1U3wrvxHX6rD1sV6xS3WXG3r8TRQ2r6w8OhvSdWt+z41hQNwNm3Xw==}
    engines: {node: '>= 6'}
    dependencies:
      '@types/babel__traverse': 7.18.2

  /babel-plugin-polyfill-corejs2@0.3.3(@babel/core@7.20.2):
    resolution: {integrity: sha512-8hOdmFYFSZhqg2C/JgLUQ+t52o5nirNwaWM2B9LWteozwIvM14VSwdsCAUET10qT+kmySAlseadmfeeSWFCy+Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.20.1
      '@babel/core': 7.20.2
      '@babel/helper-define-polyfill-provider': 0.3.3(@babel/core@7.20.2)
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-polyfill-corejs3@0.6.0(@babel/core@7.20.2):
    resolution: {integrity: sha512-+eHqR6OPcBhJOGgsIar7xoAB1GcSwVUA3XjAd7HJNzOXT4wv6/H7KIdA/Nc60cvUlDbKApmqNvD1B1bzOt4nyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-define-polyfill-provider': 0.3.3(@babel/core@7.20.2)
      core-js-compat: 3.26.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-polyfill-regenerator@0.4.1(@babel/core@7.20.2):
    resolution: {integrity: sha512-NtQGmyQDXjQqQ+IzRkBVwEOz9lQ4zxAQZgoAYEtU9dJjnl1Oc98qnN7jcp+bE7O7aYzVpavXE3/VKXNzUbh7aw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-define-polyfill-provider': 0.3.3(@babel/core@7.20.2)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-transform-es2015-modules-commonjs@6.26.2:
    resolution: {integrity: sha512-CV9ROOHEdrjcwhIaJNBGMBCodN+1cfkwtM1SbUHmvyy35KGT7fohbpOxkE2uLz1o6odKK2Ck/tz47z+VqQfi9Q==}
    dependencies:
      babel-plugin-transform-strict-mode: 6.24.1
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-types: 6.26.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-transform-strict-mode@6.24.1:
    resolution: {integrity: sha512-j3KtSpjyLSJxNoCDrhwiJad8kw0gJ9REGj8/CqL0HeRyLnvUNYV9zcqluL6QJSXh3nfsLEmSLvwRfGzrgR96Pw==}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-preset-jest@24.9.0(@babel/core@7.20.2):
    resolution: {integrity: sha512-izTUuhE4TMfTRPF92fFwD2QfdXaZW08qvWTFCI51V8rW5x00UuPgc3ajRoWofXOuxjfcOM5zzSYsQS3H8KGCAg==}
    engines: {node: '>= 6'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.20.2)
      babel-plugin-jest-hoist: 24.9.0

  /babel-runtime@6.26.0:
    resolution: {integrity: sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==}
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1

  /babel-template@6.26.0:
    resolution: {integrity: sha512-PCOcLFW7/eazGUKIoqH97sO9A2UYMahsn/yRQ7uOk37iutwjq7ODtcTNF+iFDSHNfkctqsLRjLP7URnOx0T1fg==}
    dependencies:
      babel-runtime: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
      babylon: 6.18.0
      lodash: 4.17.21
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-traverse@6.26.0:
    resolution: {integrity: sha512-iSxeXx7apsjCHe9c7n8VtRXGzI2Bk1rBSOJgCCjfyXb6v1aCqE1KSEpq/8SXuVN8Ka/Rh1WDTF0MDzkvTA4MIA==}
    dependencies:
      babel-code-frame: 6.26.0
      babel-messages: 6.23.0
      babel-runtime: 6.26.0
      babel-types: 6.26.0
      babylon: 6.18.0
      debug: 2.6.9(supports-color@7.2.0)
      globals: 9.18.0
      invariant: 2.2.4
      lodash: 4.17.21
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-types@6.26.0:
    resolution: {integrity: sha512-zhe3V/26rCWsEZK8kZN+HaQj5yQ1CilTObixFzKW1UWjqG7618Twz6YEsCnjfg5gBcJh02DrpCkS9h98ZqDY+g==}
    dependencies:
      babel-runtime: 6.26.0
      esutils: 2.0.3
      lodash: 4.17.21
      to-fast-properties: 1.0.3
    dev: true

  /babylon@6.18.0:
    resolution: {integrity: sha512-q/UEjfGJ2Cm3oKV71DJz9d25TPnq5rhBVL2Q4fA5wcC3jcrdn7+SssEybFIxwAvvP+YCsCYNKughoF33GxgycQ==}
    hasBin: true
    dev: true

  /balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  /base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  /base@0.11.2:
    resolution: {integrity: sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      cache-base: 1.0.1
      class-utils: 0.3.6
      component-emitter: 1.3.0
      define-property: 1.0.0
      isobject: 3.0.1
      mixin-deep: 1.3.2
      pascalcase: 0.1.1

  /batch@0.6.1:
    resolution: {integrity: sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=}

  /bcrypt-pbkdf@1.0.2:
    resolution: {integrity: sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==}
    dependencies:
      tweetnacl: 0.14.5

  /big.js@5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==}

  /binary-extensions@1.13.1:
    resolution: {integrity: sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw==}
    engines: {node: '>=0.10.0'}
    optional: true

  /binary-extensions@2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==}
    engines: {node: '>=8'}

  /bindings@1.5.0:
    resolution: {integrity: sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==}
    dependencies:
      file-uri-to-path: 1.0.0

  /bl@4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==}
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.0

  /blob-util@2.0.2:
    resolution: {integrity: sha512-T7JQa+zsXXEa6/8ZhHcQEW1UFfVM49Ts65uBkFL6fz2QmrElqmbajIDJvuA0tEhRe5eIjpV9ZF+0RfZR9voJFQ==}
    dev: true

  /bluebird@3.7.2:
    resolution: {integrity: sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==}

  /bn.js@4.12.0:
    resolution: {integrity: sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA==}

  /bn.js@5.2.1:
    resolution: {integrity: sha512-eXRvHzWyYPBuB4NBy0cmYQjGitUrtqwbvlzP3G6VFnNRbsZQIxQ10PbKKHt8gZ/HW/D/747aDl+QkDqg3KQLMQ==}

  /body-parser@1.20.1:
    resolution: {integrity: sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.4
      debug: 2.6.9(supports-color@7.2.0)
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.11.0
      raw-body: 2.5.1
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  /bonjour-service@1.0.14:
    resolution: {integrity: sha512-HIMbgLnk1Vqvs6B4Wq5ep7mxvj9sGz5d1JJyDNSGNIdA/w2MCz6GTjWTdjqOJV1bEPj+6IkxDvWNFKEBxNt4kQ==}
    dependencies:
      array-flatten: 2.1.2
      dns-equal: 1.0.0
      fast-deep-equal: 3.1.3
      multicast-dns: 7.2.5

  /boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}

  /bootstrap-vue@2.23.1(jquery@3.6.1)(vue@2.7.14):
    resolution: {integrity: sha512-SEWkG4LzmMuWjQdSYmAQk1G/oOKm37dtNfjB5kxq0YafnL2W6qUAmeDTcIZVbPiQd2OQlIkWOMPBRGySk/zGsg==}
    requiresBuild: true
    dependencies:
      '@nuxt/opencollective': 0.3.3
      bootstrap: 4.6.2(jquery@3.6.1)(popper.js@1.16.1)
      popper.js: 1.16.1
      portal-vue: 2.1.7(vue@2.7.14)
      vue-functional-data-merge: 3.1.0
    transitivePeerDependencies:
      - encoding
      - jquery
      - vue
    dev: false

  /bootstrap@4.6.2(jquery@3.6.1)(popper.js@1.16.1):
    resolution: {integrity: sha512-51Bbp/Uxr9aTuy6ca/8FbFloBUJZLHwnhTcnjIeRn2suQWsWzcuJhGjKDB5eppVte/8oCdOL3VuwxvZDUggwGQ==}
    peerDependencies:
      jquery: 1.9.1 - 3
      popper.js: ^1.16.1
    dependencies:
      jquery: 3.6.1
      popper.js: 1.16.1
    dev: false

  /brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  /brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}
    dependencies:
      balanced-match: 1.0.2
    dev: true

  /braces@2.3.2:
    resolution: {integrity: sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-flatten: 1.1.0
      array-unique: 0.3.2
      extend-shallow: 2.0.1
      fill-range: 4.0.0
      isobject: 3.0.1
      repeat-element: 1.1.4
      snapdragon: 0.8.2
      snapdragon-node: 2.1.1
      split-string: 3.1.0
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  /braces@3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.0.1

  /brorand@1.1.0:
    resolution: {integrity: sha512-cKV8tMCEpQs4hK/ik71d6LrPOnpkpGBR0wzxqr68g2m/LB2GxVYQroAjMJZRVM1Y4BCjCKc3vAamxSzOY2RP+w==}

  /brotli-webpack-plugin@1.1.0:
    resolution: {integrity: sha512-byxKhV7L/OsN9urBgcD8Gkgq0OW61KYdSBwN249vT77VRl+9iz6TlWDXhg45rwYynZYetr+WRVuUvQ5WzwcYDQ==}
    dependencies:
      async: 2.6.4
      webpack-sources: 1.4.3
    optionalDependencies:
      brotli: 1.3.3
      iltorb: 2.4.5
    dev: true

  /brotli@1.3.3:
    resolution: {integrity: sha512-oTKjJdShmDuGW94SyyaoQvAjf30dZaHnjJ8uAF+u2/vGJkJbJPJAT1gDiOJP5v1Zb6f9KEyW/1HpuaWIXtGHPg==}
    requiresBuild: true
    dependencies:
      base64-js: 1.5.1
    dev: true
    optional: true

  /browser-process-hrtime@1.0.0:
    resolution: {integrity: sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow==}

  /browser-resolve@1.11.3:
    resolution: {integrity: sha512-exDi1BYWB/6raKHmDTCicQfTkqwN5fioMFV4j8BsfMU4R2DK/QfZfK7kOVkmWCNANf0snkBzqGqAJBao9gZMdQ==}
    dependencies:
      resolve: 1.1.7

  /browserify-aes@1.2.0:
    resolution: {integrity: sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==}
    dependencies:
      buffer-xor: 1.0.3
      cipher-base: 1.0.4
      create-hash: 1.2.0
      evp_bytestokey: 1.0.3
      inherits: 2.0.4
      safe-buffer: 5.2.1

  /browserify-cipher@1.0.1:
    resolution: {integrity: sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==}
    dependencies:
      browserify-aes: 1.2.0
      browserify-des: 1.0.2
      evp_bytestokey: 1.0.3

  /browserify-des@1.0.2:
    resolution: {integrity: sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A==}
    dependencies:
      cipher-base: 1.0.4
      des.js: 1.0.1
      inherits: 2.0.4
      safe-buffer: 5.2.1

  /browserify-rsa@4.1.0:
    resolution: {integrity: sha512-AdEER0Hkspgno2aR97SAf6vi0y0k8NuOpGnVH3O99rcA5Q6sh8QxcngtHuJ6uXwnfAXNM4Gn1Gb7/MV1+Ymbog==}
    dependencies:
      bn.js: 5.2.1
      randombytes: 2.1.0

  /browserify-sign@4.2.1:
    resolution: {integrity: sha512-/vrA5fguVAKKAVTNJjgSm1tRQDHUU6DbwO9IROu/0WAzC8PKhucDSh18J0RMvVeHAn5puMd+QHC2erPRNf8lmg==}
    dependencies:
      bn.js: 5.2.1
      browserify-rsa: 4.1.0
      create-hash: 1.2.0
      create-hmac: 1.1.7
      elliptic: 6.5.4
      inherits: 2.0.4
      parse-asn1: 5.1.6
      readable-stream: 3.6.0
      safe-buffer: 5.2.1

  /browserify-zlib@0.2.0:
    resolution: {integrity: sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==}
    dependencies:
      pako: 1.0.11

  /browserslist@4.21.4:
    resolution: {integrity: sha512-CBHJJdDmgjl3daYjN5Cp5kbTf1mUhZoS+beLklHIvkOWscs83YAhLlF3Wsh/lciQYAcbBJgTOD44VtG31ZM4Hw==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001431
      electron-to-chromium: 1.4.284
      node-releases: 2.0.6
      update-browserslist-db: 1.0.10(browserslist@4.21.4)

  /bs-logger@0.2.6:
    resolution: {integrity: sha512-pd8DCoxmbgc7hyPKOvxtqNcjYoOsABPQdcCUjGp3d42VR2CX1ORhk2A87oqqu5R1kk+76nsxZupkmyd+MVtCog==}
    engines: {node: '>= 6'}
    dependencies:
      fast-json-stable-stringify: 2.1.0
    dev: true

  /bser@2.1.1:
    resolution: {integrity: sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==}
    dependencies:
      node-int64: 0.4.0

  /buffer-crc32@0.2.13:
    resolution: {integrity: sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==}
    dev: true

  /buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  /buffer-xor@1.0.3:
    resolution: {integrity: sha512-571s0T7nZWK6vB67HI5dyUF7wXiNcfaPPPTl6zYCNApANjIvYJTg7hlud/+cJpdAhS7dVzqMLmfhfHR3rAcOjQ==}

  /buffer@4.9.2:
    resolution: {integrity: sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
      isarray: 1.0.0

  /buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  /builtin-status-codes@3.0.0:
    resolution: {integrity: sha512-HpGFw18DgFWlncDfjTa2rcQ4W88O1mC8e8yZ2AvQY5KDaktSTwo+KRf6nHK6FRI5FyRyb/5T6+TSxfP7QyGsmQ==}

  /bytes@3.0.0:
    resolution: {integrity: sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=}
    engines: {node: '>= 0.8'}

  /bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  /cacache@12.0.4:
    resolution: {integrity: sha512-a0tMB40oefvuInr4Cwb3GerbL9xTj1D5yg0T5xrjGCGyfvbxseIXX7BAO/u/hIXdafzOI5JC3wDwHyf24buOAQ==}
    dependencies:
      bluebird: 3.7.2
      chownr: 1.1.4
      figgy-pudding: 3.5.2
      glob: 7.2.3
      graceful-fs: 4.2.10
      infer-owner: 1.0.4
      lru-cache: 5.1.1
      mississippi: 3.0.0
      mkdirp: 0.5.6
      move-concurrently: 1.0.1
      promise-inflight: 1.0.1(bluebird@3.7.2)
      rimraf: 2.7.1
      ssri: 6.0.2
      unique-filename: 1.1.1
      y18n: 4.0.3

  /cacache@15.3.0:
    resolution: {integrity: sha512-VVdYzXEn+cnbXpFgWs5hTT7OScegHVmLhJIR8Ufqk3iFD6A6j5iSX1KuBTfNEv4tdJWE2PzA6IVFtcLC7fN9wQ==}
    engines: {node: '>= 10'}
    dependencies:
      '@npmcli/fs': 1.1.1
      '@npmcli/move-file': 1.1.2
      chownr: 2.0.0
      fs-minipass: 2.1.0
      glob: 7.2.3
      infer-owner: 1.0.4
      lru-cache: 6.0.0
      minipass: 3.3.4
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      mkdirp: 1.0.4
      p-map: 4.0.0
      promise-inflight: 1.0.1(bluebird@3.7.2)
      rimraf: 3.0.2
      ssri: 8.0.1
      tar: 6.1.12
      unique-filename: 1.1.1
    transitivePeerDependencies:
      - bluebird
    dev: true

  /cache-base@1.0.1:
    resolution: {integrity: sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      collection-visit: 1.0.0
      component-emitter: 1.3.0
      get-value: 2.0.6
      has-value: 1.0.0
      isobject: 3.0.1
      set-value: 2.0.1
      to-object-path: 0.3.0
      union-value: 1.0.1
      unset-value: 1.0.0

  /cachedir@2.3.0:
    resolution: {integrity: sha512-A+Fezp4zxnit6FanDmv9EqXNAi3vt9DWp51/71UEhXukb7QUuvtv9344h91dyAxuTLoSYJFU299qzR3tzwPAhw==}
    engines: {node: '>=6'}
    dev: true

  /call-bind@1.0.2:
    resolution: {integrity: sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==}
    dependencies:
      function-bind: 1.1.1
      get-intrinsic: 1.1.3

  /caller-callsite@2.0.0:
    resolution: {integrity: sha512-JuG3qI4QOftFsZyOn1qq87fq5grLIyk1JYd5lJmdA+fG7aQ9pA/i3JIJGcO3q0MrRcHlOt1U+ZeHW8Dq9axALQ==}
    engines: {node: '>=4'}
    dependencies:
      callsites: 2.0.0
    dev: true

  /caller-path@2.0.0:
    resolution: {integrity: sha512-MCL3sf6nCSXOwCTzvPKhN18TU7AHTvdtam8DAogxcrJ8Rjfbbg7Lgng64H9Iy+vUV6VGFClN/TyxBkAebLRR4A==}
    engines: {node: '>=4'}
    dependencies:
      caller-callsite: 2.0.0
    dev: true

  /callsites@2.0.0:
    resolution: {integrity: sha512-ksWePWBloaWPxJYQ8TL0JHvtci6G5QTKwQ95RcWAa/lzoAKuAOflGdAK92hpHXjkwb8zLxoLNUoNYZgVsaJzvQ==}
    engines: {node: '>=4'}
    dev: true

  /callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  /camel-case@4.1.2:
    resolution: {integrity: sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==}
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.4.1

  /camelcase@4.1.0:
    resolution: {integrity: sha512-FxAv7HpHrXbh3aPo4o2qxHay2lkLY3x5Mw3KeE4KQE8ysVfziWeRZDwcjauvwBSGEC/nXUPzZy8zeh4HokqOnw==}
    engines: {node: '>=4'}
    dev: true

  /camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  /caniuse-api@3.0.0:
    resolution: {integrity: sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==}
    dependencies:
      browserslist: 4.21.4
      caniuse-lite: 1.0.30001431
      lodash.memoize: 4.1.2
      lodash.uniq: 4.5.0

  /caniuse-lite@1.0.30001431:
    resolution: {integrity: sha512-zBUoFU0ZcxpvSt9IU66dXVT/3ctO1cy4y9cscs1szkPlcWb6pasYM144GqrUygUbT+k7cmUCW61cvskjcv0enQ==}

  /capture-exit@2.0.0:
    resolution: {integrity: sha512-PiT/hQmTonHhl/HFGN+Lx3JJUznrVYJ3+AQsnthneZbvW7x+f08Tk7yLJTLEOUvBTbduLeeBkxEaYXUOUrRq6g==}
    engines: {node: 6.* || 8.* || >= 10.*}
    dependencies:
      rsvp: 4.8.5

  /case-sensitive-paths-webpack-plugin@2.4.0:
    resolution: {integrity: sha512-roIFONhcxog0JSSWbvVAh3OocukmSgpqOH6YpMkCvav/ySIV3JKg4Dc8vYtQjYi/UxpNE36r/9v+VqTQqgkYmw==}
    engines: {node: '>=4'}

  /caseless@0.12.0:
    resolution: {integrity: sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==}

  /chalk@1.1.3:
    resolution: {integrity: sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0
    dev: true

  /chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  /chalk@3.0.0:
    resolution: {integrity: sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  /chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  /chardet@0.7.0:
    resolution: {integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==}

  /check-more-types@2.24.0:
    resolution: {integrity: sha512-Pj779qHxV2tuapviy1bSZNEL1maXr13bPYpsvSDB68HlYcYuhlDrmGd63i0JHMCLKzc7rUSNIrpdJlhVlNwrxA==}
    engines: {node: '>= 0.8.0'}
    dev: true

  /chokidar@2.1.8:
    resolution: {integrity: sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==}
    deprecated: Chokidar 2 does not receive security updates since 2019. Upgrade to chokidar 3 with 15x fewer dependencies
    dependencies:
      anymatch: 2.0.0
      async-each: 1.0.3
      braces: 2.3.2
      glob-parent: 3.1.0
      inherits: 2.0.4
      is-binary-path: 1.0.1
      is-glob: 4.0.3
      normalize-path: 3.0.0
      path-is-absolute: 1.0.1
      readdirp: 2.2.1
      upath: 1.2.0
    optionalDependencies:
      fsevents: 1.2.13
    transitivePeerDependencies:
      - supports-color
    optional: true

  /chokidar@3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.2
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.2

  /chownr@1.1.4:
    resolution: {integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==}

  /chownr@2.0.0:
    resolution: {integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==}
    engines: {node: '>=10'}
    dev: true

  /chrome-trace-event@1.0.3:
    resolution: {integrity: sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==}
    engines: {node: '>=6.0'}

  /ci-info@2.0.0:
    resolution: {integrity: sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==}

  /cipher-base@1.0.4:
    resolution: {integrity: sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q==}
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1

  /class-utils@0.3.6:
    resolution: {integrity: sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      define-property: 0.2.5
      isobject: 3.0.1
      static-extend: 0.1.2

  /clean-css@4.2.4:
    resolution: {integrity: sha512-EJUDT7nDVFDvaQgAo2G/PJvxmp1o/c6iXLbswsBbUFXi1Nr+AjA2cKmfbKDMjMvzEe75g3P6JkaDDAKk96A85A==}
    engines: {node: '>= 4.0'}
    dependencies:
      source-map: 0.6.1
    dev: true

  /clean-css@5.3.1:
    resolution: {integrity: sha512-lCr8OHhiWCTw4v8POJovCoh4T7I9U11yVsPjMWWnnMmp9ZowCxyad1Pathle/9HjaDp+fdQKjO9fQydE6RHTZg==}
    engines: {node: '>= 10.0'}
    dependencies:
      source-map: 0.6.1

  /clean-stack@2.2.0:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==}
    engines: {node: '>=6'}
    dev: true

  /cli-cursor@1.0.2:
    resolution: {integrity: sha512-25tABq090YNKkF6JH7lcwO0zFJTRke4Jcq9iX2nr/Sz0Cjjv4gckmwlW6Ty/aoyFd6z3ysR2hMGC2GFugmBo6A==}
    engines: {node: '>=0.10.0'}
    dependencies:
      restore-cursor: 1.0.1
    dev: true

  /cli-cursor@2.1.0:
    resolution: {integrity: sha512-8lgKz8LmCRYZZQDpRyT2m5rKJ08TnU4tR9FFFW2rxpxR1FzWi4PQ/NfyODchAatHaUgnSPVcx/R5w6NuTBzFiw==}
    engines: {node: '>=4'}
    dependencies:
      restore-cursor: 2.0.0

  /cli-cursor@3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: 3.1.0

  /cli-highlight@2.1.11:
    resolution: {integrity: sha512-9KDcoEVwyUXrjcJNvHD0NFc/hiwe/WPVYIleQh2O1N2Zro5gWJZ/K+3DGn8w8P/F6FxOgzyC5bxDyHIgCSPhGg==}
    engines: {node: '>=8.0.0', npm: '>=5.0.0'}
    hasBin: true
    dependencies:
      chalk: 4.1.2
      highlight.js: 10.7.3
      mz: 2.7.0
      parse5: 5.1.1
      parse5-htmlparser2-tree-adapter: 6.0.1
      yargs: 16.2.0

  /cli-spinners@2.7.0:
    resolution: {integrity: sha512-qu3pN8Y3qHNgE2AFweciB1IfMnmZ/fsNTEE+NOFjmGB2F/7rLhnhzppvpCnN4FovtP26k8lHyy9ptEbNwWFLzw==}
    engines: {node: '>=6'}

  /cli-table3@0.6.3:
    resolution: {integrity: sha512-w5Jac5SykAeZJKntOxJCrm63Eg5/4dhMWIcuTbo9rpE+brgaSZo0RuNJZeOyMgsUdhDeojvgyQLmjI+K50ZGyg==}
    engines: {node: 10.* || >= 12.*}
    dependencies:
      string-width: 4.2.3
    optionalDependencies:
      '@colors/colors': 1.5.0
    dev: true

  /cli-truncate@0.2.1:
    resolution: {integrity: sha512-f4r4yJnbT++qUPI9NR4XLDLq41gQ+uqnPItWG0F5ZkehuNiTTa3EY0S4AqTSUOeJ7/zU41oWPQSNkW5BqPL9bg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      slice-ansi: 0.0.4
      string-width: 1.0.2
    dev: true

  /cli-width@3.0.0:
    resolution: {integrity: sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==}
    engines: {node: '>= 10'}

  /clipboardy@2.3.0:
    resolution: {integrity: sha512-mKhiIL2DrQIsuXMgBgnfEHOZOryC7kY7YO//TN6c63wlEm3NG5tz+YgY5rVi29KCmq/QQjKYvM7a19+MDOTHOQ==}
    engines: {node: '>=8'}
    dependencies:
      arch: 2.2.0
      execa: 1.0.0
      is-wsl: 2.2.0

  /cliui@5.0.0:
    resolution: {integrity: sha512-PYeGSEmmHM6zvoef2w8TPzlrnNpXIjTipYK780YswmIP9vjxmd6Y2a3CB2Ks6/AU8NHjZugXvo8w3oWM2qnwXA==}
    dependencies:
      string-width: 3.1.0
      strip-ansi: 5.2.0
      wrap-ansi: 5.1.0

  /cliui@7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  /clone-deep@4.0.1:
    resolution: {integrity: sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==}
    engines: {node: '>=6'}
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1

  /clone@1.0.4:
    resolution: {integrity: sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==}
    engines: {node: '>=0.8'}

  /clone@2.1.2:
    resolution: {integrity: sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==}
    engines: {node: '>=0.8'}
    dev: true

  /co@4.6.0:
    resolution: {integrity: sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}

  /coa@2.0.2:
    resolution: {integrity: sha512-q5/jG+YQnSy4nRTV4F7lPepBJZ8qBNJJDBuJdoejDyLXgmL7IEo+Le2JDZudFTFt7mrCqIRaSjws4ygRCTCAXA==}
    engines: {node: '>= 4.0'}
    dependencies:
      '@types/q': 1.5.5
      chalk: 2.4.2
      q: 1.5.1
    dev: true

  /code-point-at@1.1.0:
    resolution: {integrity: sha512-RpAVKQA5T63xEj6/giIbUEtZwJ4UFIc3ZtvEkiaUERylqe8xb5IvqcgOurZLahv93CLKfxcw5YI+DZcUBRyLXA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /collection-visit@1.0.0:
    resolution: {integrity: sha512-lNkKvzEeMBBjUGHZ+q6z9pSJla0KWAQPvtzhEV9+iGyQYG+pBpl7xKDhxoNSOZH2hhv0v5k0y2yAM4o4SjoSkw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-visit: 1.0.0
      object-visit: 1.0.1

  /color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3

  /color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4

  /color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  /color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  /color-string@1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2
    dev: true

  /color@3.2.1:
    resolution: {integrity: sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA==}
    dependencies:
      color-convert: 1.9.3
      color-string: 1.9.1
    dev: true

  /colord@2.9.3:
    resolution: {integrity: sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw==}

  /colorette@2.0.19:
    resolution: {integrity: sha512-3tlv/dIP7FWvj3BsbHrGLJ6l/oKh1O3TcgBqMn+yyCagOxc23fyzDS6HypQbgxWbkpDnf52p1LuR4eWDQ/K9WQ==}

  /combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0

  /commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  /commander@4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}
    dev: false

  /commander@5.1.0:
    resolution: {integrity: sha512-P0CysNDQ7rtVw4QIQtm+MRxV66vKFSvlsQvGYXZWR3qFU0jlMKHZZZgw8e+8DSah4UDKMqnknRDQz+xuQXQ/Zg==}
    engines: {node: '>= 6'}
    dev: true

  /commander@7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==}
    engines: {node: '>= 10'}

  /commander@8.3.0:
    resolution: {integrity: sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==}
    engines: {node: '>= 12'}

  /common-tags@1.8.2:
    resolution: {integrity: sha512-gk/Z852D2Wtb//0I+kRFNKKE9dIIVirjoqPoA1wJU+XePVXZfGeBpk45+A1rKO4Q43prqWBNY/MiIeRLbPWUaA==}
    engines: {node: '>=4.0.0'}
    dev: true

  /commondir@1.0.1:
    resolution: {integrity: sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==}

  /component-emitter@1.3.0:
    resolution: {integrity: sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg==}

  /compressible@2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0

  /compression-webpack-plugin@4.0.1(webpack@4.46.0):
    resolution: {integrity: sha512-0mg6PgwTsUe5LEcUrOu3ob32vraDx2VdbMGAT1PARcOV+UJWDYZFdkSo6RbHoGQ061mmmkC7XpRKOlvwm/gzJQ==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      cacache: 15.3.0
      find-cache-dir: 3.3.2
      schema-utils: 2.7.1
      serialize-javascript: 4.0.0
      webpack: 4.46.0(webpack-cli@4.10.0)
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - bluebird
    dev: true

  /compression@1.7.4:
    resolution: {integrity: sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      accepts: 1.3.8
      bytes: 3.0.0
      compressible: 2.0.18
      debug: 2.6.9(supports-color@7.2.0)
      on-headers: 1.0.2
      safe-buffer: 5.1.2
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  /concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=}

  /concat-stream@1.6.2:
    resolution: {integrity: sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==}
    engines: {'0': node >= 0.8}
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 2.3.7
      typedarray: 0.0.6

  /condense-newlines@0.2.1:
    resolution: {integrity: sha512-P7X+QL9Hb9B/c8HI5BFFKmjgBu2XpQuF98WZ9XkO+dBGgk5XgwiQz7o1SmpglNWId3581UcS0SFAWfoIhMHPfg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-whitespace: 0.3.0
      kind-of: 3.2.2
    dev: true

  /config-chain@1.1.13:
    resolution: {integrity: sha512-qj+f8APARXHrM0hraqXYb2/bOVSV4PvJQlNZ/DVj0QrmNM2q2euizkeuVckQ57J+W0mRH6Hvi+k50M4Jul2VRQ==}
    dependencies:
      ini: 1.3.8
      proto-list: 1.2.4
    dev: true

  /connect-history-api-fallback@2.0.0:
    resolution: {integrity: sha512-U73+6lQFmfiNPrYbXqr6kZ1i1wiRqXnp2nhMsINseWXO8lDau0LGEffJ8kQi4EjLZympVgRdvqjAgiZ1tgzDDA==}
    engines: {node: '>=0.8'}

  /consola@2.15.3:
    resolution: {integrity: sha512-9vAdYbHj6x2fLKC4+oPH0kFzY/orMZyG2Aj+kNylHxKGJ/Ed4dpNyAQYwJOdqO4zdM7XpVHmyejQDcQHrnuXbw==}
    dev: false

  /console-browserify@1.2.0:
    resolution: {integrity: sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA==}

  /console-control-strings@1.1.0:
    resolution: {integrity: sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==}
    dev: true
    optional: true

  /consolidate@0.15.1(babel-core@7.0.0-bridge.0)(lodash@4.17.21):
    resolution: {integrity: sha512-DW46nrsMJgy9kqAbPt5rKaCr7uFtpo4mSUvLHIUbJEjm0vo+aY5QLwBUq3FK4tRnJr/X0Psc0C4jf/h+HtXSMw==}
    engines: {node: '>= 0.10.0'}
    peerDependencies:
      arc-templates: ^0.5.3
      atpl: '>=0.7.6'
      babel-core: ^6.26.3
      bracket-template: ^1.1.5
      coffee-script: ^1.12.7
      dot: ^1.1.3
      dust: ^0.3.0
      dustjs-helpers: ^1.7.4
      dustjs-linkedin: ^2.7.5
      eco: ^1.1.0-rc-3
      ect: ^0.5.9
      ejs: ^3.1.5
      haml-coffee: ^1.14.1
      hamlet: ^0.3.3
      hamljs: ^0.6.2
      handlebars: ^4.7.6
      hogan.js: ^3.0.2
      htmling: ^0.0.8
      jade: ^1.11.0
      jazz: ^0.0.18
      jqtpl: ~1.1.0
      just: ^0.1.8
      liquid-node: ^3.0.1
      liquor: ^0.0.5
      lodash: ^4.17.20
      marko: ^3.14.4
      mote: ^0.2.0
      mustache: ^3.0.0
      nunjucks: ^3.2.2
      plates: ~0.4.11
      pug: ^3.0.0
      qejs: ^3.0.5
      ractive: ^1.3.12
      razor-tmpl: ^1.3.1
      react: ^16.13.1
      react-dom: ^16.13.1
      slm: ^2.0.0
      squirrelly: ^5.1.0
      swig: ^1.4.2
      swig-templates: ^2.0.3
      teacup: ^2.0.0
      templayed: '>=0.2.3'
      then-jade: '*'
      then-pug: '*'
      tinyliquid: ^0.2.34
      toffee: ^0.3.6
      twig: ^1.15.2
      twing: ^5.0.2
      underscore: ^1.11.0
      vash: ^0.13.0
      velocityjs: ^2.0.1
      walrus: ^0.10.1
      whiskers: ^0.4.0
    peerDependenciesMeta:
      arc-templates:
        optional: true
      atpl:
        optional: true
      babel-core:
        optional: true
      bracket-template:
        optional: true
      coffee-script:
        optional: true
      dot:
        optional: true
      dust:
        optional: true
      dustjs-helpers:
        optional: true
      dustjs-linkedin:
        optional: true
      eco:
        optional: true
      ect:
        optional: true
      ejs:
        optional: true
      haml-coffee:
        optional: true
      hamlet:
        optional: true
      hamljs:
        optional: true
      handlebars:
        optional: true
      hogan.js:
        optional: true
      htmling:
        optional: true
      jade:
        optional: true
      jazz:
        optional: true
      jqtpl:
        optional: true
      just:
        optional: true
      liquid-node:
        optional: true
      liquor:
        optional: true
      lodash:
        optional: true
      marko:
        optional: true
      mote:
        optional: true
      mustache:
        optional: true
      nunjucks:
        optional: true
      plates:
        optional: true
      pug:
        optional: true
      qejs:
        optional: true
      ractive:
        optional: true
      razor-tmpl:
        optional: true
      react:
        optional: true
      react-dom:
        optional: true
      slm:
        optional: true
      squirrelly:
        optional: true
      swig:
        optional: true
      swig-templates:
        optional: true
      teacup:
        optional: true
      templayed:
        optional: true
      then-jade:
        optional: true
      then-pug:
        optional: true
      tinyliquid:
        optional: true
      toffee:
        optional: true
      twig:
        optional: true
      twing:
        optional: true
      underscore:
        optional: true
      vash:
        optional: true
      velocityjs:
        optional: true
      walrus:
        optional: true
      whiskers:
        optional: true
    dependencies:
      babel-core: 7.0.0-bridge.0(@babel/core@7.20.2)
      bluebird: 3.7.2
      lodash: 4.17.21

  /constants-browserify@1.0.0:
    resolution: {integrity: sha512-xFxOwqIzR/e1k1gLiWEophSCMqXcwVHIH7akf7b/vxcUeGunlj3hvZaaqxwHsTgn+IndtkQJgSztIDWeumWJDQ==}

  /contains-path@0.1.0:
    resolution: {integrity: sha512-OKZnPGeMQy2RPaUIBPFFd71iNf4791H12MCRuVQDnzGRwCYNYmTDy5pdafo2SLAcEMKzTOQnLWG4QdcjeJUMEg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /content-disposition@0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}
    dependencies:
      safe-buffer: 5.2.1

  /content-type@1.0.4:
    resolution: {integrity: sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==}
    engines: {node: '>= 0.6'}

  /convert-source-map@1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}

  /cookie-signature@1.0.6:
    resolution: {integrity: sha1-4wOogrNCzD7oylE6eZmXNNqzriw=}

  /cookie@0.5.0:
    resolution: {integrity: sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==}
    engines: {node: '>= 0.6'}

  /copy-concurrently@1.0.5:
    resolution: {integrity: sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A==}
    dependencies:
      aproba: 1.2.0
      fs-write-stream-atomic: 1.0.10
      iferr: 0.1.5
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3

  /copy-descriptor@0.1.1:
    resolution: {integrity: sha512-XgZ0pFcakEUlbwQEVNg3+QAis1FyTL3Qel9FYy8pSkQqoG3PNoT0bOCQtOXcOkur21r2Eq2kI+IE+gsmAEVlYw==}
    engines: {node: '>=0.10.0'}

  /copy-webpack-plugin@9.1.0(webpack@5.75.0):
    resolution: {integrity: sha512-rxnR7PaGigJzhqETHGmAcxKnLZSR5u1Y3/bcIv/1FnqXedcL/E2ewK7ZCNrArJKCiSv8yVXhTqetJh8inDvfsA==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.1.0
    dependencies:
      fast-glob: 3.2.12
      glob-parent: 6.0.2
      globby: 11.1.0
      normalize-path: 3.0.0
      schema-utils: 3.1.1
      serialize-javascript: 6.0.0
      webpack: 5.75.0(webpack-cli@4.10.0)

  /core-js-compat@3.26.0:
    resolution: {integrity: sha512-piOX9Go+Z4f9ZiBFLnZ5VrOpBl0h7IGCkiFUN11QTe6LjAvOT3ifL/5TdoizMh99hcGy5SoLyWbapIY/PIb/3A==}
    dependencies:
      browserslist: 4.21.4
    dev: true

  /core-js@2.6.12:
    resolution: {integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.
    requiresBuild: true

  /core-js@3.26.0:
    resolution: {integrity: sha512-+DkDrhoR4Y0PxDz6rurahuB+I45OsEUv8E1maPTB6OuHRohMMcznBq9TMpdpDMm/hUPob/mJJS3PqgbHpMTQgw==}
    requiresBuild: true
    dev: false

  /core-util-is@1.0.2:
    resolution: {integrity: sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==}

  /core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  /cosmiconfig@5.2.1:
    resolution: {integrity: sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA==}
    engines: {node: '>=4'}
    dependencies:
      import-fresh: 2.0.0
      is-directory: 0.3.1
      js-yaml: 3.14.1
      parse-json: 4.0.0
    dev: true

  /cosmiconfig@7.0.1:
    resolution: {integrity: sha512-a1YWNUV2HwGimB7dU2s1wUMurNKjpx60HxBB6xUM8Re+2s1g1IIfJvFR0/iCF+XHdE0GMTKTuLR32UQff4TEyQ==}
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': 4.0.0
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2

  /create-ecdh@4.0.4:
    resolution: {integrity: sha512-mf+TCx8wWc9VpuxfP2ht0iSISLZnt0JgWlrOKZiNqyUZWnjIaCIVNQArMHnCZKfEYRg6IM7A+NeJoN8gf/Ws0A==}
    dependencies:
      bn.js: 4.12.0
      elliptic: 6.5.4

  /create-hash@1.2.0:
    resolution: {integrity: sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==}
    dependencies:
      cipher-base: 1.0.4
      inherits: 2.0.4
      md5.js: 1.3.5
      ripemd160: 2.0.2
      sha.js: 2.4.11

  /create-hmac@1.1.7:
    resolution: {integrity: sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==}
    dependencies:
      cipher-base: 1.0.4
      create-hash: 1.2.0
      inherits: 2.0.4
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11

  /cross-spawn@6.0.5:
    resolution: {integrity: sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==}
    engines: {node: '>=4.8'}
    dependencies:
      nice-try: 1.0.5
      path-key: 2.0.1
      semver: 5.7.1
      shebang-command: 1.2.0
      which: 1.3.1

  /cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  /crypto-browserify@3.12.0:
    resolution: {integrity: sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg==}
    dependencies:
      browserify-cipher: 1.0.1
      browserify-sign: 4.2.1
      create-ecdh: 4.0.4
      create-hash: 1.2.0
      create-hmac: 1.1.7
      diffie-hellman: 5.0.3
      inherits: 2.0.4
      pbkdf2: 3.1.2
      public-encrypt: 4.0.3
      randombytes: 2.1.0
      randomfill: 1.0.4

  /css-color-names@0.0.4:
    resolution: {integrity: sha512-zj5D7X1U2h2zsXOAM8EyUREBnnts6H+Jm+d1M2DbiQQcUtnqgQsMrdo8JW9R80YFUmIdBZeMu5wvYM7hcgWP/Q==}
    dev: true

  /css-declaration-sorter@4.0.1:
    resolution: {integrity: sha512-BcxQSKTSEEQUftYpBVnsH4SF05NTuBokb19/sBt6asXGKZ/6VP7PLG1CBCkFDYOnhXhPh0jMhO6xZ71oYHXHBA==}
    engines: {node: '>4'}
    dependencies:
      postcss: 7.0.39
      timsort: 0.3.0
    dev: true

  /css-declaration-sorter@6.3.1(postcss@8.4.19):
    resolution: {integrity: sha512-fBffmak0bPAnyqc/HO8C3n2sHrp9wcqQz6ES9koRF2/mLOVAx9zIQ3Y7R29sYCteTPqMCwns4WYQoCX91Xl3+w==}
    engines: {node: ^10 || ^12 || >=14}
    peerDependencies:
      postcss: ^8.0.9
    dependencies:
      postcss: 8.4.19

  /css-loader@3.6.0(webpack@4.46.0):
    resolution: {integrity: sha512-M5lSukoWi1If8dhQAUCvj4H8vUt3vOnwbQBH9DdTm/s4Ym2B/3dPMtYZeJmq7Q3S3Pa+I94DcZ7pc9bP14cWIQ==}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      camelcase: 5.3.1
      cssesc: 3.0.0
      icss-utils: 4.1.1
      loader-utils: 1.4.2
      normalize-path: 3.0.0
      postcss: 7.0.39
      postcss-modules-extract-imports: 2.0.0
      postcss-modules-local-by-default: 3.0.3
      postcss-modules-scope: 2.2.0
      postcss-modules-values: 3.0.0
      postcss-value-parser: 4.2.0
      schema-utils: 2.7.1
      semver: 6.3.0
      webpack: 4.46.0(webpack-cli@4.10.0)
    dev: true

  /css-loader@6.7.1(webpack@5.75.0):
    resolution: {integrity: sha512-yB5CNFa14MbPJcomwNh3wLThtkZgcNyI2bNMRt8iE5Z8Vwl7f8vQXFAzn2HDOJvtDq2NTZBUGMSUNNyrv3/+cw==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.19)
      postcss: 8.4.19
      postcss-modules-extract-imports: 3.0.0(postcss@8.4.19)
      postcss-modules-local-by-default: 4.0.0(postcss@8.4.19)
      postcss-modules-scope: 3.0.0(postcss@8.4.19)
      postcss-modules-values: 4.0.0(postcss@8.4.19)
      postcss-value-parser: 4.2.0
      semver: 7.3.8
      webpack: 5.75.0(webpack-cli@4.10.0)

  /css-minimizer-webpack-plugin@3.4.1(webpack@5.75.0):
    resolution: {integrity: sha512-1u6D71zeIfgngN2XNRJefc/hY7Ybsxd74Jm4qngIXyUEk7fss3VUzuHxLAq/R8NAba4QU9OUSaMZlbpRc7bM4Q==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      '@parcel/css': '*'
      clean-css: '*'
      csso: '*'
      esbuild: '*'
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@parcel/css':
        optional: true
      clean-css:
        optional: true
      csso:
        optional: true
      esbuild:
        optional: true
    dependencies:
      cssnano: 5.1.14(postcss@8.4.19)
      jest-worker: 27.5.1
      postcss: 8.4.19
      schema-utils: 4.0.0
      serialize-javascript: 6.0.0
      source-map: 0.6.1
      webpack: 5.75.0(webpack-cli@4.10.0)

  /css-select-base-adapter@0.1.1:
    resolution: {integrity: sha512-jQVeeRG70QI08vSTwf1jHxp74JoZsr2XSgETae8/xC8ovSnL2WF87GTLO86Sbwdt2lK4Umg4HnnwMO4YF3Ce7w==}
    dev: true

  /css-select@2.1.0:
    resolution: {integrity: sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ==}
    dependencies:
      boolbase: 1.0.0
      css-what: 3.4.2
      domutils: 1.7.0
      nth-check: 1.0.2
    dev: true

  /css-select@4.3.0:
    resolution: {integrity: sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==}
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1

  /css-tree@1.0.0-alpha.37:
    resolution: {integrity: sha512-DMxWJg0rnz7UgxKT0Q1HU/L9BeJI0M6ksor0OgqOnF+aRCDWg/N2641HmVyU9KVIu0OVVWOb2IpC9A+BJRnejg==}
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: 2.0.4
      source-map: 0.6.1
    dev: true

  /css-tree@1.1.3:
    resolution: {integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==}
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1

  /css-what@3.4.2:
    resolution: {integrity: sha512-ACUm3L0/jiZTqfzRM3Hi9Q8eZqd6IK37mMWPLz9PJxkLWllYeRf+EHUSHYEtFop2Eqytaq1FizFVh7XfBnXCDQ==}
    engines: {node: '>= 6'}
    dev: true

  /css-what@6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}

  /css@2.2.4:
    resolution: {integrity: sha512-oUnjmWpy0niI3x/mPL8dVEI1l7MnG3+HHyRPHf+YFSbK+svOhXpmSOcDURUh2aOCgl2grzrOPt1nHLuCVFULLw==}
    dependencies:
      inherits: 2.0.4
      source-map: 0.6.1
      source-map-resolve: 0.5.3
      urix: 0.1.0
    dev: true

  /cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  /cssnano-preset-default@4.0.8:
    resolution: {integrity: sha512-LdAyHuq+VRyeVREFmuxUZR1TXjQm8QQU/ktoo/x7bz+SdOge1YKc5eMN6pRW7YWBmyq59CqYba1dJ5cUukEjLQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      css-declaration-sorter: 4.0.1
      cssnano-util-raw-cache: 4.0.1
      postcss: 7.0.39
      postcss-calc: 7.0.5
      postcss-colormin: 4.0.3
      postcss-convert-values: 4.0.1
      postcss-discard-comments: 4.0.2
      postcss-discard-duplicates: 4.0.2
      postcss-discard-empty: 4.0.1
      postcss-discard-overridden: 4.0.1
      postcss-merge-longhand: 4.0.11
      postcss-merge-rules: 4.0.3
      postcss-minify-font-values: 4.0.2
      postcss-minify-gradients: 4.0.2
      postcss-minify-params: 4.0.2
      postcss-minify-selectors: 4.0.2
      postcss-normalize-charset: 4.0.1
      postcss-normalize-display-values: 4.0.2
      postcss-normalize-positions: 4.0.2
      postcss-normalize-repeat-style: 4.0.2
      postcss-normalize-string: 4.0.2
      postcss-normalize-timing-functions: 4.0.2
      postcss-normalize-unicode: 4.0.1
      postcss-normalize-url: 4.0.1
      postcss-normalize-whitespace: 4.0.2
      postcss-ordered-values: 4.1.2
      postcss-reduce-initial: 4.0.3
      postcss-reduce-transforms: 4.0.2
      postcss-svgo: 4.0.3
      postcss-unique-selectors: 4.0.1
    dev: true

  /cssnano-preset-default@5.2.13(postcss@8.4.19):
    resolution: {integrity: sha512-PX7sQ4Pb+UtOWuz8A1d+Rbi+WimBIxJTRyBdgGp1J75VU0r/HFQeLnMYgHiCAp6AR4rqrc7Y4R+1Rjk3KJz6DQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      css-declaration-sorter: 6.3.1(postcss@8.4.19)
      cssnano-utils: 3.1.0(postcss@8.4.19)
      postcss: 8.4.19
      postcss-calc: 8.2.4(postcss@8.4.19)
      postcss-colormin: 5.3.0(postcss@8.4.19)
      postcss-convert-values: 5.1.3(postcss@8.4.19)
      postcss-discard-comments: 5.1.2(postcss@8.4.19)
      postcss-discard-duplicates: 5.1.0(postcss@8.4.19)
      postcss-discard-empty: 5.1.1(postcss@8.4.19)
      postcss-discard-overridden: 5.1.0(postcss@8.4.19)
      postcss-merge-longhand: 5.1.7(postcss@8.4.19)
      postcss-merge-rules: 5.1.3(postcss@8.4.19)
      postcss-minify-font-values: 5.1.0(postcss@8.4.19)
      postcss-minify-gradients: 5.1.1(postcss@8.4.19)
      postcss-minify-params: 5.1.4(postcss@8.4.19)
      postcss-minify-selectors: 5.2.1(postcss@8.4.19)
      postcss-normalize-charset: 5.1.0(postcss@8.4.19)
      postcss-normalize-display-values: 5.1.0(postcss@8.4.19)
      postcss-normalize-positions: 5.1.1(postcss@8.4.19)
      postcss-normalize-repeat-style: 5.1.1(postcss@8.4.19)
      postcss-normalize-string: 5.1.0(postcss@8.4.19)
      postcss-normalize-timing-functions: 5.1.0(postcss@8.4.19)
      postcss-normalize-unicode: 5.1.1(postcss@8.4.19)
      postcss-normalize-url: 5.1.0(postcss@8.4.19)
      postcss-normalize-whitespace: 5.1.1(postcss@8.4.19)
      postcss-ordered-values: 5.1.3(postcss@8.4.19)
      postcss-reduce-initial: 5.1.1(postcss@8.4.19)
      postcss-reduce-transforms: 5.1.0(postcss@8.4.19)
      postcss-svgo: 5.1.0(postcss@8.4.19)
      postcss-unique-selectors: 5.1.1(postcss@8.4.19)

  /cssnano-util-get-arguments@4.0.0:
    resolution: {integrity: sha512-6RIcwmV3/cBMG8Aj5gucQRsJb4vv4I4rn6YjPbVWd5+Pn/fuG+YseGvXGk00XLkoZkaj31QOD7vMUpNPC4FIuw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /cssnano-util-get-match@4.0.0:
    resolution: {integrity: sha512-JPMZ1TSMRUPVIqEalIBNoBtAYbi8okvcFns4O0YIhcdGebeYZK7dMyHJiQ6GqNBA9kE0Hym4Aqym5rPdsV/4Cw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /cssnano-util-raw-cache@4.0.1:
    resolution: {integrity: sha512-qLuYtWK2b2Dy55I8ZX3ky1Z16WYsx544Q0UWViebptpwn/xDBmog2TLg4f+DBMg1rJ6JDWtn96WHbOKDWt1WQA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /cssnano-util-same-parent@4.0.1:
    resolution: {integrity: sha512-WcKx5OY+KoSIAxBW6UBBRay1U6vkYheCdjyVNDm85zt5K9mHoGOfsOsqIszfAqrQQFIIKgjh2+FDgIj/zsl21Q==}
    engines: {node: '>=6.9.0'}
    dev: true

  /cssnano-utils@3.1.0(postcss@8.4.19):
    resolution: {integrity: sha512-JQNR19/YZhz4psLX/rQ9M83e3z2Wf/HdJbryzte4a3NSuafyp9w/I4U+hx5C2S9g41qlstH7DEWnZaaj83OuEA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19

  /cssnano@4.1.11:
    resolution: {integrity: sha512-6gZm2htn7xIPJOHY824ERgj8cNPgPxyCSnkXc4v7YvNW+TdVfzgngHcEhy/8D11kUWRUMbke+tC+AUcUsnMz2g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cosmiconfig: 5.2.1
      cssnano-preset-default: 4.0.8
      is-resolvable: 1.1.0
      postcss: 7.0.39
    dev: true

  /cssnano@5.1.14(postcss@8.4.19):
    resolution: {integrity: sha512-Oou7ihiTocbKqi0J1bB+TRJIQX5RMR3JghA8hcWSw9mjBLQ5Y3RWqEDoYG3sRNlAbCIXpqMoZGbq5KDR3vdzgw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      cssnano-preset-default: 5.2.13(postcss@8.4.19)
      lilconfig: 2.0.6
      postcss: 8.4.19
      yaml: 1.10.2

  /csso@4.2.0:
    resolution: {integrity: sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      css-tree: 1.1.3

  /cssom@0.3.8:
    resolution: {integrity: sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==}

  /cssom@0.4.4:
    resolution: {integrity: sha512-p3pvU7r1MyyqbTk+WbNJIgJjG2VmTIaB10rI93LzVPrmDJKkzKYMtxxyAvQXR/NS6otuzveI7+7BBq3SjBS2mw==}
    dev: true

  /cssstyle@1.4.0:
    resolution: {integrity: sha512-GBrLZYZ4X4x6/QEoBnIrqb8B/f5l4+8me2dkom/j1Gtbxy0kBv6OGzKuAsGM75bkGwGAFkt56Iwg28S3XTZgSA==}
    dependencies:
      cssom: 0.3.8

  /cssstyle@2.3.0:
    resolution: {integrity: sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A==}
    engines: {node: '>=8'}
    dependencies:
      cssom: 0.3.8
    dev: true

  /csstype@3.1.1:
    resolution: {integrity: sha512-DJR/VvkAvSZW9bTouZue2sSxDwdTN92uHjqeKVm+0dAqdfNykRzQ95tay8aXMBAAPpUiq4Qcug2L7neoRh2Egw==}

  /cyclist@1.0.1:
    resolution: {integrity: sha512-NJGVKPS81XejHcLhaLJS7plab0fK3slPh11mESeeDq2W4ZI5kUKK/LRRdVDvjJseojbPB7ZwjnyOybg3Igea/A==}

  /cypress@6.9.1:
    resolution: {integrity: sha512-/RVx6sOhsyTR9sd9v0BHI4tnDZAhsH9rNat7CIKCUEr5VPWxyfGH0EzK4IHhAqAH8vjFcD4U14tPiJXshoUrmQ==}
    engines: {node: '>=10.0.0'}
    hasBin: true
    requiresBuild: true
    dependencies:
      '@cypress/listr-verbose-renderer': 0.4.1
      '@cypress/request': 2.88.10
      '@cypress/xvfb': 1.2.4(supports-color@7.2.0)
      '@types/node': 12.12.50
      '@types/sinonjs__fake-timers': 6.0.4
      '@types/sizzle': 2.3.3
      arch: 2.2.0
      blob-util: 2.0.2
      bluebird: 3.7.2
      cachedir: 2.3.0
      chalk: 4.1.2
      check-more-types: 2.24.0
      cli-table3: 0.6.3
      commander: 5.1.0
      common-tags: 1.8.2
      dayjs: 1.11.6
      debug: 4.3.2(supports-color@7.2.0)
      eventemitter2: 6.4.9
      execa: 4.1.0
      executable: 4.1.1
      extract-zip: 1.7.0(supports-color@7.2.0)
      fs-extra: 9.1.0
      getos: 3.2.1
      is-ci: 2.0.0
      is-installed-globally: 0.3.2
      lazy-ass: 1.6.0
      listr: 0.14.3
      lodash: 4.17.21
      log-symbols: 4.1.0
      minimist: 1.2.7
      moment: 2.29.4
      ospath: 1.2.2
      pretty-bytes: 5.6.0
      ramda: 0.27.2
      request-progress: 3.0.0
      supports-color: 7.2.0
      tmp: 0.2.1
      untildify: 4.0.0
      url: 0.11.0
      yauzl: 2.10.0
    transitivePeerDependencies:
      - zen-observable
      - zenObservable
    dev: true

  /dashdash@1.14.1:
    resolution: {integrity: sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==}
    engines: {node: '>=0.10'}
    dependencies:
      assert-plus: 1.0.0

  /data-urls@1.1.0:
    resolution: {integrity: sha512-YTWYI9se1P55u58gL5GkQHW4P6VJBJ5iBT+B5a7i2Tjadhv52paJG0qHX4A0OR6/t52odI64KP2YvFpkDOi3eQ==}
    dependencies:
      abab: 2.0.6
      whatwg-mimetype: 2.3.0
      whatwg-url: 7.1.0

  /date-fns-tz@1.3.7(date-fns@2.29.3):
    resolution: {integrity: sha512-1t1b8zyJo+UI8aR+g3iqr5fkUHWpd58VBx8J/ZSQ+w7YrGlw80Ag4sA86qkfCXRBLmMc4I2US+aPMd4uKvwj5g==}
    peerDependencies:
      date-fns: '>=2.0.0'
    dependencies:
      date-fns: 2.29.3
    dev: false

  /date-fns@1.30.1:
    resolution: {integrity: sha512-hBSVCvSmWC+QypYObzwGOd9wqdDpOt+0wl0KbU+R+uuZBS1jN8VsD1ss3irQDknRj5NvxiTF6oj/nDRnN/UQNw==}
    dev: true

  /date-fns@2.29.3:
    resolution: {integrity: sha512-dDCnyH2WnnKusqvZZ6+jA1O51Ibt8ZMRNkDZdyAyK4YfbDwa/cEmuztzG5pk6hqlp9aSBPYcjOlktquahGwGeA==}
    engines: {node: '>=0.11'}
    dev: false

  /dayjs@1.11.6:
    resolution: {integrity: sha512-zZbY5giJAinCG+7AGaw0wIhNZ6J8AhWuSXKvuc1KAyMiRsvGQWqh4L+MomvhdAYjN+lqvVCMq1I41e3YHvXkyQ==}
    dev: true

  /de-indent@1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==}

  /deasync@0.1.28:
    resolution: {integrity: sha512-QqLF6inIDwiATrfROIyQtwOQxjZuek13WRYZ7donU5wJPLoP67MnYxA6QtqdvdBy2mMqv5m3UefBVdJjvevOYg==}
    engines: {node: '>=0.11.0'}
    requiresBuild: true
    dependencies:
      bindings: 1.5.0
      node-addon-api: 1.7.2
    dev: true

  /debug-log@1.0.1:
    resolution: {integrity: sha512-gV/pe1YIaKNgLYnd1g9VNW80tcb7oV5qvNUxG7NM8rbDpnl6RGunzlAtlGSb0wEs3nesu2vHNiX9TSsZ+Y+RjA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /debug@2.6.9(supports-color@7.2.0):
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.0.0
      supports-color: 7.2.0

  /debug@3.2.7(supports-color@7.2.0):
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3
      supports-color: 7.2.0

  /debug@4.3.2(supports-color@7.2.0):
    resolution: {integrity: sha512-mOp8wKcvj7XxC78zLgw/ZA+6TSgkoE2C/ienthhRD298T7UNwAg9diBpLRxC0mOezLl4B0xV7M0cCO6P/O0Xhw==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
      supports-color: 7.2.0
    dev: true

  /debug@4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2

  /decamelize@1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}

  /decode-uri-component@0.2.0:
    resolution: {integrity: sha512-hjf+xovcEn31w/EUYdTXQh/8smFL/dzYjohQGEIgjyNavaJfBY2p5F527Bo1VPATxv0VYTUC2bOcXvqFwk78Og==}
    engines: {node: '>=0.10'}

  /decompress-response@4.2.1:
    resolution: {integrity: sha512-jOSne2qbyE+/r8G1VU+G/82LBs2Fs4LAsTiLSHOCOMZQl2OKZ6i8i4IyHemTe+/yIXOtTcRQMzPcgyhoFlqPkw==}
    engines: {node: '>=8'}
    dependencies:
      mimic-response: 2.1.0
    dev: true
    optional: true

  /deep-extend@0.6.0:
    resolution: {integrity: sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==}
    engines: {node: '>=4.0.0'}
    dev: true
    optional: true

  /deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  /deepmerge@1.5.2:
    resolution: {integrity: sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ==}
    engines: {node: '>=0.10.0'}

  /deepmerge@4.2.2:
    resolution: {integrity: sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg==}
    engines: {node: '>=0.10.0'}

  /default-gateway@6.0.3:
    resolution: {integrity: sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==}
    engines: {node: '>= 10'}
    dependencies:
      execa: 5.1.1

  /defaults@1.0.4:
    resolution: {integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==}
    dependencies:
      clone: 1.0.4

  /define-lazy-prop@2.0.0:
    resolution: {integrity: sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==}
    engines: {node: '>=8'}

  /define-properties@1.1.4:
    resolution: {integrity: sha512-uckOqKcfaVvtBdsVkdPv3XjveQJsNQqmhXgRi8uhvWWuPYZCNlzT8qAyblUgNoXdHdjMTzAqeGjAoli8f+bzPA==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-property-descriptors: 1.0.0
      object-keys: 1.1.1

  /define-property@0.2.5:
    resolution: {integrity: sha512-Rr7ADjQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 0.1.6

  /define-property@1.0.0:
    resolution: {integrity: sha512-cZTYKFWspt9jZsMscWo8sc/5lbPC9Q0N5nBLgb+Yd915iL3udB1uFgS3B8YCx66UVHq018DAVFoee7x+gxggeA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.2

  /define-property@2.0.2:
    resolution: {integrity: sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.2
      isobject: 3.0.1

  /deglob@4.0.1:
    resolution: {integrity: sha512-/g+RDZ7yf2HvoW+E5Cy+K94YhgcFgr6C8LuHZD1O5HoNPkf3KY6RfXJ0DBGlB/NkLi5gml+G9zqRzk9S0mHZCg==}
    dependencies:
      find-root: 1.1.0
      glob: 7.2.3
      ignore: 5.2.0
      pkg-config: 1.1.1
      run-parallel: 1.2.0
      uniq: 1.0.1
    dev: true

  /delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  /delegates@1.0.0:
    resolution: {integrity: sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==}
    dev: true
    optional: true

  /depd@1.1.2:
    resolution: {integrity: sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==}
    engines: {node: '>= 0.6'}

  /depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  /des.js@1.0.1:
    resolution: {integrity: sha512-Q0I4pfFrv2VPd34/vfLrFOoRmlYj3OV50i7fskps1jZWK1kApMWWT9G6RRUeYedLcBDIhnSDaUvJMb3AhUlaEA==}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1

  /destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  /detect-libc@1.0.3:
    resolution: {integrity: sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==}
    engines: {node: '>=0.10'}
    hasBin: true
    dev: true
    optional: true

  /detect-newline@2.1.0:
    resolution: {integrity: sha512-CwffZFvlJffUg9zZA0uqrjQayUTC8ob94pnr5sFwaVv3IOmkfUHcWH+jXaQK3askE51Cqe8/9Ql/0uXNwqZ8Zg==}
    engines: {node: '>=0.10.0'}

  /detect-node@2.1.0:
    resolution: {integrity: sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==}

  /diff-sequences@24.9.0:
    resolution: {integrity: sha512-Dj6Wk3tWyTE+Fo1rW8v0Xhwk80um6yFYKbuAxc9c3EZxIHFDYwbi34Uk42u1CdnIiVorvt4RmlSDjIPyzGC2ew==}
    engines: {node: '>= 6'}

  /diffie-hellman@5.0.3:
    resolution: {integrity: sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==}
    dependencies:
      bn.js: 4.12.0
      miller-rabin: 4.0.1
      randombytes: 2.1.0

  /dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}
    dependencies:
      path-type: 4.0.0

  /dns-equal@1.0.0:
    resolution: {integrity: sha512-z+paD6YUQsk+AbGCEM4PrOXSss5gd66QfcVBFTKR/HpFL9jCqikS94HYwKww6fQyO7IxrIIyUu+g0Ka9tUS2Cg==}

  /dns-packet@5.4.0:
    resolution: {integrity: sha512-EgqGeaBB8hLiHLZtp/IbaDQTL8pZ0+IvwzSHA6d7VyMDM+B9hgddEMa9xjK5oYnw0ci0JQ6g2XCD7/f6cafU6g==}
    engines: {node: '>=6'}
    dependencies:
      '@leichtgewicht/ip-codec': 2.0.4

  /doctrine@1.5.0:
    resolution: {integrity: sha512-lsGyRuYr4/PIB0txi+Fy2xOMI2dGaTguCaotzFGkVZuKR5usKfcRWIFKNM3QNrU7hh/+w2bwTW+ZeXPK5l8uVg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      esutils: 2.0.3
      isarray: 1.0.0
    dev: true

  /doctrine@2.1.0:
    resolution: {integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /doctrine@3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: 2.0.3

  /dom-converter@0.2.0:
    resolution: {integrity: sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==}
    dependencies:
      utila: 0.4.0

  /dom-event-types@1.1.0:
    resolution: {integrity: sha512-jNCX+uNJ3v38BKvPbpki6j5ItVlnSqVV6vDWGS6rExzCMjsc39frLjm1n91o6YaKK6AZl0wLloItW6C6mr61BQ==}
    dev: true

  /dom-serializer@0.2.2:
    resolution: {integrity: sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==}
    dependencies:
      domelementtype: 2.3.0
      entities: 2.2.0
    dev: true

  /dom-serializer@1.4.1:
    resolution: {integrity: sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0

  /domain-browser@1.2.0:
    resolution: {integrity: sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA==}
    engines: {node: '>=0.4', npm: '>=1.2'}

  /domelementtype@1.3.1:
    resolution: {integrity: sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w==}
    dev: true

  /domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}

  /domexception@1.0.1:
    resolution: {integrity: sha512-raigMkn7CJNNo6Ihro1fzG7wr3fHuYVytzquZKX5n0yizGsTcYgzdIUwj1X9pK0VvjeihV+XiclP+DjwbsSKug==}
    dependencies:
      webidl-conversions: 4.0.2

  /domhandler@4.3.1:
    resolution: {integrity: sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.3.0

  /domutils@1.7.0:
    resolution: {integrity: sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg==}
    dependencies:
      dom-serializer: 0.2.2
      domelementtype: 1.3.1
    dev: true

  /domutils@2.8.0:
    resolution: {integrity: sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==}
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1

  /dot-case@3.0.4:
    resolution: {integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.4.1

  /dot-object@2.1.4:
    resolution: {integrity: sha512-7FXnyyCLFawNYJ+NhkqyP9Wd2yzuo+7n9pGiYpkmXCTYa8Ci2U0eUNDVg5OuO5Pm6aFXI2SWN8/N/w7SJWu1WA==}
    hasBin: true
    dependencies:
      commander: 4.1.1
      glob: 7.2.3
    dev: false

  /dot-prop@5.3.0:
    resolution: {integrity: sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==}
    engines: {node: '>=8'}
    dependencies:
      is-obj: 2.0.0
    dev: true

  /dotenv-expand@5.1.0:
    resolution: {integrity: sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==}

  /dotenv@10.0.0:
    resolution: {integrity: sha512-rlBi9d8jpv9Sf1klPjNfFAuWDjKLwTIJJ/VxtoTwIR6hnZxcEOQCZg2oIL3MWBYw5GpUDKOEnND7LXTbIpQ03Q==}
    engines: {node: '>=10'}

  /duplexer@0.1.2:
    resolution: {integrity: sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==}

  /duplexify@3.7.1:
    resolution: {integrity: sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g==}
    dependencies:
      end-of-stream: 1.4.4
      inherits: 2.0.4
      readable-stream: 2.3.7
      stream-shift: 1.0.1

  /easy-stack@1.0.1:
    resolution: {integrity: sha512-wK2sCs4feiiJeFXn3zvY0p41mdU5VUgbgs1rNsc/y5ngFUijdWd+iIN8eoyuZHKB8xN6BL4PdWmzqFmxNg6V2w==}
    engines: {node: '>=6.0.0'}

  /ecc-jsbn@0.1.2:
    resolution: {integrity: sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==}
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2

  /editorconfig@0.15.3:
    resolution: {integrity: sha512-M9wIMFx96vq0R4F+gRpY3o2exzb8hEj/n9S8unZtHSvYjibBp/iMufSzvmOcV/laG0ZtuTVGtiJggPOSW2r93g==}
    hasBin: true
    dependencies:
      commander: 2.20.3
      lru-cache: 4.1.5
      semver: 5.7.1
      sigmund: 1.0.1
    dev: true

  /ee-first@1.1.1:
    resolution: {integrity: sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=}

  /electron-to-chromium@1.4.284:
    resolution: {integrity: sha512-M8WEXFuKXMYMVr45fo8mq0wUrrJHheiKZf6BArTKk9ZBYCKJEOU5H8cdWgDT+qCVZf7Na4lVUaZsA+h6uA9+PA==}

  /elegant-spinner@1.0.1:
    resolution: {integrity: sha512-B+ZM+RXvRqQaAmkMlO/oSe5nMUOaUnyfGYCEHoR8wrXsZR2mA0XVibsxV1bvTwxdRWah1PkQqso2EzhILGHtEQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /elliptic@6.5.4:
    resolution: {integrity: sha512-iLhC6ULemrljPZb+QutR5TQGB+pdW6KGD5RSegS+8sorOZT+rdQFbsQFJgvN3eRqNALqJer4oQ16YvJHlU8hzQ==}
    dependencies:
      bn.js: 4.12.0
      brorand: 1.1.0
      hash.js: 1.1.7
      hmac-drbg: 1.0.1
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1

  /emoji-regex@7.0.3:
    resolution: {integrity: sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==}

  /emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  /emojis-list@3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==}
    engines: {node: '>= 4'}

  /encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}

  /end-of-stream@1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==}
    dependencies:
      once: 1.4.0

  /enhanced-resolve@4.5.0:
    resolution: {integrity: sha512-Nv9m36S/vxpsI+Hc4/ZGRs0n9mXqSWGGq49zxb/cJfPAQMbUtttJAlNPS4AQzaBdw/pKskw5bMbekT/Y7W/Wlg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      graceful-fs: 4.2.10
      memory-fs: 0.5.0
      tapable: 1.1.3

  /enhanced-resolve@5.10.0:
    resolution: {integrity: sha512-T0yTFjdpldGY8PmuXXR0PyQ1ufZpEGiHVrp7zHKB7jdR4qlmZHhONVM5AQOAWXuF/w3dnHbEQVrNptJgt7F+cQ==}
    engines: {node: '>=10.13.0'}
    dependencies:
      graceful-fs: 4.2.10
      tapable: 2.2.1

  /entities@2.2.0:
    resolution: {integrity: sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==}

  /envinfo@7.8.1:
    resolution: {integrity: sha512-/o+BXHmB7ocbHEAs6F2EnG0ogybVVUdkRunTT2glZU9XAaGmhqskrvKwqXuDfNjEO0LZKWdejEEpnq8aM0tOaw==}
    engines: {node: '>=4'}
    hasBin: true

  /errno@0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true
    dependencies:
      prr: 1.0.1

  /error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}
    dependencies:
      is-arrayish: 0.2.1

  /error-stack-parser@2.1.4:
    resolution: {integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==}
    dependencies:
      stackframe: 1.3.4

  /es-abstract@1.20.4:
    resolution: {integrity: sha512-0UtvRN79eMe2L+UNEF1BwRe364sj/DXhQ/k5FmivgoSdpM90b8Jc0mDzKMGo7QS0BVbOP/bTwBKNnDc9rNzaPA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      es-to-primitive: 1.2.1
      function-bind: 1.1.1
      function.prototype.name: 1.1.5
      get-intrinsic: 1.1.3
      get-symbol-description: 1.0.0
      has: 1.0.3
      has-property-descriptors: 1.0.0
      has-symbols: 1.0.3
      internal-slot: 1.0.3
      is-callable: 1.2.7
      is-negative-zero: 2.0.2
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.2
      is-string: 1.0.7
      is-weakref: 1.0.2
      object-inspect: 1.12.2
      object-keys: 1.1.1
      object.assign: 4.1.4
      regexp.prototype.flags: 1.4.3
      safe-regex-test: 1.0.0
      string.prototype.trimend: 1.0.6
      string.prototype.trimstart: 1.0.6
      unbox-primitive: 1.0.2

  /es-array-method-boxes-properly@1.0.0:
    resolution: {integrity: sha512-wd6JXUmyHmt8T5a2xreUwKcGPq6f1f+WwIJkijUqiGcJz1qqnZgP6XIK+QyIWU5lT7imeNxUll48bziG+TSYcA==}

  /es-module-lexer@0.9.3:
    resolution: {integrity: sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ==}

  /es-to-primitive@1.2.1:
    resolution: {integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4

  /es6-promise@4.2.8:
    resolution: {integrity: sha512-HJDGx5daxeIvxdBxvG2cb9g4tEvwIk3i8+nhX0yGrYmZUzbkdg8QbDevheDB8gd0//uPj4c1EQua8Q+MViT0/w==}
    dev: false

  /escalade@3.1.1:
    resolution: {integrity: sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==}
    engines: {node: '>=6'}

  /escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  /escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  /escape-string-regexp@2.0.0:
    resolution: {integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==}
    engines: {node: '>=8'}

  /escodegen@1.14.3:
    resolution: {integrity: sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw==}
    engines: {node: '>=4.0'}
    hasBin: true
    dependencies:
      esprima: 4.0.1
      estraverse: 4.3.0
      esutils: 2.0.3
      optionator: 0.8.3
    optionalDependencies:
      source-map: 0.6.1

  /eslint-config-standard-jsx@8.1.0(eslint-plugin-react@7.14.3)(eslint@6.8.0):
    resolution: {integrity: sha512-ULVC8qH8qCqbU792ZOO6DaiaZyHNS/5CZt3hKqHkEhVlhPEPN3nfBqqxJCyp59XrjIBZPu1chMYe9T2DXZ7TMw==}
    peerDependencies:
      eslint: '>=6.2.2'
      eslint-plugin-react: '>=7.14.2'
    dependencies:
      eslint: 6.8.0
      eslint-plugin-react: 7.14.3(eslint@6.8.0)
    dev: true

  /eslint-config-standard@14.1.1(eslint-plugin-import@2.18.2)(eslint-plugin-node@10.0.0)(eslint-plugin-promise@4.2.1)(eslint-plugin-standard@4.0.2)(eslint@6.8.0):
    resolution: {integrity: sha512-Z9B+VR+JIXRxz21udPTL9HpFMyoMUEeX1G251EQ6e05WD9aPVtVBn09XUmZ259wCMlCDmYDSZG62Hhm+ZTJcUg==}
    peerDependencies:
      eslint: '>=6.2.2'
      eslint-plugin-import: '>=2.18.0'
      eslint-plugin-node: '>=9.1.0'
      eslint-plugin-promise: '>=4.2.1'
      eslint-plugin-standard: '>=4.0.0'
    dependencies:
      eslint: 6.8.0
      eslint-plugin-import: 2.18.2(eslint@6.8.0)
      eslint-plugin-node: 10.0.0(eslint@6.8.0)
      eslint-plugin-promise: 4.2.1
      eslint-plugin-standard: 4.0.2(eslint@6.8.0)
    dev: true

  /eslint-import-resolver-node@0.3.6:
    resolution: {integrity: sha512-0En0w03NRVMn9Uiyn8YRPDKvWjxCWkslUEhGNTdGx15RvPJYQ+lbOlqrlNI2vEAs4pDYK4f/HN2TbDmk5TP0iw==}
    dependencies:
      debug: 3.2.7(supports-color@7.2.0)
      resolve: 1.22.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-module-utils@2.7.4(eslint-import-resolver-node@0.3.6)(eslint@6.8.0):
    resolution: {integrity: sha512-j4GT+rqzCoRKHwURX7pddtIPGySnX9Si/cgMI5ztrcqOPtk5dDEeZ34CQVPphnqkJytlc97Vuk05Um2mJ3gEQA==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true
    dependencies:
      debug: 3.2.7(supports-color@7.2.0)
      eslint: 6.8.0
      eslint-import-resolver-node: 0.3.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-plugin-cypress@2.12.1(eslint@6.8.0):
    resolution: {integrity: sha512-c2W/uPADl5kospNDihgiLc7n87t5XhUbFDoTl6CfVkmG+kDAb5Ux10V9PoLPu9N+r7znpc+iQlcmAqT1A/89HA==}
    peerDependencies:
      eslint: '>= 3.2.1'
    dependencies:
      eslint: 6.8.0
      globals: 11.12.0
    dev: true

  /eslint-plugin-es@2.0.0(eslint@6.8.0):
    resolution: {integrity: sha512-f6fceVtg27BR02EYnBhgWLFQfK6bN4Ll0nQFrBHOlCsAyxeZkn0NHns5O0YZOPrV1B3ramd6cgFwaoFLcSkwEQ==}
    engines: {node: '>=8.10.0'}
    peerDependencies:
      eslint: '>=4.19.1'
    dependencies:
      eslint: 6.8.0
      eslint-utils: 1.4.3
      regexpp: 3.2.0
    dev: true

  /eslint-plugin-import@2.18.2(eslint@6.8.0):
    resolution: {integrity: sha512-5ohpsHAiUBRNaBWAF08izwUGlbrJoJJ+W9/TBwsGoR1MnlgfwMIKrFeSjWbt6moabiXW9xNvtFz+97KHRfI4HQ==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: 2.x - 6.x
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
    dependencies:
      array-includes: 3.1.6
      contains-path: 0.1.0
      debug: 2.6.9(supports-color@7.2.0)
      doctrine: 1.5.0
      eslint: 6.8.0
      eslint-import-resolver-node: 0.3.6
      eslint-module-utils: 2.7.4(eslint-import-resolver-node@0.3.6)(eslint@6.8.0)
      has: 1.0.3
      minimatch: 3.1.2
      object.values: 1.1.6
      read-pkg-up: 2.0.0
      resolve: 1.22.1
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color
    dev: true

  /eslint-plugin-jest@26.9.0(eslint@6.8.0)(jest@24.9.0)(typescript@4.8.4):
    resolution: {integrity: sha512-TWJxWGp1J628gxh2KhaH1H1paEdgE2J61BBF1I59c6xWeL5+D1BzMxGDN/nXAfX+aSkR5u80K+XhskK6Gwq9ng==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      '@typescript-eslint/eslint-plugin': ^5.0.0
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      jest: '*'
    peerDependenciesMeta:
      '@typescript-eslint/eslint-plugin':
        optional: true
      jest:
        optional: true
    dependencies:
      '@typescript-eslint/utils': 5.42.1(eslint@6.8.0)(typescript@4.8.4)
      eslint: 6.8.0
      jest: 24.9.0
    transitivePeerDependencies:
      - supports-color
      - typescript
    dev: false

  /eslint-plugin-node@10.0.0(eslint@6.8.0):
    resolution: {integrity: sha512-1CSyM/QCjs6PXaT18+zuAXsjXGIGo5Rw630rSKwokSs2jrYURQc4R5JZpoanNCqwNmepg+0eZ9L7YiRUJb8jiQ==}
    engines: {node: '>=8.10.0'}
    peerDependencies:
      eslint: '>=5.16.0'
    dependencies:
      eslint: 6.8.0
      eslint-plugin-es: 2.0.0(eslint@6.8.0)
      eslint-utils: 1.4.3
      ignore: 5.2.0
      minimatch: 3.1.2
      resolve: 1.22.1
      semver: 6.3.0
    dev: true

  /eslint-plugin-promise@4.2.1:
    resolution: {integrity: sha512-VoM09vT7bfA7D+upt+FjeBO5eHIJQBUWki1aPvB+vbNiHS3+oGIJGIeyBtKQTME6UPXXy3vV07OL1tHd3ANuDw==}
    engines: {node: '>=6'}
    dev: true

  /eslint-plugin-react@7.14.3(eslint@6.8.0):
    resolution: {integrity: sha512-EzdyyBWC4Uz2hPYBiEJrKCUi2Fn+BJ9B/pJQcjw5X+x/H2Nm59S4MJIvL4O5NEE0+WbnQwEBxWY03oUk+Bc3FA==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0
    dependencies:
      array-includes: 3.1.6
      doctrine: 2.1.0
      eslint: 6.8.0
      has: 1.0.3
      jsx-ast-utils: 2.4.1
      object.entries: 1.1.6
      object.fromentries: 2.0.6
      object.values: 1.1.6
      prop-types: 15.8.1
      resolve: 1.22.1
    dev: true

  /eslint-plugin-standard@4.0.2(eslint@6.8.0):
    resolution: {integrity: sha512-nKptN8l7jksXkwFk++PhJB3cCDTcXOEyhISIN86Ue2feJ1LFyY3PrY3/xT2keXlJSY5bpmbiTG0f885/YKAvTA==}
    peerDependencies:
      eslint: '>=5.0.0'
    dependencies:
      eslint: 6.8.0
    dev: true

  /eslint-plugin-vue@6.2.2(eslint@6.8.0):
    resolution: {integrity: sha512-Nhc+oVAHm0uz/PkJAWscwIT4ijTrK5fqNqz9QB1D35SbbuMG1uB6Yr5AJpvPSWg+WOw7nYNswerYh0kOk64gqQ==}
    engines: {node: '>=8.10'}
    peerDependencies:
      eslint: ^5.0.0 || ^6.0.0
    dependencies:
      eslint: 6.8.0
      natural-compare: 1.4.0
      semver: 5.7.1
      vue-eslint-parser: 7.11.0(eslint@6.8.0)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-scope@4.0.3:
    resolution: {integrity: sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg==}
    engines: {node: '>=4.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  /eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  /eslint-utils@1.4.3:
    resolution: {integrity: sha512-fbBN5W2xdY45KulGXmLHZ3c3FHfVYmKg0IrAKGOkT/464PQsx2UeIzfz1RmEci+KLm1bBaAzZAh8+/E+XAeZ8Q==}
    engines: {node: '>=6'}
    dependencies:
      eslint-visitor-keys: 1.3.0

  /eslint-utils@3.0.0(eslint@6.8.0):
    resolution: {integrity: sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==}
    engines: {node: ^10.0.0 || ^12.0.0 || >= 14.0.0}
    peerDependencies:
      eslint: '>=5'
    dependencies:
      eslint: 6.8.0
      eslint-visitor-keys: 2.1.0
    dev: false

  /eslint-visitor-keys@1.3.0:
    resolution: {integrity: sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==}
    engines: {node: '>=4'}

  /eslint-visitor-keys@2.1.0:
    resolution: {integrity: sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==}
    engines: {node: '>=10'}

  /eslint-visitor-keys@3.3.0:
    resolution: {integrity: sha512-mQ+suqKJVyeuwGYHAdjMFqjCyfl8+Ldnxuyp3ldiMBFKkvytrXUZWaiPCEav8qDHKty44bD+qV1IP4T+w+xXRA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: false

  /eslint@6.8.0:
    resolution: {integrity: sha512-K+Iayyo2LtyYhDSYwz5D5QdWw0hCacNzyq1Y821Xna2xSJj7cijoLLYmLxTQgcgZ9mC61nryMy9S7GRbYpI5Ig==}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}
    hasBin: true
    dependencies:
      '@babel/code-frame': 7.18.6
      ajv: 6.12.6
      chalk: 2.4.2
      cross-spawn: 6.0.5
      debug: 4.3.4
      doctrine: 3.0.0
      eslint-scope: 5.1.1
      eslint-utils: 1.4.3
      eslint-visitor-keys: 1.3.0
      espree: 6.2.1
      esquery: 1.4.0
      esutils: 2.0.3
      file-entry-cache: 5.0.1
      functional-red-black-tree: 1.0.1
      glob-parent: 5.1.2
      globals: 12.4.0
      ignore: 4.0.6
      import-fresh: 3.3.0
      imurmurhash: 0.1.4
      inquirer: 7.3.3
      is-glob: 4.0.3
      js-yaml: 3.14.1
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.3.0
      lodash: 4.17.21
      minimatch: 3.1.2
      mkdirp: 0.5.6
      natural-compare: 1.4.0
      optionator: 0.8.3
      progress: 2.0.3
      regexpp: 2.0.1
      semver: 6.3.0
      strip-ansi: 5.2.0
      strip-json-comments: 3.1.1
      table: 5.4.6
      text-table: 0.2.0
      v8-compile-cache: 2.3.0
    transitivePeerDependencies:
      - supports-color

  /espree@6.2.1:
    resolution: {integrity: sha512-ysCxRQY3WaXJz9tdbWOwuWr5Y/XrPTGX9Kiz3yoUXwW0VZ4w30HTkQLaGx/+ttFjF8i+ACbArnB4ce68a9m5hw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      acorn: 7.4.1
      acorn-jsx: 5.3.2(acorn@7.4.1)
      eslint-visitor-keys: 1.3.0

  /esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  /esquery@1.4.0:
    resolution: {integrity: sha512-cCDispWt5vHHtwMY2YrAQ4ibFkAL8RbH5YGBnZBc90MolvvfkkQcJro/aZiAQUlQ3qgrYS6D6v8Gc5G5CQsc9w==}
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: 5.3.0

  /esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0

  /estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  /estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  /esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  /etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}

  /event-pubsub@4.3.0:
    resolution: {integrity: sha512-z7IyloorXvKbFx9Bpie2+vMJKKx1fH1EN5yiTfp8CiLOTptSYy1g8H4yDpGlEdshL1PBiFtBHepF2cNsqeEeFQ==}
    engines: {node: '>=4.0.0'}

  /eventemitter2@6.4.9:
    resolution: {integrity: sha512-JEPTiaOt9f04oa6NOkc4aH+nVp5I3wEjpHbIPqfgCdD5v5bUzy7xQqwcVO2aDQgOWhI28da57HksMrzK9HlRxg==}
    dev: true

  /eventemitter3@4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}

  /events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  /evp_bytestokey@1.0.3:
    resolution: {integrity: sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA==}
    dependencies:
      md5.js: 1.3.5
      safe-buffer: 5.2.1

  /exec-sh@0.3.6:
    resolution: {integrity: sha512-nQn+hI3yp+oD0huYhKwvYI32+JFeq+XkNcD1GAo3Y/MjxsfVGmrrzrnzjWiNY6f+pUCP440fThsFh5gZrRAU/w==}

  /execa@1.0.0:
    resolution: {integrity: sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==}
    engines: {node: '>=6'}
    dependencies:
      cross-spawn: 6.0.5
      get-stream: 4.1.0
      is-stream: 1.1.0
      npm-run-path: 2.0.2
      p-finally: 1.0.0
      signal-exit: 3.0.7
      strip-eof: 1.0.0

  /execa@4.1.0:
    resolution: {integrity: sha512-j5W0//W7f8UxAn8hXVnwG8tLwdiUy4FJLcSupCg6maBYZDpyBvTApK7KyuI4bKj8KOh1r2YH+6ucuYtJv1bTZA==}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 5.2.0
      human-signals: 1.1.1
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: true

  /execa@5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  /executable@4.1.1:
    resolution: {integrity: sha512-8iA79xD3uAch729dUG8xaaBBFGaEa0wdD2VkYLFHwlqosEj/jT66AzcreRDSgV7ehnNLBW2WR5jIXwGKjVdTLg==}
    engines: {node: '>=4'}
    dependencies:
      pify: 2.3.0
    dev: true

  /exit-hook@1.1.1:
    resolution: {integrity: sha512-MsG3prOVw1WtLXAZbM3KiYtooKR1LvxHh3VHsVtIy0uiUu8usxgB/94DP2HxtD/661lLdB6yzQ09lGJSQr6nkg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /exit@0.1.2:
    resolution: {integrity: sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==}
    engines: {node: '>= 0.8.0'}

  /expand-brackets@2.1.4:
    resolution: {integrity: sha512-w/ozOKR9Obk3qoWeY/WDi6MFta9AoMR+zud60mdnbniMcBxRuFJyDt2LdX/14A1UABeqk+Uk+LDfUpvoGKppZA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      debug: 2.6.9(supports-color@7.2.0)
      define-property: 0.2.5
      extend-shallow: 2.0.1
      posix-character-classes: 0.1.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  /expand-template@2.0.3:
    resolution: {integrity: sha512-XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg==}
    engines: {node: '>=6'}
    dev: true
    optional: true

  /expect@24.9.0:
    resolution: {integrity: sha512-wvVAx8XIol3Z5m9zvZXiyZOQ+sRJqNTIm6sGjdWlaZIeupQGO3WbYI+15D/AmEwZywL6wtJkbAbJtzkOfBuR0Q==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      ansi-styles: 3.2.1
      jest-get-type: 24.9.0
      jest-matcher-utils: 24.9.0
      jest-message-util: 24.9.0
      jest-regex-util: 24.9.0
    transitivePeerDependencies:
      - supports-color

  /express@4.18.2:
    resolution: {integrity: sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==}
    engines: {node: '>= 0.10.0'}
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.1
      content-disposition: 0.5.4
      content-type: 1.0.4
      cookie: 0.5.0
      cookie-signature: 1.0.6
      debug: 2.6.9(supports-color@7.2.0)
      depd: 2.0.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.2.0
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.1
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.7
      proxy-addr: 2.0.7
      qs: 6.11.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.18.0
      serve-static: 1.15.0
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  /extend-shallow@2.0.1:
    resolution: {integrity: sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: 0.1.1

  /extend-shallow@3.0.2:
    resolution: {integrity: sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q==}
    engines: {node: '>=0.10.0'}
    dependencies:
      assign-symbols: 1.0.0
      is-extendable: 1.0.1

  /extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  /external-editor@3.1.0:
    resolution: {integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==}
    engines: {node: '>=4'}
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33

  /extglob@2.0.4:
    resolution: {integrity: sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-unique: 0.3.2
      define-property: 1.0.0
      expand-brackets: 2.1.4
      extend-shallow: 2.0.1
      fragment-cache: 0.2.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  /extract-from-css@0.4.4:
    resolution: {integrity: sha512-41qWGBdtKp9U7sgBxAQ7vonYqSXzgW/SiAYzq4tdWSVhAShvpVCH1nyvPQgjse6EdgbW7Y7ERdT3674/lKr65A==}
    engines: {node: '>=0.10.0', npm: '>=2.0.0'}
    dependencies:
      css: 2.2.4
    dev: true

  /extract-zip@1.7.0(supports-color@7.2.0):
    resolution: {integrity: sha512-xoh5G1W/PB0/27lXgMQyIhP5DSY/LhoCsOyZgb+6iMmRtCwVBo55uKaMoEYrDCKQhWvqEip5ZPKAc6eFNyf/MA==}
    hasBin: true
    dependencies:
      concat-stream: 1.6.2
      debug: 2.6.9(supports-color@7.2.0)
      mkdirp: 0.5.6
      yauzl: 2.10.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /extsprintf@1.3.0:
    resolution: {integrity: sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==}
    engines: {'0': node >=0.6.0}

  /fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  /fast-glob@3.2.12:
    resolution: {integrity: sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5

  /fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  /fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  /fastest-levenshtein@1.0.16:
    resolution: {integrity: sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==}
    engines: {node: '>= 4.9.1'}

  /fastq@1.13.0:
    resolution: {integrity: sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==}
    dependencies:
      reusify: 1.0.4

  /faye-websocket@0.11.4:
    resolution: {integrity: sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==}
    engines: {node: '>=0.8.0'}
    dependencies:
      websocket-driver: 0.7.4

  /fb-watchman@2.0.2:
    resolution: {integrity: sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==}
    dependencies:
      bser: 2.1.1

  /fd-slicer@1.1.0:
    resolution: {integrity: sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==}
    dependencies:
      pend: 1.2.0
    dev: true

  /figgy-pudding@3.5.2:
    resolution: {integrity: sha512-0btnI/H8f2pavGMN8w40mlSKOfTK2SVJmBfBeVIj3kNw0swwgzyRq0d5TJVOwodFmtvpPeWPN/MCcfuWF0Ezbw==}

  /figures@1.7.0:
    resolution: {integrity: sha512-UxKlfCRuCBxSXU4C6t9scbDyWZ4VlaFFdojKtzJuSkuOBQ5CNFum+zZXFwHjo+CxBC1t6zlYPgHIgFjL8ggoEQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      escape-string-regexp: 1.0.5
      object-assign: 4.1.1
    dev: true

  /figures@2.0.0:
    resolution: {integrity: sha512-Oa2M9atig69ZkfwiApY8F2Yy+tzMbazyvqv21R0NsSC8floSOC09BbT1ITWAdoMGQvJ/aZnR1KMwdx9tvHnTNA==}
    engines: {node: '>=4'}
    dependencies:
      escape-string-regexp: 1.0.5

  /figures@3.2.0:
    resolution: {integrity: sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==}
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: 1.0.5

  /file-entry-cache@5.0.1:
    resolution: {integrity: sha512-bCg29ictuBaKUwwArK4ouCaqDgLZcysCFLmM/Yn/FDoqndh/9vNuQfXRDvTuXKLxfD/JtZQGKFT8MGcJBK644g==}
    engines: {node: '>=4'}
    dependencies:
      flat-cache: 2.0.1

  /file-loader@6.2.0(webpack@4.46.0):
    resolution: {integrity: sha512-qo3glqyTa61Ytg4u73GultjHGjdRyig3tG6lPtyX/jOEJvHif9uB0/OCI2Kif6ctF3caQTW2G5gym21oAsI4pw==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 3.1.1
      webpack: 4.46.0(webpack-cli@4.10.0)
    dev: true

  /file-uri-to-path@1.0.0:
    resolution: {integrity: sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==}

  /fill-range@4.0.0:
    resolution: {integrity: sha512-VcpLTWqWDiTerugjj8e3+esbg+skS3M9e54UuR3iCeIDMXCLTsAH8hTSzDQU/X6/6t3eYkOKoZSef2PlU6U1XQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-number: 3.0.0
      repeat-string: 1.6.1
      to-regex-range: 2.1.1

  /fill-range@7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1

  /finalhandler@1.2.0:
    resolution: {integrity: sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==}
    engines: {node: '>= 0.8'}
    dependencies:
      debug: 2.6.9(supports-color@7.2.0)
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  /find-babel-config@1.2.0:
    resolution: {integrity: sha512-jB2CHJeqy6a820ssiqwrKMeyC6nNdmrcgkKWJWmpoxpE8RKciYJXCcXRq1h2AzCo5I5BJeN2tkGEO3hLTuePRA==}
    engines: {node: '>=4.0.0'}
    dependencies:
      json5: 0.5.1
      path-exists: 3.0.0
    dev: true

  /find-cache-dir@2.1.0:
    resolution: {integrity: sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ==}
    engines: {node: '>=6'}
    dependencies:
      commondir: 1.0.1
      make-dir: 2.1.0
      pkg-dir: 3.0.0

  /find-cache-dir@3.3.2:
    resolution: {integrity: sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==}
    engines: {node: '>=8'}
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0
    dev: true

  /find-root@1.1.0:
    resolution: {integrity: sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==}
    dev: true

  /find-up@2.1.0:
    resolution: {integrity: sha512-NWzkk0jSJtTt08+FBFMvXoeZnOJD+jTtsRmBYbAIzJdX6l7dLgR7CTubCM5/eDdPUBvLCeVasP1brfVR/9/EZQ==}
    engines: {node: '>=4'}
    dependencies:
      locate-path: 2.0.0
    dev: true

  /find-up@3.0.0:
    resolution: {integrity: sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==}
    engines: {node: '>=6'}
    dependencies:
      locate-path: 3.0.0

  /find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  /flat-cache@2.0.1:
    resolution: {integrity: sha512-LoQe6yDuUMDzQAEH8sgmh4Md6oZnc/7PjtwjNFSzveXqSHt6ka9fPBuso7IGf9Rz4uqnSnWiFH2B/zj24a5ReA==}
    engines: {node: '>=4'}
    dependencies:
      flatted: 2.0.2
      rimraf: 2.6.3
      write: 1.0.3

  /flatted@2.0.2:
    resolution: {integrity: sha512-r5wGx7YeOwNWNlCA0wQ86zKyDLMQr+/RB8xy74M4hTphfmjlijTSSXGuH8rnvKZnfT9i+75zmd8jcKdMR4O6jA==}

  /flush-write-stream@1.1.1:
    resolution: {integrity: sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.7

  /follow-redirects@1.15.2(debug@4.3.4):
    resolution: {integrity: sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dependencies:
      debug: 4.3.4

  /for-each@0.3.3:
    resolution: {integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==}
    dependencies:
      is-callable: 1.2.7

  /for-in@1.0.2:
    resolution: {integrity: sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ==}
    engines: {node: '>=0.10.0'}

  /forever-agent@0.6.1:
    resolution: {integrity: sha512-j0KLYPhm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==}

  /form-data@2.3.3:
    resolution: {integrity: sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==}
    engines: {node: '>= 0.12'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  /forwarded@0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}

  /fraction.js@4.2.0:
    resolution: {integrity: sha512-MhLuK+2gUcnZe8ZHlaaINnQLl0xRIGRfcGk2yl8xoQAfHrSsL3rYu6FCmBdkdbhc9EPlwyGHewaRsvwRMJtAlA==}

  /fragment-cache@0.2.1:
    resolution: {integrity: sha512-GMBAbW9antB8iZRHLoGw0b3HANt57diZYFO/HL1JGIC1MjKrdmhxvrJbupnVvpys0zsz7yBApXdQyfepKly2kA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-cache: 0.2.2

  /fresh@0.5.2:
    resolution: {integrity: sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=}
    engines: {node: '>= 0.6'}

  /from2@2.3.0:
    resolution: {integrity: sha512-OMcX/4IC/uqEPVgGeyfN22LJk6AZrMkRZHxcHBMBvHScDGgwTm2GT2Wkgtocyd3JfZffjj2kYUDXXII0Fk9W0g==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.7

  /fs-constants@1.0.0:
    resolution: {integrity: sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==}
    dev: true
    optional: true

  /fs-extra@9.1.0:
    resolution: {integrity: sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==}
    engines: {node: '>=10'}
    dependencies:
      at-least-node: 1.0.0
      graceful-fs: 4.2.10
      jsonfile: 6.1.0
      universalify: 2.0.0

  /fs-minipass@2.1.0:
    resolution: {integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.4
    dev: true

  /fs-monkey@1.0.3:
    resolution: {integrity: sha512-cybjIfiiE+pTWicSCLFHSrXZ6EilF30oh91FDP9S2B051prEa7QWfrVTQm10/dDpswBDXZugPa1Ogu8Yh+HV0Q==}

  /fs-write-stream-atomic@1.0.10:
    resolution: {integrity: sha512-gehEzmPn2nAwr39eay+x3X34Ra+M2QlVUTLhkXPjWdeO8RF9kszk116avgBJM3ZyNHgHXBNx+VmPaFC36k0PzA==}
    dependencies:
      graceful-fs: 4.2.10
      iferr: 0.1.5
      imurmurhash: 0.1.4
      readable-stream: 2.3.7

  /fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  /fsevents@1.2.13:
    resolution: {integrity: sha512-oWb1Z6mkHIskLzEJ/XWX0srkpkTQ7vaopMQkyaEIoq0fmtFVxOthb8cCxeT+p3ynTdkk/RZwbgG4brR5BeWECw==}
    engines: {node: '>= 4.0'}
    os: [darwin]
    deprecated: The v1 package contains DANGEROUS / INSECURE binaries. Upgrade to safe fsevents v2
    requiresBuild: true
    dependencies:
      bindings: 1.5.0
      nan: 2.17.0
    optional: true

  /fsevents@2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    optional: true

  /function-bind@1.1.1:
    resolution: {integrity: sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==}

  /function.prototype.name@1.1.5:
    resolution: {integrity: sha512-uN7m/BzVKQnCUF/iW8jYea67v++2u7m5UgENbHRtdDVclOUP+FMPlCNdmk0h/ysGyo2tavMJEDqJAkJdRa1vMA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
      functions-have-names: 1.2.3

  /functional-red-black-tree@1.0.1:
    resolution: {integrity: sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==}

  /functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}

  /gauge@2.7.4:
    resolution: {integrity: sha512-14x4kjc6lkD3ltw589k0NrPD6cCNTD6CWoVUNpB85+DrtONoZn+Rug6xZU5RvSC4+TZPxA5AnBibQYAvZn41Hg==}
    dependencies:
      aproba: 1.2.0
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      object-assign: 4.1.1
      signal-exit: 3.0.7
      string-width: 1.0.2
      strip-ansi: 3.0.1
      wide-align: 1.1.5
    dev: true
    optional: true

  /gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  /get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  /get-intrinsic@1.1.3:
    resolution: {integrity: sha512-QJVz1Tj7MS099PevUG5jvnt9tSkXN8K14dxQlikJuPt4uD9hHAHjLyLBiLR5zELelBdD9QNRAXZzsJx0WaDL9A==}
    dependencies:
      function-bind: 1.1.1
      has: 1.0.3
      has-symbols: 1.0.3

  /get-stdin@7.0.0:
    resolution: {integrity: sha512-zRKcywvrXlXsA0v0i9Io4KDRaAw7+a1ZpjRwl9Wox8PFlVCCHra7E9c4kqXCoCM9nR5tBkaTTZRBoCm60bFqTQ==}
    engines: {node: '>=8'}
    dev: true

  /get-stream@4.1.0:
    resolution: {integrity: sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==}
    engines: {node: '>=6'}
    dependencies:
      pump: 3.0.0

  /get-stream@5.2.0:
    resolution: {integrity: sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==}
    engines: {node: '>=8'}
    dependencies:
      pump: 3.0.0
    dev: true

  /get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}

  /get-symbol-description@1.0.0:
    resolution: {integrity: sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.1.3

  /get-value@2.0.6:
    resolution: {integrity: sha512-Ln0UQDlxH1BapMu3GPtf7CuYNwRZf2gwCuPqbyG6pB8WfmFpzqcy4xtAaAMUhnNqjMKTiCPZG2oMT3YSx8U2NA==}
    engines: {node: '>=0.10.0'}

  /getos@3.2.1:
    resolution: {integrity: sha512-U56CfOK17OKgTVqozZjUKNdkfEv6jk5WISBJ8SHoagjE6L69zOwl3Z+O8myjY9MEW3i2HPWQBt/LTbCgcC973Q==}
    dependencies:
      async: 3.2.4
    dev: true

  /getpass@0.1.7:
    resolution: {integrity: sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==}
    dependencies:
      assert-plus: 1.0.0

  /github-from-package@0.0.0:
    resolution: {integrity: sha1-l/tdlr/eiXMxPyDoKI75oWf6ZM4=}
    dev: true
    optional: true

  /glob-parent@3.1.0:
    resolution: {integrity: sha512-E8Ak/2+dZY6fnzlR7+ueWvhsH1SjHr4jjss4YS/h4py44jY9MhK/VFdaZJAWDz6BbL21KeteKxFSFpq8OS5gVA==}
    dependencies:
      is-glob: 3.1.0
      path-dirname: 1.0.2
    optional: true

  /glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3

  /glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: 4.0.3

  /glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}

  /glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  /glob@8.0.3:
    resolution: {integrity: sha512-ull455NHSHI/Y1FqGaaYFaLGkNMMJbavMrEGFXG/PGrg6y7sutWHUHrz6gy6WEBH6akM1M414dWKCNs+IhKdiQ==}
    engines: {node: '>=12'}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 5.1.0
      once: 1.4.0
    dev: true

  /global-dirs@2.1.0:
    resolution: {integrity: sha512-MG6kdOUh/xBnyo9cJFeIKkLEc1AyFq42QTU4XiX51i2NEdxLxLWXIjEjmqKeSuKR7pAZjTqUVoT2b2huxVLgYQ==}
    engines: {node: '>=8'}
    dependencies:
      ini: 1.3.7
    dev: true

  /globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  /globals@12.4.0:
    resolution: {integrity: sha512-BWICuzzDvDoH54NHKCseDanAhE3CeDorgDL5MT6LMXXj2WCnd9UC2szdk4AWLfjdgNBCXLUanXYcpBBKOSWGwg==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.8.1

  /globals@9.18.0:
    resolution: {integrity: sha512-S0nG3CLEQiY/ILxqtztTWH/3iRRdyBLw6KMDxnKMchrtbj2OFmehVh0WUCfW3DUrIgx/qFrJPICrq4Z4sTR9UQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /globby@11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.2.12
      ignore: 5.2.0
      merge2: 1.4.1
      slash: 3.0.0

  /graceful-fs@4.2.10:
    resolution: {integrity: sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==}

  /growly@1.3.0:
    resolution: {integrity: sha512-+xGQY0YyAWCnqy7Cd++hc2JqMYzlm0dG30Jd0beaA64sROr8C4nt8Yc9V5Ro3avlSUDTN0ulqP/VBKi1/lLygw==}

  /gzip-size@6.0.0:
    resolution: {integrity: sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==}
    engines: {node: '>=10'}
    dependencies:
      duplexer: 0.1.2

  /handle-thing@2.0.1:
    resolution: {integrity: sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==}

  /har-schema@2.0.0:
    resolution: {integrity: sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==}
    engines: {node: '>=4'}

  /har-validator@5.1.5:
    resolution: {integrity: sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0

  /has-ansi@2.0.0:
    resolution: {integrity: sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: true

  /has-bigints@1.0.2:
    resolution: {integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==}

  /has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  /has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  /has-property-descriptors@1.0.0:
    resolution: {integrity: sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==}
    dependencies:
      get-intrinsic: 1.1.3

  /has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}

  /has-tostringtag@1.0.0:
    resolution: {integrity: sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3

  /has-unicode@2.0.1:
    resolution: {integrity: sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==}
    dev: true
    optional: true

  /has-value@0.3.1:
    resolution: {integrity: sha512-gpG936j8/MzaeID5Yif+577c17TxaDmhuyVgSwtnL/q8UUTySg8Mecb+8Cf1otgLoD7DDH75axp86ER7LFsf3Q==}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 0.1.4
      isobject: 2.1.0

  /has-value@1.0.0:
    resolution: {integrity: sha512-IBXk4GTsLYdQ7Rvt+GRBrFSVEkmuOUy4re0Xjd9kJSUQpnTrWR4/y9RpfexN9vkAPMFuQoeWKwqzPozRTlasGw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 1.0.0
      isobject: 3.0.1

  /has-values@0.1.4:
    resolution: {integrity: sha512-J8S0cEdWuQbqD9//tlZxiMuMNmxB8PlEwvYwuxsTmR1G5RXUePEX/SJn7aD0GMLieuZYSwNH0cQuJGwnYunXRQ==}
    engines: {node: '>=0.10.0'}

  /has-values@1.0.0:
    resolution: {integrity: sha512-ODYZC64uqzmtfGMEAX/FvZiRyWLpAC3vYnNunURUnkGVTS+mI0smVsWaPydRBsE3g+ok7h960jChO8mFcWlHaQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      kind-of: 4.0.0

  /has@1.0.3:
    resolution: {integrity: sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==}
    engines: {node: '>= 0.4.0'}
    dependencies:
      function-bind: 1.1.1

  /hash-base@3.1.0:
    resolution: {integrity: sha512-1nmYp/rhMDiE7AYkDw+lLwlAzz0AntGIe51F3RfFfEqyQ3feY2eI/NcwC6umIQVOASPMsWJLJScWKSSvzL9IVA==}
    engines: {node: '>=4'}
    dependencies:
      inherits: 2.0.4
      readable-stream: 3.6.0
      safe-buffer: 5.2.1

  /hash-sum@1.0.2:
    resolution: {integrity: sha512-fUs4B4L+mlt8/XAtSOGMUO1TXmAelItBPtJG7CyHJfYTdDjwisntGO2JQz7oUsatOY9o68+57eziUVNw/mRHmA==}

  /hash-sum@2.0.0:
    resolution: {integrity: sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg==}

  /hash.js@1.1.7:
    resolution: {integrity: sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1

  /he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true

  /hex-color-regex@1.1.0:
    resolution: {integrity: sha512-l9sfDFsuqtOqKDsQdqrMRk0U85RZc0RtOR9yPI7mRVOa4FsR/BVnZ0shmQRM96Ji99kYZP/7hn1cedc1+ApsTQ==}
    dev: true

  /highlight.js@10.7.3:
    resolution: {integrity: sha512-tzcUFauisWKNHaRkN4Wjl/ZA07gENAjFl3J/c480dprkGTg5EQstgaNFqBfUqCq54kZRIEcreTsAgF/m2quD7A==}

  /hmac-drbg@1.0.1:
    resolution: {integrity: sha512-Tti3gMqLdZfhOQY1Mzf/AanLiqh1WTiJgEj26ZuYQ9fbkLomzGchCws4FyrSd4VkpBfiNhaE1On+lOz894jvXg==}
    dependencies:
      hash.js: 1.1.7
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1

  /hosted-git-info@2.8.9:
    resolution: {integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==}

  /hpack.js@2.1.6:
    resolution: {integrity: sha512-zJxVehUdMGIKsRaNt7apO2Gqp0BdqW5yaiGHXXmbpvxgBYVZnAql+BJb4RO5ad2MgpbZKn5G6nMnegrH1FcNYQ==}
    dependencies:
      inherits: 2.0.4
      obuf: 1.1.2
      readable-stream: 2.3.7
      wbuf: 1.7.3

  /hsl-regex@1.0.0:
    resolution: {integrity: sha512-M5ezZw4LzXbBKMruP+BNANf0k+19hDQMgpzBIYnya//Al+fjNct9Wf3b1WedLqdEs2hKBvxq/jh+DsHJLj0F9A==}
    dev: true

  /hsla-regex@1.0.0:
    resolution: {integrity: sha512-7Wn5GMLuHBjZCb2bTmnDOycho0p/7UVaAeqXZGbHrBCl6Yd/xDhQJAXe6Ga9AXJH2I5zY1dEdYw2u1UptnSBJA==}
    dev: true

  /html-encoding-sniffer@1.0.2:
    resolution: {integrity: sha512-71lZziiDnsuabfdYiUeWdCVyKuqwWi23L8YeIgV9jSSZHCtb6wB1BKWooH7L3tn4/FuZJMVWyNaIDr4RGmaSYw==}
    dependencies:
      whatwg-encoding: 1.0.5

  /html-entities@2.3.3:
    resolution: {integrity: sha512-DV5Ln36z34NNTDgnz0EWGBLZENelNAtkiFA4kyNOG2tDI6Mz1uSWiq1wAKdyjnJwyDiDO7Fa2SO1CTxPXL8VxA==}

  /html-escaper@2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}

  /html-minifier-terser@6.1.0:
    resolution: {integrity: sha512-YXxSlJBZTP7RS3tWnQw74ooKa6L9b9i9QYXY21eUEvhZ3u9XLfv6OnFsQq6RxkhHygsaUMvYsZRV5rU/OVNZxw==}
    engines: {node: '>=12'}
    hasBin: true
    dependencies:
      camel-case: 4.1.2
      clean-css: 5.3.1
      commander: 8.3.0
      he: 1.2.0
      param-case: 3.0.4
      relateurl: 0.2.7
      terser: 5.15.1

  /html-webpack-plugin@5.5.0(webpack@5.75.0):
    resolution: {integrity: sha512-sy88PC2cRTVxvETRgUHFrL4No3UxvcH8G1NepGhqaTT+GXN2kTamqasot0inS5hXeg1cMbFDt27zzo9p35lZVw==}
    engines: {node: '>=10.13.0'}
    peerDependencies:
      webpack: ^5.20.0
    dependencies:
      '@types/html-minifier-terser': 6.1.0
      html-minifier-terser: 6.1.0
      lodash: 4.17.21
      pretty-error: 4.0.0
      tapable: 2.2.1
      webpack: 5.75.0(webpack-cli@4.10.0)

  /htmlparser2@6.1.0:
    resolution: {integrity: sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
      entities: 2.2.0

  /http-deceiver@1.2.7:
    resolution: {integrity: sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw==}

  /http-errors@1.6.3:
    resolution: {integrity: sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==}
    engines: {node: '>= 0.6'}
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0

  /http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  /http-parser-js@0.5.8:
    resolution: {integrity: sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q==}

  /http-proxy-middleware@2.0.6(@types/express@4.17.14)(debug@4.3.4):
    resolution: {integrity: sha512-ya/UeJ6HVBYxrgYotAZo1KvPWlgB48kUJLDePFeneHsVujFaW5WNj2NgWCAE//B1Dl02BIfYlpNgBy8Kf8Rjmw==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/express': ^4.17.13
    peerDependenciesMeta:
      '@types/express':
        optional: true
    dependencies:
      '@types/express': 4.17.14
      '@types/http-proxy': 1.17.9
      http-proxy: 1.18.1(debug@4.3.4)
      is-glob: 4.0.3
      is-plain-obj: 3.0.0
      micromatch: 4.0.5
    transitivePeerDependencies:
      - debug

  /http-proxy@1.18.1(debug@4.3.4):
    resolution: {integrity: sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==}
    engines: {node: '>=8.0.0'}
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.2(debug@4.3.4)
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug

  /http-signature@1.2.0:
    resolution: {integrity: sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==}
    engines: {node: '>=0.8', npm: '>=1.3.7'}
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.17.0

  /http-signature@1.3.6:
    resolution: {integrity: sha512-3adrsD6zqo4GsTqtO7FyrejHNv+NgiIfAfv68+jVlFmSr9OGy7zrxONceFRLKvnnZA5jbxQBX1u9PpB6Wi32Gw==}
    engines: {node: '>=0.10'}
    dependencies:
      assert-plus: 1.0.0
      jsprim: 2.0.2
      sshpk: 1.17.0
    dev: true

  /https-browserify@1.0.0:
    resolution: {integrity: sha512-J+FkSdyD+0mA0N+81tMotaRMfSL9SGi+xpD3T6YApKsc3bGSXJlfXri3VyFOeYkfLRQisDk1W+jIFFKBeUBbBg==}

  /human-signals@1.1.1:
    resolution: {integrity: sha512-SEQu7vl8KjNL2eoGBLF3+wAjpsNfA9XMlXAYj/3EdaNfAlxKthD1xjEQfGOUhllCGGJVNY34bRr6lPINhNjyZw==}
    engines: {node: '>=8.12.0'}
    dev: true

  /human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}

  /iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2

  /icss-utils@4.1.1:
    resolution: {integrity: sha512-4aFq7wvWyMHKgxsH8QQtGpvbASCf+eM3wPRLI6R+MgAnTCZ6STYsRvttLvRWK0Nfif5piF394St3HeJDaljGPA==}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /icss-utils@5.1.0(postcss@8.4.19):
    resolution: {integrity: sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.4.19

  /ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  /iferr@0.1.5:
    resolution: {integrity: sha512-DUNFN5j7Tln0D+TxzloUjKB+CtVu6myn0JEFak6dG18mNt9YkQ6lzGCdafwofISZ1lLF3xRHJ98VKy9ynkcFaA==}

  /ignore@4.0.6:
    resolution: {integrity: sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==}
    engines: {node: '>= 4'}

  /ignore@5.2.0:
    resolution: {integrity: sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==}
    engines: {node: '>= 4'}

  /iltorb@2.4.5:
    resolution: {integrity: sha512-EMCMl3LnnNSZJS5QrxyZmMTaAC4+TJkM5woD+xbpm9RB+mFYCr7C05GFE3TEGCsVQSVHmjX+3sf5AiwsylNInQ==}
    deprecated: The zlib module provides APIs for brotli compression/decompression starting with Node.js v10.16.0, please use it over iltorb
    requiresBuild: true
    dependencies:
      detect-libc: 1.0.3
      nan: 2.17.0
      npmlog: 4.1.2
      prebuild-install: 5.3.6
      which-pm-runs: 1.1.0
    dev: true
    optional: true

  /immutable@4.1.0:
    resolution: {integrity: sha512-oNkuqVTA8jqG1Q6c+UglTOD1xhC1BtjKI7XkCXRkZHrN5m18/XsnUp8Q89GkQO/z+0WjonSvl0FLhDYftp46nQ==}

  /import-fresh@2.0.0:
    resolution: {integrity: sha512-eZ5H8rcgYazHbKC3PG4ClHNykCSxtAhxSSEM+2mb+7evD2CKF5V7c0dNum7AdpDh0ZdICwZY9sRSn8f+KH96sg==}
    engines: {node: '>=4'}
    dependencies:
      caller-path: 2.0.0
      resolve-from: 3.0.0
    dev: true

  /import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  /import-local@2.0.0:
    resolution: {integrity: sha512-b6s04m3O+s3CGSbqDIyP4R6aAwAeYlVq9+WUWep6iHa8ETRf9yei1U48C5MmfJmV9AiLYYBKPMq/W+/WRpQmCQ==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      pkg-dir: 3.0.0
      resolve-cwd: 2.0.0

  /import-local@3.1.0:
    resolution: {integrity: sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==}
    engines: {node: '>=8'}
    hasBin: true
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  /imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  /indent-string@3.2.0:
    resolution: {integrity: sha512-BYqTHXTGUIvg7t1r4sJNKcbDZkL92nkXA8YtRpbjFHRHGDL/NtUeiBJMeE60kIFN/Mg8ESaWQvftaYMGJzQZCQ==}
    engines: {node: '>=4'}
    dev: true

  /indent-string@4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}
    dev: true

  /indexes-of@1.0.1:
    resolution: {integrity: sha512-bup+4tap3Hympa+JBJUG7XuOsdNQ6fxt0MHyXMKuLBKn0OqsTfvUxkUrroEX1+B2VsSHvCjiIcZVxRtYa4nllA==}
    dev: true

  /infer-owner@1.0.4:
    resolution: {integrity: sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==}

  /inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  /inherits@2.0.1:
    resolution: {integrity: sha512-8nWq2nLTAwd02jTqJExUYFSD/fKq6VH9Y/oG2accc/kdI0V98Bag8d5a4gi3XHz73rDWa2PvTtvcWYquKqSENA==}

  /inherits@2.0.3:
    resolution: {integrity: sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==}

  /inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  /ini@1.3.7:
    resolution: {integrity: sha512-iKpRpXP+CrP2jyrxvg1kMUpXDyRUFDWurxbnVT1vQPx+Wz9uCYsMIqYuSBLV+PAaZG/d7kRLKRFc9oDMsH+mFQ==}
    dev: true

  /ini@1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}
    dev: true

  /inquirer@7.3.3:
    resolution: {integrity: sha512-JG3eIAj5V9CwcGvuOmoo6LB9kbAYT8HXffUl6memuszlwDC/qvFAJw49XJ5NROSFNPxp3iQg1GqkFhaY/CR0IA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8

  /internal-slot@1.0.3:
    resolution: {integrity: sha512-O0DB1JC/sPyZl7cIo78n5dR7eUSwwpYPiXRhTzNxZVAMUuB8vlnRFyLxdrVToks6XPLVnFfbzaVd5WLjhgg+vA==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.1.3
      has: 1.0.3
      side-channel: 1.0.4

  /interpret@2.2.0:
    resolution: {integrity: sha512-Ju0Bz/cEia55xDwUWEa8+olFpCiQoypjnQySseKtmjNrnps3P+xfpUmGr90T7yjlVJmOtybRvPXhKMbHr+fWnw==}
    engines: {node: '>= 0.10'}

  /invariant@2.2.4:
    resolution: {integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==}
    dependencies:
      loose-envify: 1.4.0

  /ip-regex@2.1.0:
    resolution: {integrity: sha512-58yWmlHpp7VYfcdTwMTvwMmqx/Elfxjd9RXTDyMsbL7lLWmhMylLEqiYVLKuLzOZqVgiWXD9MfR62Vv89VRxkw==}
    engines: {node: '>=4'}
    dev: true

  /ipaddr.js@1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}

  /ipaddr.js@2.0.1:
    resolution: {integrity: sha512-1qTgH9NG+IIJ4yfKs2e6Pp1bZg8wbDbKHT21HrLIeYBTRLgMYKnMTPAuI3Lcs61nfx5h1xlXnbJtH1kX5/d/ng==}
    engines: {node: '>= 10'}

  /is-absolute-url@2.1.0:
    resolution: {integrity: sha512-vOx7VprsKyllwjSkLV79NIhpyLfr3jAp7VaTCMXOJHu4m0Ew1CZ2fcjASwmV1jI3BWuWHB013M48eyeldk9gYg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-accessor-descriptor@0.1.6:
    resolution: {integrity: sha512-e1BM1qnDbMRG3ll2U9dSK0UMHuWOs3pY3AtcFsmvwPtKL3MML/Q86i+GilLfvqEs4GW+ExB91tQ3Ig9noDIZ+A==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /is-accessor-descriptor@1.0.0:
    resolution: {integrity: sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 6.0.3

  /is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}

  /is-arrayish@0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}
    dev: true

  /is-bigint@1.0.4:
    resolution: {integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==}
    dependencies:
      has-bigints: 1.0.2

  /is-binary-path@1.0.1:
    resolution: {integrity: sha512-9fRVlXc0uCxEDj1nQzaWONSpbTfx0FmJfzHF7pwlI8DkWGoHBBea4Pg5Ky0ojwwxQmnSifgbKkI06Qv0Ljgj+Q==}
    engines: {node: '>=0.10.0'}
    dependencies:
      binary-extensions: 1.13.1
    optional: true

  /is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.2.0

  /is-boolean-object@1.1.2:
    resolution: {integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0

  /is-buffer@1.1.6:
    resolution: {integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==}

  /is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}

  /is-ci@2.0.0:
    resolution: {integrity: sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w==}
    hasBin: true
    dependencies:
      ci-info: 2.0.0

  /is-color-stop@1.1.0:
    resolution: {integrity: sha512-H1U8Vz0cfXNujrJzEcvvwMDW9Ra+biSYA3ThdQvAnMLJkEHQXn6bWzLkxHtVYJ+Sdbx0b6finn3jZiaVe7MAHA==}
    dependencies:
      css-color-names: 0.0.4
      hex-color-regex: 1.1.0
      hsl-regex: 1.0.0
      hsla-regex: 1.0.0
      rgb-regex: 1.0.1
      rgba-regex: 1.0.0
    dev: true

  /is-core-module@2.11.0:
    resolution: {integrity: sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw==}
    dependencies:
      has: 1.0.3

  /is-data-descriptor@0.1.4:
    resolution: {integrity: sha512-+w9D5ulSoBNlmw9OHn3U2v51SyoCd0he+bB3xMl62oijhrspxowjU+AIcDY0N3iEJbUEkB15IlMASQsxYigvXg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /is-data-descriptor@1.0.0:
    resolution: {integrity: sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 6.0.3

  /is-date-object@1.0.5:
    resolution: {integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0

  /is-descriptor@0.1.6:
    resolution: {integrity: sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-accessor-descriptor: 0.1.6
      is-data-descriptor: 0.1.4
      kind-of: 5.1.0

  /is-descriptor@1.0.2:
    resolution: {integrity: sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-accessor-descriptor: 1.0.0
      is-data-descriptor: 1.0.0
      kind-of: 6.0.3

  /is-directory@0.3.1:
    resolution: {integrity: sha512-yVChGzahRFvbkscn2MlwGismPO12i9+znNruC5gVEntG3qu0xQMzsGg/JFbrsqDOHtHFPci+V5aP5T9I+yeKqw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-docker@2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==}
    engines: {node: '>=8'}
    hasBin: true

  /is-extendable@0.1.1:
    resolution: {integrity: sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==}
    engines: {node: '>=0.10.0'}

  /is-extendable@1.0.1:
    resolution: {integrity: sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-object: 2.0.4

  /is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  /is-file-esm@1.0.0:
    resolution: {integrity: sha512-rZlaNKb4Mr8WlRu2A9XdeoKgnO5aA53XdPHgCKVyCrQ/rWi89RET1+bq37Ru46obaQXeiX4vmFIm1vks41hoSA==}
    dependencies:
      read-pkg-up: 7.0.1

  /is-fullwidth-code-point@1.0.0:
    resolution: {integrity: sha512-1pqUqRjkhPJ9miNq9SwMfdvi6lBJcd6eFxvfaivQhaH3SgisfiuudvFntdKOmxuee/77l+FPjKrQjWvmPjWrRw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      number-is-nan: 1.0.1
    dev: true

  /is-fullwidth-code-point@2.0.0:
    resolution: {integrity: sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==}
    engines: {node: '>=4'}

  /is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  /is-generator-fn@2.1.0:
    resolution: {integrity: sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==}
    engines: {node: '>=6'}

  /is-glob@3.1.0:
    resolution: {integrity: sha512-UFpDDrPgM6qpnFNI+rh/p3bUaq9hKLZN8bMUWzxmcnZVS3omf4IPK+BrewlnWjO1WmUsMYuSjKh4UJuV4+Lqmw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    optional: true

  /is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1

  /is-installed-globally@0.3.2:
    resolution: {integrity: sha512-wZ8x1js7Ia0kecP/CHM/3ABkAmujX7WPvQk6uu3Fly/Mk44pySulQpnHG46OMjHGXApINnV4QhY3SWnECO2z5g==}
    engines: {node: '>=8'}
    dependencies:
      global-dirs: 2.1.0
      is-path-inside: 3.0.3
    dev: true

  /is-interactive@1.0.0:
    resolution: {integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==}
    engines: {node: '>=8'}

  /is-negative-zero@2.0.2:
    resolution: {integrity: sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==}
    engines: {node: '>= 0.4'}

  /is-number-object@1.0.7:
    resolution: {integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0

  /is-number@3.0.0:
    resolution: {integrity: sha512-4cboCqIpliH+mAvFNegjZQ4kgKc3ZUhQVr3HvWbSh5q3WH2v82ct+T2Y1hdU5Gdtorx/cLifQjqCbL7bpznLTg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  /is-obj@2.0.0:
    resolution: {integrity: sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==}
    engines: {node: '>=8'}
    dev: true

  /is-observable@1.1.0:
    resolution: {integrity: sha512-NqCa4Sa2d+u7BWc6CukaObG3Fh+CU9bvixbpcXYhy2VvYS7vVGIdAgnIS5Ks3A/cqk4rebLJ9s8zBstT2aKnIA==}
    engines: {node: '>=4'}
    dependencies:
      symbol-observable: 1.2.0
    dev: true

  /is-path-inside@3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==}
    engines: {node: '>=8'}
    dev: true

  /is-plain-obj@1.1.0:
    resolution: {integrity: sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-plain-obj@3.0.0:
    resolution: {integrity: sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==}
    engines: {node: '>=10'}

  /is-plain-object@2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1

  /is-promise@2.2.2:
    resolution: {integrity: sha512-+lP4/6lKUBfQjZ2pdxThZvLUAafmZb8OAxFb8XXtiQmS35INgr85hdOGoEs124ez1FCnZJt6jau/T+alh58QFQ==}
    dev: true

  /is-regex@1.1.4:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0

  /is-resolvable@1.1.0:
    resolution: {integrity: sha512-qgDYXFSR5WvEfuS5dMj6oTMEbrrSaM0CrFk2Yiq/gXnBvD9pMa2jGXxyhGLfvhZpuMZe18CJpFxAt3CRs42NMg==}
    dev: true

  /is-shared-array-buffer@1.0.2:
    resolution: {integrity: sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==}
    dependencies:
      call-bind: 1.0.2

  /is-stream@1.1.0:
    resolution: {integrity: sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==}
    engines: {node: '>=0.10.0'}

  /is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}

  /is-string@1.0.7:
    resolution: {integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0

  /is-symbol@1.0.4:
    resolution: {integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3

  /is-typedarray@1.0.0:
    resolution: {integrity: sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==}

  /is-unicode-supported@0.1.0:
    resolution: {integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==}
    engines: {node: '>=10'}

  /is-weakref@1.0.2:
    resolution: {integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==}
    dependencies:
      call-bind: 1.0.2

  /is-whitespace@0.3.0:
    resolution: {integrity: sha512-RydPhl4S6JwAyj0JJjshWJEFG6hNye3pZFBRZaTUfZFwGHxzppNaNOVgQuS/E/SlhrApuMXrpnK1EEIXfdo3Dg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-windows@1.0.2:
    resolution: {integrity: sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==}
    engines: {node: '>=0.10.0'}

  /is-wsl@1.1.0:
    resolution: {integrity: sha512-gfygJYZ2gLTDlmbWMI0CE2MwnFzSN/2SZfkMlItC4K/JBlsWVDB0bO6XhqcY13YXE7iMcAJnzTCJjPiTeJJ0Mw==}
    engines: {node: '>=4'}

  /is-wsl@2.2.0:
    resolution: {integrity: sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==}
    engines: {node: '>=8'}
    dependencies:
      is-docker: 2.2.1

  /isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}

  /isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  /isobject@2.1.0:
    resolution: {integrity: sha512-+OUdGJlgjOBZDfxnDjYYG6zp487z0JGNQq3cYQYg5f5hKR+syHMsaztzGeml/4kGG55CSpKSpWTY+jYGgsHLgA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isarray: 1.0.0

  /isobject@3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==}
    engines: {node: '>=0.10.0'}

  /isstream@0.1.2:
    resolution: {integrity: sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==}

  /istanbul-lib-coverage@2.0.5:
    resolution: {integrity: sha512-8aXznuEPCJvGnMSRft4udDRDtb1V3pkQkMMI5LI+6HuQz5oQ4J2UFn1H82raA3qJtyOLkkwVqICBQkjnGtn5mA==}
    engines: {node: '>=6'}

  /istanbul-lib-instrument@3.3.0:
    resolution: {integrity: sha512-5nnIN4vo5xQZHdXno/YDXJ0G+I3dAm4XgzfSVTPLQpj/zAV2dV6Juy0yaf10/zrJOJeHoN3fraFe+XRq2bFVZA==}
    engines: {node: '>=6'}
    dependencies:
      '@babel/generator': 7.20.4
      '@babel/parser': 7.20.3
      '@babel/template': 7.18.10
      '@babel/traverse': 7.20.1
      '@babel/types': 7.20.2
      istanbul-lib-coverage: 2.0.5
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color

  /istanbul-lib-report@2.0.8:
    resolution: {integrity: sha512-fHBeG573EIihhAblwgxrSenp0Dby6tJMFR/HvlerBsrCTD5bkUuoNtn3gVh29ZCS824cGGBPn7Sg7cNk+2xUsQ==}
    engines: {node: '>=6'}
    dependencies:
      istanbul-lib-coverage: 2.0.5
      make-dir: 2.1.0
      supports-color: 6.1.0

  /istanbul-lib-source-maps@3.0.6:
    resolution: {integrity: sha512-R47KzMtDJH6X4/YW9XTx+jrLnZnscW4VpNN+1PViSYTejLVPWv7oov+Duf8YQSPyVRUvueQqz1TcsC6mooZTXw==}
    engines: {node: '>=6'}
    dependencies:
      debug: 4.3.4
      istanbul-lib-coverage: 2.0.5
      make-dir: 2.1.0
      rimraf: 2.7.1
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color

  /istanbul-reports@2.2.7:
    resolution: {integrity: sha512-uu1F/L1o5Y6LzPVSVZXNOoD/KXpJue9aeLRd0sM9uMXfZvzomB0WxVamWb5ue8kA2vVWEmW7EG+A5n3f1kqHKg==}
    engines: {node: '>=6'}
    dependencies:
      html-escaper: 2.0.2

  /javascript-stringify@2.1.0:
    resolution: {integrity: sha512-JVAfqNPTvNq3sB/VHQJAFxN/sPgKnsKrCwyRt15zwNCdrMMJDdcEOdubuy+DuJYYdm0ox1J4uzEuYKkN+9yhVg==}

  /jest-changed-files@24.9.0:
    resolution: {integrity: sha512-6aTWpe2mHF0DhL28WjdkO8LyGjs3zItPET4bMSeXU6T3ub4FPMw+mcOcbdGXQOAfmLcxofD23/5Bl9Z4AkFwqg==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      execa: 1.0.0
      throat: 4.1.0

  /jest-cli@24.9.0:
    resolution: {integrity: sha512-+VLRKyitT3BWoMeSUIHRxV/2g8y9gw91Jh5z2UmXZzkZKpbC08CSehVxgHUwTpy+HwGcns/tqafQDJW7imYvGg==}
    engines: {node: '>= 6'}
    hasBin: true
    dependencies:
      '@jest/core': 24.9.0
      '@jest/test-result': 24.9.0
      '@jest/types': 24.9.0
      chalk: 2.4.2
      exit: 0.1.2
      import-local: 2.0.0
      is-ci: 2.0.0
      jest-config: 24.9.0
      jest-util: 24.9.0
      jest-validate: 24.9.0
      prompts: 2.4.2
      realpath-native: 1.1.0
      yargs: 13.3.2
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  /jest-config@24.9.0:
    resolution: {integrity: sha512-RATtQJtVYQrp7fvWg6f5y3pEFj9I+H8sWw4aKxnDZ96mob5i5SD6ZEGWgMLXQ4LE8UurrjbdlLWdUeo+28QpfQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@babel/core': 7.20.2
      '@jest/test-sequencer': 24.9.0
      '@jest/types': 24.9.0
      babel-jest: 24.9.0(@babel/core@7.20.2)
      chalk: 2.4.2
      glob: 7.2.3
      jest-environment-jsdom: 24.9.0
      jest-environment-node: 24.9.0
      jest-get-type: 24.9.0
      jest-jasmine2: 24.9.0
      jest-regex-util: 24.9.0
      jest-resolve: 24.9.0
      jest-util: 24.9.0
      jest-validate: 24.9.0
      micromatch: 3.1.10
      pretty-format: 24.9.0
      realpath-native: 1.1.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  /jest-diff@24.9.0:
    resolution: {integrity: sha512-qMfrTs8AdJE2iqrTp0hzh7kTd2PQWrsFyj9tORoKmu32xjPjeE4NyjVRDz8ybYwqS2ik8N4hsIpiVTyFeo2lBQ==}
    engines: {node: '>= 6'}
    dependencies:
      chalk: 2.4.2
      diff-sequences: 24.9.0
      jest-get-type: 24.9.0
      pretty-format: 24.9.0

  /jest-docblock@24.9.0:
    resolution: {integrity: sha512-F1DjdpDMJMA1cN6He0FNYNZlo3yYmOtRUnktrT9Q37njYzC5WEaDdmbynIgy0L/IvXvvgsG8OsqhLPXTpfmZAA==}
    engines: {node: '>= 6'}
    dependencies:
      detect-newline: 2.1.0

  /jest-each@24.9.0:
    resolution: {integrity: sha512-ONi0R4BvW45cw8s2Lrx8YgbeXL1oCQ/wIDwmsM3CqM/nlblNCPmnC3IPQlMbRFZu3wKdQ2U8BqM6lh3LJ5Bsog==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      chalk: 2.4.2
      jest-get-type: 24.9.0
      jest-util: 24.9.0
      pretty-format: 24.9.0
    transitivePeerDependencies:
      - supports-color

  /jest-environment-jsdom-fifteen@1.0.2:
    resolution: {integrity: sha512-nfrnAfwklE1872LIB31HcjM65cWTh1wzvMSp10IYtPJjLDUbTTvDpajZgIxUnhRmzGvogdHDayCIlerLK0OBBg==}
    dependencies:
      '@jest/environment': 24.9.0
      '@jest/fake-timers': 24.9.0
      '@jest/types': 24.9.0
      jest-mock: 24.9.0
      jest-util: 24.9.0
      jsdom: 15.2.1
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate
    dev: true

  /jest-environment-jsdom@24.9.0:
    resolution: {integrity: sha512-Zv9FV9NBRzLuALXjvRijO2351DRQeLYXtpD4xNvfoVFw21IOKNhZAEUKcbiEtjTkm2GsJ3boMVgkaR7rN8qetA==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/environment': 24.9.0
      '@jest/fake-timers': 24.9.0
      '@jest/types': 24.9.0
      jest-mock: 24.9.0
      jest-util: 24.9.0
      jsdom: 11.12.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  /jest-environment-node@24.9.0:
    resolution: {integrity: sha512-6d4V2f4nxzIzwendo27Tr0aFm+IXWa0XEUnaH6nU0FMaozxovt+sfRvh4J47wL1OvF83I3SSTu0XK+i4Bqe7uA==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/environment': 24.9.0
      '@jest/fake-timers': 24.9.0
      '@jest/types': 24.9.0
      jest-mock: 24.9.0
      jest-util: 24.9.0
    transitivePeerDependencies:
      - supports-color

  /jest-get-type@24.9.0:
    resolution: {integrity: sha512-lUseMzAley4LhIcpSP9Jf+fTrQ4a1yHQwLNeeVa2cEmbCGeoZAtYPOIv8JaxLD/sUpKxetKGP+gsHl8f8TSj8Q==}
    engines: {node: '>= 6'}

  /jest-haste-map@24.9.0:
    resolution: {integrity: sha512-kfVFmsuWui2Sj1Rp1AJ4D9HqJwE4uwTlS/vO+eRUaMmd54BFpli2XhMQnPC2k4cHFVbB2Q2C+jtI1AGLgEnCjQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      anymatch: 2.0.0
      fb-watchman: 2.0.2
      graceful-fs: 4.2.10
      invariant: 2.2.4
      jest-serializer: 24.9.0
      jest-util: 24.9.0
      jest-worker: 24.9.0
      micromatch: 3.1.10
      sane: 4.1.0
      walker: 1.0.8
    optionalDependencies:
      fsevents: 1.2.13
    transitivePeerDependencies:
      - supports-color

  /jest-jasmine2@24.9.0:
    resolution: {integrity: sha512-Cq7vkAgaYKp+PsX+2/JbTarrk0DmNhsEtqBXNwUHkdlbrTBLtMJINADf2mf5FkowNsq8evbPc07/qFO0AdKTzw==}
    engines: {node: '>= 6'}
    dependencies:
      '@babel/traverse': 7.20.1
      '@jest/environment': 24.9.0
      '@jest/test-result': 24.9.0
      '@jest/types': 24.9.0
      chalk: 2.4.2
      co: 4.6.0
      expect: 24.9.0
      is-generator-fn: 2.1.0
      jest-each: 24.9.0
      jest-matcher-utils: 24.9.0
      jest-message-util: 24.9.0
      jest-runtime: 24.9.0
      jest-snapshot: 24.9.0
      jest-util: 24.9.0
      pretty-format: 24.9.0
      throat: 4.1.0
    transitivePeerDependencies:
      - supports-color

  /jest-leak-detector@24.9.0:
    resolution: {integrity: sha512-tYkFIDsiKTGwb2FG1w8hX9V0aUb2ot8zY/2nFg087dUageonw1zrLMP4W6zsRO59dPkTSKie+D4rhMuP9nRmrA==}
    engines: {node: '>= 6'}
    dependencies:
      jest-get-type: 24.9.0
      pretty-format: 24.9.0

  /jest-matcher-utils@24.9.0:
    resolution: {integrity: sha512-OZz2IXsu6eaiMAwe67c1T+5tUAtQyQx27/EMEkbFAGiw52tB9em+uGbzpcgYVpA8wl0hlxKPZxrly4CXU/GjHA==}
    engines: {node: '>= 6'}
    dependencies:
      chalk: 2.4.2
      jest-diff: 24.9.0
      jest-get-type: 24.9.0
      pretty-format: 24.9.0

  /jest-message-util@24.9.0:
    resolution: {integrity: sha512-oCj8FiZ3U0hTP4aSui87P4L4jC37BtQwUMqk+zk/b11FR19BJDeZsZAvIHutWnmtw7r85UmR3CEWZ0HWU2mAlw==}
    engines: {node: '>= 6'}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@jest/test-result': 24.9.0
      '@jest/types': 24.9.0
      '@types/stack-utils': 1.0.1
      chalk: 2.4.2
      micromatch: 3.1.10
      slash: 2.0.0
      stack-utils: 1.0.5
    transitivePeerDependencies:
      - supports-color

  /jest-mock@24.9.0:
    resolution: {integrity: sha512-3BEYN5WbSq9wd+SyLDES7AHnjH9A/ROBwmz7l2y+ol+NtSFO8DYiEBzoO1CeFc9a8DYy10EO4dDFVv/wN3zl1w==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0

  /jest-pnp-resolver@1.2.2(jest-resolve@24.9.0):
    resolution: {integrity: sha512-olV41bKSMm8BdnuMsewT4jqlZ8+3TCARAXjZGT9jcoSnrfUnRCqnMoF9XEeoWjbzObpqF9dRhHQj0Xb9QdF6/w==}
    engines: {node: '>=6'}
    peerDependencies:
      jest-resolve: '*'
    peerDependenciesMeta:
      jest-resolve:
        optional: true
    dependencies:
      jest-resolve: 24.9.0

  /jest-regex-util@24.9.0:
    resolution: {integrity: sha512-05Cmb6CuxaA+Ys6fjr3PhvV3bGQmO+2p2La4hFbU+W5uOc479f7FdLXUWXw4pYMAhhSZIuKHwSXSu6CsSBAXQA==}
    engines: {node: '>= 6'}

  /jest-resolve-dependencies@24.9.0:
    resolution: {integrity: sha512-Fm7b6AlWnYhT0BXy4hXpactHIqER7erNgIsIozDXWl5dVm+k8XdGVe1oTg1JyaFnOxarMEbax3wyRJqGP2Pq+g==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      jest-regex-util: 24.9.0
      jest-snapshot: 24.9.0
    transitivePeerDependencies:
      - supports-color

  /jest-resolve@24.9.0:
    resolution: {integrity: sha512-TaLeLVL1l08YFZAt3zaPtjiVvyy4oSA6CRe+0AFPPVX3Q/VI0giIWWoAvoS5L96vj9Dqxj4fB5p2qrHCmTU/MQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      browser-resolve: 1.11.3
      chalk: 2.4.2
      jest-pnp-resolver: 1.2.2(jest-resolve@24.9.0)
      realpath-native: 1.1.0

  /jest-runner@24.9.0:
    resolution: {integrity: sha512-KksJQyI3/0mhcfspnxxEOBueGrd5E4vV7ADQLT9ESaCzz02WnbdbKWIf5Mkaucoaj7obQckYPVX6JJhgUcoWWg==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/console': 24.9.0
      '@jest/environment': 24.9.0
      '@jest/test-result': 24.9.0
      '@jest/types': 24.9.0
      chalk: 2.4.2
      exit: 0.1.2
      graceful-fs: 4.2.10
      jest-config: 24.9.0
      jest-docblock: 24.9.0
      jest-haste-map: 24.9.0
      jest-jasmine2: 24.9.0
      jest-leak-detector: 24.9.0
      jest-message-util: 24.9.0
      jest-resolve: 24.9.0
      jest-runtime: 24.9.0
      jest-util: 24.9.0
      jest-worker: 24.9.0
      source-map-support: 0.5.21
      throat: 4.1.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  /jest-runtime@24.9.0:
    resolution: {integrity: sha512-8oNqgnmF3v2J6PVRM2Jfuj8oX3syKmaynlDMMKQ4iyzbQzIG6th5ub/lM2bCMTmoTKM3ykcUYI2Pw9xwNtjMnw==}
    engines: {node: '>= 6'}
    hasBin: true
    dependencies:
      '@jest/console': 24.9.0
      '@jest/environment': 24.9.0
      '@jest/source-map': 24.9.0
      '@jest/transform': 24.9.0
      '@jest/types': 24.9.0
      '@types/yargs': 13.0.12
      chalk: 2.4.2
      exit: 0.1.2
      glob: 7.2.3
      graceful-fs: 4.2.10
      jest-config: 24.9.0
      jest-haste-map: 24.9.0
      jest-message-util: 24.9.0
      jest-mock: 24.9.0
      jest-regex-util: 24.9.0
      jest-resolve: 24.9.0
      jest-snapshot: 24.9.0
      jest-util: 24.9.0
      jest-validate: 24.9.0
      realpath-native: 1.1.0
      slash: 2.0.0
      strip-bom: 3.0.0
      yargs: 13.3.2
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  /jest-serializer-vue@2.0.2:
    resolution: {integrity: sha512-nK/YIFo6qe3i9Ge+hr3h4PpRehuPPGZFt8LDBdTHYldMb7ZWlkanZS8Ls7D8h6qmQP2lBQVDLP0DKn5bJ9QApQ==}
    dependencies:
      pretty: 2.0.0
    dev: true

  /jest-serializer@24.9.0:
    resolution: {integrity: sha512-DxYipDr8OvfrKH3Kel6NdED3OXxjvxXZ1uIY2I9OFbGg+vUkkg7AGvi65qbhbWNPvDckXmzMPbK3u3HaDO49bQ==}
    engines: {node: '>= 6'}

  /jest-snapshot@24.9.0:
    resolution: {integrity: sha512-uI/rszGSs73xCM0l+up7O7a40o90cnrk429LOiK3aeTvfC0HHmldbd81/B7Ix81KSFe1lwkbl7GnBGG4UfuDew==}
    engines: {node: '>= 6'}
    dependencies:
      '@babel/types': 7.20.2
      '@jest/types': 24.9.0
      chalk: 2.4.2
      expect: 24.9.0
      jest-diff: 24.9.0
      jest-get-type: 24.9.0
      jest-matcher-utils: 24.9.0
      jest-message-util: 24.9.0
      jest-resolve: 24.9.0
      mkdirp: 0.5.6
      natural-compare: 1.4.0
      pretty-format: 24.9.0
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color

  /jest-sonar-reporter@2.0.0:
    resolution: {integrity: sha512-ZervDCgEX5gdUbdtWsjdipLN3bKJwpxbvhkYNXTAYvAckCihobSLr9OT/IuyNIRT1EZMDDwR6DroWtrq+IL64w==}
    engines: {node: '>=8.0.0'}
    dependencies:
      xml: 1.0.1
    dev: false

  /jest-transform-stub@2.0.0:
    resolution: {integrity: sha512-lspHaCRx/mBbnm3h4uMMS3R5aZzMwyNpNIJLXj4cEsV0mIUtS4IjYJLSoyjRCtnxb6RIGJ4NL2quZzfIeNhbkg==}
    dev: true

  /jest-util@24.9.0:
    resolution: {integrity: sha512-x+cZU8VRmOJxbA1K5oDBdxQmdq0OIdADarLxk0Mq+3XS4jgvhG/oKGWcIDCtPG0HgjxOYvF+ilPJQsAyXfbNOg==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/console': 24.9.0
      '@jest/fake-timers': 24.9.0
      '@jest/source-map': 24.9.0
      '@jest/test-result': 24.9.0
      '@jest/types': 24.9.0
      callsites: 3.1.0
      chalk: 2.4.2
      graceful-fs: 4.2.10
      is-ci: 2.0.0
      mkdirp: 0.5.6
      slash: 2.0.0
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color

  /jest-validate@24.9.0:
    resolution: {integrity: sha512-HPIt6C5ACwiqSiwi+OfSSHbK8sG7akG8eATl+IPKaeIjtPOeBUd/g3J7DghugzxrGjI93qS/+RPKe1H6PqvhRQ==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      camelcase: 5.3.1
      chalk: 2.4.2
      jest-get-type: 24.9.0
      leven: 3.1.0
      pretty-format: 24.9.0

  /jest-watch-typeahead@0.4.2:
    resolution: {integrity: sha512-f7VpLebTdaXs81rg/oj4Vg/ObZy2QtGzAmGLNsqUS5G5KtSN68tFcIsbvNODfNyQxU78g7D8x77o3bgfBTR+2Q==}
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 2.4.2
      jest-regex-util: 24.9.0
      jest-watcher: 24.9.0
      slash: 3.0.0
      string-length: 3.1.0
      strip-ansi: 5.2.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /jest-watcher@24.9.0:
    resolution: {integrity: sha512-+/fLOfKPXXYJDYlks62/4R4GoT+GU1tYZed99JSCOsmzkkF7727RqKrjNAxtfO4YpGv11wybgRvCjR73lK2GZw==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/test-result': 24.9.0
      '@jest/types': 24.9.0
      '@types/yargs': 13.0.12
      ansi-escapes: 3.2.0
      chalk: 2.4.2
      jest-util: 24.9.0
      string-length: 2.0.0
    transitivePeerDependencies:
      - supports-color

  /jest-worker@24.9.0:
    resolution: {integrity: sha512-51PE4haMSXcHohnSMdM42anbvZANYTqMrr52tVKPqqsPJMzoP6FYYDVqahX/HrAoKEKz3uUPzSvKs9A3qR4iVw==}
    engines: {node: '>= 6'}
    dependencies:
      merge-stream: 2.0.0
      supports-color: 6.1.0

  /jest-worker@27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/node': 18.11.9
      merge-stream: 2.0.0
      supports-color: 8.1.1

  /jest@24.9.0:
    resolution: {integrity: sha512-YvkBL1Zm7d2B1+h5fHEOdyjCG+sGMz4f8D86/0HiqJ6MB4MnDc8FgP5vdWsGnemOQro7lnYo8UakZ3+5A0jxGw==}
    engines: {node: '>= 6'}
    hasBin: true
    dependencies:
      import-local: 2.0.0
      jest-cli: 24.9.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  /joi@17.7.0:
    resolution: {integrity: sha512-1/ugc8djfn93rTE3WRKdCzGGt/EtiYKxITMO4Wiv6q5JL1gl9ePt4kBsl1S499nbosspfctIQTpYIhSmHA3WAg==}
    dependencies:
      '@hapi/hoek': 9.3.0
      '@hapi/topo': 5.1.0
      '@sideway/address': 4.1.4
      '@sideway/formula': 3.0.0
      '@sideway/pinpoint': 2.0.0

  /jquery@3.6.1:
    resolution: {integrity: sha512-opJeO4nCucVnsjiXOE+/PcCgYw9Gwpvs/a6B1LL/lQhwWwpbVEVYDZ1FokFr8PRc7ghYlrFPuyHuiiDNTQxmcw==}
    dev: false

  /js-beautify@1.14.7:
    resolution: {integrity: sha512-5SOX1KXPFKx+5f6ZrPsIPEY7NwKeQz47n3jm2i+XeHx9MoRsfQenlOP13FQhWvg8JRS0+XLO6XYUQ2GX+q+T9A==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      config-chain: 1.1.13
      editorconfig: 0.15.3
      glob: 8.0.3
      nopt: 6.0.0
    dev: true

  /js-message@1.0.7:
    resolution: {integrity: sha512-efJLHhLjIyKRewNS9EGZ4UpI8NguuL6fKkhRxVuMmrGV2xN/0APGdQYwLFky5w9naebSZ0OwAGp0G6/2Cg90rA==}
    engines: {node: '>=0.6.0'}

  /js-tokens@3.0.2:
    resolution: {integrity: sha512-RjTcuD4xjtthQkaWH7dFlH85L+QaVtSoOyGdZ3g6HFhS9dFNDfLyqgm2NFe2X6cQpeFmt0452FJjFG5UameExg==}
    dev: true

  /js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  /js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  /jsbn@0.1.1:
    resolution: {integrity: sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==}

  /jsdom@11.12.0:
    resolution: {integrity: sha512-y8Px43oyiBM13Zc1z780FrfNLJCXTL40EWlty/LXUtcjykRBNgLlCjWXpfSPBl2iv+N7koQN+dvqszHZgT/Fjw==}
    dependencies:
      abab: 2.0.6
      acorn: 5.7.4
      acorn-globals: 4.3.4
      array-equal: 1.0.0
      cssom: 0.3.8
      cssstyle: 1.4.0
      data-urls: 1.1.0
      domexception: 1.0.1
      escodegen: 1.14.3
      html-encoding-sniffer: 1.0.2
      left-pad: 1.3.0
      nwsapi: 2.2.2
      parse5: 4.0.0
      pn: 1.1.0
      request: 2.88.2
      request-promise-native: 1.0.9(request@2.88.2)
      sax: 1.2.4
      symbol-tree: 3.2.4
      tough-cookie: 2.5.0
      w3c-hr-time: 1.0.2
      webidl-conversions: 4.0.2
      whatwg-encoding: 1.0.5
      whatwg-mimetype: 2.3.0
      whatwg-url: 6.5.0
      ws: 5.2.3
      xml-name-validator: 3.0.0
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  /jsdom@15.2.1:
    resolution: {integrity: sha512-fAl1W0/7T2G5vURSyxBzrJ1LSdQn6Tr5UX/xD4PXDx/PDgwygedfW6El/KIj3xJ7FU61TTYnc/l/B7P49Eqt6g==}
    engines: {node: '>=8'}
    peerDependencies:
      canvas: ^2.5.0
    peerDependenciesMeta:
      canvas:
        optional: true
    dependencies:
      abab: 2.0.6
      acorn: 7.4.1
      acorn-globals: 4.3.4
      array-equal: 1.0.0
      cssom: 0.4.4
      cssstyle: 2.3.0
      data-urls: 1.1.0
      domexception: 1.0.1
      escodegen: 1.14.3
      html-encoding-sniffer: 1.0.2
      nwsapi: 2.2.2
      parse5: 5.1.0
      pn: 1.1.0
      request: 2.88.2
      request-promise-native: 1.0.9(request@2.88.2)
      saxes: 3.1.11
      symbol-tree: 3.2.4
      tough-cookie: 3.0.1
      w3c-hr-time: 1.0.2
      w3c-xmlserializer: 1.1.2
      webidl-conversions: 4.0.2
      whatwg-encoding: 1.0.5
      whatwg-mimetype: 2.3.0
      whatwg-url: 7.1.0
      ws: 7.5.9
      xml-name-validator: 3.0.0
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: true

  /jsesc@0.5.0:
    resolution: {integrity: sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==}
    hasBin: true
    dev: true

  /jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true

  /json-parse-better-errors@1.0.2:
    resolution: {integrity: sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==}

  /json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  /json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  /json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}

  /json-schema@0.4.0:
    resolution: {integrity: sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==}

  /json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  /json-stringify-safe@5.0.1:
    resolution: {integrity: sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==}

  /json5@0.5.1:
    resolution: {integrity: sha512-4xrs1aW+6N5DalkqSVA8fxh458CXvR99WU8WLKmq4v8eWAL86Xo3BVqyd3SkA9wEVjCMqyvvRRkshAdOnBp5rw==}
    hasBin: true
    dev: true

  /json5@1.0.1:
    resolution: {integrity: sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==}
    hasBin: true
    dependencies:
      minimist: 1.2.7

  /json5@2.2.1:
    resolution: {integrity: sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==}
    engines: {node: '>=6'}
    hasBin: true

  /jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}
    dependencies:
      universalify: 2.0.0
    optionalDependencies:
      graceful-fs: 4.2.10

  /jsprim@1.4.2:
    resolution: {integrity: sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0

  /jsprim@2.0.2:
    resolution: {integrity: sha512-gqXddjPqQ6G40VdnI6T6yObEC+pDNvyP95wdQhkWkg7crHH3km5qP1FsOXEkzEQwnz6gz5qGTn1c2Y52wP3OyQ==}
    engines: {'0': node >=0.6.0}
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0
    dev: true

  /jsx-ast-utils@2.4.1:
    resolution: {integrity: sha512-z1xSldJ6imESSzOjd3NNkieVJKRlKYSOtMG8SFyCj2FIrvSaSuli/WjpBkEzCBoR9bYYYFgqJw61Xhu7Lcgk+w==}
    engines: {node: '>=4.0'}
    dependencies:
      array-includes: 3.1.6
      object.assign: 4.1.4
    dev: true

  /kind-of@3.2.2:
    resolution: {integrity: sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6

  /kind-of@4.0.0:
    resolution: {integrity: sha512-24XsCxmEbRwEDbz/qz3stgin8TTzZ1ESR56OMCN0ujYg+vRutNSiOj9bHH9u85DKgXguraugV5sFuvbD4FW/hw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6

  /kind-of@5.1.0:
    resolution: {integrity: sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw==}
    engines: {node: '>=0.10.0'}

  /kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}

  /kleur@3.0.3:
    resolution: {integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==}
    engines: {node: '>=6'}

  /klona@1.1.2:
    resolution: {integrity: sha512-xf88rTeHiXk+XE2Vhi6yj8Wm3gMZrygGdKjJqN8HkV+PwF/t50/LdAKHoHpPcxFAlmQszTZ1CugrK25S7qDRLA==}
    engines: {node: '>= 8'}

  /klona@2.0.5:
    resolution: {integrity: sha512-pJiBpiXMbt7dkzXe8Ghj/u4FfXOOa98fPW+bihOJ4SjnoijweJrNThJfd3ifXpXhREjpoF2mZVH1GfS9LV3kHQ==}
    engines: {node: '>= 8'}

  /launch-editor-middleware@2.6.0:
    resolution: {integrity: sha512-K2yxgljj5TdCeRN1lBtO3/J26+AIDDDw+04y6VAiZbWcTdBwsYN6RrZBnW5DN/QiSIdKNjKdATLUUluWWFYTIA==}
    dependencies:
      launch-editor: 2.6.0

  /launch-editor@2.6.0:
    resolution: {integrity: sha512-JpDCcQnyAAzZZaZ7vEiSqL690w7dAEyLao+KC96zBplnYbJS7TYNjvM3M7y3dGz+v7aIsJk3hllWuc0kWAjyRQ==}
    dependencies:
      picocolors: 1.0.0
      shell-quote: 1.7.4

  /lazy-ass@1.6.0:
    resolution: {integrity: sha512-cc8oEVoctTvsFZ/Oje/kGnHbpWHYBe8IAJe4C0QNc3t8uM/0Y8+erSz/7Y1ALuXTEZTMvxXwO6YbX1ey3ujiZw==}
    engines: {node: '> 0.8'}
    dev: true

  /left-pad@1.3.0:
    resolution: {integrity: sha512-XI5MPzVNApjAyhQzphX8BkmKsKUxD4LdyK24iZeQGinBN9yTQT3bFlCBy/aVx2HrNcqQGsdot8ghrjyrvMCoEA==}
    deprecated: use String.prototype.padStart()

  /leven@3.1.0:
    resolution: {integrity: sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==}
    engines: {node: '>=6'}

  /levn@0.3.0:
    resolution: {integrity: sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2
      type-check: 0.3.2

  /lilconfig@2.0.6:
    resolution: {integrity: sha512-9JROoBW7pobfsx+Sq2JsASvCo6Pfo6WWoUW79HuB1BCoBXD4PLWJPqDF6fNj67pqBYTbAHkE57M1kS/+L1neOg==}
    engines: {node: '>=10'}

  /lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  /listr-silent-renderer@1.1.1:
    resolution: {integrity: sha512-L26cIFm7/oZeSNVhWB6faeorXhMg4HNlb/dS/7jHhr708jxlXrtrBWo4YUxZQkc6dGoxEAe6J/D3juTRBUzjtA==}
    engines: {node: '>=4'}
    dev: true

  /listr-update-renderer@0.5.0(listr@0.14.3):
    resolution: {integrity: sha512-tKRsZpKz8GSGqoI/+caPmfrypiaq+OQCbd+CovEC24uk1h952lVj5sC7SqyFUm+OaJ5HN/a1YLt5cit2FMNsFA==}
    engines: {node: '>=6'}
    peerDependencies:
      listr: ^0.14.2
    dependencies:
      chalk: 1.1.3
      cli-truncate: 0.2.1
      elegant-spinner: 1.0.1
      figures: 1.7.0
      indent-string: 3.2.0
      listr: 0.14.3
      log-symbols: 1.0.2
      log-update: 2.3.0
      strip-ansi: 3.0.1
    dev: true

  /listr-verbose-renderer@0.5.0:
    resolution: {integrity: sha512-04PDPqSlsqIOaaaGZ+41vq5FejI9auqTInicFRndCBgE3bXG8D6W1I+mWhk+1nqbHmyhla/6BUrd5OSiHwKRXw==}
    engines: {node: '>=4'}
    dependencies:
      chalk: 2.4.2
      cli-cursor: 2.1.0
      date-fns: 1.30.1
      figures: 2.0.0
    dev: true

  /listr@0.14.3:
    resolution: {integrity: sha512-RmAl7su35BFd/xoMamRjpIE4j3v+L28o8CT5YhAXQJm1fD+1l9ngXY8JAQRJ+tFK2i5njvi0iRUKV09vPwA0iA==}
    engines: {node: '>=6'}
    dependencies:
      '@samverschueren/stream-to-observable': 0.3.1(rxjs@6.6.7)
      is-observable: 1.1.0
      is-promise: 2.2.2
      is-stream: 1.1.0
      listr-silent-renderer: 1.1.1
      listr-update-renderer: 0.5.0(listr@0.14.3)
      listr-verbose-renderer: 0.5.0
      p-map: 2.1.0
      rxjs: 6.6.7
    transitivePeerDependencies:
      - zen-observable
      - zenObservable
    dev: true

  /load-json-file@2.0.0:
    resolution: {integrity: sha512-3p6ZOGNbiX4CdvEd1VcE6yi78UrGNpjHO33noGwHCnT/o2fyllJDepsm8+mFFv/DvtwFHht5HIHSyOy5a+ChVQ==}
    engines: {node: '>=4'}
    dependencies:
      graceful-fs: 4.2.10
      parse-json: 2.2.0
      pify: 2.3.0
      strip-bom: 3.0.0
    dev: true

  /load-json-file@4.0.0:
    resolution: {integrity: sha512-Kx8hMakjX03tiGTLAIdJ+lL0htKnXjEZN6hk/tozf/WOuYGdZBJrZ+rCJRbVCugsjB3jMLn9746NsQIf5VjBMw==}
    engines: {node: '>=4'}
    dependencies:
      graceful-fs: 4.2.10
      parse-json: 4.0.0
      pify: 3.0.0
      strip-bom: 3.0.0

  /load-json-file@5.3.0:
    resolution: {integrity: sha512-cJGP40Jc/VXUsp8/OrnyKyTZ1y6v/dphm3bioS+RrKXjK2BB6wHUd6JptZEFDGgGahMT+InnZO5i1Ei9mpC8Bw==}
    engines: {node: '>=6'}
    dependencies:
      graceful-fs: 4.2.10
      parse-json: 4.0.0
      pify: 4.0.1
      strip-bom: 3.0.0
      type-fest: 0.3.1
    dev: true

  /loader-runner@2.4.0:
    resolution: {integrity: sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw==}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}

  /loader-runner@4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}

  /loader-utils@1.4.2:
    resolution: {integrity: sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==}
    engines: {node: '>=4.0.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 1.0.1

  /loader-utils@2.0.4:
    resolution: {integrity: sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==}
    engines: {node: '>=8.9.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.1

  /locate-path@2.0.0:
    resolution: {integrity: sha512-NCI2kiDkyR7VeEKm27Kda/iQHyKJe1Bu0FlTbYp3CqJu+9IFe9bLyAjMxf5ZDDbEg+iMPzB5zYyUTSm8wVTKmA==}
    engines: {node: '>=4'}
    dependencies:
      p-locate: 2.0.0
      path-exists: 3.0.0
    dev: true

  /locate-path@3.0.0:
    resolution: {integrity: sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==}
    engines: {node: '>=6'}
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0

  /locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}
    dependencies:
      p-locate: 4.1.0

  /lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}
    dev: true

  /lodash.defaultsdeep@4.6.1:
    resolution: {integrity: sha512-3j8wdDzYuWO3lM3Reg03MuQR957t287Rpcxp1njpEa8oDrikb+FwGdW3n+FELh/A6qib6yPit0j/pv9G/yeAqA==}

  /lodash.mapvalues@4.6.0:
    resolution: {integrity: sha512-JPFqXFeZQ7BfS00H58kClY7SPVeHertPE0lNuCyZ26/XlN8TvakYD7b9bGyNmXbT/D3BbtPAAmq90gPWqLkxlQ==}

  /lodash.memoize@4.1.2:
    resolution: {integrity: sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==}

  /lodash.once@4.1.1:
    resolution: {integrity: sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg==}
    dev: true

  /lodash.sortby@4.7.0:
    resolution: {integrity: sha512-HDWXG8isMntAyRF5vZ7xKuEvOhT4AhlRt/3czTSjvGUxjYCBVRQY48ViDHyfYz9VIoBkW4TMGQNapx+l3RUwdA==}

  /lodash.uniq@4.5.0:
    resolution: {integrity: sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==}

  /lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  /log-symbols@1.0.2:
    resolution: {integrity: sha512-mmPrW0Fh2fxOzdBbFv4g1m6pR72haFLPJ2G5SJEELf1y+iaQrDG6cWCPjy54RHYbZAt7X+ls690Kw62AdWXBzQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      chalk: 1.1.3
    dev: true

  /log-symbols@2.2.0:
    resolution: {integrity: sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg==}
    engines: {node: '>=4'}
    dependencies:
      chalk: 2.4.2
    dev: true

  /log-symbols@4.1.0:
    resolution: {integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==}
    engines: {node: '>=10'}
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0

  /log-update@2.3.0:
    resolution: {integrity: sha512-vlP11XfFGyeNQlmEn9tJ66rEW1coA/79m5z6BCkudjbAGE83uhAcGYrBFwfs3AdLiLzGRusRPAbSPK9xZteCmg==}
    engines: {node: '>=4'}
    dependencies:
      ansi-escapes: 3.2.0
      cli-cursor: 2.1.0
      wrap-ansi: 3.0.1

  /loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0

  /lower-case@2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==}
    dependencies:
      tslib: 2.4.1

  /lru-cache@4.1.5:
    resolution: {integrity: sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==}
    dependencies:
      pseudomap: 1.0.2
      yallist: 2.1.2

  /lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}
    dependencies:
      yallist: 3.1.1

  /lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0

  /make-dir@2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}
    dependencies:
      pify: 4.0.1
      semver: 5.7.1

  /make-dir@3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}
    dependencies:
      semver: 6.3.0
    dev: true

  /make-error@1.3.6:
    resolution: {integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==}
    dev: true

  /makeerror@1.0.12:
    resolution: {integrity: sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==}
    dependencies:
      tmpl: 1.0.5

  /map-cache@0.2.2:
    resolution: {integrity: sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg==}
    engines: {node: '>=0.10.0'}

  /map-visit@1.0.0:
    resolution: {integrity: sha512-4y7uGv8bd2WdM9vpQsiQNo41Ln1NvhvDRuVt0k2JZQ+ezN2uaQes7lZeZ+QQUHOLQAtDaBJ+7wCbi+ab/KFs+w==}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-visit: 1.0.1

  /marker-clusterer-plus@2.1.4:
    resolution: {integrity: sha512-4WLZnYCkgsUfSC0pftldd0YrLNupSqVIEdxL979f3sXVMBHTUOF3gDa6cEuOk2z8UGyVGcANiNZgvVc333mrHA==}
    dev: false

  /md5.js@1.3.5:
    resolution: {integrity: sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg==}
    dependencies:
      hash-base: 3.1.0
      inherits: 2.0.4
      safe-buffer: 5.2.1

  /mdn-data@2.0.14:
    resolution: {integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==}

  /mdn-data@2.0.4:
    resolution: {integrity: sha512-iV3XNKw06j5Q7mi6h+9vbx23Tv7JkjEVgKHW4pimwyDGWm0OIQntJJ+u1C6mg6mK1EaTv42XQ7w76yuzH7M2cA==}
    dev: true

  /media-typer@0.3.0:
    resolution: {integrity: sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=}
    engines: {node: '>= 0.6'}

  /memfs@3.4.10:
    resolution: {integrity: sha512-0bCUP+L79P4am30yP1msPzApwuMQG23TjwlwdHeEV5MxioDR1a0AgB0T9FfggU52eJuDCq8WVwb5ekznFyWiTQ==}
    engines: {node: '>= 4.0.0'}
    dependencies:
      fs-monkey: 1.0.3

  /memory-fs@0.4.1:
    resolution: {integrity: sha512-cda4JKCxReDXFXRqOHPQscuIYg1PvxbE2S2GP45rnwfEK+vZaXC8C1OFvdHIbgw0DLzowXGVoxLaAmlgRy14GQ==}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.7

  /memory-fs@0.5.0:
    resolution: {integrity: sha512-jA0rdU5KoQMC0e6ppoNRtpp6vjFq6+NY7r8hywnC7V+1Xj/MtHwGIbB1QaK/dunyjWteJzmkpd7ooeWg10T7GA==}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.7

  /merge-descriptors@1.0.1:
    resolution: {integrity: sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=}

  /merge-source-map@1.1.0:
    resolution: {integrity: sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw==}
    dependencies:
      source-map: 0.6.1

  /merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  /merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  /methods@1.1.2:
    resolution: {integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==}
    engines: {node: '>= 0.6'}

  /micromatch@3.1.10:
    resolution: {integrity: sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      extglob: 2.0.4
      fragment-cache: 0.2.1
      kind-of: 6.0.3
      nanomatch: 1.2.13
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  /micromatch@4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1

  /miller-rabin@4.0.1:
    resolution: {integrity: sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA==}
    hasBin: true
    dependencies:
      bn.js: 4.12.0
      brorand: 1.1.0

  /mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  /mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0

  /mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  /mimic-fn@1.2.0:
    resolution: {integrity: sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==}
    engines: {node: '>=4'}

  /mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}

  /mimic-response@2.1.0:
    resolution: {integrity: sha512-wXqjST+SLt7R009ySCglWBCFpjUygmCIfD790/kVbiGmUgfYGuB14PiTd5DwVxSV4NcYHjzMkoj5LjQZwTQLEA==}
    engines: {node: '>=8'}
    dev: true
    optional: true

  /mini-css-extract-plugin@0.9.0(webpack@4.46.0):
    resolution: {integrity: sha512-lp3GeY7ygcgAmVIcRPBVhIkf8Us7FZjA+ILpal44qLdSu11wmjKQ3d9k15lfD7pO4esu9eUIAW7qiYIBppv40A==}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.4.0
    dependencies:
      loader-utils: 1.4.2
      normalize-url: 1.9.1
      schema-utils: 1.0.0
      webpack: 4.46.0(webpack-cli@4.10.0)
      webpack-sources: 1.4.3
    dev: true

  /mini-css-extract-plugin@2.6.1(webpack@5.75.0):
    resolution: {integrity: sha512-wd+SD57/K6DiV7jIR34P+s3uckTRuQvx0tKPcvjFlrEylk6P4mQ2KSWk1hblj1Kxaqok7LogKOieygXqBczNlg==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0
    dependencies:
      schema-utils: 4.0.0
      webpack: 5.75.0(webpack-cli@4.10.0)

  /minimalistic-assert@1.0.1:
    resolution: {integrity: sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==}

  /minimalistic-crypto-utils@1.0.1:
    resolution: {integrity: sha512-JIYlbt6g8i5jKfJ3xz7rF0LXmv2TkDxBLUkiBeZ7bAx4GnnNMr8xFpGnOxn6GhTEHx3SjRrZEoU+j04prX1ktg==}

  /minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.11

  /minimatch@5.1.0:
    resolution: {integrity: sha512-9TPBGGak4nHfGZsPBohm9AWg6NoT7QTCehS3BIJABslyZbzxfV78QM2Y6+i741OPZIafFAaiiEMh5OyIrJPgtg==}
    engines: {node: '>=10'}
    dependencies:
      brace-expansion: 2.0.1
    dev: true

  /minimist@1.2.7:
    resolution: {integrity: sha512-bzfL1YUZsP41gmu/qjrEk0Q6i2ix/cVeAhbCbqH9u3zYutS1cLg00qhrD0M2MVdCcx4Sc0UpP2eBWo9rotpq6g==}

  /minipass-collect@1.0.2:
    resolution: {integrity: sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.4
    dev: true

  /minipass-flush@1.0.5:
    resolution: {integrity: sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.4
    dev: true

  /minipass-pipeline@1.2.4:
    resolution: {integrity: sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==}
    engines: {node: '>=8'}
    dependencies:
      minipass: 3.3.4
    dev: true

  /minipass@3.3.4:
    resolution: {integrity: sha512-I9WPbWHCGu8W+6k1ZiGpPu0GkoKBeorkfKNuAFBNS1HNFJvke82sxvI5bzcCNpWPorkOO5QQ+zomzzwRxejXiw==}
    engines: {node: '>=8'}
    dependencies:
      yallist: 4.0.0

  /minizlib@2.1.2:
    resolution: {integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.4
      yallist: 4.0.0
    dev: true

  /mississippi@3.0.0:
    resolution: {integrity: sha512-x471SsVjUtBRtcvd4BzKE9kFC+/2TeWgKCgw0bZcw1b9l2X3QX5vCWgF+KaZaYm87Ss//rHnWryupDrgLvmSkA==}
    engines: {node: '>=4.0.0'}
    dependencies:
      concat-stream: 1.6.2
      duplexify: 3.7.1
      end-of-stream: 1.4.4
      flush-write-stream: 1.1.1
      from2: 2.3.0
      parallel-transform: 1.2.0
      pump: 3.0.0
      pumpify: 1.5.1
      stream-each: 1.2.3
      through2: 2.0.5

  /mixin-deep@1.3.2:
    resolution: {integrity: sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-in: 1.0.2
      is-extendable: 1.0.1

  /mkdirp-classic@0.5.3:
    resolution: {integrity: sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A==}
    dev: true
    optional: true

  /mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true
    dependencies:
      minimist: 1.2.7

  /mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  /module-alias@2.2.2:
    resolution: {integrity: sha512-A/78XjoX2EmNvppVWEhM2oGk3x4lLxnkEA4jTbaK97QKSDjkIoOsKQlfylt/d3kKKi596Qy3NP5XrXJ6fZIC9Q==}

  /moment-timezone@0.5.38:
    resolution: {integrity: sha512-nMIrzGah4+oYZPflDvLZUgoVUO4fvAqHstvG3xAUnMolWncuAiLDWNnJZj6EwJGMGfb1ZcuTFE6GI3hNOVWI/Q==}
    dependencies:
      moment: 2.29.4
    dev: false

  /moment@2.29.4:
    resolution: {integrity: sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==}

  /move-concurrently@1.0.1:
    resolution: {integrity: sha512-hdrFxZOycD/g6A6SoI2bB5NA/5NEqD0569+S47WZhPvm46sD50ZHdYaFmnua5lndde9rCHGjmfK7Z8BuCt/PcQ==}
    dependencies:
      aproba: 1.2.0
      copy-concurrently: 1.0.5
      fs-write-stream-atomic: 1.0.10
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3

  /mrmime@1.0.1:
    resolution: {integrity: sha512-hzzEagAgDyoU1Q6yg5uI+AorQgdvMCur3FcKf7NhMKWsaYg+RnbTyHRa/9IlLF9rf455MOCtcqqrQQ83pPP7Uw==}
    engines: {node: '>=10'}

  /ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  /ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}

  /ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  /multicast-dns@7.2.5:
    resolution: {integrity: sha512-2eznPJP8z2BFLX50tf0LuODrpINqP1RVIm/CObbTcBRITQgmC/TjcREF1NeTBzIcR5XO/ukWo+YHOjBbFwIupg==}
    hasBin: true
    dependencies:
      dns-packet: 5.4.0
      thunky: 1.1.0

  /mute-stream@0.0.8:
    resolution: {integrity: sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==}

  /mz@2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  /nan@2.17.0:
    resolution: {integrity: sha512-2ZTgtl0nJsO0KQCjEpxcIr5D+Yv90plTitZt9JBfQvVJDS5seMl3FOvsh3+9CoYWXf/1l5OaZzzF6nDm4cagaQ==}
    optional: true

  /nanoid@3.3.4:
    resolution: {integrity: sha512-MqBkQh/OHTS2egovRtLk45wEyNXwF+cokD+1YPf9u5VfJiRdAiRwB2froX5Co9Rh20xs4siNPm8naNotSD6RBw==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  /nanomatch@1.2.13:
    resolution: {integrity: sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      fragment-cache: 0.2.1
      is-windows: 1.0.2
      kind-of: 6.0.3
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  /napi-build-utils@1.0.2:
    resolution: {integrity: sha512-ONmRUqK7zj7DWX0D9ADe03wbwOBZxNAfF20PlGfCWQcD3+/MakShIHrMqx9YwPTfxDdF1zLeL+RGZiR9kGMLdg==}
    dev: true
    optional: true

  /natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  /negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  /neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}

  /nice-try@1.0.5:
    resolution: {integrity: sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==}

  /no-case@3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==}
    dependencies:
      lower-case: 2.0.2
      tslib: 2.4.1

  /node-abi@2.30.1:
    resolution: {integrity: sha512-/2D0wOQPgaUWzVSVgRMx+trKJRC2UG4SUc4oCJoXx9Uxjtp0Vy3/kt7zcbxHF8+Z/pK3UloLWzBISg72brfy1w==}
    dependencies:
      semver: 5.7.1
    dev: true
    optional: true

  /node-addon-api@1.7.2:
    resolution: {integrity: sha512-ibPK3iA+vaY1eEjESkQkM0BbCqFOaZMiXRTtdB0u7b4djtY6JnsjvPdUHVMg6xQt3B8fpTTWHI9A+ADjM9frzg==}
    dev: true

  /node-cache@4.2.1:
    resolution: {integrity: sha512-BOb67bWg2dTyax5kdef5WfU3X8xu4wPg+zHzkvls0Q/QpYycIFRLEEIdAx9Wma43DxG6Qzn4illdZoYseKWa4A==}
    engines: {node: '>= 0.4.6'}
    dependencies:
      clone: 2.1.2
      lodash: 4.17.21
    dev: true

  /node-fetch@2.6.7:
    resolution: {integrity: sha512-ZjMPFEfVx5j+y2yF35Kzx5sF7kDzxuDj6ziH4FFbOp87zKDZNx8yExJIb05OGF4Nlt9IHFIMBkRl41VdvcNdbQ==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true
    dependencies:
      whatwg-url: 5.0.0

  /node-forge@1.3.1:
    resolution: {integrity: sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==}
    engines: {node: '>= 6.13.0'}

  /node-int64@0.4.0:
    resolution: {integrity: sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==}

  /node-libs-browser@2.2.1:
    resolution: {integrity: sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q==}
    dependencies:
      assert: 1.5.0
      browserify-zlib: 0.2.0
      buffer: 4.9.2
      console-browserify: 1.2.0
      constants-browserify: 1.0.0
      crypto-browserify: 3.12.0
      domain-browser: 1.2.0
      events: 3.3.0
      https-browserify: 1.0.0
      os-browserify: 0.3.0
      path-browserify: 0.0.1
      process: 0.11.10
      punycode: 1.4.1
      querystring-es3: 0.2.1
      readable-stream: 2.3.7
      stream-browserify: 2.0.2
      stream-http: 2.8.3
      string_decoder: 1.3.0
      timers-browserify: 2.0.12
      tty-browserify: 0.0.0
      url: 0.11.0
      util: 0.11.1
      vm-browserify: 1.1.2

  /node-notifier@5.4.5:
    resolution: {integrity: sha512-tVbHs7DyTLtzOiN78izLA85zRqB9NvEXkAf014Vx3jtSvn/xBl6bR8ZYifj+dFcFrKI21huSQgJZ6ZtL3B4HfQ==}
    dependencies:
      growly: 1.3.0
      is-wsl: 1.1.0
      semver: 5.7.1
      shellwords: 0.1.1
      which: 1.3.1

  /node-releases@2.0.6:
    resolution: {integrity: sha512-PiVXnNuFm5+iYkLBNeq5211hvO38y63T0i2KKh2KnUs3RpzJ+JtODFjkD8yjLwnDkTYF1eKXheUwdssR+NRZdg==}

  /noop-logger@0.1.1:
    resolution: {integrity: sha512-6kM8CLXvuW5crTxsAtva2YLrRrDaiTIkIePWs9moLHqbFWT94WpNFjwS/5dfLfECg5i/lkmw3aoqVidxt23TEQ==}
    dev: true
    optional: true

  /nopt@6.0.0:
    resolution: {integrity: sha512-ZwLpbTgdhuZUnZzjd7nb1ZV+4DoiC6/sfiVKok72ym/4Tlf+DFdlHYmT2JPmcNNWV6Pi3SDf1kT+A4r9RTuT9g==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    hasBin: true
    dependencies:
      abbrev: 1.1.1
    dev: true

  /normalize-package-data@2.5.0:
    resolution: {integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==}
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.1
      semver: 5.7.1
      validate-npm-package-license: 3.0.4

  /normalize-path@2.1.1:
    resolution: {integrity: sha512-3pKJwH184Xo/lnH6oyP1q2pMd7HcypqqmRs91/6/i2CGtWwIKGCkOOMTm/zXbgTEWHw1uNpNi/igc3ePOYHb6w==}
    engines: {node: '>=0.10.0'}
    dependencies:
      remove-trailing-separator: 1.1.0

  /normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  /normalize-range@0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==}
    engines: {node: '>=0.10.0'}

  /normalize-url@1.9.1:
    resolution: {integrity: sha512-A48My/mtCklowHBlI8Fq2jFWK4tX4lJ5E6ytFsSOq1fzpvT0SQSgKhSg7lN5c2uYFOrUAOQp6zhhJnpp1eMloQ==}
    engines: {node: '>=4'}
    dependencies:
      object-assign: 4.1.1
      prepend-http: 1.0.4
      query-string: 4.3.4
      sort-keys: 1.1.2
    dev: true

  /normalize-url@3.3.0:
    resolution: {integrity: sha512-U+JJi7duF1o+u2pynbp2zXDW2/PADgC30f0GsHZtRh+HOcXHnw137TrNlyxxRvWW5fjKd3bcLHPxofWuCjaeZg==}
    engines: {node: '>=6'}
    dev: true

  /normalize-url@6.1.0:
    resolution: {integrity: sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==}
    engines: {node: '>=10'}

  /npm-run-path@2.0.2:
    resolution: {integrity: sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw==}
    engines: {node: '>=4'}
    dependencies:
      path-key: 2.0.1

  /npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1

  /npmlog@4.1.2:
    resolution: {integrity: sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==}
    dependencies:
      are-we-there-yet: 1.1.7
      console-control-strings: 1.1.0
      gauge: 2.7.4
      set-blocking: 2.0.0
    dev: true
    optional: true

  /nth-check@1.0.2:
    resolution: {integrity: sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg==}
    dependencies:
      boolbase: 1.0.0
    dev: true

  /nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}
    dependencies:
      boolbase: 1.0.0

  /num2fraction@1.2.2:
    resolution: {integrity: sha512-Y1wZESM7VUThYY+4W+X4ySH2maqcA+p7UR+w8VWNWVAd6lwuXXWz/w/Cz43J/dI2I+PS6wD5N+bJUF+gjWvIqg==}
    dev: true

  /number-is-nan@1.0.1:
    resolution: {integrity: sha512-4jbtZXNAsfZbAHiiqjLPBiCl16dES1zI4Hpzzxw61Tk+loF+sBDBKx1ICKKKwIqQ7M0mFn1TmkN7euSncWgHiQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /numeral@2.0.6:
    resolution: {integrity: sha512-qaKRmtYPZ5qdw4jWJD6bxEf1FJEqllJrwxCLIm0sQU/A7v2/czigzOb+C2uSiFsa9lBUzeH7M1oK+Q+OLxL3kA==}
    dev: true

  /nwsapi@2.2.2:
    resolution: {integrity: sha512-90yv+6538zuvUMnN+zCr8LuV6bPFdq50304114vJYJ8RDyK8D5O9Phpbd6SZWgI7PwzmmfN1upeOJlvybDSgCw==}

  /oauth-sign@0.9.0:
    resolution: {integrity: sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==}

  /object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  /object-copy@0.1.0:
    resolution: {integrity: sha512-79LYn6VAb63zgtmAteVOWo9Vdj71ZVBy3Pbse+VqxDpEP83XuujMrGqHIwAXJ5I/aM0zU7dIyIAhifVTPrNItQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      copy-descriptor: 0.1.1
      define-property: 0.2.5
      kind-of: 3.2.2

  /object-inspect@1.12.2:
    resolution: {integrity: sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ==}

  /object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  /object-visit@1.0.1:
    resolution: {integrity: sha512-GBaMwwAVK9qbQN3Scdo0OyvgPW7l3lnaVMj84uTOZlswkX0KpF6fyDBJhtTthf7pymztoN36/KEr1DyhF96zEA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1

  /object.assign@4.1.4:
    resolution: {integrity: sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      has-symbols: 1.0.3
      object-keys: 1.1.1

  /object.entries@1.1.6:
    resolution: {integrity: sha512-leTPzo4Zvg3pmbQ3rDK69Rl8GQvIqMWubrkxONG9/ojtFE2rD9fjMKfSI5BxW3osRH1m6VdzmqK8oAY9aT4x5w==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
    dev: true

  /object.fromentries@2.0.6:
    resolution: {integrity: sha512-VciD13dswC4j1Xt5394WR4MzmAQmlgN72phd/riNp9vtD7tp4QQWJ0R4wvclXcafgcYK8veHRed2W6XeGBvcfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
    dev: true

  /object.getownpropertydescriptors@2.1.5:
    resolution: {integrity: sha512-yDNzckpM6ntyQiGTik1fKV1DcVDRS+w8bvpWNCBanvH5LfRX9O8WTHqQzG4RZwRAM4I0oU7TV11Lj5v0g20ibw==}
    engines: {node: '>= 0.8'}
    dependencies:
      array.prototype.reduce: 1.0.5
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4

  /object.pick@1.3.0:
    resolution: {integrity: sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1

  /object.values@1.1.6:
    resolution: {integrity: sha512-FVVTkD1vENCsAcwNs9k6jea2uHC/X0+JcjG8YA60FN5CMaJmG95wT9jek/xX9nornqGRrBkKtzuAu2wuHpKqvw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4
    dev: true

  /obuf@1.1.2:
    resolution: {integrity: sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==}

  /on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: 1.1.1

  /on-headers@1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==}
    engines: {node: '>= 0.8'}

  /once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}
    dependencies:
      wrappy: 1.0.2

  /onetime@1.1.0:
    resolution: {integrity: sha512-GZ+g4jayMqzCRMgB2sol7GiCLjKfS1PINkjmx8spcKce1LiVqcbQreXwqs2YAFXC6R03VIG28ZS31t8M866v6A==}
    engines: {node: '>=0.10.0'}
    dev: true

  /onetime@2.0.1:
    resolution: {integrity: sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ==}
    engines: {node: '>=4'}
    dependencies:
      mimic-fn: 1.2.0

  /onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0

  /open@6.4.0:
    resolution: {integrity: sha512-IFenVPgF70fSm1keSd2iDBIDIBZkroLeuffXq+wKTzTJlBpesFWojV9lb8mzOfaAzM1sr7HQHuO0vtV0zYekGg==}
    engines: {node: '>=8'}
    dependencies:
      is-wsl: 1.1.0
    dev: true

  /open@8.4.0:
    resolution: {integrity: sha512-XgFPPM+B28FtCCgSb9I+s9szOC1vZRSwgWsRUA5ylIxRTgKozqjOCrVOqGsYABPYK5qnfqClxZTFBa8PKt2v6Q==}
    engines: {node: '>=12'}
    dependencies:
      define-lazy-prop: 2.0.0
      is-docker: 2.2.1
      is-wsl: 2.2.0

  /opener@1.5.2:
    resolution: {integrity: sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==}
    hasBin: true

  /optionator@0.8.3:
    resolution: {integrity: sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.3.0
      prelude-ls: 1.1.2
      type-check: 0.3.2
      word-wrap: 1.2.3

  /ora@3.4.0:
    resolution: {integrity: sha512-eNwHudNbO1folBP3JsZ19v9azXWtQZjICdr3Q0TDPIaeBQ3mXLrh54wM+er0+hSp+dWKf+Z8KM58CYzEyIYxYg==}
    engines: {node: '>=6'}
    dependencies:
      chalk: 2.4.2
      cli-cursor: 2.1.0
      cli-spinners: 2.7.0
      log-symbols: 2.2.0
      strip-ansi: 5.2.0
      wcwidth: 1.0.1
    dev: true

  /ora@5.4.1:
    resolution: {integrity: sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==}
    engines: {node: '>=10'}
    dependencies:
      bl: 4.1.0
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-spinners: 2.7.0
      is-interactive: 1.0.0
      is-unicode-supported: 0.1.0
      log-symbols: 4.1.0
      strip-ansi: 6.0.1
      wcwidth: 1.0.1

  /os-browserify@0.3.0:
    resolution: {integrity: sha512-gjcpUc3clBf9+210TRaDWbf+rZZZEshZ+DlXMRCeAjp0xhTrnQsKHypIy1J3d5hKdUzj69t708EHtU8P6bUn0A==}

  /os-tmpdir@1.0.2:
    resolution: {integrity: sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==}
    engines: {node: '>=0.10.0'}

  /ospath@1.2.2:
    resolution: {integrity: sha512-o6E5qJV5zkAbIDNhGSIlyOhScKXgQrSRMilfph0clDfM0nEnBOlKlH4sWDmG95BW/CvwNz0vmm7dJVtU2KlMiA==}
    dev: true

  /p-each-series@1.0.0:
    resolution: {integrity: sha512-J/e9xiZZQNrt+958FFzJ+auItsBGq+UrQ7nE89AUP7UOTtjHnkISANXLdayhVzh538UnLMCSlf13lFfRIAKQOA==}
    engines: {node: '>=4'}
    dependencies:
      p-reduce: 1.0.0

  /p-finally@1.0.0:
    resolution: {integrity: sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==}
    engines: {node: '>=4'}

  /p-limit@1.3.0:
    resolution: {integrity: sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==}
    engines: {node: '>=4'}
    dependencies:
      p-try: 1.0.0
    dev: true

  /p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}
    dependencies:
      p-try: 2.2.0

  /p-locate@2.0.0:
    resolution: {integrity: sha512-nQja7m7gSKuewoVRen45CtVfODR3crN3goVQ0DDZ9N3yHxgpkuBhZqsaiotSQRrADUrne346peY7kT3TSACykg==}
    engines: {node: '>=4'}
    dependencies:
      p-limit: 1.3.0
    dev: true

  /p-locate@3.0.0:
    resolution: {integrity: sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==}
    engines: {node: '>=6'}
    dependencies:
      p-limit: 2.3.0

  /p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}
    dependencies:
      p-limit: 2.3.0

  /p-map@2.1.0:
    resolution: {integrity: sha512-y3b8Kpd8OAN444hxfBbFfj1FY/RjtTd8tzYwhUqNYXx0fXx2iX4maP4Qr6qhIKbQXI02wTLAda4fYUbDagTUFw==}
    engines: {node: '>=6'}
    dev: true

  /p-map@4.0.0:
    resolution: {integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==}
    engines: {node: '>=10'}
    dependencies:
      aggregate-error: 3.1.0
    dev: true

  /p-reduce@1.0.0:
    resolution: {integrity: sha512-3Tx1T3oM1xO/Y8Gj0sWyE78EIJZ+t+aEmXUdvQgvGmSMri7aPTHoovbXEreWKkL5j21Er60XAWLTzKbAKYOujQ==}
    engines: {node: '>=4'}

  /p-retry@4.6.2:
    resolution: {integrity: sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==}
    engines: {node: '>=8'}
    dependencies:
      '@types/retry': 0.12.0
      retry: 0.13.1

  /p-try@1.0.0:
    resolution: {integrity: sha512-U1etNYuMJoIz3ZXSrrySFjsXQTWOx2/jdi86L+2pRvph/qMKL6sbcCYdH23fqsbm8TH2Gn0OybpT4eSFlCVHww==}
    engines: {node: '>=4'}
    dev: true

  /p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  /pako@1.0.11:
    resolution: {integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==}

  /parallel-transform@1.2.0:
    resolution: {integrity: sha512-P2vSmIu38uIlvdcU7fDkyrxj33gTUy/ABO5ZUbGowxNCopBq/OoD42bP4UmMrJoPyk4Uqf0mu3mtWBhHCZD8yg==}
    dependencies:
      cyclist: 1.0.1
      inherits: 2.0.4
      readable-stream: 2.3.7

  /param-case@3.0.4:
    resolution: {integrity: sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==}
    dependencies:
      dot-case: 3.0.4
      tslib: 2.4.1

  /parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0

  /parse-asn1@5.1.6:
    resolution: {integrity: sha512-RnZRo1EPU6JBnra2vGHj0yhp6ebyjBZpmUCLHWiFhxlzvBCCpAuZ7elsBp1PVAbQN0/04VD/19rfzlBSwLstMw==}
    dependencies:
      asn1.js: 5.4.1
      browserify-aes: 1.2.0
      evp_bytestokey: 1.0.3
      pbkdf2: 3.1.2
      safe-buffer: 5.2.1

  /parse-json@2.2.0:
    resolution: {integrity: sha512-QR/GGaKCkhwk1ePQNYDRKYZ3mwU9ypsKhB0XyFnLQdomyEqk3e8wpW3V5Jp88zbxK4n5ST1nqo+g9juTpownhQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      error-ex: 1.3.2
    dev: true

  /parse-json@4.0.0:
    resolution: {integrity: sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw==}
    engines: {node: '>=4'}
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2

  /parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.18.6
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  /parse5-htmlparser2-tree-adapter@6.0.1:
    resolution: {integrity: sha512-qPuWvbLgvDGilKc5BoicRovlT4MtYT6JfJyBOMDsKoiT+GiuP5qyrPCnR9HcPECIJJmZh5jRndyNThnhhb/vlA==}
    dependencies:
      parse5: 6.0.1

  /parse5@4.0.0:
    resolution: {integrity: sha512-VrZ7eOd3T1Fk4XWNXMgiGBK/z0MG48BWG2uQNU4I72fkQuKUTZpl+u9k+CxEG0twMVzSmXEEz12z5Fnw1jIQFA==}

  /parse5@5.1.0:
    resolution: {integrity: sha512-fxNG2sQjHvlVAYmzBZS9YlDp6PTSSDwa98vkD4QgVDDCAo84z5X1t5XyJQ62ImdLXx5NdIIfihey6xpum9/gRQ==}
    dev: true

  /parse5@5.1.1:
    resolution: {integrity: sha512-ugq4DFI0Ptb+WWjAdOK16+u/nHfiIrcE+sh8kZMaM0WllQKLI9rOUq6c2b7cwPkXdzfQESqvoqK6ug7U/Yyzug==}

  /parse5@6.0.1:
    resolution: {integrity: sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==}

  /parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}

  /pascal-case@3.1.2:
    resolution: {integrity: sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.4.1

  /pascalcase@0.1.1:
    resolution: {integrity: sha512-XHXfu/yOQRy9vYOtUDVMN60OEJjW013GoObG1o+xwQTpB9eYJX/BjXMsdW13ZDPruFhYYn0AG22w0xgQMwl3Nw==}
    engines: {node: '>=0.10.0'}

  /path-browserify@0.0.1:
    resolution: {integrity: sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ==}

  /path-dirname@1.0.2:
    resolution: {integrity: sha512-ALzNPpyNq9AqXMBjeymIjFDAkAFH06mHJH/cSBHAgU0s4vfpBn6b2nf8tiRLvagKD8RbTpq2FKTBg7cl9l3c7Q==}
    optional: true

  /path-exists@3.0.0:
    resolution: {integrity: sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==}
    engines: {node: '>=4'}

  /path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  /path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  /path-key@2.0.1:
    resolution: {integrity: sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==}
    engines: {node: '>=4'}

  /path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  /path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  /path-to-regexp@0.1.7:
    resolution: {integrity: sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==}

  /path-to-regexp@6.2.1:
    resolution: {integrity: sha512-JLyh7xT1kizaEvcaXOQwOc2/Yhw6KZOvPf1S8401UyLk86CU79LN3vl7ztXGm/pZ+YjoyAJ4rxmHwbkBXJX+yw==}
    dev: false

  /path-type@2.0.0:
    resolution: {integrity: sha512-dUnb5dXUf+kzhC/W/F4e5/SkluXIFf5VUHolW1Eg1irn1hGWjPGdsRcvYJ1nD6lhk8Ir7VM0bHJKsYTx8Jx9OQ==}
    engines: {node: '>=4'}
    dependencies:
      pify: 2.3.0
    dev: true

  /path-type@3.0.0:
    resolution: {integrity: sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==}
    engines: {node: '>=4'}
    dependencies:
      pify: 3.0.0

  /path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  /pbkdf2@3.1.2:
    resolution: {integrity: sha512-iuh7L6jA7JEGu2WxDwtQP1ddOpaJNC4KlDEFfdQajSGgGPNi4OyDc2R7QnbY2bR9QjBVGwgvTdNJZoE7RaxUMA==}
    engines: {node: '>=0.12'}
    dependencies:
      create-hash: 1.2.0
      create-hmac: 1.1.7
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11

  /pend@1.2.0:
    resolution: {integrity: sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==}
    dev: true

  /performance-now@2.1.0:
    resolution: {integrity: sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==}

  /picocolors@0.2.1:
    resolution: {integrity: sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==}

  /picocolors@1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}

  /picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  /pify@2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}
    dev: true

  /pify@3.0.0:
    resolution: {integrity: sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==}
    engines: {node: '>=4'}

  /pify@4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}

  /pirates@4.0.5:
    resolution: {integrity: sha512-8V9+HQPupnaXMA23c5hvl69zXvTwTzyAYasnkb0Tts4XvO4CliqONMOnvlq26rkhLC3nWDFBJf73LU1e1VZLaQ==}
    engines: {node: '>= 6'}

  /pkg-conf@3.1.0:
    resolution: {integrity: sha512-m0OTbR/5VPNPqO1ph6Fqbj7Hv6QU7gR/tQW40ZqrL1rjgCU85W6C1bJn0BItuJqnR98PWzw7Z8hHeChD1WrgdQ==}
    engines: {node: '>=6'}
    dependencies:
      find-up: 3.0.0
      load-json-file: 5.3.0
    dev: true

  /pkg-config@1.1.1:
    resolution: {integrity: sha512-ft/WI9YK6FuTuw4Ql+QUaNXtm/ASQNqDUUsZEgFZKyFpW6amyP8Gx01xrRs8KdiNbbqXfYxkOXplpq1euWbOjw==}
    engines: {node: '>=0.10'}
    dependencies:
      debug-log: 1.0.1
      find-root: 1.1.0
      xtend: 4.0.2
    dev: true

  /pkg-dir@3.0.0:
    resolution: {integrity: sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==}
    engines: {node: '>=6'}
    dependencies:
      find-up: 3.0.0

  /pkg-dir@4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0

  /pn@1.1.0:
    resolution: {integrity: sha512-2qHaIQr2VLRFoxe2nASzsV6ef4yOOH+Fi9FBOVH6cqeSgUnoyySPZkxzLuzd+RYOQTRpROA0ztTMqxROKSb/nA==}

  /popper.js@1.16.1:
    resolution: {integrity: sha512-Wb4p1J4zyFTbM+u6WuO4XstYx4Ky9Cewe4DWrel7B0w6VVICvPwdOpotjzcf6eD8TsckVnIMNONQyPIUFOUbCQ==}
    deprecated: You can find the new Popper v2 at @popperjs/core, this package is dedicated to the legacy v1
    dev: false

  /portal-vue@2.1.7(vue@2.7.14):
    resolution: {integrity: sha512-+yCno2oB3xA7irTt0EU5Ezw22L2J51uKAacE/6hMPMoO/mx3h4rXFkkBkT4GFsMDv/vEe8TNKC3ujJJ0PTwb6g==}
    peerDependencies:
      vue: ^2.5.18
    dependencies:
      vue: 2.7.14
    dev: false

  /portfinder@1.0.32:
    resolution: {integrity: sha512-on2ZJVVDXRADWE6jnQaX0ioEylzgBpQk8r55NE4wjXW1ZxO+BgDlY6DXwj20i0V8eB4SenDQ00WEaxfiIQPcxg==}
    engines: {node: '>= 0.12.0'}
    dependencies:
      async: 2.6.4
      debug: 3.2.7(supports-color@7.2.0)
      mkdirp: 0.5.6
    transitivePeerDependencies:
      - supports-color

  /posix-character-classes@0.1.1:
    resolution: {integrity: sha512-xTgYBc3fuo7Yt7JbiuFxSYGToMoz8fLoE6TC9Wx1P/u+LfeThMOAqmuyECnlBaaJb+u1m9hHiXUEtwW4OzfUJg==}
    engines: {node: '>=0.10.0'}

  /postcss-calc@7.0.5:
    resolution: {integrity: sha512-1tKHutbGtLtEZF6PT4JSihCHfIVldU72mZ8SdZHIYriIZ9fh9k9aWSppaT8rHsyI3dX+KSR+W+Ix9BMY3AODrg==}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 6.0.10
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-calc@8.2.4(postcss@8.4.19):
    resolution: {integrity: sha512-SmWMSJmB8MRnnULldx0lQIyhSNvuDl9HfrZkaqqE/WHAhToYsAvDq+yAsA/kIyINDszOp3Rh0GFoNuH5Ypsm3Q==}
    peerDependencies:
      postcss: ^8.2.2
    dependencies:
      postcss: 8.4.19
      postcss-selector-parser: 6.0.10
      postcss-value-parser: 4.2.0

  /postcss-clean@1.2.2:
    resolution: {integrity: sha512-DpuMWW19Dd2K9KY4wknMz3khq9q2yZYa2U37bnhzdtBdBv0ggIfUj5T2XD3ir6gKVlDkb5OtOqw1iQJWq6qvpw==}
    engines: {node: '>=4.0.0'}
    dependencies:
      clean-css: 4.2.4
      postcss: 6.0.23
    dev: true

  /postcss-colormin@4.0.3:
    resolution: {integrity: sha512-WyQFAdDZpExQh32j0U0feWisZ0dmOtPl44qYmJKkq9xFWY3p+4qnRzCHeNrkeRhwPHz9bQ3mo0/yVkaply0MNw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.4
      color: 3.2.1
      has: 1.0.3
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-colormin@5.3.0(postcss@8.4.19):
    resolution: {integrity: sha512-WdDO4gOFG2Z8n4P8TWBpshnL3JpmNmJwdnfP2gbk2qBA8PWwOYcmjmI/t3CmMeL72a7Hkd+x/Mg9O2/0rD54Pg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.4
      caniuse-api: 3.0.0
      colord: 2.9.3
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-convert-values@4.0.1:
    resolution: {integrity: sha512-Kisdo1y77KUC0Jmn0OXU/COOJbzM8cImvw1ZFsBgBgMgb1iL23Zs/LXRe3r+EZqM3vGYKdQ2YJVQ5VkJI+zEJQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-convert-values@5.1.3(postcss@8.4.19):
    resolution: {integrity: sha512-82pC1xkJZtcJEfiLw6UXnXVXScgtBrjlO5CBmuDQc+dlb88ZYheFsjTn40+zBVi3DkfF7iezO0nJUPLcJK3pvA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.4
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-discard-comments@4.0.2:
    resolution: {integrity: sha512-RJutN259iuRf3IW7GZyLM5Sw4GLTOH8FmsXBnv8Ab/Tc2k4SR4qbV4DNbyyY4+Sjo362SyDmW2DQ7lBSChrpkg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-discard-comments@5.1.2(postcss@8.4.19):
    resolution: {integrity: sha512-+L8208OVbHVF2UQf1iDmRcbdjJkuBF6IS29yBDSiWUIzpYaAhtNl6JYnYm12FnkeCwQqF5LeklOu6rAqgfBZqQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19

  /postcss-discard-duplicates@4.0.2:
    resolution: {integrity: sha512-ZNQfR1gPNAiXZhgENFfEglF93pciw0WxMkJeVmw8eF+JZBbMD7jp6C67GqJAXVZP2BWbOztKfbsdmMp/k8c6oQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-discard-duplicates@5.1.0(postcss@8.4.19):
    resolution: {integrity: sha512-zmX3IoSI2aoenxHV6C7plngHWWhUOV3sP1T8y2ifzxzbtnuhk1EdPwm0S1bIUNaJ2eNbWeGLEwzw8huPD67aQw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19

  /postcss-discard-empty@4.0.1:
    resolution: {integrity: sha512-B9miTzbznhDjTfjvipfHoqbWKwd0Mj+/fL5s1QOz06wufguil+Xheo4XpOnc4NqKYBCNqqEzgPv2aPBIJLox0w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-discard-empty@5.1.1(postcss@8.4.19):
    resolution: {integrity: sha512-zPz4WljiSuLWsI0ir4Mcnr4qQQ5e1Ukc3i7UfE2XcrwKK2LIPIqE5jxMRxO6GbI3cv//ztXDsXwEWT3BHOGh3A==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19

  /postcss-discard-overridden@4.0.1:
    resolution: {integrity: sha512-IYY2bEDD7g1XM1IDEsUT4//iEYCxAmP5oDSFMVU/JVvT7gh+l4fmjciLqGgwjdWpQIdb0Che2VX00QObS5+cTg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-discard-overridden@5.1.0(postcss@8.4.19):
    resolution: {integrity: sha512-21nOL7RqWR1kasIVdKs8HNqQJhFxLsyRfAnUDm4Fe4t4mCWL9OJiHvlHPjcd8zc5Myu89b/7wZDnOSjFgeWRtw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19

  /postcss-import@12.0.1:
    resolution: {integrity: sha512-3Gti33dmCjyKBgimqGxL3vcV8w9+bsHwO5UrBawp796+jdardbcFl4RP5w/76BwNL7aGzpKstIfF9I+kdE8pTw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      read-cache: 1.0.0
      resolve: 1.22.1
    dev: true

  /postcss-loader@4.3.0(postcss@7.0.39)(webpack@4.46.0):
    resolution: {integrity: sha512-M/dSoIiNDOo8Rk0mUqoj4kpGq91gcxCfb9PoyZVdZ76/AuhxylHDYZblNE8o+EQ9AMSASeMFEKxZf5aU6wlx1Q==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      postcss: ^7.0.0 || ^8.0.1
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      cosmiconfig: 7.0.1
      klona: 2.0.5
      loader-utils: 2.0.4
      postcss: 7.0.39
      schema-utils: 3.1.1
      semver: 7.3.8
      webpack: 4.46.0(webpack-cli@4.10.0)
    dev: true

  /postcss-loader@6.2.1(postcss@8.4.19)(webpack@5.75.0):
    resolution: {integrity: sha512-WbbYpmAaKcux/P66bZ40bpWsBucjx/TTgVVzRZ9yUO8yQfVBlameJ0ZGVaPfH64hNSBh63a+ICP5nqOpBA0w+Q==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      postcss: ^7.0.0 || ^8.0.1
      webpack: ^5.0.0
    dependencies:
      cosmiconfig: 7.0.1
      klona: 2.0.5
      postcss: 8.4.19
      semver: 7.3.8
      webpack: 5.75.0(webpack-cli@4.10.0)

  /postcss-merge-longhand@4.0.11:
    resolution: {integrity: sha512-alx/zmoeXvJjp7L4mxEMjh8lxVlDFX1gqWHzaaQewwMZiVhLo42TEClKaeHbRf6J7j82ZOdTJ808RtN0ZOZwvw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      css-color-names: 0.0.4
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      stylehacks: 4.0.3
    dev: true

  /postcss-merge-longhand@5.1.7(postcss@8.4.19):
    resolution: {integrity: sha512-YCI9gZB+PLNskrK0BB3/2OzPnGhPkBEwmwhfYk1ilBHYVAZB7/tkTHFBAnCrvBBOmeYyMYw3DMjT55SyxMBzjQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19
      postcss-value-parser: 4.2.0
      stylehacks: 5.1.1(postcss@8.4.19)

  /postcss-merge-rules@4.0.3:
    resolution: {integrity: sha512-U7e3r1SbvYzO0Jr3UT/zKBVgYYyhAz0aitvGIYOYK5CPmkNih+WDSsS5tvPrJ8YMQYlEMvsZIiqmn7HdFUaeEQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.4
      caniuse-api: 3.0.0
      cssnano-util-same-parent: 4.0.1
      postcss: 7.0.39
      postcss-selector-parser: 3.1.2
      vendors: 1.0.4
    dev: true

  /postcss-merge-rules@5.1.3(postcss@8.4.19):
    resolution: {integrity: sha512-LbLd7uFC00vpOuMvyZop8+vvhnfRGpp2S+IMQKeuOZZapPRY4SMq5ErjQeHbHsjCUgJkRNrlU+LmxsKIqPKQlA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.4
      caniuse-api: 3.0.0
      cssnano-utils: 3.1.0(postcss@8.4.19)
      postcss: 8.4.19
      postcss-selector-parser: 6.0.10

  /postcss-minify-font-values@4.0.2:
    resolution: {integrity: sha512-j85oO6OnRU9zPf04+PZv1LYIYOprWm6IA6zkXkrJXyRveDEuQggG6tvoy8ir8ZwjLxLuGfNkCZEQG7zan+Hbtg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-minify-font-values@5.1.0(postcss@8.4.19):
    resolution: {integrity: sha512-el3mYTgx13ZAPPirSVsHqFzl+BBBDrXvbySvPGFnQcTI4iNslrPaFq4muTkLZmKlGk4gyFAYUBMH30+HurREyA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-minify-gradients@4.0.2:
    resolution: {integrity: sha512-qKPfwlONdcf/AndP1U8SJ/uzIJtowHlMaSioKzebAXSG4iJthlWC9iSWznQcX4f66gIWX44RSA841HTHj3wK+Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      is-color-stop: 1.1.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-minify-gradients@5.1.1(postcss@8.4.19):
    resolution: {integrity: sha512-VGvXMTpCEo4qHTNSa9A0a3D+dxGFZCYwR6Jokk+/3oB6flu2/PnPXAh2x7x52EkY5xlIHLm+Le8tJxe/7TNhzw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      colord: 2.9.3
      cssnano-utils: 3.1.0(postcss@8.4.19)
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-minify-params@4.0.2:
    resolution: {integrity: sha512-G7eWyzEx0xL4/wiBBJxJOz48zAKV2WG3iZOqVhPet/9geefm/Px5uo1fzlHu+DOjT+m0Mmiz3jkQzVHe6wxAWg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      alphanum-sort: 1.0.2
      browserslist: 4.21.4
      cssnano-util-get-arguments: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      uniqs: 2.0.0
    dev: true

  /postcss-minify-params@5.1.4(postcss@8.4.19):
    resolution: {integrity: sha512-+mePA3MgdmVmv6g+30rn57USjOGSAyuxUmkfiWpzalZ8aiBkdPYjXWtHuwJGm1v5Ojy0Z0LaSYhHaLJQB0P8Jw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.4
      cssnano-utils: 3.1.0(postcss@8.4.19)
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-minify-selectors@4.0.2:
    resolution: {integrity: sha512-D5S1iViljXBj9kflQo4YutWnJmwm8VvIsU1GeXJGiG9j8CIg9zs4voPMdQDUmIxetUOh60VilsNzCiAFTOqu3g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      alphanum-sort: 1.0.2
      has: 1.0.3
      postcss: 7.0.39
      postcss-selector-parser: 3.1.2
    dev: true

  /postcss-minify-selectors@5.2.1(postcss@8.4.19):
    resolution: {integrity: sha512-nPJu7OjZJTsVUmPdm2TcaiohIwxP+v8ha9NehQ2ye9szv4orirRU3SDdtUmKH+10nzn0bAyOXZ0UEr7OpvLehg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19
      postcss-selector-parser: 6.0.10

  /postcss-modules-extract-imports@2.0.0:
    resolution: {integrity: sha512-LaYLDNS4SG8Q5WAWqIJgdHPJrDDr/Lv775rMBFUbgjTz6j34lUznACHcdRWroPvXANP2Vj7yNK57vp9eFqzLWQ==}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-modules-extract-imports@3.0.0(postcss@8.4.19):
    resolution: {integrity: sha512-bdHleFnP3kZ4NYDhuGlVK+CMrQ/pqUm8bx/oGL93K6gVwiclvX5x0n76fYMKuIGKzlABOy13zsvqjb0f92TEXw==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.4.19

  /postcss-modules-local-by-default@3.0.3:
    resolution: {integrity: sha512-e3xDq+LotiGesympRlKNgaJ0PCzoUIdpH0dj47iWAui/kyTgh3CiAr1qP54uodmJhl6p9rN6BoNcdEDVJx9RDw==}
    engines: {node: '>= 6'}
    dependencies:
      icss-utils: 4.1.1
      postcss: 7.0.39
      postcss-selector-parser: 6.0.10
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-modules-local-by-default@4.0.0(postcss@8.4.19):
    resolution: {integrity: sha512-sT7ihtmGSF9yhm6ggikHdV0hlziDTX7oFoXtuVWeDd3hHObNkcHRo9V3yg7vCAY7cONyxJC/XXCmmiHHcvX7bQ==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.19)
      postcss: 8.4.19
      postcss-selector-parser: 6.0.10
      postcss-value-parser: 4.2.0

  /postcss-modules-scope@2.2.0:
    resolution: {integrity: sha512-YyEgsTMRpNd+HmyC7H/mh3y+MeFWevy7V1evVhJWewmMbjDHIbZbOXICC2y+m1xI1UVfIT1HMW/O04Hxyu9oXQ==}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 6.0.10
    dev: true

  /postcss-modules-scope@3.0.0(postcss@8.4.19):
    resolution: {integrity: sha512-hncihwFA2yPath8oZ15PZqvWGkWf+XUfQgUGamS4LqoP1anQLOsOJw0vr7J7IwLpoY9fatA2qiGUGmuZL0Iqlg==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.4.19
      postcss-selector-parser: 6.0.10

  /postcss-modules-values@3.0.0:
    resolution: {integrity: sha512-1//E5jCBrZ9DmRX+zCtmQtRSV6PV42Ix7Bzj9GbwJceduuf7IqP8MgeTXuRDHOWj2m0VzZD5+roFWDuU8RQjcg==}
    dependencies:
      icss-utils: 4.1.1
      postcss: 7.0.39
    dev: true

  /postcss-modules-values@4.0.0(postcss@8.4.19):
    resolution: {integrity: sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.19)
      postcss: 8.4.19

  /postcss-normalize-charset@4.0.1:
    resolution: {integrity: sha512-gMXCrrlWh6G27U0hF3vNvR3w8I1s2wOBILvA87iNXaPvSNo5uZAMYsZG7XjCUf1eVxuPfyL4TJ7++SGZLc9A3g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-normalize-charset@5.1.0(postcss@8.4.19):
    resolution: {integrity: sha512-mSgUJ+pd/ldRGVx26p2wz9dNZ7ji6Pn8VWBajMXFf8jk7vUoSrZ2lt/wZR7DtlZYKesmZI680qjr2CeFF2fbUg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19

  /postcss-normalize-display-values@4.0.2:
    resolution: {integrity: sha512-3F2jcsaMW7+VtRMAqf/3m4cPFhPD3EFRgNs18u+k3lTJJlVe7d0YPO+bnwqo2xg8YiRpDXJI2u8A0wqJxMsQuQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-match: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-display-values@5.1.0(postcss@8.4.19):
    resolution: {integrity: sha512-WP4KIM4o2dazQXWmFaqMmcvsKmhdINFblgSeRgn8BJ6vxaMyaJkwAzpPpuvSIoG/rmX3M+IrRZEz2H0glrQNEA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-normalize-positions@4.0.2:
    resolution: {integrity: sha512-Dlf3/9AxpxE+NF1fJxYDeggi5WwV35MXGFnnoccP/9qDtFrTArZ0D0R+iKcg5WsUd8nUYMIl8yXDCtcrT8JrdA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      has: 1.0.3
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-positions@5.1.1(postcss@8.4.19):
    resolution: {integrity: sha512-6UpCb0G4eofTCQLFVuI3EVNZzBNPiIKcA1AKVka+31fTVySphr3VUgAIULBhxZkKgwLImhzMR2Bw1ORK+37INg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-normalize-repeat-style@4.0.2:
    resolution: {integrity: sha512-qvigdYYMpSuoFs3Is/f5nHdRLJN/ITA7huIoCyqqENJe9PvPmLhNLMu7QTjPdtnVf6OcYYO5SHonx4+fbJE1+Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      cssnano-util-get-match: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-repeat-style@5.1.1(postcss@8.4.19):
    resolution: {integrity: sha512-mFpLspGWkQtBcWIRFLmewo8aC3ImN2i/J3v8YCFUwDnPu3Xz4rLohDO26lGjwNsQxB3YF0KKRwspGzE2JEuS0g==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-normalize-string@4.0.2:
    resolution: {integrity: sha512-RrERod97Dnwqq49WNz8qo66ps0swYZDSb6rM57kN2J+aoyEAJfZ6bMx0sx/F9TIEX0xthPGCmeyiam/jXif0eA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      has: 1.0.3
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-string@5.1.0(postcss@8.4.19):
    resolution: {integrity: sha512-oYiIJOf4T9T1N4i+abeIc7Vgm/xPCGih4bZz5Nm0/ARVJ7K6xrDlLwvwqOydvyL3RHNf8qZk6vo3aatiw/go3w==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-normalize-timing-functions@4.0.2:
    resolution: {integrity: sha512-acwJY95edP762e++00Ehq9L4sZCEcOPyaHwoaFOhIwWCDfik6YvqsYNxckee65JHLKzuNSSmAdxwD2Cud1Z54A==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-match: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-timing-functions@5.1.0(postcss@8.4.19):
    resolution: {integrity: sha512-DOEkzJ4SAXv5xkHl0Wa9cZLF3WCBhF3o1SKVxKQAa+0pYKlueTpCgvkFAHfk+Y64ezX9+nITGrDZeVGgITJXjg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-normalize-unicode@4.0.1:
    resolution: {integrity: sha512-od18Uq2wCYn+vZ/qCOeutvHjB5jm57ToxRaMeNuf0nWVHaP9Hua56QyMF6fs/4FSUnVIw0CBPsU0K4LnBPwYwg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.4
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-unicode@5.1.1(postcss@8.4.19):
    resolution: {integrity: sha512-qnCL5jzkNUmKVhZoENp1mJiGNPcsJCs1aaRmURmeJGES23Z/ajaln+EPTD+rBeNkSryI+2WTdW+lwcVdOikrpA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.4
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-normalize-url@4.0.1:
    resolution: {integrity: sha512-p5oVaF4+IHwu7VpMan/SSpmpYxcJMtkGppYf0VbdH5B6hN8YNmVyJLuY9FmLQTzY3fag5ESUUHDqM+heid0UVA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      is-absolute-url: 2.1.0
      normalize-url: 3.3.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-url@5.1.0(postcss@8.4.19):
    resolution: {integrity: sha512-5upGeDO+PVthOxSmds43ZeMeZfKH+/DKgGRD7TElkkyS46JXAUhMzIKiCa7BabPeIy3AQcTkXwVVN7DbqsiCew==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      normalize-url: 6.1.0
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-normalize-whitespace@4.0.2:
    resolution: {integrity: sha512-tO8QIgrsI3p95r8fyqKV+ufKlSHh9hMJqACqbv2XknufqEDhDvbguXGBBqxw9nsQoXWf0qOqppziKJKHMD4GtA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-whitespace@5.1.1(postcss@8.4.19):
    resolution: {integrity: sha512-83ZJ4t3NUDETIHTa3uEg6asWjSBYL5EdkVB0sDncx9ERzOKBVJIUeDO9RyA9Zwtig8El1d79HBp0JEi8wvGQnA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-ordered-values@4.1.2:
    resolution: {integrity: sha512-2fCObh5UanxvSxeXrtLtlwVThBvHn6MQcu4ksNT2tsaV2Fg76R2CV98W7wNSlX+5/pFwEyaDwKLLoEV7uRybAw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-ordered-values@5.1.3(postcss@8.4.19):
    resolution: {integrity: sha512-9UO79VUhPwEkzbb3RNpqqghc6lcYej1aveQteWY+4POIwlqkYE21HKWaLDF6lWNuqCobEAyTovVhtI32Rbv2RQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      cssnano-utils: 3.1.0(postcss@8.4.19)
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-reduce-initial@4.0.3:
    resolution: {integrity: sha512-gKWmR5aUulSjbzOfD9AlJiHCGH6AEVLaM0AV+aSioxUDd16qXP1PCh8d1/BGVvpdWn8k/HiK7n6TjeoXN1F7DA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.4
      caniuse-api: 3.0.0
      has: 1.0.3
      postcss: 7.0.39
    dev: true

  /postcss-reduce-initial@5.1.1(postcss@8.4.19):
    resolution: {integrity: sha512-//jeDqWcHPuXGZLoolFrUXBDyuEGbr9S2rMo19bkTIjBQ4PqkaO+oI8wua5BOUxpfi97i3PCoInsiFIEBfkm9w==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.4
      caniuse-api: 3.0.0
      postcss: 8.4.19

  /postcss-reduce-transforms@4.0.2:
    resolution: {integrity: sha512-EEVig1Q2QJ4ELpJXMZR8Vt5DQx8/mo+dGWSR7vWXqcob2gQLyQGsionYcGKATXvQzMPn6DSN1vTN7yFximdIAg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-match: 4.0.0
      has: 1.0.3
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-reduce-transforms@5.1.0(postcss@8.4.19):
    resolution: {integrity: sha512-2fbdbmgir5AvpW9RLtdONx1QoYG2/EtqpNQbFASDlixBbAYuTcJ0dECwlqNqH7VbaUnEnh8SrxOe2sRIn24XyQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19
      postcss-value-parser: 4.2.0

  /postcss-selector-parser@3.1.2:
    resolution: {integrity: sha512-h7fJ/5uWuRVyOtkO45pnt1Ih40CEleeyCHzipqAZO2e5H20g25Y48uYnFUiShvY4rZWNJ/Bib/KVPmanaCtOhA==}
    engines: {node: '>=8'}
    dependencies:
      dot-prop: 5.3.0
      indexes-of: 1.0.1
      uniq: 1.0.1
    dev: true

  /postcss-selector-parser@6.0.10:
    resolution: {integrity: sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  /postcss-svgo@4.0.3:
    resolution: {integrity: sha512-NoRbrcMWTtUghzuKSoIm6XV+sJdvZ7GZSc3wdBN0W19FTtp2ko8NqLsgoh/m9CzNhU3KLPvQmjIwtaNFkaFTvw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      svgo: 1.3.2
    dev: true

  /postcss-svgo@5.1.0(postcss@8.4.19):
    resolution: {integrity: sha512-D75KsH1zm5ZrHyxPakAxJWtkyXew5qwS70v56exwvw542d9CRtTo78K0WeFxZB4G7JXKKMbEZtZayTGdIky/eA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19
      postcss-value-parser: 4.2.0
      svgo: 2.8.0

  /postcss-unique-selectors@4.0.1:
    resolution: {integrity: sha512-+JanVaryLo9QwZjKrmJgkI4Fn8SBgRO6WXQBJi7KiAVPlmxikB5Jzc4EvXMT2H0/m0RjrVVm9rGNhZddm/8Spg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      alphanum-sort: 1.0.2
      postcss: 7.0.39
      uniqs: 2.0.0
    dev: true

  /postcss-unique-selectors@5.1.1(postcss@8.4.19):
    resolution: {integrity: sha512-5JiODlELrz8L2HwxfPnhOWZYWDxVHWL83ufOv84NrcgipI7TaeRsatAhK4Tr2/ZiYldpK/wBvw5BD3qfaK96GA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.19
      postcss-selector-parser: 6.0.10

  /postcss-value-parser@3.3.1:
    resolution: {integrity: sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ==}
    dev: true

  /postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  /postcss@6.0.23:
    resolution: {integrity: sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==}
    engines: {node: '>=4.0.0'}
    dependencies:
      chalk: 2.4.2
      source-map: 0.6.1
      supports-color: 5.5.0
    dev: true

  /postcss@7.0.39:
    resolution: {integrity: sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      picocolors: 0.2.1
      source-map: 0.6.1

  /postcss@8.4.19:
    resolution: {integrity: sha512-h+pbPsyhlYj6N2ozBmHhHrs9DzGmbaarbLvWipMRO7RLS+v4onj26MPFXA5OBYFxyqYhUJK456SwDcY9H2/zsA==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.4
      picocolors: 1.0.0
      source-map-js: 1.0.2

  /prebuild-install@5.3.6:
    resolution: {integrity: sha512-s8Aai8++QQGi4sSbs/M1Qku62PFK49Jm1CbgXklGz4nmHveDq0wzJkg7Na5QbnO1uNH8K7iqx2EQ/mV0MZEmOg==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      detect-libc: 1.0.3
      expand-template: 2.0.3
      github-from-package: 0.0.0
      minimist: 1.2.7
      mkdirp-classic: 0.5.3
      napi-build-utils: 1.0.2
      node-abi: 2.30.1
      noop-logger: 0.1.1
      npmlog: 4.1.2
      pump: 3.0.0
      rc: 1.2.8
      simple-get: 3.1.1
      tar-fs: 2.1.1
      tunnel-agent: 0.6.0
      which-pm-runs: 1.1.0
    dev: true
    optional: true

  /prelude-ls@1.1.2:
    resolution: {integrity: sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w==}
    engines: {node: '>= 0.8.0'}

  /prepend-http@1.0.4:
    resolution: {integrity: sha512-PhmXi5XmoyKw1Un4E+opM2KcsJInDvKyuOumcjjw3waw86ZNjHwVUOOWLc4bCzLdcKNaWBH9e99sbWzDQsVaYg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /prettier@2.7.1:
    resolution: {integrity: sha512-ujppO+MkdPqoVINuDFDRLClm7D78qbDt0/NR+wp5FqEZOoTNAjPHWj17QRhu7geIHJfcNhRk1XVQmF8Bp3ye+g==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    requiresBuild: true
    optional: true

  /pretty-bytes@5.6.0:
    resolution: {integrity: sha512-FFw039TmrBqFK8ma/7OL3sDz/VytdtJr044/QUJtH0wK9lb9jLq9tJyIxUwtQJHwar2BqtiA4iCWSwo9JLkzFg==}
    engines: {node: '>=6'}
    dev: true

  /pretty-error@4.0.0:
    resolution: {integrity: sha512-AoJ5YMAcXKYxKhuJGdcvse+Voc6v1RgnsR3nWcYU7q4t6z0Q6T86sv5Zq8VIRbOWWFpvdGE83LtdSMNd+6Y0xw==}
    dependencies:
      lodash: 4.17.21
      renderkid: 3.0.0

  /pretty-format@24.9.0:
    resolution: {integrity: sha512-00ZMZUiHaJrNfk33guavqgvfJS30sLYf0f8+Srklv0AMPodGGHcoHgksZ3OThYnIvOd+8yMCn0YiEOogjlgsnA==}
    engines: {node: '>= 6'}
    dependencies:
      '@jest/types': 24.9.0
      ansi-regex: 4.1.1
      ansi-styles: 3.2.1
      react-is: 16.13.1

  /pretty@2.0.0:
    resolution: {integrity: sha512-G9xUchgTEiNpormdYBl+Pha50gOUovT18IvAe7EYMZ1/f9W/WWMPRn+xI68yXNMUk3QXHDwo/1wV/4NejVNe1w==}
    engines: {node: '>=0.10.0'}
    dependencies:
      condense-newlines: 0.2.1
      extend-shallow: 2.0.1
      js-beautify: 1.14.7
    dev: true

  /process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  /process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}

  /progress-webpack-plugin@1.0.16(webpack@5.75.0):
    resolution: {integrity: sha512-sdiHuuKOzELcBANHfrupYo+r99iPRyOnw15qX+rNlVUqXGfjXdH4IgxriKwG1kNJwVswKQHMdj1hYZMcb9jFaA==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0
    dependencies:
      chalk: 2.4.2
      figures: 2.0.0
      log-update: 2.3.0
      webpack: 5.75.0(webpack-cli@4.10.0)

  /progress@2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==}
    engines: {node: '>=0.4.0'}

  /promise-inflight@1.0.1(bluebird@3.7.2):
    resolution: {integrity: sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true
    dependencies:
      bluebird: 3.7.2

  /prompts@2.4.2:
    resolution: {integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==}
    engines: {node: '>= 6'}
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  /prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1
    dev: true

  /proto-list@1.2.4:
    resolution: {integrity: sha512-vtK/94akxsTMhe0/cbfpR+syPuszcuwhqVjJq26CuNDgFGj682oRBXOP5MJpv2r7JtE8MsiepGIqvvOTBwn2vA==}
    dev: true

  /proxy-addr@2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  /prr@1.0.1:
    resolution: {integrity: sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==}

  /pseudomap@1.0.2:
    resolution: {integrity: sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==}

  /psl@1.9.0:
    resolution: {integrity: sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==}

  /public-encrypt@4.0.3:
    resolution: {integrity: sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q==}
    dependencies:
      bn.js: 4.12.0
      browserify-rsa: 4.1.0
      create-hash: 1.2.0
      parse-asn1: 5.1.6
      randombytes: 2.1.0
      safe-buffer: 5.2.1

  /pump@2.0.1:
    resolution: {integrity: sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  /pump@3.0.0:
    resolution: {integrity: sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  /pumpify@1.5.1:
    resolution: {integrity: sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ==}
    dependencies:
      duplexify: 3.7.1
      inherits: 2.0.4
      pump: 2.0.1

  /punycode@1.3.2:
    resolution: {integrity: sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw==}

  /punycode@1.4.1:
    resolution: {integrity: sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==}

  /punycode@2.1.1:
    resolution: {integrity: sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==}
    engines: {node: '>=6'}

  /q@1.5.1:
    resolution: {integrity: sha512-kV/CThkXo6xyFEZUugw/+pIOywXcDbFYgSct5cT3gqlbkBE1SJdwy6UQoZvodiWF/ckQLZyDE/Bu1M6gVu5lVw==}
    engines: {node: '>=0.6.0', teleport: '>=0.2.0'}
    dev: true

  /qs@6.11.0:
    resolution: {integrity: sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.0.4

  /qs@6.5.3:
    resolution: {integrity: sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==}
    engines: {node: '>=0.6'}

  /query-string@4.3.4:
    resolution: {integrity: sha512-O2XLNDBIg1DnTOa+2XrIwSiXEV8h2KImXUnjhhn2+UsvZ+Es2uyd5CCRTNQlDGbzUQOW3aYCBx9rVA6dzsiY7Q==}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-assign: 4.1.1
      strict-uri-encode: 1.1.0
    dev: true

  /querystring-es3@0.2.1:
    resolution: {integrity: sha512-773xhDQnZBMFobEiztv8LIl70ch5MSF/jUQVlhwFyBILqq96anmoctVIYz+ZRp0qbCKATTn6ev02M3r7Ga5vqA==}
    engines: {node: '>=0.4.x'}

  /querystring@0.2.0:
    resolution: {integrity: sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=}
    engines: {node: '>=0.4.x'}
    deprecated: The querystring API is considered Legacy. new code should use the URLSearchParams API instead.

  /querystringify@2.2.0:
    resolution: {integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==}
    dev: false

  /queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  /ramda@0.27.2:
    resolution: {integrity: sha512-SbiLPU40JuJniHexQSAgad32hfwd+DRUdwF2PlVuI5RZD0/vahUco7R8vD86J/tcEKKF9vZrUVwgtmGCqlCKyA==}
    dev: true

  /randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}
    dependencies:
      safe-buffer: 5.2.1

  /randomfill@1.0.4:
    resolution: {integrity: sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw==}
    dependencies:
      randombytes: 2.1.0
      safe-buffer: 5.2.1

  /range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}

  /raw-body@2.5.1:
    resolution: {integrity: sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==}
    engines: {node: '>= 0.8'}
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  /rc@1.2.8:
    resolution: {integrity: sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==}
    hasBin: true
    dependencies:
      deep-extend: 0.6.0
      ini: 1.3.8
      minimist: 1.2.7
      strip-json-comments: 2.0.1
    dev: true
    optional: true

  /react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  /read-cache@1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}
    dependencies:
      pify: 2.3.0
    dev: true

  /read-pkg-up@2.0.0:
    resolution: {integrity: sha512-1orxQfbWGUiTn9XsPlChs6rLie/AV9jwZTGmu2NZw/CUDJQchXJFYE0Fq5j7+n558T1JhDWLdhyd1Zj+wLY//w==}
    engines: {node: '>=4'}
    dependencies:
      find-up: 2.1.0
      read-pkg: 2.0.0
    dev: true

  /read-pkg-up@4.0.0:
    resolution: {integrity: sha512-6etQSH7nJGsK0RbG/2TeDzZFa8shjQ1um+SwQQ5cwKy0dhSXdOncEhb1CPpvQG4h7FyOV6EB6YlV0yJvZQNAkA==}
    engines: {node: '>=6'}
    dependencies:
      find-up: 3.0.0
      read-pkg: 3.0.0

  /read-pkg-up@7.0.1:
    resolution: {integrity: sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
      read-pkg: 5.2.0
      type-fest: 0.8.1

  /read-pkg@2.0.0:
    resolution: {integrity: sha512-eFIBOPW7FGjzBuk3hdXEuNSiTZS/xEMlH49HxMyzb0hyPfu4EhVjT2DH32K1hSSmVq4sebAWnZuuY5auISUTGA==}
    engines: {node: '>=4'}
    dependencies:
      load-json-file: 2.0.0
      normalize-package-data: 2.5.0
      path-type: 2.0.0
    dev: true

  /read-pkg@3.0.0:
    resolution: {integrity: sha512-BLq/cCO9two+lBgiTYNqD6GdtK8s4NpaWrl6/rCO9w0TUS8oJl7cmToOZfRYllKTISY6nt1U7jQ53brmKqY6BA==}
    engines: {node: '>=4'}
    dependencies:
      load-json-file: 4.0.0
      normalize-package-data: 2.5.0
      path-type: 3.0.0

  /read-pkg@5.2.0:
    resolution: {integrity: sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==}
    engines: {node: '>=8'}
    dependencies:
      '@types/normalize-package-data': 2.4.1
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0

  /readable-stream@2.3.7:
    resolution: {integrity: sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  /readable-stream@3.6.0:
    resolution: {integrity: sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==}
    engines: {node: '>= 6'}
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  /readdirp@2.2.1:
    resolution: {integrity: sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==}
    engines: {node: '>=0.10'}
    dependencies:
      graceful-fs: 4.2.10
      micromatch: 3.1.10
      readable-stream: 2.3.7
    transitivePeerDependencies:
      - supports-color
    optional: true

  /readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1

  /realpath-native@1.1.0:
    resolution: {integrity: sha512-wlgPA6cCIIg9gKz0fgAPjnzh4yR/LnXovwuo9hvyGvx3h8nX4+/iLZplfUWasXpqD8BdnGnP5njOFjkUwPzvjA==}
    engines: {node: '>=4'}
    dependencies:
      util.promisify: 1.1.1

  /rechoir@0.7.1:
    resolution: {integrity: sha512-/njmZ8s1wVeR6pjTZ+0nCnv8SpZNRMT2D1RLOJQESlYFDBvwpTA4KWJpZ+sBJ4+vhjILRcK7JIFdGCdxEAAitg==}
    engines: {node: '>= 0.10'}
    dependencies:
      resolve: 1.22.1

  /regenerate-unicode-properties@10.1.0:
    resolution: {integrity: sha512-d1VudCLoIGitcU/hEg2QqvyGZQmdC0Lf8BqdOMXGFSvJP4bNV1+XqbPQeHHLD51Jh4QJJ225dlIFvY4Ly6MXmQ==}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2
    dev: true

  /regenerate@1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}
    dev: true

  /regenerator-runtime@0.11.1:
    resolution: {integrity: sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==}

  /regenerator-runtime@0.13.10:
    resolution: {integrity: sha512-KepLsg4dU12hryUO7bp/axHAKvwGOCV0sGloQtpagJ12ai+ojVDqkeGSiRX1zlq+kjIMZ1t7gpze+26QqtdGqw==}

  /regenerator-transform@0.15.0:
    resolution: {integrity: sha512-LsrGtPmbYg19bcPHwdtmXwbW+TqNvtY4riE3P83foeHRroMbH6/2ddFBfab3t7kbzc7v7p4wbkIecHImqt0QNg==}
    dependencies:
      '@babel/runtime': 7.20.1
    dev: true

  /regex-not@1.0.2:
    resolution: {integrity: sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2
      safe-regex: 1.1.0

  /regexp.prototype.flags@1.4.3:
    resolution: {integrity: sha512-fjggEOO3slI6Wvgjwflkc4NFRCTZAu5CnNfBd5qOMYhWdn67nJBBu34/TkD++eeFmd8C9r9jfXJ27+nSiRkSUA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      functions-have-names: 1.2.3

  /regexpp@2.0.1:
    resolution: {integrity: sha512-lv0M6+TkDVniA3aD1Eg0DVpfU/booSu7Eev3TDO/mZKHBfVjgCGTV4t4buppESEYDtkArYFOxTJWv6S5C+iaNw==}
    engines: {node: '>=6.5.0'}

  /regexpp@3.2.0:
    resolution: {integrity: sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==}
    engines: {node: '>=8'}
    dev: true

  /regexpu-core@5.2.1:
    resolution: {integrity: sha512-HrnlNtpvqP1Xkb28tMhBUO2EbyUHdQlsnlAhzWcwHy8WJR53UWr7/MAvqrsQKMbV4qdpv03oTMG8iIhfsPFktQ==}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.1.0
      regjsgen: 0.7.1
      regjsparser: 0.9.1
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.0.0
    dev: true

  /regjsgen@0.7.1:
    resolution: {integrity: sha512-RAt+8H2ZEzHeYWxZ3H2z6tF18zyyOnlcdaafLrm21Bguj7uZy6ULibiAFdXEtKQY4Sy7wDTwDiOazasMLc4KPA==}
    dev: true

  /regjsparser@0.9.1:
    resolution: {integrity: sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==}
    hasBin: true
    dependencies:
      jsesc: 0.5.0
    dev: true

  /relateurl@0.2.7:
    resolution: {integrity: sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog==}
    engines: {node: '>= 0.10'}

  /remove-trailing-separator@1.1.0:
    resolution: {integrity: sha512-/hS+Y0u3aOfIETiaiirUFwDBDzmXPvO+jAfKTitUngIPzdKc6Z0LoFjM/CK5PL4C+eKwHohlHAb6H0VFfmmUsw==}

  /renderkid@3.0.0:
    resolution: {integrity: sha512-q/7VIQA8lmM1hF+jn+sFSPWGlMkSAeNYcPLmDQx2zzuiDfaLrOmumR8iaUKlenFgh0XRPIUeSPlH3A+AW3Z5pg==}
    dependencies:
      css-select: 4.3.0
      dom-converter: 0.2.0
      htmlparser2: 6.1.0
      lodash: 4.17.21
      strip-ansi: 6.0.1

  /repeat-element@1.1.4:
    resolution: {integrity: sha512-LFiNfRcSu7KK3evMyYOuCzv3L10TW7yC1G2/+StMjK8Y6Vqd2MG7r/Qjw4ghtuCOjFvlnms/iMmLqpvW/ES/WQ==}
    engines: {node: '>=0.10.0'}

  /repeat-string@1.6.1:
    resolution: {integrity: sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==}
    engines: {node: '>=0.10'}

  /request-progress@3.0.0:
    resolution: {integrity: sha512-MnWzEHHaxHO2iWiQuHrUPBi/1WeBf5PkxQqNyNvLl9VAYSdXkP8tQ3pBSeCPD+yw0v0Aq1zosWLz0BdeXpWwZg==}
    dependencies:
      throttleit: 1.0.0
    dev: true

  /request-promise-core@1.1.4(request@2.88.2):
    resolution: {integrity: sha512-TTbAfBBRdWD7aNNOoVOBH4pN/KigV6LyapYNNlAPA8JwbovRti1E88m3sYAwsLi5ryhPKsE9APwnjFTgdUjTpw==}
    engines: {node: '>=0.10.0'}
    peerDependencies:
      request: ^2.34
    dependencies:
      lodash: 4.17.21
      request: 2.88.2

  /request-promise-native@1.0.9(request@2.88.2):
    resolution: {integrity: sha512-wcW+sIUiWnKgNY0dqCpOZkUbF/I+YPi+f09JZIDa39Ec+q82CpSYniDp+ISgTTbKmnpJWASeJBPZmoxH84wt3g==}
    engines: {node: '>=0.12.0'}
    deprecated: request-promise-native has been deprecated because it extends the now deprecated request package, see https://github.com/request/request/issues/3142
    peerDependencies:
      request: ^2.34
    dependencies:
      request: 2.88.2
      request-promise-core: 1.1.4(request@2.88.2)
      stealthy-require: 1.1.1
      tough-cookie: 2.5.0

  /request@2.88.2:
    resolution: {integrity: sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.11.0
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0

  /require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  /require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}

  /require-main-filename@2.0.0:
    resolution: {integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==}

  /requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}

  /resolve-cwd@2.0.0:
    resolution: {integrity: sha512-ccu8zQTrzVr954472aUVPLEcB3YpKSYR3cg/3lo1okzobPBM+1INXBbBZlDbnI/hbEocnf8j0QVo43hQKrbchg==}
    engines: {node: '>=4'}
    dependencies:
      resolve-from: 3.0.0

  /resolve-cwd@3.0.0:
    resolution: {integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==}
    engines: {node: '>=8'}
    dependencies:
      resolve-from: 5.0.0

  /resolve-from@3.0.0:
    resolution: {integrity: sha512-GnlH6vxLymXJNMBo7XP1fJIzBFbdYt49CuTwmB/6N53t+kMPRMFKz783LlQ4tv28XoQfMWinAJX6WCGf2IlaIw==}
    engines: {node: '>=4'}

  /resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  /resolve-from@5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}

  /resolve-url@0.2.1:
    resolution: {integrity: sha512-ZuF55hVUQaaczgOIwqWzkEcEidmlD/xl44x1UZnhOXcYuFN2S6+rcxpG+C1N3So0wvNI3DmJICUFfu2SxhBmvg==}
    deprecated: https://github.com/lydell/resolve-url#deprecated

  /resolve@1.1.7:
    resolution: {integrity: sha512-9znBF0vBcaSN3W2j7wKvdERPwqTxSpCq+if5C0WoTCyV9n24rua28jeuQ2pL/HOf+yUe/Mef+H/5p60K0Id3bg==}

  /resolve@1.22.1:
    resolution: {integrity: sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==}
    hasBin: true
    dependencies:
      is-core-module: 2.11.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  /restore-cursor@1.0.1:
    resolution: {integrity: sha512-reSjH4HuiFlxlaBaFCiS6O76ZGG2ygKoSlCsipKdaZuKSPx/+bt9mULkn4l0asVzbEfQQmXRg6Wp6gv6m0wElw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      exit-hook: 1.1.1
      onetime: 1.1.0
    dev: true

  /restore-cursor@2.0.0:
    resolution: {integrity: sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q==}
    engines: {node: '>=4'}
    dependencies:
      onetime: 2.0.1
      signal-exit: 3.0.7

  /restore-cursor@3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  /ret@0.1.15:
    resolution: {integrity: sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==}
    engines: {node: '>=0.12'}

  /retry@0.13.1:
    resolution: {integrity: sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==}
    engines: {node: '>= 4'}

  /reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  /rgb-regex@1.0.1:
    resolution: {integrity: sha512-gDK5mkALDFER2YLqH6imYvK6g02gpNGM4ILDZ472EwWfXZnC2ZEpoB2ECXTyOVUKuk/bPJZMzwQPBYICzP+D3w==}
    dev: true

  /rgba-regex@1.0.0:
    resolution: {integrity: sha512-zgn5OjNQXLUTdq8m17KdaicF6w89TZs8ZU8y0AYENIU6wG8GG6LLm0yLSiPY8DmaYmHdgRW8rnApjoT0fQRfMg==}
    dev: true

  /rimraf@2.6.3:
    resolution: {integrity: sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==}
    hasBin: true
    dependencies:
      glob: 7.2.3

  /rimraf@2.7.1:
    resolution: {integrity: sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==}
    hasBin: true
    dependencies:
      glob: 7.2.3

  /rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    hasBin: true
    dependencies:
      glob: 7.2.3

  /ripemd160@2.0.2:
    resolution: {integrity: sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA==}
    dependencies:
      hash-base: 3.1.0
      inherits: 2.0.4

  /rsvp@4.8.5:
    resolution: {integrity: sha512-nfMOlASu9OnRJo1mbEk2cz0D56a1MBNrJ7orjRZQG10XDyuvwksKbuXNp6qa+kbn839HwjwhBzhFmdsaEAfauA==}
    engines: {node: 6.* || >= 7.*}

  /run-async@2.4.1:
    resolution: {integrity: sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==}
    engines: {node: '>=0.12.0'}

  /run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    dependencies:
      queue-microtask: 1.2.3

  /run-queue@1.0.3:
    resolution: {integrity: sha512-ntymy489o0/QQplUDnpYAYUsO50K9SBrIVaKCWDOJzYJts0f9WH9RFJkyagebkw5+y1oi00R7ynNW/d12GBumg==}
    dependencies:
      aproba: 1.2.0

  /rxjs@6.6.7:
    resolution: {integrity: sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ==}
    engines: {npm: '>=2.0.0'}
    dependencies:
      tslib: 1.14.1

  /safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  /safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  /safe-regex-test@1.0.0:
    resolution: {integrity: sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.1.3
      is-regex: 1.1.4

  /safe-regex@1.1.0:
    resolution: {integrity: sha512-aJXcif4xnaNUzvUuC5gcb46oTS7zvg4jpMTnuqtrEPlR3vFr4pxtdTwaF1Qs3Enjn9HK+ZlwQui+a7z0SywIzg==}
    dependencies:
      ret: 0.1.15

  /safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  /sane@4.1.0:
    resolution: {integrity: sha512-hhbzAgTIX8O7SHfp2c8/kREfEn4qO/9q8C9beyY6+tvZ87EpoZ3i1RIEvp27YBswnNbY9mWd6paKVmKbAgLfZA==}
    engines: {node: 6.* || 8.* || >= 10.*}
    deprecated: some dependency vulnerabilities fixed, support for node < 10 dropped, and newer ECMAScript syntax/features added
    hasBin: true
    dependencies:
      '@cnakazawa/watch': 1.0.4
      anymatch: 2.0.0
      capture-exit: 2.0.0
      exec-sh: 0.3.6
      execa: 1.0.0
      fb-watchman: 2.0.2
      micromatch: 3.1.10
      minimist: 1.2.7
      walker: 1.0.8
    transitivePeerDependencies:
      - supports-color

  /sass-loader@9.0.3(sass@1.56.1)(webpack@4.46.0):
    resolution: {integrity: sha512-fOwsP98ac1VMme+V3+o0HaaMHp8Q/C9P+MUazLFVi3Jl7ORGHQXL1XeRZt3zLSGZQQPC8xE42Y2WptItvGjDQg==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      fibers: '>= 3.1.0'
      node-sass: ^4.0.0
      sass: ^1.3.0
      webpack: ^4.36.0 || ^5.0.0
    peerDependenciesMeta:
      fibers:
        optional: true
      node-sass:
        optional: true
      sass:
        optional: true
    dependencies:
      klona: 1.1.2
      loader-utils: 2.0.4
      neo-async: 2.6.2
      sass: 1.56.1
      schema-utils: 2.7.1
      semver: 7.3.8
      webpack: 4.46.0(webpack-cli@4.10.0)

  /sass-resources-loader@2.2.5:
    resolution: {integrity: sha512-po8rfETH9cOQACWxubT/1CCu77KjxwRtCDm6QAXZH99aUHBydwSoxdIjC40SGp/dcS/FkSNJl0j1VEojGZqlvQ==}
    dependencies:
      async: 3.2.4
      chalk: 4.1.2
      glob: 7.2.3
      loader-utils: 2.0.4
    dev: true

  /sass@1.56.1:
    resolution: {integrity: sha512-VpEyKpyBPCxE7qGDtOcdJ6fFbcpOM+Emu7uZLxVrkX8KVU/Dp5UF7WLvzqRuUhB6mqqQt1xffLoG+AndxTZrCQ==}
    engines: {node: '>=12.0.0'}
    hasBin: true
    dependencies:
      chokidar: 3.5.3
      immutable: 4.1.0
      source-map-js: 1.0.2

  /sax@1.2.4:
    resolution: {integrity: sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==}

  /saxes@3.1.11:
    resolution: {integrity: sha512-Ydydq3zC+WYDJK1+gRxRapLIED9PWeSuuS41wqyoRmzvhhh9nc+QQrVMKJYzJFULazeGhzSV0QleN2wD3boh2g==}
    engines: {node: '>=8'}
    dependencies:
      xmlchars: 2.2.0
    dev: true

  /schema-utils@1.0.0:
    resolution: {integrity: sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==}
    engines: {node: '>= 4'}
    dependencies:
      ajv: 6.12.6
      ajv-errors: 1.0.1(ajv@6.12.6)
      ajv-keywords: 3.5.2(ajv@6.12.6)

  /schema-utils@2.7.1:
    resolution: {integrity: sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg==}
    engines: {node: '>= 8.9.0'}
    dependencies:
      '@types/json-schema': 7.0.11
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  /schema-utils@3.1.1:
    resolution: {integrity: sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': 7.0.11
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  /schema-utils@4.0.0:
    resolution: {integrity: sha512-1edyXKgh6XnJsJSQ8mKWXnN/BVaIbFMLpouRUrXgVq7WYne5kw3MW7UPhO44uRXQSIpTSXoJbmrR2X0w9kUTyg==}
    engines: {node: '>= 12.13.0'}
    dependencies:
      '@types/json-schema': 7.0.11
      ajv: 8.11.0
      ajv-formats: 2.1.1(ajv@8.11.0)
      ajv-keywords: 5.1.0(ajv@8.11.0)

  /select-hose@2.0.0:
    resolution: {integrity: sha512-mEugaLK+YfkijB4fx0e6kImuJdCIt2LxCRcbEYPqRGCs4F2ogyfZU5IAZRdjCP8JPq2AtdNoC/Dux63d9Kiryg==}

  /selfsigned@2.1.1:
    resolution: {integrity: sha512-GSL3aowiF7wa/WtSFwnUrludWFoNhftq8bUkH9pkzjpN2XSPOAYEgg6e0sS9s0rZwgJzJiQRPU18A6clnoW5wQ==}
    engines: {node: '>=10'}
    dependencies:
      node-forge: 1.3.1

  /semver@5.7.1:
    resolution: {integrity: sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==}
    hasBin: true

  /semver@6.3.0:
    resolution: {integrity: sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==}
    hasBin: true

  /semver@7.3.8:
    resolution: {integrity: sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: 6.0.0

  /send@0.18.0:
    resolution: {integrity: sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      debug: 2.6.9(supports-color@7.2.0)
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  /serialize-javascript@4.0.0:
    resolution: {integrity: sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw==}
    dependencies:
      randombytes: 2.1.0

  /serialize-javascript@6.0.0:
    resolution: {integrity: sha512-Qr3TosvguFt8ePWqsvRfrKyQXIiW+nGbYpy8XK24NQHE83caxWt+mIymTT19DGFbNWNLfEwsrkSmN64lVWB9ag==}
    dependencies:
      randombytes: 2.1.0

  /serve-index@1.9.1:
    resolution: {integrity: sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      accepts: 1.3.8
      batch: 0.6.1
      debug: 2.6.9(supports-color@7.2.0)
      escape-html: 1.0.3
      http-errors: 1.6.3
      mime-types: 2.1.35
      parseurl: 1.3.3
    transitivePeerDependencies:
      - supports-color

  /serve-static@1.15.0:
    resolution: {integrity: sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      encodeurl: 1.0.2
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.18.0
    transitivePeerDependencies:
      - supports-color

  /set-blocking@2.0.0:
    resolution: {integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==}

  /set-value@2.0.1:
    resolution: {integrity: sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-extendable: 0.1.1
      is-plain-object: 2.0.4
      split-string: 3.1.0

  /setimmediate@1.0.5:
    resolution: {integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==}

  /setprototypeof@1.1.0:
    resolution: {integrity: sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==}

  /setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  /sha.js@2.4.11:
    resolution: {integrity: sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==}
    hasBin: true
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1

  /shallow-clone@3.0.1:
    resolution: {integrity: sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==}
    engines: {node: '>=8'}
    dependencies:
      kind-of: 6.0.3

  /shebang-command@1.2.0:
    resolution: {integrity: sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      shebang-regex: 1.0.0

  /shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0

  /shebang-regex@1.0.0:
    resolution: {integrity: sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==}
    engines: {node: '>=0.10.0'}

  /shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  /shell-quote@1.7.4:
    resolution: {integrity: sha512-8o/QEhSSRb1a5i7TFR0iM4G16Z0vYB2OQVs4G3aAFXjn3T6yEx8AZxy1PgDF7I00LZHYA3WxaSYIf5e5sAX8Rw==}

  /shellwords@0.1.1:
    resolution: {integrity: sha512-vFwSUfQvqybiICwZY5+DAWIPLKsWO31Q91JSKl3UYv+K5c2QRPzn0qzec6QPu1Qc9eHYItiP3NdJqNVqetYAww==}

  /shvl@2.0.3:
    resolution: {integrity: sha512-V7C6S9Hlol6SzOJPnQ7qzOVEWUQImt3BNmmzh40wObhla3XOYMe4gGiYzLrJd5TFa+cI2f9LKIRJTTKZSTbWgw==}
    deprecated: older versions vulnerable to prototype pollution
    dev: false

  /side-channel@1.0.4:
    resolution: {integrity: sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.1.3
      object-inspect: 1.12.2

  /sigmund@1.0.1:
    resolution: {integrity: sha512-fCvEXfh6NWpm+YSuY2bpXb/VIihqWA6hLsgboC+0nl71Q7N7o2eaCW8mJa/NLvQhs6jpd3VZV4UiUQlV6+lc8g==}
    dev: true

  /signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  /simple-concat@1.0.1:
    resolution: {integrity: sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==}
    dev: true
    optional: true

  /simple-get@3.1.1:
    resolution: {integrity: sha512-CQ5LTKGfCpvE1K0n2us+kuMPbk/q0EKl82s4aheV9oXjFEz6W/Y7oQFVJuU6QG77hRT4Ghb5RURteF5vnWjupA==}
    dependencies:
      decompress-response: 4.2.1
      once: 1.4.0
      simple-concat: 1.0.1
    dev: true
    optional: true

  /simple-swizzle@0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}
    dependencies:
      is-arrayish: 0.3.2
    dev: true

  /sirv@1.0.19:
    resolution: {integrity: sha512-JuLThK3TnZG1TAKDwNIqNq6QA2afLOCcm+iE8D1Kj3GA40pSPsxQjjJl0J8X3tsR7T+CP1GavpzLwYkgVLWrZQ==}
    engines: {node: '>= 10'}
    dependencies:
      '@polka/url': 1.0.0-next.21
      mrmime: 1.0.1
      totalist: 1.1.0

  /sisteransi@1.0.5:
    resolution: {integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==}

  /slash@2.0.0:
    resolution: {integrity: sha512-ZYKh3Wh2z1PpEXWr0MpSBZ0V6mZHAQfYevttO11c51CaWjGTaadiKZ+wVt1PbMlDV5qhMFslpZCemhwOK7C89A==}
    engines: {node: '>=6'}

  /slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}

  /slice-ansi@0.0.4:
    resolution: {integrity: sha512-up04hB2hR92PgjpyU3y/eg91yIBILyjVY26NvvciY3EVVPjybkMszMpXQ9QAkcS3I5rtJBDLoTxxg+qvW8c7rw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /slice-ansi@2.1.0:
    resolution: {integrity: sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ==}
    engines: {node: '>=6'}
    dependencies:
      ansi-styles: 3.2.1
      astral-regex: 1.0.0
      is-fullwidth-code-point: 2.0.0

  /snapdragon-node@2.1.1:
    resolution: {integrity: sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 1.0.0
      isobject: 3.0.1
      snapdragon-util: 3.0.1

  /snapdragon-util@3.0.1:
    resolution: {integrity: sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /snapdragon@0.8.2:
    resolution: {integrity: sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      base: 0.11.2
      debug: 2.6.9(supports-color@7.2.0)
      define-property: 0.2.5
      extend-shallow: 2.0.1
      map-cache: 0.2.2
      source-map: 0.5.7
      source-map-resolve: 0.5.3
      use: 3.1.1
    transitivePeerDependencies:
      - supports-color

  /sockjs@0.3.24:
    resolution: {integrity: sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==}
    dependencies:
      faye-websocket: 0.11.4
      uuid: 8.3.2
      websocket-driver: 0.7.4

  /sort-keys@1.1.2:
    resolution: {integrity: sha512-vzn8aSqKgytVik0iwdBEi+zevbTYZogewTUM6dtpmGwEcdzbub/TX4bCzRhebDCRC3QzXgJsLRKB2V/Oof7HXg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-obj: 1.1.0
    dev: true

  /source-list-map@2.0.1:
    resolution: {integrity: sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==}

  /source-map-js@1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==}
    engines: {node: '>=0.10.0'}

  /source-map-resolve@0.5.3:
    resolution: {integrity: sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw==}
    deprecated: See https://github.com/lydell/source-map-resolve#deprecated
    dependencies:
      atob: 2.1.2
      decode-uri-component: 0.2.0
      resolve-url: 0.2.1
      source-map-url: 0.4.1
      urix: 0.1.0

  /source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  /source-map-url@0.4.1:
    resolution: {integrity: sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw==}
    deprecated: See https://github.com/lydell/source-map-url#deprecated

  /source-map@0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==}
    engines: {node: '>=0.10.0'}

  /source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  /spdx-correct@3.1.1:
    resolution: {integrity: sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w==}
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.12

  /spdx-exceptions@2.3.0:
    resolution: {integrity: sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A==}

  /spdx-expression-parse@3.0.1:
    resolution: {integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==}
    dependencies:
      spdx-exceptions: 2.3.0
      spdx-license-ids: 3.0.12

  /spdx-license-ids@3.0.12:
    resolution: {integrity: sha512-rr+VVSXtRhO4OHbXUiAF7xW3Bo9DuuF6C5jH+q/x15j2jniycgKbxU09Hr0WqlSLUs4i4ltHGXqTe7VHclYWyA==}

  /spdy-transport@3.0.0:
    resolution: {integrity: sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==}
    dependencies:
      debug: 4.3.4
      detect-node: 2.1.0
      hpack.js: 2.1.6
      obuf: 1.1.2
      readable-stream: 3.6.0
      wbuf: 1.7.3
    transitivePeerDependencies:
      - supports-color

  /spdy@4.0.2:
    resolution: {integrity: sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      debug: 4.3.4
      handle-thing: 2.0.1
      http-deceiver: 1.2.7
      select-hose: 2.0.0
      spdy-transport: 3.0.0
    transitivePeerDependencies:
      - supports-color

  /split-string@3.1.0:
    resolution: {integrity: sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2

  /sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}

  /sshpk@1.17.0:
    resolution: {integrity: sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5

  /ssri@6.0.2:
    resolution: {integrity: sha512-cepbSq/neFK7xB6A50KHN0xHDotYzq58wWCa5LeWqnPrHG8GzfEjO/4O8kpmcGW+oaxkvhEJCWgbgNk4/ZV93Q==}
    dependencies:
      figgy-pudding: 3.5.2

  /ssri@8.0.1:
    resolution: {integrity: sha512-97qShzy1AiyxvPNIkLWoGua7xoQzzPjQ0HAH4B0rWKo7SZ6USuPcrUiAFrws0UH8RrbWmgq3LMTObhPIHbbBeQ==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.4

  /stable@0.1.8:
    resolution: {integrity: sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==}
    deprecated: 'Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility'

  /stack-utils@1.0.5:
    resolution: {integrity: sha512-KZiTzuV3CnSnSvgMRrARVCj+Ht7rMbauGDK0LdVFRGyenwdylpajAp4Q0i6SX8rEmbTpMMf6ryq2gb8pPq2WgQ==}
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: 2.0.0

  /stackframe@1.3.4:
    resolution: {integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==}

  /standard-engine@12.1.0:
    resolution: {integrity: sha512-DVJnWM1CGkag4ucFLGdiYWa5/kJURPONmMmk17p8FT5NE4UnPZB1vxWnXnRo2sPSL78pWJG8xEM+1Tu19z0deg==}
    engines: {node: '>=8.10'}
    dependencies:
      deglob: 4.0.1
      get-stdin: 7.0.0
      minimist: 1.2.7
      pkg-conf: 3.1.0
    dev: true

  /standard@14.3.4:
    resolution: {integrity: sha512-+lpOkFssMkljJ6eaILmqxHQ2n4csuEABmcubLTb9almFi1ElDzXb1819fjf/5ygSyePCq4kU2wMdb2fBfb9P9Q==}
    engines: {node: '>=8.10.0'}
    hasBin: true
    dependencies:
      eslint: 6.8.0
      eslint-config-standard: 14.1.1(eslint-plugin-import@2.18.2)(eslint-plugin-node@10.0.0)(eslint-plugin-promise@4.2.1)(eslint-plugin-standard@4.0.2)(eslint@6.8.0)
      eslint-config-standard-jsx: 8.1.0(eslint-plugin-react@7.14.3)(eslint@6.8.0)
      eslint-plugin-import: 2.18.2(eslint@6.8.0)
      eslint-plugin-node: 10.0.0(eslint@6.8.0)
      eslint-plugin-promise: 4.2.1
      eslint-plugin-react: 7.14.3(eslint@6.8.0)
      eslint-plugin-standard: 4.0.2(eslint@6.8.0)
      standard-engine: 12.1.0
    transitivePeerDependencies:
      - '@typescript-eslint/parser'
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color
    dev: true

  /static-extend@0.1.2:
    resolution: {integrity: sha512-72E9+uLc27Mt718pMHt9VMNiAL4LMsmDbBva8mxWUCkT07fSzEGMYUCk0XWY6lp0j6RBAG4cJ3mWuZv2OE3s0g==}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 0.2.5
      object-copy: 0.1.0

  /statuses@1.5.0:
    resolution: {integrity: sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==}
    engines: {node: '>= 0.6'}

  /statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  /stealthy-require@1.1.1:
    resolution: {integrity: sha512-ZnWpYnYugiOVEY5GkcuJK1io5V8QmNYChG62gSit9pQVGErXtrKuPC55ITaVSukmMta5qpMU7vqLt2Lnni4f/g==}
    engines: {node: '>=0.10.0'}

  /store@2.0.12:
    resolution: {integrity: sha512-eO9xlzDpXLiMr9W1nQ3Nfp9EzZieIQc10zPPMP5jsVV7bLOziSFFBP0XoDXACEIFtdI+rIz0NwWVA/QVJ8zJtw==}
    dev: false

  /stream-browserify@2.0.2:
    resolution: {integrity: sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.7

  /stream-each@1.2.3:
    resolution: {integrity: sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw==}
    dependencies:
      end-of-stream: 1.4.4
      stream-shift: 1.0.1

  /stream-http@2.8.3:
    resolution: {integrity: sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw==}
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 2.3.7
      to-arraybuffer: 1.0.1
      xtend: 4.0.2

  /stream-shift@1.0.1:
    resolution: {integrity: sha512-AiisoFqQ0vbGcZgQPY1cdP2I76glaVA/RauYR4G4thNFgkTqr90yXTo4LYX60Jl+sIlPNHHdGSwo01AvbKUSVQ==}

  /strict-uri-encode@1.1.0:
    resolution: {integrity: sha512-R3f198pcvnB+5IpnBlRkphuE9n46WyVl8I39W/ZUTZLz4nqSP/oLYUrcnJrw462Ds8he4YKMov2efsTIw1BDGQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /string-length@2.0.0:
    resolution: {integrity: sha512-Qka42GGrS8Mm3SZ+7cH8UXiIWI867/b/Z/feQSpQx/rbfB8UGknGEZVaUQMOUVj+soY6NpWAxily63HI1OckVQ==}
    engines: {node: '>=4'}
    dependencies:
      astral-regex: 1.0.0
      strip-ansi: 4.0.0

  /string-length@3.1.0:
    resolution: {integrity: sha512-Ttp5YvkGm5v9Ijagtaz1BnN+k9ObpvS0eIBblPMp2YWL8FBmi9qblQ9fexc2k/CXFgrTIteU3jAw3payCnwSTA==}
    engines: {node: '>=8'}
    dependencies:
      astral-regex: 1.0.0
      strip-ansi: 5.2.0
    dev: true

  /string-width@1.0.2:
    resolution: {integrity: sha512-0XsVpQLnVCXHJfyEs8tC0zpTVIr5PKKsQtkT29IwupnPTjtPmQ3xT/4yCREF9hYkV/3M3kzcUTSAZT6a6h81tw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      code-point-at: 1.1.0
      is-fullwidth-code-point: 1.0.0
      strip-ansi: 3.0.1
    dev: true

  /string-width@2.1.1:
    resolution: {integrity: sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==}
    engines: {node: '>=4'}
    dependencies:
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 4.0.0

  /string-width@3.1.0:
    resolution: {integrity: sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==}
    engines: {node: '>=6'}
    dependencies:
      emoji-regex: 7.0.3
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 5.2.0

  /string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  /string.prototype.trimend@1.0.6:
    resolution: {integrity: sha512-JySq+4mrPf9EsDBEDYMOb/lM7XQLulwg5R/m1r0PXEFqrV0qHvl58sdTilSXtKOflCsK2E8jxf+GKC0T07RWwQ==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4

  /string.prototype.trimstart@1.0.6:
    resolution: {integrity: sha512-omqjMDaY92pbn5HOX7f9IccLA+U1tA9GvtU4JrodiXFfYB7jPzzHpRzpglLAjtUV6bB557zwClJezTqnAiYnQA==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      es-abstract: 1.20.4

  /string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}
    dependencies:
      safe-buffer: 5.1.2

  /string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}
    dependencies:
      safe-buffer: 5.2.1

  /strip-ansi@3.0.1:
    resolution: {integrity: sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: true

  /strip-ansi@4.0.0:
    resolution: {integrity: sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow==}
    engines: {node: '>=4'}
    dependencies:
      ansi-regex: 3.0.1

  /strip-ansi@5.2.0:
    resolution: {integrity: sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==}
    engines: {node: '>=6'}
    dependencies:
      ansi-regex: 4.1.1

  /strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1

  /strip-bom@3.0.0:
    resolution: {integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==}
    engines: {node: '>=4'}

  /strip-eof@1.0.0:
    resolution: {integrity: sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q==}
    engines: {node: '>=0.10.0'}

  /strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}

  /strip-json-comments@2.0.1:
    resolution: {integrity: sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  /style-loader@1.3.0(webpack@4.46.0):
    resolution: {integrity: sha512-V7TCORko8rs9rIqkSrlMfkqA63DfoGBBJmK1kKGCcSi+BWb4cqz0SRsnp4l6rU5iwOEd0/2ePv68SV22VXon4Q==}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 2.7.1
      webpack: 4.46.0(webpack-cli@4.10.0)
    dev: true

  /stylehacks@4.0.3:
    resolution: {integrity: sha512-7GlLk9JwlElY4Y6a/rmbH2MhVlTyVmiJd1PfTCqFaIBEGMYNsrO/v3SeGTdhBThLg4Z+NbOk/qFMwCa+J+3p/g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.4
      postcss: 7.0.39
      postcss-selector-parser: 3.1.2
    dev: true

  /stylehacks@5.1.1(postcss@8.4.19):
    resolution: {integrity: sha512-sBpcd5Hx7G6seo7b1LkpttvTz7ikD0LlH5RmdcBNb6fFR0Fl7LQwHDFr300q4cwUqi+IYrFGmsIHieMBfnN/Bw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.21.4
      postcss: 8.4.19
      postcss-selector-parser: 6.0.10

  /supports-color@2.0.0:
    resolution: {integrity: sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==}
    engines: {node: '>=0.8.0'}
    dev: true

  /supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0

  /supports-color@6.1.0:
    resolution: {integrity: sha512-qe1jfm1Mg7Nq/NSh6XE24gPXROEVsWHxC1LIx//XNlD9iw7YZQGjZNjYN7xGaEG6iKdA8EtNFW6R0gjnVXp+wQ==}
    engines: {node: '>=6'}
    dependencies:
      has-flag: 3.0.0

  /supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0

  /supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}
    dependencies:
      has-flag: 4.0.0

  /supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  /svgo@1.3.2:
    resolution: {integrity: sha512-yhy/sQYxR5BkC98CY7o31VGsg014AKLEPxdfhora76l36hD9Rdy5NZA/Ocn6yayNPgSamYdtX2rFJdcv07AYVw==}
    engines: {node: '>=4.0.0'}
    deprecated: This SVGO version is no longer supported. Upgrade to v2.x.x.
    hasBin: true
    dependencies:
      chalk: 2.4.2
      coa: 2.0.2
      css-select: 2.1.0
      css-select-base-adapter: 0.1.1
      css-tree: 1.0.0-alpha.37
      csso: 4.2.0
      js-yaml: 3.14.1
      mkdirp: 0.5.6
      object.values: 1.1.6
      sax: 1.2.4
      stable: 0.1.8
      unquote: 1.1.1
      util.promisify: 1.0.1
    dev: true

  /svgo@2.8.0:
    resolution: {integrity: sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 4.3.0
      css-tree: 1.1.3
      csso: 4.2.0
      picocolors: 1.0.0
      stable: 0.1.8

  /symbol-observable@1.2.0:
    resolution: {integrity: sha512-e900nM8RRtGhlV36KGEU9k65K3mPb1WV70OdjfxlG2EAuM1noi/E/BaW/uMhL7bPEssK8QV57vN3esixjUvcXQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /symbol-tree@3.2.4:
    resolution: {integrity: sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==}

  /table@5.4.6:
    resolution: {integrity: sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug==}
    engines: {node: '>=6.0.0'}
    dependencies:
      ajv: 6.12.6
      lodash: 4.17.21
      slice-ansi: 2.1.0
      string-width: 3.1.0

  /tapable@1.1.3:
    resolution: {integrity: sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA==}
    engines: {node: '>=6'}

  /tapable@2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==}
    engines: {node: '>=6'}

  /tar-fs@2.1.1:
    resolution: {integrity: sha512-V0r2Y9scmbDRLCNex/+hYzvp/zyYjvFbHPNgVTKfQvVrb6guiE/fxP+XblDNR011utopbkex2nM4dHNV6GDsng==}
    dependencies:
      chownr: 1.1.4
      mkdirp-classic: 0.5.3
      pump: 3.0.0
      tar-stream: 2.2.0
    dev: true
    optional: true

  /tar-stream@2.2.0:
    resolution: {integrity: sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==}
    engines: {node: '>=6'}
    dependencies:
      bl: 4.1.0
      end-of-stream: 1.4.4
      fs-constants: 1.0.0
      inherits: 2.0.4
      readable-stream: 3.6.0
    dev: true
    optional: true

  /tar@6.1.12:
    resolution: {integrity: sha512-jU4TdemS31uABHd+Lt5WEYJuzn+TJTCBLljvIAHZOz6M9Os5pJ4dD+vRFLxPa/n3T0iEFzpi+0x1UfuDZYbRMw==}
    engines: {node: '>=10'}
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 3.3.4
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0
    dev: true

  /terser-webpack-plugin@1.4.5(webpack@4.46.0):
    resolution: {integrity: sha512-04Rfe496lN8EYruwi6oPQkG0vo8C+HT49X687FZnpPF0qMAIHONI6HEXYPKDOE8e5HjXTyKfqRd/agHtH0kOtw==}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      cacache: 12.0.4
      find-cache-dir: 2.1.0
      is-wsl: 1.1.0
      schema-utils: 1.0.0
      serialize-javascript: 4.0.0
      source-map: 0.6.1
      terser: 4.8.1
      webpack: 4.46.0(webpack-cli@4.10.0)
      webpack-sources: 1.4.3
      worker-farm: 1.7.0

  /terser-webpack-plugin@5.3.6(webpack@5.75.0):
    resolution: {integrity: sha512-kfLFk+PoLUQIbLmB1+PZDMRSZS99Mp+/MHqDNmMA6tOItzRt+Npe3E+fsMs5mfcM0wCtrrdU387UnV+vnSffXQ==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true
    dependencies:
      '@jridgewell/trace-mapping': 0.3.17
      jest-worker: 27.5.1
      schema-utils: 3.1.1
      serialize-javascript: 6.0.0
      terser: 5.15.1
      webpack: 5.75.0(webpack-cli@4.10.0)

  /terser@4.8.1:
    resolution: {integrity: sha512-4GnLC0x667eJG0ewJTa6z/yXrbLGv80D9Ru6HIpCQmO+Q4PfEtBFi0ObSckqwL6VyQv/7ENJieXHo2ANmdQwgw==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      acorn: 8.8.1
      commander: 2.20.3
      source-map: 0.6.1
      source-map-support: 0.5.21

  /terser@5.15.1:
    resolution: {integrity: sha512-K1faMUvpm/FBxjBXud0LWVAGxmvoPbZbfTCYbSgaaYQaIXI3/TdI7a7ZGA73Zrou6Q8Zmz3oeUTsp/dj+ag2Xw==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@jridgewell/source-map': 0.3.2
      acorn: 8.8.1
      commander: 2.20.3
      source-map-support: 0.5.21

  /test-exclude@5.2.3:
    resolution: {integrity: sha512-M+oxtseCFO3EDtAaGH7iiej3CBkzXqFMbzqYAACdzKui4eZA+pq3tZEwChvOdNfa7xxy8BfbmgJSIr43cC/+2g==}
    engines: {node: '>=6'}
    dependencies:
      glob: 7.2.3
      minimatch: 3.1.2
      read-pkg-up: 4.0.0
      require-main-filename: 2.0.0

  /text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}

  /textarea-caret@3.1.0:
    resolution: {integrity: sha512-cXAvzO9pP5CGa6NKx0WYHl+8CHKZs8byMkt3PCJBCmq2a34YA9pO1NrQET5pzeqnBjBdToF5No4rrmkDUgQC2Q==}
    dev: false

  /thenify-all@1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}
    dependencies:
      thenify: 3.3.1

  /thenify@3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}
    dependencies:
      any-promise: 1.3.0

  /thread-loader@3.0.4(webpack@5.75.0):
    resolution: {integrity: sha512-ByaL2TPb+m6yArpqQUZvP+5S1mZtXsEP7nWKKlAUTm7fCml8kB5s1uI3+eHRP2bk5mVYfRSBI7FFf+tWEyLZwA==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.27.0 || ^5.0.0
    dependencies:
      json-parse-better-errors: 1.0.2
      loader-runner: 4.3.0
      loader-utils: 2.0.4
      neo-async: 2.6.2
      schema-utils: 3.1.1
      webpack: 5.75.0(webpack-cli@4.10.0)

  /throat@4.1.0:
    resolution: {integrity: sha512-wCVxLDcFxw7ujDxaeJC6nfl2XfHJNYs8yUYJnvMgtPEFlttP9tHSfRUv2vBe6C4hkVFPWoP1P6ZccbYjmSEkKA==}

  /throttleit@1.0.0:
    resolution: {integrity: sha512-rkTVqu6IjfQ/6+uNuuc3sZek4CEYxTJom3IktzgdSxcZqdARuebbA/f4QmAxMQIxqq9ZLEUkSYqvuk1I6VKq4g==}
    dev: true

  /through2@2.0.5:
    resolution: {integrity: sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==}
    dependencies:
      readable-stream: 2.3.7
      xtend: 4.0.2

  /through@2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==}

  /thunky@1.1.0:
    resolution: {integrity: sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==}

  /timers-browserify@2.0.12:
    resolution: {integrity: sha512-9phl76Cqm6FhSX9Xe1ZUAMLtm1BLkKj2Qd5ApyWkXzsMRaA7dgr81kf4wJmQf/hAvg8EEyJxDo3du/0KlhPiKQ==}
    engines: {node: '>=0.6.0'}
    dependencies:
      setimmediate: 1.0.5

  /timsort@0.3.0:
    resolution: {integrity: sha512-qsdtZH+vMoCARQtyod4imc2nIJwg9Cc7lPRrw9CzF8ZKR0khdr8+2nX80PBhET3tcyTtJDxAffGh2rXH4tyU8A==}
    dev: true

  /tmp@0.0.33:
    resolution: {integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      os-tmpdir: 1.0.2

  /tmp@0.2.1:
    resolution: {integrity: sha512-76SUhtfqR2Ijn+xllcI5P1oyannHNHByD80W1q447gU3mp9G9PSpGdWmjUOHRDPiHYacIk66W7ubDTuPF3BEtQ==}
    engines: {node: '>=8.17.0'}
    dependencies:
      rimraf: 3.0.2
    dev: true

  /tmpl@1.0.5:
    resolution: {integrity: sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==}

  /to-arraybuffer@1.0.1:
    resolution: {integrity: sha512-okFlQcoGTi4LQBG/PgSYblw9VOyptsz2KJZqc6qtgGdes8VktzUQkj4BI2blit072iS8VODNcMA+tvnS9dnuMA==}

  /to-fast-properties@1.0.3:
    resolution: {integrity: sha512-lxrWP8ejsq+7E3nNjwYmUBMAgjMTZoTI+sdBOpvNyijeDLa29LUn9QaoXAHv4+Z578hbmHHJKZknzxVtvo77og==}
    engines: {node: '>=0.10.0'}
    dev: true

  /to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}

  /to-object-path@0.3.0:
    resolution: {integrity: sha512-9mWHdnGRuh3onocaHzukyvCZhzvr6tiflAy/JRFXcJX0TjgfWA9pk9t8CMbzmBE4Jfw58pXbkngtBtqYxzNEyg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2

  /to-regex-range@2.1.1:
    resolution: {integrity: sha512-ZZWNfCjUokXXDGXFpZehJIkZqq91BcULFq/Pi7M5i4JnxXdhMKAK682z8bCW3o8Hj1wuuzoKcW3DfVzaP6VuNg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      repeat-string: 1.6.1

  /to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0

  /to-regex@3.0.2:
    resolution: {integrity: sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 2.0.2
      extend-shallow: 3.0.2
      regex-not: 1.0.2
      safe-regex: 1.1.0

  /toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  /totalist@1.1.0:
    resolution: {integrity: sha512-gduQwd1rOdDMGxFG1gEvhV88Oirdo2p+KjoYFU7k2g+i7n6AFFbDQ5kMPUsW0pNbfQsB/cwXvT1i4Bue0s9g5g==}
    engines: {node: '>=6'}

  /tough-cookie@2.5.0:
    resolution: {integrity: sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==}
    engines: {node: '>=0.8'}
    dependencies:
      psl: 1.9.0
      punycode: 2.1.1

  /tough-cookie@3.0.1:
    resolution: {integrity: sha512-yQyJ0u4pZsv9D4clxO69OEjLWYw+jbgspjTue4lTQZLfV0c5l1VmK2y1JK8E9ahdpltPOaAThPcp5nKPUgSnsg==}
    engines: {node: '>=6'}
    dependencies:
      ip-regex: 2.1.0
      psl: 1.9.0
      punycode: 2.1.1
    dev: true

  /tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  /tr46@1.0.1:
    resolution: {integrity: sha512-dTpowEjclQ7Kgx5SdBkqRzVhERQXov8/l9Ft9dVM9fmg0W0KQSVaXX9T4i6twCPNtYiZM53lpSSUAwJbFPOHxA==}
    dependencies:
      punycode: 2.1.1

  /ts-jest@24.3.0(jest@24.9.0):
    resolution: {integrity: sha512-Hb94C/+QRIgjVZlJyiWwouYUF+siNJHJHknyspaOcZ+OQAIdFG/UrdQVXw/0B8Z3No34xkUXZJpOTy9alOWdVQ==}
    engines: {node: '>= 6'}
    hasBin: true
    peerDependencies:
      jest: '>=24 <25'
    dependencies:
      bs-logger: 0.2.6
      buffer-from: 1.1.2
      fast-json-stable-stringify: 2.1.0
      jest: 24.9.0
      json5: 2.2.1
      lodash.memoize: 4.1.2
      make-error: 1.3.6
      mkdirp: 0.5.6
      resolve: 1.22.1
      semver: 5.7.1
      yargs-parser: 10.1.0
    dev: true

  /tsconfig@7.0.0:
    resolution: {integrity: sha512-vZXmzPrL+EmC4T/4rVlT2jNVMWCi/O4DIiSj3UHg1OE5kCKbk4mfrXc6dZksLgRM/TZlKnousKH9bbTazUWRRw==}
    dependencies:
      '@types/strip-bom': 3.0.0
      '@types/strip-json-comments': 0.0.30
      strip-bom: 3.0.0
      strip-json-comments: 2.0.1
    dev: true

  /tslib@1.14.1:
    resolution: {integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==}

  /tslib@2.4.1:
    resolution: {integrity: sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==}

  /tsutils@3.21.0(typescript@4.8.4):
    resolution: {integrity: sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==}
    engines: {node: '>= 6'}
    peerDependencies:
      typescript: '>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta'
    dependencies:
      tslib: 1.14.1
      typescript: 4.8.4
    dev: false

  /tty-browserify@0.0.0:
    resolution: {integrity: sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=}

  /tunnel-agent@0.6.0:
    resolution: {integrity: sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==}
    dependencies:
      safe-buffer: 5.2.1

  /tweetnacl@0.14.5:
    resolution: {integrity: sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==}

  /type-check@0.3.2:
    resolution: {integrity: sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2

  /type-fest@0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}

  /type-fest@0.3.1:
    resolution: {integrity: sha512-cUGJnCdr4STbePCgqNFbpVNCepa+kAVohJs1sLhxzdH+gnEoOd8VhbYa7pD3zZYGiURWM2xzEII3fQcRizDkYQ==}
    engines: {node: '>=6'}
    dev: true

  /type-fest@0.6.0:
    resolution: {integrity: sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==}
    engines: {node: '>=8'}

  /type-fest@0.8.1:
    resolution: {integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==}
    engines: {node: '>=8'}

  /type-is@1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  /typedarray@0.0.6:
    resolution: {integrity: sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==}

  /typescript@4.8.4:
    resolution: {integrity: sha512-QCh+85mCy+h0IGff8r5XWzOVSbBO+KfeYrMQh7NJ58QujwcE22u+NUSmUxqF+un70P9GXKxa2HCNiTTMJknyjQ==}
    engines: {node: '>=4.2.0'}
    hasBin: true
    dev: false

  /unbox-primitive@1.0.2:
    resolution: {integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==}
    dependencies:
      call-bind: 1.0.2
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2

  /unicode-canonical-property-names-ecmascript@2.0.0:
    resolution: {integrity: sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==}
    engines: {node: '>=4'}
    dev: true

  /unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==}
    engines: {node: '>=4'}
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.0
      unicode-property-aliases-ecmascript: 2.1.0
    dev: true

  /unicode-match-property-value-ecmascript@2.0.0:
    resolution: {integrity: sha512-7Yhkc0Ye+t4PNYzOGKedDhXbYIBe1XEQYQxOPyhcXNMJ0WCABqqj6ckydd6pWRZTHV4GuCPKdBAUiMc60tsKVw==}
    engines: {node: '>=4'}
    dev: true

  /unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}
    dev: true

  /union-value@1.0.1:
    resolution: {integrity: sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      set-value: 2.0.1

  /uniq@1.0.1:
    resolution: {integrity: sha512-Gw+zz50YNKPDKXs+9d+aKAjVwpjNwqzvNpLigIruT4HA9lMZNdMqs9x07kKHB/L9WRzqp4+DlTU5s4wG2esdoA==}
    dev: true

  /uniqs@2.0.0:
    resolution: {integrity: sha512-mZdDpf3vBV5Efh29kMw5tXoup/buMgxLzOt/XKFKcVmi+15ManNQWr6HfZ2aiZTYlYixbdNJ0KFmIZIv52tHSQ==}
    dev: true

  /unique-filename@1.1.1:
    resolution: {integrity: sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==}
    dependencies:
      unique-slug: 2.0.2

  /unique-slug@2.0.2:
    resolution: {integrity: sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==}
    dependencies:
      imurmurhash: 0.1.4

  /universalify@2.0.0:
    resolution: {integrity: sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==}
    engines: {node: '>= 10.0.0'}

  /unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}

  /unquote@1.1.1:
    resolution: {integrity: sha512-vRCqFv6UhXpWxZPyGDh/F3ZpNv8/qo7w6iufLpQg9aKnQ71qM4B5KiI7Mia9COcjEhrO9LueHpMYjYzsWH3OIg==}
    dev: true

  /unset-value@1.0.0:
    resolution: {integrity: sha512-PcA2tsuGSF9cnySLHTLSh2qrQiJ70mn+r+Glzxv2TWZblxsxCC52BDlZoPCsz7STd9pN7EZetkWZBAvk4cgZdQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      has-value: 0.3.1
      isobject: 3.0.1

  /untildify@4.0.0:
    resolution: {integrity: sha512-KK8xQ1mkzZeg9inewmFVDNkg3l5LUhoq9kN6iWYB/CC9YMG8HA+c1Q8HwDe6dEX7kErrEVNVBO3fWsVq5iDgtw==}
    engines: {node: '>=8'}
    dev: true

  /upath@1.2.0:
    resolution: {integrity: sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg==}
    engines: {node: '>=4'}
    optional: true

  /update-browserslist-db@1.0.10(browserslist@4.21.4):
    resolution: {integrity: sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.21.4
      escalade: 3.1.1
      picocolors: 1.0.0

  /uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    dependencies:
      punycode: 2.1.1

  /urix@0.1.0:
    resolution: {integrity: sha512-Am1ousAhSLBeB9cG/7k7r2R0zj50uDRlZHPGbazid5s9rlF1F/QKYObEKSIunSjIOkJZqwRRLpvewjEkM7pSqg==}
    deprecated: Please see https://github.com/lydell/urix#deprecated

  /url-parse@1.5.10:
    resolution: {integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==}
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0
    dev: false

  /url@0.11.0:
    resolution: {integrity: sha512-kbailJa29QrtXnxgq+DdCEGlbTeYM2eJUxsz6vjZavrCYPMIFHMKQmSKYAIuUK2i7hgPm28a8piX5NTUtM/LKQ==}
    dependencies:
      punycode: 1.3.2
      querystring: 0.2.0

  /use@3.1.1:
    resolution: {integrity: sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ==}
    engines: {node: '>=0.10.0'}

  /util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  /util.promisify@1.0.1:
    resolution: {integrity: sha512-g9JpC/3He3bm38zsLupWryXHoEcS22YHthuPQSJdMy6KNrzIRzWqcsHzD/WUnqe45whVou4VIsPew37DoXWNrA==}
    dependencies:
      define-properties: 1.1.4
      es-abstract: 1.20.4
      has-symbols: 1.0.3
      object.getownpropertydescriptors: 2.1.5
    dev: true

  /util.promisify@1.1.1:
    resolution: {integrity: sha512-/s3UsZUrIfa6xDhr7zZhnE9SLQ5RIXyYfiVnMMyMDzOc8WhWN4Nbh36H842OyurKbCDAesZOJaVyvmSl6fhGQw==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.1.4
      for-each: 0.3.3
      has-symbols: 1.0.3
      object.getownpropertydescriptors: 2.1.5

  /util@0.10.3:
    resolution: {integrity: sha512-5KiHfsmkqacuKjkRkdV7SsfDJ2EGiPsK92s2MhNSY0craxjTdKTtqKsJaCWp4LW33ZZ0OPUv1WO/TFvNQRiQxQ==}
    dependencies:
      inherits: 2.0.1

  /util@0.11.1:
    resolution: {integrity: sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ==}
    dependencies:
      inherits: 2.0.3

  /utila@0.4.0:
    resolution: {integrity: sha512-Z0DbgELS9/L/75wZbro8xAnT50pBVFQZ+hUEueGDU5FN51YSCYM+jdxsfCiHjwNP/4LCDD0i/graKpeBnOXKRA==}

  /utils-merge@1.0.1:
    resolution: {integrity: sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=}
    engines: {node: '>= 0.4.0'}

  /uuid@3.4.0:
    resolution: {integrity: sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  /uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true

  /v-calendar@2.4.1(@popperjs/core@2.11.6)(vue@2.7.14):
    resolution: {integrity: sha512-nhzOlHM2cinv+8jIcnAx+nTo63U40szv3Ig41uLMpGK1U5sApgCP6ggigprsnlMOM5VRq1G/1B8rNHkRrLbGjw==}
    peerDependencies:
      '@popperjs/core': ^2.4.0
      vue: ^2.5.18
    dependencies:
      '@popperjs/core': 2.11.6
      core-js: 3.26.0
      date-fns: 2.29.3
      date-fns-tz: 1.3.7(date-fns@2.29.3)
      lodash: 4.17.21
      vue: 2.7.14
    dev: false

  /v-tooltip@2.1.3(vue@2.7.14):
    resolution: {integrity: sha512-xXngyxLQTOx/yUEy50thb8te7Qo4XU6h4LZB6cvEfVd9mnysUxLEoYwGWDdqR+l69liKsy3IPkdYff3J1gAJ5w==}
    dependencies:
      '@babel/runtime': 7.20.1
      lodash: 4.17.21
      popper.js: 1.16.1
      vue-resize: 1.0.1(vue@2.7.14)
    transitivePeerDependencies:
      - vue
    dev: false

  /v8-compile-cache@2.3.0:
    resolution: {integrity: sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA==}

  /validate-npm-package-license@3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==}
    dependencies:
      spdx-correct: 3.1.1
      spdx-expression-parse: 3.0.1

  /vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  /vee-validate@3.4.14(vue@2.7.14):
    resolution: {integrity: sha512-Hqqic8G9WcRSIzCxiCPqMZv4qB8JE1lIQqIOLDm2K5BXUiL8d4a2+kqkanv8gQSGDzYpnCQZ7BO/T99Aj05T1Q==}
    peerDependencies:
      vue: ^2.5.18
    dependencies:
      vue: 2.7.14
    dev: false

  /vendors@1.0.4:
    resolution: {integrity: sha512-/juG65kTL4Cy2su4P8HjtkTxk6VmJDiOPBufWniqQ6wknac6jNiXS9vU+hO3wgusiyqWlzTbVHi0dyJqRONg3w==}
    dev: true

  /verror@1.10.0:
    resolution: {integrity: sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=}
    engines: {'0': node >=0.6.0}
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0

  /vm-browserify@1.1.2:
    resolution: {integrity: sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ==}

  /vue-class-component@7.2.6(vue@2.7.14):
    resolution: {integrity: sha512-+eaQXVrAm/LldalI272PpDe3+i4mPis0ORiMYxF6Ae4hyuCh15W8Idet7wPUEs4N4YptgFHGys4UrgNQOMyO6w==}
    peerDependencies:
      vue: ^2.0.0
    dependencies:
      vue: 2.7.14
    dev: false

  /vue-content-loader@0.2.3:
    resolution: {integrity: sha512-gJlNEdXkuHGvgnyY0lBMsrSsOMk+TTog5uNAil5MSLv08f/mE7Apag0VavpxJ6ieb4P5J1iVKEIhHI41HQNq9Q==}
    dependencies:
      babel-helper-vue-jsx-merge-props: 2.0.3
    dev: false

  /vue-currency-filter@5.2.0:
    resolution: {integrity: sha512-31XXEtXLDf3A7mdIAoyEkZTwpti22omp4MjoVKv0ksIC8Gk9RqW5xzeqvubeIlrxACvCPjKNTwqTmUrxFplDHw==}
    dev: false

  /vue-eslint-parser@7.11.0(eslint@6.8.0):
    resolution: {integrity: sha512-qh3VhDLeh773wjgNTl7ss0VejY9bMMa0GoDG2fQVyDzRFdiU3L7fw74tWZDHNQXdZqxO3EveQroa9ct39D2nqg==}
    engines: {node: '>=8.10'}
    peerDependencies:
      eslint: '>=5.0.0'
    dependencies:
      debug: 4.3.4
      eslint: 6.8.0
      eslint-scope: 5.1.1
      eslint-visitor-keys: 1.3.0
      espree: 6.2.1
      esquery: 1.4.0
      lodash: 4.17.21
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /vue-form-wizard@0.8.4:
    resolution: {integrity: sha512-/Zk1+B7bz7qHFJ16mwu021lpPXWf/9Tlr2mTNG3J7M0Hdy3rgA802lWsbKYySns0B0qtsD8BYGjQ2Wyxgg+4uw==}
    engines: {node: '>= 4.0.0', npm: '>= 3.0.0'}
    dev: false

  /vue-functional-data-merge@3.1.0:
    resolution: {integrity: sha512-leT4kdJVQyeZNY1kmnS1xiUlQ9z1B/kdBFCILIjYYQDqZgLqCLa0UhjSSeRX6c3mUe6U5qYeM8LrEqkHJ1B4LA==}
    dev: false

  /vue-hot-reload-api@2.3.4:
    resolution: {integrity: sha512-BXq3jwIagosjgNVae6tkHzzIk6a8MHFtzAdwhnV5VlvPTFxDCvIttgSiHWjdGoTJvXtmRu5HacExfdarRcFhog==}

  /vue-jest@3.0.7(babel-core@7.0.0-bridge.0)(vue-template-compiler@2.7.14)(vue@2.7.14):
    resolution: {integrity: sha512-PIOxFM+wsBMry26ZpfBvUQ/DGH2hvp5khDQ1n51g3bN0TwFwTy4J85XVfxTRMukqHji/GnAoGUnlZ5Ao73K62w==}
    peerDependencies:
      babel-core: ^6.25.0 || ^7.0.0-0
      vue: ^2.x
      vue-template-compiler: ^2.x
    dependencies:
      babel-core: 7.0.0-bridge.0(@babel/core@7.20.2)
      babel-plugin-transform-es2015-modules-commonjs: 6.26.2
      chalk: 2.4.2
      deasync: 0.1.28
      extract-from-css: 0.4.4
      find-babel-config: 1.2.0
      js-beautify: 1.14.7
      node-cache: 4.2.1
      object-assign: 4.1.1
      source-map: 0.5.7
      tsconfig: 7.0.0
      vue: 2.7.14
      vue-template-compiler: 2.7.14
      vue-template-es2015-compiler: 1.9.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /vue-json-pretty@1.9.3:
    resolution: {integrity: sha512-b13DP1WGQ+ACUU2K5hmwFfHrHnydCFSTerE7fppeYMojSWN/5EOPODQECfIIRaJ7zzHtPW9OifkThFGPyY0xRg==}
    engines: {node: '>= 10.0.0', npm: '>= 5.0.0'}
    dev: false

  /vue-loader@15.10.0(babel-core@7.0.0-bridge.0)(css-loader@3.6.0)(lodash@4.17.21)(vue-template-compiler@2.7.14)(webpack@4.46.0):
    resolution: {integrity: sha512-VU6tuO8eKajrFeBzMssFUP9SvakEeeSi1BxdTH5o3+1yUyrldp8IERkSdXlMI2t4kxF2sqYUDsQY+WJBxzBmZg==}
    peerDependencies:
      '@vue/compiler-sfc': ^3.0.8
      cache-loader: '*'
      css-loader: '*'
      vue-template-compiler: '*'
      webpack: ^3.0.0 || ^4.1.0 || ^5.0.0-0
    peerDependenciesMeta:
      '@vue/compiler-sfc':
        optional: true
      cache-loader:
        optional: true
      vue-template-compiler:
        optional: true
    dependencies:
      '@vue/component-compiler-utils': 3.3.0(babel-core@7.0.0-bridge.0)(lodash@4.17.21)
      css-loader: 3.6.0(webpack@4.46.0)
      hash-sum: 1.0.2
      loader-utils: 1.4.2
      vue-hot-reload-api: 2.3.4
      vue-style-loader: 4.1.3
      vue-template-compiler: 2.7.14
      webpack: 4.46.0(webpack-cli@4.10.0)
    transitivePeerDependencies:
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - underscore
      - vash
      - velocityjs
      - walrus
      - whiskers
    dev: true

  /vue-loader@15.10.0(babel-core@7.0.0-bridge.0)(css-loader@6.7.1)(lodash@4.17.21)(vue-template-compiler@2.7.14)(webpack@5.75.0):
    resolution: {integrity: sha512-VU6tuO8eKajrFeBzMssFUP9SvakEeeSi1BxdTH5o3+1yUyrldp8IERkSdXlMI2t4kxF2sqYUDsQY+WJBxzBmZg==}
    peerDependencies:
      '@vue/compiler-sfc': ^3.0.8
      cache-loader: '*'
      css-loader: '*'
      vue-template-compiler: '*'
      webpack: ^3.0.0 || ^4.1.0 || ^5.0.0-0
    peerDependenciesMeta:
      '@vue/compiler-sfc':
        optional: true
      cache-loader:
        optional: true
      vue-template-compiler:
        optional: true
    dependencies:
      '@vue/component-compiler-utils': 3.3.0(babel-core@7.0.0-bridge.0)(lodash@4.17.21)
      css-loader: 6.7.1(webpack@5.75.0)
      hash-sum: 1.0.2
      loader-utils: 1.4.2
      vue-hot-reload-api: 2.3.4
      vue-style-loader: 4.1.3
      vue-template-compiler: 2.7.14
      webpack: 5.75.0(webpack-cli@4.10.0)
    transitivePeerDependencies:
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - underscore
      - vash
      - velocityjs
      - walrus
      - whiskers

  /vue-loader@17.0.1(vue@2.7.14)(webpack@5.75.0):
    resolution: {integrity: sha512-/OOyugJnImKCkAKrAvdsWMuwoCqGxWT5USLsjohzWbMgOwpA5wQmzQiLMzZd7DjhIfunzAGIApTOgIylz/kwcg==}
    peerDependencies:
      '@vue/compiler-sfc': '*'
      vue: '*'
      webpack: ^4.1.0 || ^5.0.0-0
    peerDependenciesMeta:
      '@vue/compiler-sfc':
        optional: true
      vue:
        optional: true
    dependencies:
      chalk: 4.1.2
      hash-sum: 2.0.0
      loader-utils: 2.0.4
      vue: 2.7.14
      webpack: 5.75.0(webpack-cli@4.10.0)

  /vue-lodash-filters@1.3.2:
    resolution: {integrity: sha512-q/fZUqnwFUzLKBuowWSS3OQIamIeJJRJjHECWlYqkaRLi755ddyISRaR5fsSJ4Vi4PzZeie1eof1LKUAZRbnGw==}
    dependencies:
      lodash: 4.17.21
      vue: 2.7.14
      vue-template-compiler: 2.7.14
    dev: false

  /vue-mention@1.1.0(v-tooltip@2.1.3)(vue@2.7.14):
    resolution: {integrity: sha512-yuFewv8i70qIL5HWdi9UHdwXAxeis1RR/FN00GQ0P7LJeqkh8YD9CWIjnD2owO1yLjgFol7t9uvax3rsl+oigg==}
    peerDependencies:
      v-tooltip: ^2.0.0
      vue: ^2.6.0
    dependencies:
      textarea-caret: 3.1.0
      v-tooltip: 2.1.3(vue@2.7.14)
      vue: 2.7.14
    dev: false

  /vue-moment@4.1.0(vue@2.7.14):
    resolution: {integrity: sha512-Gzisqpg82ItlrUyiD9d0Kfru+JorW2o4mQOH06lEDZNgxci0tv/fua1Hl0bo4DozDV2JK1r52Atn/8QVCu8qQw==}
    peerDependencies:
      vue: '>=1.x.x'
    dependencies:
      moment: 2.29.4
      vue: 2.7.14
    dev: false

  /vue-multiselect@2.1.6:
    resolution: {integrity: sha512-s7jmZPlm9FeueJg1RwJtnE9KNPtME/7C8uRWSfp9/yEN4M8XcS/d+bddoyVwVnvFyRh9msFo0HWeW0vTL8Qv+w==}
    engines: {node: '>= 4.0.0', npm: '>= 3.0.0'}
    dev: false

  /vue-plugin-load-script@1.3.6:
    resolution: {integrity: sha512-O+mpw32dXY3tMBBNKm7/qIByJV1QbHJ+0CXI4GeXx4NHU/Rg+bv7bvzugkEWnYcB/43WvR8ZD2K9KQIeVng1bA==}
    dev: true

  /vue-resize@1.0.1(vue@2.7.14):
    resolution: {integrity: sha512-z5M7lJs0QluJnaoMFTIeGx6dIkYxOwHThlZDeQnWZBizKblb99GSejPnK37ZbNE/rVwDcYcHY+Io+AxdpY952w==}
    peerDependencies:
      vue: ^2.6.0
    dependencies:
      '@babel/runtime': 7.20.1
      vue: 2.7.14
    dev: false

  /vue-router@3.6.5(vue@2.7.14):
    resolution: {integrity: sha512-VYXZQLtjuvKxxcshuRAwjHnciqZVoXAjTjcqBTz4rKc8qih9g9pI3hbDjmqXaHdgL3v8pV6P8Z335XvHzESxLQ==}
    peerDependencies:
      vue: ^2
    dependencies:
      vue: 2.7.14
    dev: false

  /vue-star-rating@1.7.0:
    resolution: {integrity: sha512-2qdONqnIlvFzJoallfr7VuFRB6hBzYgBVEbXC3yYZz2TfMhx36WtYQ5p7JRjW+pzZx2PGQYZ75UFoBE0jwIANw==}
    dependencies:
      vue: 2.7.14
    dev: false

  /vue-style-loader@4.1.3:
    resolution: {integrity: sha512-sFuh0xfbtpRlKfm39ss/ikqs9AbKCoXZBpHeVZ8Tx650o0k0q/YCM7FRvigtxpACezfq6af+a7JeqVTWvncqDg==}
    dependencies:
      hash-sum: 1.0.2
      loader-utils: 1.4.2

  /vue-suglify@1.0.14:
    resolution: {integrity: sha512-Rq8NQ1avMoIHERbkBb1y7kiC3pI0UokTXCCtugtvuo/hX7qSdykYv+VJvm9UlkmEOR8UtPWBEpESlfgYGf3V1Q==}
    dev: false

  /vue-template-compiler@2.7.14:
    resolution: {integrity: sha512-zyA5Y3ArvVG0NacJDkkzJuPQDF8RFeRlzV2vLeSnhSpieO6LK2OVbdLPi5MPPs09Ii+gMO8nY4S3iKQxBxDmWQ==}
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0

  /vue-template-es2015-compiler@1.9.1:
    resolution: {integrity: sha512-4gDntzrifFnCEvyoO8PqyJDmguXgVPxKiIxrBKjIowvL9l+N66196+72XVYR8BBf1Uv1Fgt3bGevJ+sEmxfZzw==}

  /vue-the-mask@0.11.1:
    resolution: {integrity: sha512-UquSfnSWejD0zAfcD+3jJ1chUAkOAyoxya9Lxh9acCRtrlmGcAIvd0cQYraWqKenbuZJUdum+S174atv2AuEHQ==}
    dev: false

  /vue-toastr@2.1.2:
    resolution: {integrity: sha512-OomiRJ641K5Zl1/QEwUFg/lBcP7XeVKL044loVtBkGm9oXkm02h3dx13XNcbSXYL/hsqWPO/et5fB0ciU3oaCQ==}
    dev: false

  /vue-tree-list@1.5.0:
    resolution: {integrity: sha512-qHRJtPMmjCYA9I9kpHgIT6F1H7leR1ZxcNETrP2r97otSHYdHdK42qt12t4glf2mNvI+wBpY2fLClUGcaQJnpg==}
    dev: false

  /vue-unique-id@3.2.1:
    resolution: {integrity: sha512-Ih4vw3nx5O0M0q16Omx23T6VQW5U+RD16SEEbGa+aJWLJxFco8slQN6z6GUuzwgREgofH3ns9n7a97qr6DI73g==}
    engines: {node: '>=10.0.0'}
    dev: false

  /vue2-filters@0.11.1:
    resolution: {integrity: sha512-eKFVfFGocv/zOUDh1ZI0dE407RMeI3M5T+kUXpE8v+PMP/aVLkK024wgg0DA9/VX/GV4UdgYt29kwLEdlZMAqQ==}
    dev: false

  /vue2-google-maps@0.10.7:
    resolution: {integrity: sha512-y5aBJTAsfGvpsbfgPaeWU7o7QbOeINZKsP6SB9qrBe8U8EYBFO/bTWo3rcMq/NOtgT/XssV3Nwg/2u3no6jWbw==}
    dependencies:
      babel-runtime: 6.26.0
      marker-clusterer-plus: 2.1.4
    dev: false

  /vue2-transitions@0.3.0:
    resolution: {integrity: sha512-m1ad8K8kufqiEhj5gXHkkqOioI5sW0FaMbRiO0Tv2WFfGbO2eIKrfkFiO3HPQtMJboimaLCN4p/zL81clLbG4w==}
    dev: false

  /vue@2.7.14:
    resolution: {integrity: sha512-b2qkFyOM0kwqWFuQmgd4o+uHGU7T+2z3T+WQp8UBjADfEv2n4FEMffzBmCKNP0IGzOEEfYjvtcC62xaSKeQDrQ==}
    dependencies:
      '@vue/compiler-sfc': 2.7.14
      csstype: 3.1.1

  /vuelidate@0.7.7:
    resolution: {integrity: sha512-pT/U2lDI67wkIqI4tum7cMSIfGcAMfB+Phtqh2ttdXURwvHRBJEAQ0tVbUsW9Upg83Q5QH59bnCoXI7A9JDGnA==}
    engines: {node: '>= 4.0.0', npm: '>= 3.0.0'}
    dev: false

  /vuex-pathify@1.5.1(vue@2.7.14)(vuex@3.6.2):
    resolution: {integrity: sha512-+0cCPVIeleV8WJyUIQ9ZHqLyz9F5iRm9LzWl6dnCrGbjHFTLfS5H2uS1CtJ0t0BCzAReUrbEsRweAyooe4OqZQ==}
    peerDependencies:
      vue: ^2.5.19
      vuex: ^3.0.1
    dependencies:
      vue: 2.7.14
      vue-class-component: 7.2.6(vue@2.7.14)
      vuex: 3.6.2(vue@2.7.14)
    dev: false

  /vuex-persistedstate@3.2.1(vue@2.7.14)(vuex@3.6.2):
    resolution: {integrity: sha512-0OnHKGsCHJcvbEraaGZvuvX4aybM2oQWYRuZmIQB7zUjVM6tP+Hg+oXLrq9r6elT4she9SGtEbGE1L2+XdFgUw==}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.
    peerDependencies:
      vue: ^2.0.0
      vuex: ^2.0.0 || ^3.0.0
    dependencies:
      deepmerge: 4.2.2
      shvl: 2.0.3
      vue: 2.7.14
      vuex: 3.6.2(vue@2.7.14)
    dev: false

  /vuex@3.6.2(vue@2.7.14):
    resolution: {integrity: sha512-ETW44IqCgBpVomy520DT5jf8n0zoCac+sxWnn+hMe/CzaSejb/eVw2YToiXYX+Ex/AuHHia28vWTq4goAexFbw==}
    peerDependencies:
      vue: ^2.0.0
    dependencies:
      vue: 2.7.14
    dev: false

  /w3c-hr-time@1.0.2:
    resolution: {integrity: sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ==}
    deprecated: Use your platform's native performance.now() and performance.timeOrigin.
    dependencies:
      browser-process-hrtime: 1.0.0

  /w3c-xmlserializer@1.1.2:
    resolution: {integrity: sha512-p10l/ayESzrBMYWRID6xbuCKh2Fp77+sA0doRuGn4tTIMrrZVeqfpKjXHY+oDh3K4nLdPgNwMTVP6Vp4pvqbNg==}
    dependencies:
      domexception: 1.0.1
      webidl-conversions: 4.0.2
      xml-name-validator: 3.0.0
    dev: true

  /walker@1.0.8:
    resolution: {integrity: sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==}
    dependencies:
      makeerror: 1.0.12

  /watchpack-chokidar2@2.0.1:
    resolution: {integrity: sha512-nCFfBIPKr5Sh61s4LPpy1Wtfi0HE8isJ3d2Yb5/Ppw2P2B/3eVSEBjKfN0fmHJSK14+31KwMKmcrzs2GM4P0Ww==}
    requiresBuild: true
    dependencies:
      chokidar: 2.1.8
    transitivePeerDependencies:
      - supports-color
    optional: true

  /watchpack@1.7.5:
    resolution: {integrity: sha512-9P3MWk6SrKjHsGkLT2KHXdQ/9SNkyoJbabxnKOoJepsvJjJG8uYTR3yTPxPQvNDI3w4Nz1xnE0TLHK4RIVe/MQ==}
    dependencies:
      graceful-fs: 4.2.10
      neo-async: 2.6.2
    optionalDependencies:
      chokidar: 3.5.3
      watchpack-chokidar2: 2.0.1
    transitivePeerDependencies:
      - supports-color

  /watchpack@2.4.0:
    resolution: {integrity: sha512-Lcvm7MGST/4fup+ifyKi2hjyIAwcdI4HRgtvTpIUxBRhB+RFtUh8XtDOxUfctVCnhVi+QQj49i91OyvzkJl6cg==}
    engines: {node: '>=10.13.0'}
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.10

  /wbuf@1.7.3:
    resolution: {integrity: sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==}
    dependencies:
      minimalistic-assert: 1.0.1

  /wcwidth@1.0.1:
    resolution: {integrity: sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==}
    dependencies:
      defaults: 1.0.4

  /webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  /webidl-conversions@4.0.2:
    resolution: {integrity: sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg==}

  /webpack-bundle-analyzer@4.7.0:
    resolution: {integrity: sha512-j9b8ynpJS4K+zfO5GGwsAcQX4ZHpWV+yRiHDiL+bE0XHJ8NiPYLTNVQdlFYWxtpg9lfAQNlwJg16J9AJtFSXRg==}
    engines: {node: '>= 10.13.0'}
    hasBin: true
    dependencies:
      acorn: 8.8.1
      acorn-walk: 8.2.0
      chalk: 4.1.2
      commander: 7.2.0
      gzip-size: 6.0.0
      lodash: 4.17.21
      opener: 1.5.2
      sirv: 1.0.19
      ws: 7.5.9
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  /webpack-chain@6.5.1:
    resolution: {integrity: sha512-7doO/SRtLu8q5WM0s7vPKPWX580qhi0/yBHkOxNkv50f6qB76Zy9o2wRTrrPULqYTvQlVHuvbA8v+G5ayuUDsA==}
    engines: {node: '>=8'}
    dependencies:
      deepmerge: 1.5.2
      javascript-stringify: 2.1.0

  /webpack-cli@4.10.0(webpack-bundle-analyzer@4.7.0)(webpack@4.46.0):
    resolution: {integrity: sha512-NLhDfH/h4O6UOy+0LSso42xvYypClINuMNBVVzX4vX98TmTaTUxwRbXdhucbFMd2qLaCTcLq/PdYrvi8onw90w==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      '@webpack-cli/generators': '*'
      '@webpack-cli/migrate': '*'
      webpack: 4.x.x || 5.x.x
      webpack-bundle-analyzer: '*'
      webpack-dev-server: '*'
    peerDependenciesMeta:
      '@webpack-cli/generators':
        optional: true
      '@webpack-cli/migrate':
        optional: true
      webpack-bundle-analyzer:
        optional: true
      webpack-dev-server:
        optional: true
    dependencies:
      '@discoveryjs/json-ext': 0.5.7
      '@webpack-cli/configtest': 1.2.0(webpack-cli@4.10.0)(webpack@4.46.0)
      '@webpack-cli/info': 1.5.0(webpack-cli@4.10.0)
      '@webpack-cli/serve': 1.7.0(webpack-cli@4.10.0)
      colorette: 2.0.19
      commander: 7.2.0
      cross-spawn: 7.0.3
      fastest-levenshtein: 1.0.16
      import-local: 3.1.0
      interpret: 2.2.0
      rechoir: 0.7.1
      webpack: 4.46.0(webpack-cli@4.10.0)
      webpack-bundle-analyzer: 4.7.0
      webpack-merge: 5.8.0

  /webpack-dev-middleware@5.3.3(webpack@5.75.0):
    resolution: {integrity: sha512-hj5CYrY0bZLB+eTO+x/j67Pkrquiy7kWepMHmUMoPsmcUaeEnQJqFzHJOyxgWlq746/wUuA64p9ta34Kyb01pA==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      colorette: 2.0.19
      memfs: 3.4.10
      mime-types: 2.1.35
      range-parser: 1.2.1
      schema-utils: 4.0.0
      webpack: 5.75.0(webpack-cli@4.10.0)

  /webpack-dev-server@4.11.1(debug@4.3.4)(webpack-cli@4.10.0)(webpack@5.75.0):
    resolution: {integrity: sha512-lILVz9tAUy1zGFwieuaQtYiadImb5M3d+H+L1zDYalYoDl0cksAB1UNyuE5MMWJrG6zR1tXkCP2fitl7yoUJiw==}
    engines: {node: '>= 12.13.0'}
    hasBin: true
    peerDependencies:
      webpack: ^4.37.0 || ^5.0.0
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      '@types/bonjour': 3.5.10
      '@types/connect-history-api-fallback': 1.3.5
      '@types/express': 4.17.14
      '@types/serve-index': 1.9.1
      '@types/serve-static': 1.15.0
      '@types/sockjs': 0.3.33
      '@types/ws': 8.5.3
      ansi-html-community: 0.0.8
      bonjour-service: 1.0.14
      chokidar: 3.5.3
      colorette: 2.0.19
      compression: 1.7.4
      connect-history-api-fallback: 2.0.0
      default-gateway: 6.0.3
      express: 4.18.2
      graceful-fs: 4.2.10
      html-entities: 2.3.3
      http-proxy-middleware: 2.0.6(@types/express@4.17.14)(debug@4.3.4)
      ipaddr.js: 2.0.1
      open: 8.4.0
      p-retry: 4.6.2
      rimraf: 3.0.2
      schema-utils: 4.0.0
      selfsigned: 2.1.1
      serve-index: 1.9.1
      sockjs: 0.3.24
      spdy: 4.0.2
      webpack: 5.75.0(webpack-cli@4.10.0)
      webpack-cli: 4.10.0(webpack-bundle-analyzer@4.7.0)(webpack@4.46.0)
      webpack-dev-middleware: 5.3.3(webpack@5.75.0)
      ws: 8.11.0
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  /webpack-merge@5.8.0:
    resolution: {integrity: sha512-/SaI7xY0831XwP6kzuwhKWVKDP9t1QY1h65lAFLbZqMPIuYcD9QAW4u9STIbU9kaJbPBB/geU/gLr1wDjOhQ+Q==}
    engines: {node: '>=10.0.0'}
    dependencies:
      clone-deep: 4.0.1
      wildcard: 2.0.0

  /webpack-sources@1.4.3:
    resolution: {integrity: sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ==}
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1

  /webpack-sources@3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==}
    engines: {node: '>=10.13.0'}

  /webpack-virtual-modules@0.4.6:
    resolution: {integrity: sha512-5tyDlKLqPfMqjT3Q9TAqf2YqjwmnUleZwzJi1A5qXnlBCdj2AtOJ6wAWdglTIDOPgOiOrXeBeFcsQ8+aGQ6QbA==}

  /webpack@4.46.0(webpack-cli@4.10.0):
    resolution: {integrity: sha512-6jJuJjg8znb/xRItk7bkT0+Q7AHCYjjFnvKIWQPkNIOyRqoCGvkOs0ipeQzrqz4l5FtN5ZI/ukEHroeX/o1/5Q==}
    engines: {node: '>=6.11.5'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
      webpack-command: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
      webpack-command:
        optional: true
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-module-context': 1.9.0
      '@webassemblyjs/wasm-edit': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
      acorn: 6.4.2
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)
      chrome-trace-event: 1.0.3
      enhanced-resolve: 4.5.0
      eslint-scope: 4.0.3
      json-parse-better-errors: 1.0.2
      loader-runner: 2.4.0
      loader-utils: 1.4.2
      memory-fs: 0.4.1
      micromatch: 3.1.10
      mkdirp: 0.5.6
      neo-async: 2.6.2
      node-libs-browser: 2.2.1
      schema-utils: 1.0.0
      tapable: 1.1.3
      terser-webpack-plugin: 1.4.5(webpack@4.46.0)
      watchpack: 1.7.5
      webpack-cli: 4.10.0(webpack-bundle-analyzer@4.7.0)(webpack@4.46.0)
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - supports-color

  /webpack@5.75.0(webpack-cli@4.10.0):
    resolution: {integrity: sha512-piaIaoVJlqMsPtX/+3KTTO6jfvrSYgauFVdt8cr9LTHKmcq/AMd4mhzsiP7ZF/PGRNPGA8336jldh9l2Kt2ogQ==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      '@types/eslint-scope': 3.7.4
      '@types/estree': 0.0.51
      '@webassemblyjs/ast': 1.11.1
      '@webassemblyjs/wasm-edit': 1.11.1
      '@webassemblyjs/wasm-parser': 1.11.1
      acorn: 8.8.1
      acorn-import-assertions: 1.8.0(acorn@8.8.1)
      browserslist: 4.21.4
      chrome-trace-event: 1.0.3
      enhanced-resolve: 5.10.0
      es-module-lexer: 0.9.3
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.10
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 3.1.1
      tapable: 2.2.1
      terser-webpack-plugin: 5.3.6(webpack@5.75.0)
      watchpack: 2.4.0
      webpack-cli: 4.10.0(webpack-bundle-analyzer@4.7.0)(webpack@4.46.0)
      webpack-sources: 3.2.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js

  /websocket-driver@0.7.4:
    resolution: {integrity: sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==}
    engines: {node: '>=0.8.0'}
    dependencies:
      http-parser-js: 0.5.8
      safe-buffer: 5.2.1
      websocket-extensions: 0.1.4

  /websocket-extensions@0.1.4:
    resolution: {integrity: sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==}
    engines: {node: '>=0.8.0'}

  /whatwg-encoding@1.0.5:
    resolution: {integrity: sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw==}
    dependencies:
      iconv-lite: 0.4.24

  /whatwg-fetch@3.6.2:
    resolution: {integrity: sha512-bJlen0FcuU/0EMLrdbJ7zOnW6ITZLrZMIarMUVmdKtsGvZna8vxKYaexICWPfZ8qwf9fzNq+UEIZrnSaApt6RA==}

  /whatwg-mimetype@2.3.0:
    resolution: {integrity: sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g==}

  /whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  /whatwg-url@6.5.0:
    resolution: {integrity: sha512-rhRZRqx/TLJQWUpQ6bmrt2UV4f0HCQ463yQuONJqC6fO2VoEb1pTYddbe59SkYq87aoM5A3bdhMZiUiVws+fzQ==}
    dependencies:
      lodash.sortby: 4.7.0
      tr46: 1.0.1
      webidl-conversions: 4.0.2

  /whatwg-url@7.1.0:
    resolution: {integrity: sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg==}
    dependencies:
      lodash.sortby: 4.7.0
      tr46: 1.0.1
      webidl-conversions: 4.0.2

  /which-boxed-primitive@1.0.2:
    resolution: {integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==}
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4

  /which-module@2.0.0:
    resolution: {integrity: sha512-B+enWhmw6cjfVC7kS8Pj9pCrKSc5txArRyaYGe088shv/FGWH+0Rjx/xPgtsWfsUtS27FkP697E4DDhgrgoc0Q==}

  /which-pm-runs@1.1.0:
    resolution: {integrity: sha512-n1brCuqClxfFfq/Rb0ICg9giSZqCS+pLtccdag6C2HyufBrh3fBOiy9nb6ggRMvWOVH5GrdJskj5iGTZNxd7SA==}
    engines: {node: '>=4'}
    dev: true
    optional: true

  /which@1.3.1:
    resolution: {integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==}
    hasBin: true
    dependencies:
      isexe: 2.0.0

  /which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0

  /wide-align@1.1.5:
    resolution: {integrity: sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==}
    dependencies:
      string-width: 1.0.2
    dev: true
    optional: true

  /wildcard@2.0.0:
    resolution: {integrity: sha512-JcKqAHLPxcdb9KM49dufGXn2x3ssnfjbcaQdLlfZsL9rH9wgDQjUtDxbo8NE0F6SFvydeu1VhZe7hZuHsB2/pw==}

  /word-wrap@1.2.3:
    resolution: {integrity: sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==}
    engines: {node: '>=0.10.0'}

  /worker-farm@1.7.0:
    resolution: {integrity: sha512-rvw3QTZc8lAxyVrqcSGVm5yP/IJ2UcB3U0graE3LCFoZ0Yn2x4EoVSqJKdB/T5M+FLcRPjz4TDacRf3OCfNUzw==}
    dependencies:
      errno: 0.1.8

  /wrap-ansi@3.0.1:
    resolution: {integrity: sha512-iXR3tDXpbnTpzjKSylUJRkLuOrEC7hwEB221cgn6wtF8wpmz28puFXAEfPT5zrjM3wahygB//VuWEr1vTkDcNQ==}
    engines: {node: '>=4'}
    dependencies:
      string-width: 2.1.1
      strip-ansi: 4.0.0

  /wrap-ansi@5.1.0:
    resolution: {integrity: sha512-QC1/iN/2/RPVJ5jYK8BGttj5z83LmSKmvbvrXPNCLZSEb32KKVDJDl/MOt2N01qU2H/FkzEa9PKto1BqDjtd7Q==}
    engines: {node: '>=6'}
    dependencies:
      ansi-styles: 3.2.1
      string-width: 3.1.0
      strip-ansi: 5.2.0

  /wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  /wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  /write-file-atomic@2.4.1:
    resolution: {integrity: sha512-TGHFeZEZMnv+gBFRfjAcxL5bPHrsGKtnb4qsFAws7/vlh+QfwAaySIw4AXP9ZskTTh5GWu3FLuJhsWVdiJPGvg==}
    dependencies:
      graceful-fs: 4.2.10
      imurmurhash: 0.1.4
      signal-exit: 3.0.7

  /write@1.0.3:
    resolution: {integrity: sha512-/lg70HAjtkUgWPVZhZcm+T4hkL8Zbtp1nFNOn3lRrxnlv50SRBv7cR7RqR+GMsd3hUXy9hWBo4CHTbFTcOYwig==}
    engines: {node: '>=4'}
    dependencies:
      mkdirp: 0.5.6

  /ws@5.2.3:
    resolution: {integrity: sha512-jZArVERrMsKUatIdnLzqvcfydI85dvd/Fp1u/VOpfdDWQ4c9qWXe+VIeAbQ5FrDwciAkr+lzofXLz3Kuf26AOA==}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true
    dependencies:
      async-limiter: 1.0.1

  /ws@7.5.9:
    resolution: {integrity: sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  /ws@8.11.0:
    resolution: {integrity: sha512-HPG3wQd9sNQoT9xHyNCXoDUa+Xw/VevmY9FoHyQ+g+rrMn4j6FB4np7Z0OhdTgjx6MgQLK7jwSy1YecU1+4Asg==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  /xml-name-validator@3.0.0:
    resolution: {integrity: sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw==}

  /xml@1.0.1:
    resolution: {integrity: sha512-huCv9IH9Tcf95zuYCsQraZtWnJvBtLVE0QHMOs8bWyZAFZNDcYjsPq1nEx8jKA9y+Beo9v+7OBPRisQTjinQMw==}
    dev: false

  /xmlchars@2.2.0:
    resolution: {integrity: sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==}
    dev: true

  /xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}

  /y18n@4.0.3:
    resolution: {integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==}

  /y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  /yallist@2.1.2:
    resolution: {integrity: sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==}

  /yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  /yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

  /yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}

  /yargs-parser@10.1.0:
    resolution: {integrity: sha512-VCIyR1wJoEBZUqk5PA+oOBF6ypbwh5aNB3I50guxAL/quggdfs4TtNHQrSazFA3fYZ+tEqfs0zIGlv0c/rgjbQ==}
    dependencies:
      camelcase: 4.1.0
    dev: true

  /yargs-parser@13.1.2:
    resolution: {integrity: sha512-3lbsNRf/j+A4QuSZfDRA7HRSfWrzO0YjqTJd5kjAq37Zep1CEgaYmrH9Q3GwPiB9cHyd1Y1UwggGhJGoxipbzg==}
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  /yargs-parser@20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==}
    engines: {node: '>=10'}

  /yargs@13.3.2:
    resolution: {integrity: sha512-AX3Zw5iPruN5ie6xGRIDgqkT+ZhnRlZMLMHAs8tg7nRruy2Nb+i5o9bwghAogtM08q1dpr2LVoS8KSTMYpWXUw==}
    dependencies:
      cliui: 5.0.0
      find-up: 3.0.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 3.1.0
      which-module: 2.0.0
      y18n: 4.0.3
      yargs-parser: 13.1.2

  /yargs@16.2.0:
    resolution: {integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==}
    engines: {node: '>=10'}
    dependencies:
      cliui: 7.0.4
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9

  /yauzl@2.10.0:
    resolution: {integrity: sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==}
    dependencies:
      buffer-crc32: 0.2.13
      fd-slicer: 1.1.0
    dev: true
