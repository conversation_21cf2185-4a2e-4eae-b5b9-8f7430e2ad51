describe("Login", () => {
    beforeEach(() => {
        cy.visit("/login");
        cy.wait(500); //to give time for the Atlas modal to load
        cy.get('#remind-later').click()
    });

    it("Greets user", () => {
        cy.contains("h3", "Welcome to CarSaver");
    });

    it("Requires email", () => {
        cy.get("form")
            .contains("Login")
            .click();
        cy.location("pathname").should("eq", "/login");
    });

    it("Requires password", () => {
        cy.get("#email").type("<EMAIL>{enter}");
        cy.location("pathname").should("eq", "/login");
    });

    it("Requires valid email and password", () => {
        cy.get("#email").type("<EMAIL>");
        cy.get("#password").type("invalid{enter}");
        cy.location("pathname").should("eq", "/login");
    });

    it("navigates to /home on successful login", () => {
        cy.get("#email").type("<EMAIL>");
        cy.get("#password").type("carsaver{enter}");
        cy.location("pathname").should("eq", "/home");
    });
});
