describe('Business Users', () => {
    beforeEach(() => {

        cy.loginAdmin();
        cy.get('#side-menu').contains('Business Users').click();
        cy.location('pathname').should('eq', '/user/business-users')
    })

    it('Displays a list of Business Users', () => {
        cy.get('.page-heading').find('h2').should('have.text', 'Business Users')
    })

    it('Search by Name ', function () {
        cy.get('[data-cs=name]').click();
        cy.get('[data-cs=name').type('Admin Cypress');
        cy.get('#search-button').click();
        cy.get('.user-search-form').submit();
        cy.get('table').contains('Admin Cypress')
    })

    it('Search by Email ', function () {
        cy.get('#clear-button').click();
        cy.get('[data-cs=email]').click();
        cy.get('[data-cs=email').type('<EMAIL>');
        cy.get('#search-button').click();
        cy.get('.user-search-form').submit();
        cy.get('table').contains('Admin Cypress')
    })

    it('Navigate to User Details ', function () {
        cy.get('#clear-button').click();
        cy.get('[data-cs=email]').click();
        cy.get('[data-cs=email]').type('<EMAIL>');
        cy.get('#search-button').click();
        cy.get('.user-search-form').submit();
        cy.get('table').contains('Admin Cypress').click()
        cy.location('pathname').should('eq', '/user/business-users/41d2ba07-d66f-4720-8c32-ed8ae2d61753');
    })
})


